# File Processing in Collector

## Overview

The file processing module in the Collector service is responsible for converting various file types into a standardized text format that can be embedded and used for Retrieval-Augmented Generation (RAG). This document provides a detailed explanation of how file processing works in the Collector.

## Table of Contents

1. [Architecture](#architecture)
2. [Processing Flow](#processing-flow)
3. [File Type Converters](#file-type-converters)
4. [Chunking Strategies](#chunking-strategies)
5. [Metadata Extraction](#metadata-extraction)
6. [Output Format](#output-format)
7. [Error Handling](#error-handling)
8. [Performance Considerations](#performance-considerations)

## Architecture

The file processing module is organized as follows:

```
processSingleFile/
├── index.js                 # Main orchestration logic
├── convert/                 # File type converters
│   ├── pdf.js               # PDF converter
│   ├── docx.js              # DOCX converter
│   ├── xlsx.js              # XLSX converter
│   ├── text.js              # Text file converter
│   ├── audio.js             # Audio file converter
│   ├── image.js             # Image file converter
│   ├── email.js             # Email file converter
│   ├── code.js              # Code file converter
│   └── fallback.js          # Fallback converter
└── utils/                   # Processing utilities
    ├── chunker.js           # Text chunking utilities
    ├── metadata.js          # Metadata extraction utilities
    └── sanitizer.js         # Text sanitization utilities
```

## Processing Flow

The file processing flow follows these steps:

1. **File Validation**

   - Verify file integrity
   - Check file type and size
   - Validate file extension against content

2. **Converter Selection**

   - Determine appropriate converter based on file type
   - Fall back to generic converter if no specific converter exists

3. **Content Extraction**

   - Extract raw text content from the file
   - Preserve document structure when possible
   - Handle embedded elements (images, tables, etc.)

4. **Text Processing**

   - Clean and normalize text
   - Remove irrelevant content (headers, footers, etc.)
   - Handle special characters and encoding issues

5. **Chunking**

   - Split large documents into manageable chunks
   - Maintain context across chunk boundaries
   - Balance chunk size for optimal embedding

6. **Metadata Extraction**

   - Extract document metadata (title, author, creation date, etc.)
   - Generate processing metadata (timestamp, processor version, etc.)
   - Calculate token counts for each chunk

7. **Output Generation**
   - Create standardized JSON output
   - Include both content and metadata
   - Prepare for storage and embedding

## File Type Converters

The Collector includes specialized converters for different file types:

### PDF Converter (`convert/pdf.js`)

- Uses PDF.js for content extraction
- Handles text, images, and tables
- Preserves document structure
- Extracts metadata (title, author, creation date, etc.)
- Supports OCR for scanned documents

### DOCX Converter (`convert/docx.js`)

- Uses mammoth.js for content extraction
- Preserves formatting and structure
- Handles embedded images and tables
- Extracts document properties
- Supports styles and formatting

### XLSX Converter (`convert/xlsx.js`)

- Uses xlsx.js for content extraction
- Processes all sheets in a workbook
- Handles formulas and calculated values
- Preserves table structure
- Supports cell formatting and styles

### Text Converter (`convert/text.js`)

- Handles plain text files
- Supports various encodings
- Preserves line breaks and paragraphs
- Handles markdown formatting
- Supports CSV and other structured text formats

### Audio Converter (`convert/audio.js`)

- Transcribes audio content to text
- Supports multiple audio formats
- Preserves speaker information when available
- Includes timestamp information
- Handles background noise and audio quality issues

### Image Converter (`convert/image.js`)

- Extracts text from images using OCR
- Handles multiple image formats
- Processes diagrams and charts
- Extracts image metadata
- Includes image descriptions

### Email Converter (`convert/email.js`)

- Processes email files (MBOX, EML)
- Extracts email headers and body
- Handles attachments
- Preserves conversation threads
- Supports HTML and plain text email formats

### Code Converter (`convert/code.js`)

- Processes source code files
- Preserves code structure and comments
- Handles multiple programming languages
- Extracts function and class definitions
- Supports syntax highlighting information

### Fallback Converter (`convert/fallback.js`)

- Generic converter for unsupported file types
- Attempts basic text extraction
- Provides meaningful error messages
- Logs conversion attempts for future improvements

## Chunking Strategies

The Collector employs several chunking strategies to optimize document processing:

### 1. Semantic Chunking

- Respects document structure (paragraphs, sections, etc.)
- Maintains logical boundaries
- Preserves context within chunks
- Avoids splitting in the middle of sentences or paragraphs

### 2. Size-Based Chunking

- Ensures chunks don't exceed token limits
- Balances chunk size for optimal embedding
- Adjusts chunk size based on document type
- Handles very large documents efficiently

### 3. Hybrid Chunking

- Combines semantic and size-based approaches
- Prioritizes semantic boundaries when possible
- Falls back to size-based chunking when necessary
- Optimizes for both context preservation and embedding efficiency

### 4. Specialized Chunking

- Table chunking for spreadsheets and tabular data
- Code chunking for source code files
- Conversation chunking for emails and chat logs
- Transcript chunking for audio and video content

## Metadata Extraction

The Collector extracts and generates various metadata during file processing:

### Document Metadata

- Title and description
- Author and creation date
- Last modified date
- Source information
- Document type and format
- Language and encoding

### Processing Metadata

- Processing timestamp
- Processor version
- Conversion method used
- Chunking strategy applied
- Token count estimates
- Unique identifiers for chunks

### Content-Specific Metadata

- Table of contents for structured documents
- Headers and sections for hierarchical documents
- Speaker information for audio transcripts
- Function and class definitions for code files
- Email headers for email files

## Output Format

The Collector generates a standardized JSON output format:

```json
{
  "metadata": {
    "title": "Document Title",
    "author": "Document Author",
    "created_at": "2023-01-01T00:00:00Z",
    "modified_at": "2023-02-01T00:00:00Z",
    "source_type": "pdf",
    "language": "en",
    "processed_at": "2023-03-01T00:00:00Z",
    "processor_version": "1.0.0",
    "token_count_estimate": 1500
  },
  "chunks": [
    {
      "id": "chunk-1",
      "content": "Chunk content goes here...",
      "token_count_estimate": 250,
      "metadata": {
        "page": 1,
        "section": "Introduction",
        "chunk_type": "text"
      }
    },
    {
      "id": "chunk-2",
      "content": "More chunk content...",
      "token_count_estimate": 300,
      "metadata": {
        "page": 2,
        "section": "Background",
        "chunk_type": "text"
      }
    }
    // Additional chunks...
  ]
}
```

## Error Handling

The file processing module implements robust error handling:

### 1. Validation Errors

- File type validation
- File size validation
- File integrity validation
- Parameter validation

### 2. Conversion Errors

- Unsupported file format
- Corrupted file content
- Extraction failures
- Timeout errors

### 3. Processing Errors

- Text processing failures
- Chunking errors
- Metadata extraction failures
- Output generation errors

### 4. Error Reporting

- Detailed error messages
- Error categorization
- Suggested remediation steps
- Logging for debugging

## Performance Considerations

The file processing module is optimized for performance:

### 1. Resource Management

- Efficient memory usage for large files
- Stream processing when possible
- Temporary file cleanup
- Resource pooling for concurrent processing

### 2. Concurrency

- Parallel processing of multiple files
- Worker threads for CPU-intensive tasks
- Asynchronous I/O operations
- Rate limiting for external services (e.g., OCR)

### 3. Caching

- Caching of frequently accessed files
- Caching of conversion results
- Metadata caching
- Configuration caching

### 4. Optimization Techniques

- Lazy loading of converters
- Progressive processing for large files
- Early termination for invalid files
- Adaptive chunking based on file size and type
