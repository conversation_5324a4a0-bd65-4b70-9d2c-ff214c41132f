#!/usr/bin/env node

// This script fixes the structure of the locale files by ensuring that the
// keys in files outside of common.js are aligned with English special keys files.
// Uses AST-based reordering with recast and Babel parser to preserve comments,
// then formats with Prettier.

import fs from "fs";
import path from "path";
import { fileURLToPath, pathToFileURL } from "url";
import { execSync } from "child_process";
import recast from "recast";
import babelParser from "recast/parsers/babel.js";

// Helper to load ESM file
async function loadModule(filePath) {
  const url = pathToFileURL(filePath).href;
  return (await import(url)).default;
}

// Recursively build ordered object based on template
function buildOrdered(template, source) {
  const ordered = {};
  for (const key of Object.keys(template)) {
    const val = template[key];
    if (typeof val === "object" && val !== null && !Array.isArray(val)) {
      ordered[key] = buildOrdered(
        val,
        source && source[key] ? source[key] : {}
      );
    } else {
      ordered[key] = source && source[key] !== undefined ? source[key] : "";
    }
  }
  return ordered;
}

async function fixLocaleFile(locale, fileName) {
  const localesDir = path.resolve(
    path.dirname(fileURLToPath(import.meta.url)),
    "../frontend/src/locales"
  );
  const enPath = path.join(localesDir, "en", fileName);
  const localePath = path.join(localesDir, locale, fileName);

  if (!fs.existsSync(localePath)) {
    console.warn(`Locale file not found: ${localePath}`);
    return;
  }

  if (!fs.existsSync(enPath)) {
    console.warn(`English template file not found: ${enPath}`);
    return;
  }

  // Load English template
  const template = await loadModule(enPath);
  // Read original file to preserve comments
  const fileContent = fs.readFileSync(localePath, "utf-8");
  // Parse file using recast and Babel parser
  const ast = recast.parse(fileContent, { parser: babelParser });
  // Helper to reorder AST object properties based on template
  function reorderProperties(templateNode, properties) {
    const propMap = new Map();
    for (const prop of properties) {
      const key =
        prop.key.type === "Identifier" ? prop.key.name : prop.key.value;
      propMap.set(key, prop);
    }
    const newProps = [];
    for (const key of Object.keys(templateNode)) {
      const prop = propMap.get(key);
      if (prop) {
        const val = templateNode[key];
        if (
          val &&
          typeof val === "object" &&
          prop.value.type === "ObjectExpression"
        ) {
          prop.value.properties = reorderProperties(val, prop.value.properties);
        }
        newProps.push(prop);
      }
    }
    return newProps;
  }

  // Traverse AST to reorder properties on export default
  recast.types.visit(ast, {
    visitExportDefaultDeclaration(path) {
      const decl = path.node.declaration;
      if (decl.type === "ObjectExpression") {
        decl.properties = reorderProperties(template, decl.properties);
      }
      return false;
    },
  });

  // Print transformed code preserving comments
  const output = recast.print(ast).code;
  fs.writeFileSync(localePath, output, "utf-8");
  console.log(`Fixed structure for ${locale} - ${fileName}`);
}

// Run fixLocaleFile for all locale directories except English
(async () => {
  const localesDir = path.resolve(
    path.dirname(fileURLToPath(import.meta.url)),
    "../frontend/src/locales"
  );
  const localeDirs = fs.readdirSync(localesDir).filter((name) => {
    const fullPath = path.join(localesDir, name);
    return fs.statSync(fullPath).isDirectory() && name !== "en";
  });

  // Get all JavaScript files from the English directory to use as templates
  const enDir = path.join(localesDir, "en");
  const localeFiles = fs
    .readdirSync(enDir)
    .filter((file) => file.endsWith(".js"));

  for (const lang of localeDirs) {
    for (const fileName of localeFiles) {
      await fixLocaleFile(lang, fileName);
    }
  }
  // Format all locale files with Prettier
  try {
    console.log("Formatting locale files with Prettier...");
    const glob = path.join(localesDir, "**/*.js");
    execSync('npx prettier --write "' + glob + '"', { stdio: "inherit" });
  } catch (err) {
    console.error("Prettier failed:", err);
  }
})();
