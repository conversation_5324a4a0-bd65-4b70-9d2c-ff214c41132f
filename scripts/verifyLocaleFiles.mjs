#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { fileURLToPath, pathToFileURL } from "url";

// Determine script directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to locales directory
const localesDir = path.resolve(__dirname, "../frontend/src/locales");
// Folders to ignore
const ignoreDirs = ["__tests__"];

// Discover all locale code directories
const localeDirs = fs.readdirSync(localesDir).filter((name) => {
  const fullPath = path.join(localesDir, name);
  return fs.statSync(fullPath).isDirectory() && !ignoreDirs.includes(name);
});

// English locale directory
const enDir = path.join(localesDir, "en");
if (!fs.existsSync(enDir)) {
  console.error("English locale directory not found at", enDir);
  process.exit(1);
}

// List all English locale files except common.js
const enFiles = fs
  .readdirSync(enDir)
  .filter((file) => file.endsWith(".js") && file !== "common.js");

let hasErrors = false;
const errors = [];

// Compare keys of two translation objects recursively, checking missing, extra, and order mismatches
async function compareKeys(source, target, fileName, lang, basePath = "") {
  const keysA = Object.keys(source);
  const keysB = Object.keys(target);

  // Missing keys in target
  for (const key of keysA) {
    if (!keysB.includes(key)) {
      hasErrors = true;
      errors.push(
        `[${fileName}] Missing key '${basePath + key}' in locale '${lang}'`
      );
    }
  }

  // Extra keys in target
  for (const key of keysB) {
    if (!keysA.includes(key)) {
      hasErrors = true;
      errors.push(
        `[${fileName}] Extra key '${basePath + key}' in locale '${lang}'`
      );
    }
  }

  // Key order check
  const minLen = Math.min(keysA.length, keysB.length);
  for (let i = 0; i < minLen; i++) {
    if (keysA[i] !== keysB[i]) {
      hasErrors = true;
      errors.push(
        `[${fileName}] Key order mismatch at '${basePath + keysA[i]}' in locale '${lang}': expected index ${i}, found '${keysB[i]}'`
      );
      break;
    }
  }

  // Recursive check for nested objects
  for (const key of keysA) {
    const valA = source[key];
    const valB = target[key];
    if (
      valA &&
      typeof valA === "object" &&
      !Array.isArray(valA) &&
      valB &&
      typeof valB === "object"
    ) {
      await compareKeys(valA, valB, fileName, lang, `${basePath + key}.`);
    }
  }
}

(async () => {
  for (const file of enFiles) {
    const enPath = path.join(enDir, file);
    const enUrl = pathToFileURL(enPath).href;
    const { default: enTranslations } = await import(enUrl);

    for (const lang of localeDirs) {
      if (lang === "en") continue;
      const localeFilePath = path.join(localesDir, lang, file);
      if (!fs.existsSync(localeFilePath)) {
        hasErrors = true;
        errors.push(`Missing file '${file}' in locale '${lang}'`);
        continue;
      }
      const localeUrl = pathToFileURL(localeFilePath).href;
      const { default: localeTranslations } = await import(localeUrl);

      await compareKeys(enTranslations, localeTranslations, file, lang);
    }
  }

  if (hasErrors) {
    console.error("\nErrors found during locale verification:");
    errors.forEach((err) => console.error(" -", err));
    process.exit(1);
  } else {
    console.log(
      "✅ All locale files are present and consistent with English files."
    );
    process.exit(0);
  }
})();
