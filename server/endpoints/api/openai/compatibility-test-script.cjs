const OpenAI = require("openai");

/**
 * @type {import("openai").OpenAI}
 */
const client = new OpenAI({
  baseURL: "http://localhost:3001/api/v1/openai",
  apiKey: "ENTER_ISTLEGAL_API_KEY_HERE",
});

(async () => {
  // Models endpoint testing.
  console.log("Fetching /models");
  const modelList = await client.models.list();
  for await (const model of modelList) {
    console.log({ model });
  }

  // Test sync chat completion
  console.log("Running synchronous chat message");
  const syncCompletion = await client.chat.completions.create({
    messages: [
      {
        role: "system",
        content: "You are a helpful assistant who only speaks like a pirate.",
      },
      { role: "user", content: "What is ISTLegal?" },
      // {
      //   role: 'assistant',
      //   content: "Arrr, matey! ISTLegal be a fine tool fer sailin' the treacherous sea o' information with a powerful language model at yer helm. It's a potent instrument to handle all manner o' tasks involvin' text, like answerin' questions, generating prose, or even havin' a chat with digital scallywags like meself. Be there any specific treasure ye seek in the realm o' ISTLegal?"
      // },
      // { role: "user", content: "Why are you talking like a pirate?" },
    ],
    model: "istlegal", // must be workspace-slug
  });
  console.log(syncCompletion.choices[0]);

  // Test sync chat streaming completion
  console.log("Running asynchronous chat message");
  const asyncCompletion = await client.chat.completions.create({
    messages: [
      {
        role: "system",
        content: "You are a helpful assistant who only speaks like a pirate.",
      },
      { role: "user", content: "What is ISTLegal?" },
    ],
    model: "istlegal", // must be workspace-slug
    stream: true,
  });

  let message = "";
  for await (const chunk of asyncCompletion) {
    message += chunk.choices[0].delta.content;
    console.log({ message });
  }

  // Test embeddings creation
  console.log("Creating embeddings");
  const embedding = await client.embeddings.create({
    model: null, // model is optional for ISTLegal
    input: "This is a test string for embedding",
    encoding_format: "float",
  });
  console.log("Embedding created successfully:");
  console.log(`Dimensions: ${embedding.data[0].embedding.length}`);
  console.log(
    `First few values:`,
    embedding.data[0].embedding.slice(0, 5),
    `+ ${embedding.data[0].embedding.length - 5} more`
  );

  // Vector DB functionality
  console.log("Fetching /vector_stores");
  const vectorDBList = await client.beta.vectorStores.list();
  for await (const db of vectorDBList) {
    console.log(db);
  }
})();
