const { Document } = require("../models/documents");
const { reqBody, userFromSession } = require("../utils/http");
const {
  flexUserRoleValid,
  ROLES,
} = require("../utils/middleware/multiUserProtected");
const { validatedRequest } = require("../utils/middleware/validatedRequest");
const fs = require("fs");
const path = require("path");
const {
  handleFileUpload,
  getUserDocumentPathName,
} = require("../utils/files/multer");
const { CollectorApi } = require("../utils/collectorApi");
const prisma = require("../utils/prisma");

function getUserDocumentPath(user, isDocumentDrafting = false, workspaceId) {
  const { documentsPath } = require("../utils/files");
  const name = getUserDocumentPathName(user, isDocumentDrafting, workspaceId);
  let userDocumentPath = documentsPath;
  if (name.length > 0) {
    userDocumentPath = path.join(userDocumentPath, name);
  }
  return path.normalize(userDocumentPath);
}

function documentEndpoints(app) {
  const { isWithin, normalizePath, documentsPath } = require("../utils/files");
  if (!app) return;

  // Shared function to find and serve PDF files from both server and frontend locations
  const findAndServePdfFile = async (req, res, pdfPath) => {
    console.log(`PDF request received for path: ${pdfPath}`);
    const originalPath = pdfPath;
    let filesToTry = [];
    const allCheckedPaths = [];

    // Extract potentially workspace and filename info
    const filename = path.basename(pdfPath);

    // Configure paths for checks
    const documentsRootPath = documentsPath;
    const frontendHotdirPath = path.resolve(
      __dirname,
      "../../frontend/public/hotdir"
    );
    const serverPublicHotdirPath = path.resolve(__dirname, "../public/hotdir");
    const serverPdfviewPath = path.resolve(
      __dirname,
      "../storage/documents/pdfview"
    );

    // Keep track of what's been found in DB
    let dbDocumentFound = false;
    let jsonPath = null;

    // Try to resolve document from database first
    try {
      // Check if there's a document record with this ID or filename
      const document = await prisma.workspace_documents.findFirst({
        where: {
          OR: [
            { docId: pdfPath },
            { filename: pdfPath },
            { docpath: { contains: pdfPath } },
          ],
        },
        select: {
          id: true,
          docId: true,
          docpath: true,
          filename: true,
          metadata: true,
          workspaceId: true,
        },
      });

      if (document) {
        dbDocumentFound = true;
        console.log("Document found in database:", document.docId);

        try {
          // Try to parse the metadata for more info
          const metadata = JSON.parse(document.metadata || "{}");

          // Check if we have a PDF path in the document
          if (metadata.pdfPath) {
            console.log("Document has PDF path:", metadata.pdfPath);
            // If it's a server/pdfview path
            if (metadata.pdfPath.startsWith("/pdfview/")) {
              const pdfviewRelativePath = metadata.pdfPath.replace(
                "/pdfview/",
                ""
              );
              const pdfviewPath = path.join(
                serverPdfviewPath,
                pdfviewRelativePath
              );
              console.log("Checking pdfview path:", pdfviewPath);
              filesToTry.push({
                path: pdfviewPath,
                description: "Server pdfview path from metadata",
                type: "server-pdfview",
              });
            }
            // If it's a hotdir path
            else if (metadata.pdfPath.startsWith("/hotdir/")) {
              const hotdirRelativePath = metadata.pdfPath.replace(
                "/hotdir/",
                ""
              );
              const frontendPath = path.join(
                frontendHotdirPath,
                hotdirRelativePath
              );
              console.log(
                "Checking frontend hotdir path from metadata:",
                frontendPath
              );
              filesToTry.push({
                path: frontendPath,
                description: "Frontend hotdir path from metadata",
                type: "frontend",
              });
            }
          }

          // Also check if we have serverPdfPath in the document
          if (metadata.serverPdfPath) {
            console.log(
              "Document has server PDF path:",
              metadata.serverPdfPath
            );
            filesToTry.push({
              path: metadata.serverPdfPath,
              description: "Direct server PDF path from metadata",
              type: "server-direct",
            });
          }

          // Add frontendPath if available (for backwards compatibility)
          if (metadata.frontendPath) {
            const frontendHotdirFile = metadata.frontendPath.replace(
              "/hotdir/",
              ""
            );
            const frontendFullPath = path.join(
              frontendHotdirPath,
              frontendHotdirFile
            );
            console.log(
              "Checking frontend path from metadata:",
              frontendFullPath
            );
            filesToTry.push({
              path: frontendFullPath,
              description: "Frontend path from metadata",
              type: "frontend",
            });
          }
        } catch (metadataError) {
          console.warn(
            "Error parsing document metadata:",
            metadataError.message
          );
        }

        // Try to look up the JSON file path
        // ... existing code ...

        // Get path to the PDF based on JSON location
        jsonPath = document.docpath;

        // Handle both .pdf.json and .json extensions
        let derivedPdfPath;
        if (jsonPath.endsWith(".pdf.json")) {
          derivedPdfPath = jsonPath.replace(/\.pdf\.json$/, "");
        } else {
          derivedPdfPath = jsonPath.replace(/\.json$/, "");
        }

        console.log("Derived PDF path from JSON:", derivedPdfPath);
        filesToTry.push({
          path: derivedPdfPath,
          description: "Path derived from JSON document",
          type: "server",
        });

        // Also try the directory where the JSON is located
        const jsonDir = path.dirname(jsonPath);
        const dirPdfPath = path.join(jsonDir, filename);

        console.log("Also checking directory of JSON:", dirPdfPath);
        filesToTry.push({
          path: dirPdfPath,
          description: "Directory of JSON document with filename",
          type: "server",
        });
      }
    } catch (dbError) {
      console.warn("Error looking up JSON document:", dbError.message);
    }

    // Check in server pdfview directory with just the filename (no extension)
    const baseFilename = path.parse(filename).name;
    const pdfviewPath = path.join(serverPdfviewPath, baseFilename);
    console.log("Checking server pdfview with base filename:", pdfviewPath);
    filesToTry.push({
      path: pdfviewPath,
      description: "Server pdfview with base filename (no extension)",
      type: "server-pdfview",
    });

    // Also check with full filename in pdfview
    const fullPdfviewPath = path.join(serverPdfviewPath, filename);
    console.log("Checking server pdfview with full filename:", fullPdfviewPath);
    filesToTry.push({
      path: fullPdfviewPath,
      description: "Server pdfview with full filename",
      type: "server-pdfview",
    });

    // 1. Try in the frontend hotdir first (fastest access)
    const hotdirPath = path.join(frontendHotdirPath, filename);
    console.log("Checking frontend hotdir:", hotdirPath);
    filesToTry.push({
      path: hotdirPath,
      description: "Frontend hotdir with filename only",
      type: "frontend",
    });

    // 2. Try in server public hotdir (for Docker environment)
    const serverHotdirPath = path.join(serverPublicHotdirPath, filename);
    console.log("Checking server public hotdir:", serverHotdirPath);
    filesToTry.push({
      path: serverHotdirPath,
      description: "Server public hotdir with filename only",
      type: "server-public",
    });

    // Also try the full path in hotdir
    if (pdfPath !== filename) {
      const fullHotdirPath = path.join(frontendHotdirPath, pdfPath);
      console.log("Checking frontend hotdir with full path:", fullHotdirPath);
      filesToTry.push({
        path: fullHotdirPath,
        description: "Frontend hotdir with full path",
        type: "frontend",
      });

      const fullServerHotdirPath = path.join(serverPublicHotdirPath, pdfPath);
      console.log(
        "Checking server public hotdir with full path:",
        fullServerHotdirPath
      );
      filesToTry.push({
        path: fullServerHotdirPath,
        description: "Server public hotdir with full path",
        type: "server-public",
      });
    }

    // 3. Check collector hotdir as a last resort
    // Try various collector patterns
    const collectorPatterns = [
      filename,
      pdfPath,
      // Add typical collector patterns here
    ];

    for (const pattern of collectorPatterns) {
      const collectorPath = path.join(serverPdfviewPath, pattern);
      console.log("Checking collector hotdir:", collectorPath);
      filesToTry.push({
        path: collectorPath,
        description: "Collector hotdir fallback",
        type: "collector",
      });
    }

    // 4. Helper function to extract workspace information
    const extractWorkspaceFromPath = (docPath) => {
      // Try several methods to identify the workspace

      // Method 1: Look for pattern /documents/WORKSPACE/...
      const documentsMatch = docPath.match(/documents\/([^/]+)/);
      if (documentsMatch && documentsMatch[1]) {
        return documentsMatch[1];
      }

      // Method 2: First segment if path contains slashes
      if (docPath.includes("/")) {
        const firstSegment = docPath.split("/")[0];
        if (firstSegment && firstSegment.length > 0) {
          return firstSegment;
        }
      }

      return null;
    };

    // 5. Try direct path with workspace handling
    let workspace = extractWorkspaceFromPath(pdfPath);

    if (workspace) {
      console.log(`Identified workspace: ${workspace} from path: ${pdfPath}`);

      // Try with explicit workspace path: /documents/WORKSPACE/filename.pdf
      const workspaceDocPath = path.join(documentsPath, workspace, filename);
      filesToTry.push({
        path: workspaceDocPath,
        description: `Workspace root (${workspace}/${filename})`,
        type: "server",
      });

      // Try in custom-documents directory
      const customDocsPath = path.join(
        documentsPath,
        workspace,
        "custom-documents",
        filename
      );
      filesToTry.push({
        path: customDocsPath,
        description: `Workspace custom-documents (${workspace}/custom-documents/${filename})`,
        type: "server",
      });

      // Try to extract subdirectory structure
      if (pdfPath.includes("/")) {
        // Extract path after workspace
        const pathAfterWorkspace = pdfPath.substring(
          pdfPath.indexOf(workspace) + workspace.length
        );

        if (pathAfterWorkspace.includes("/")) {
          // Get everything between workspace/ and filename
          const subdirPath = pathAfterWorkspace.substring(
            1,
            pathAfterWorkspace.lastIndexOf("/")
          );

          if (subdirPath) {
            const fullSubdirPath = path.join(
              documentsPath,
              workspace,
              subdirPath,
              filename
            );
            filesToTry.push({
              path: fullSubdirPath,
              description: `Workspace subdirectory (${workspace}/${subdirPath}/${filename})`,
              type: "server",
            });
          }
        }
      }
    } else {
      console.log("Could not identify workspace from path:", pdfPath);

      // Try direct path as fallback
      filesToTry.push({
        path: path.join(documentsPath, pdfPath),
        description: "Direct path",
        type: "server",
      });

      // If we can't determine the workspace, try checking in each workspace directory
      try {
        const workspaces = fs
          .readdirSync(documentsPath)
          .filter((item) =>
            fs.statSync(path.join(documentsPath, item)).isDirectory()
          )
          .filter((dir) => !["attachments", "temp", "logs"].includes(dir));

        console.log(`Found ${workspaces.length} potential workspaces to check`);

        for (const ws of workspaces) {
          // Try in workspace root
          const wsPath = path.join(documentsPath, ws, filename);
          filesToTry.push({
            path: wsPath,
            description: `Potential workspace root (${ws}/${filename})`,
            type: "server",
          });

          // Try in custom-documents subdirectory
          const wsCustomPath = path.join(
            documentsPath,
            ws,
            "custom-documents",
            filename
          );
          filesToTry.push({
            path: wsCustomPath,
            description: `Potential workspace custom documents (${ws}/custom-documents/${filename})`,
            type: "server",
          });
        }
      } catch (err) {
        console.warn("Error listing workspace directories:", err.message);
      }
    }

    // Log all paths we're going to check
    console.log(
      `Attempting to locate PDF with ${filesToTry.length} possible paths:`
    );
    filesToTry.forEach((item, index) => {
      console.log(`Path ${index + 1}: ${item.description} - ${item.path}`);
    });

    // Try each possible file path
    for (const { path: tryPath, description, type } of [
      ...new Set(filesToTry),
    ]) {
      // Remove duplicates
      let candidatePath = tryPath;

      // Log for debugging
      console.log(`Checking ${description}:`, candidatePath);
      allCheckedPaths.push({ path: candidatePath, description });

      // Validate the path is within documents directory or frontend hotdir (security check)
      const isWithinDocs = isWithin(documentsPath, candidatePath);
      const isWithinHotdir = candidatePath.startsWith(frontendHotdirPath);

      if (!isWithinDocs && !isWithinHotdir) {
        console.warn(
          "Rejected path outside allowed directories:",
          candidatePath
        );
        continue;
      }

      // If file doesn't exist, log to debug
      if (!fs.existsSync(candidatePath)) {
        console.log(`File not found: ${candidatePath}`);
        continue;
      }

      // Log file stats to debug
      const stats = fs.statSync(candidatePath);
      console.log(`File found: ${candidatePath}`, {
        size: stats.size,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory(),
        created: stats.birthtime,
        modified: stats.mtime,
        location: isWithinHotdir ? "frontend-hotdir" : "server-docs",
      });

      // Check the file extension and content before serving
      const fileExt = path.extname(candidatePath).toLowerCase();
      if (fileExt !== ".pdf") {
        console.warn(
          `File extension is not .pdf but ${fileExt}, checking content...`
        );

        // If extension doesn't match, check file content (first few bytes)
        try {
          const fd = fs.openSync(candidatePath, "r");
          const buffer = Buffer.alloc(5);
          fs.readSync(fd, buffer, 0, 5, 0);
          fs.closeSync(fd);

          // Check if it starts with %PDF-
          const isActuallyPdf = buffer.toString().startsWith("%PDF-");
          if (!isActuallyPdf) {
            console.warn(
              `File does not appear to be a PDF based on content check: ${buffer.toString()}`
            );
            continue; // Skip this file and try next one
          } else {
            console.log("File content indicates a valid PDF despite extension");
          }
        } catch (contentError) {
          console.error("Error checking file content type:", contentError);
          // Continue anyway since we found the file
        }
      }

      console.log("PDF file found, serving directly:", candidatePath);

      // Enable CORS for all origins
      res.header("Access-Control-Allow-Origin", "*");
      res.header("Access-Control-Allow-Methods", "GET, OPTIONS");
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept"
      );

      // If this is a preflight OPTIONS request, respond with 200 OK
      if (req.method === "OPTIONS") {
        return res.status(200).send();
      }

      // Set correct content type for PDF
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `inline; filename="${path.basename(candidatePath)}"`
      );

      // Send the file
      try {
        return res.sendFile(candidatePath);
      } catch (sendError) {
        console.error("Error sending file:", sendError);
        const fileStream = fs.createReadStream(candidatePath);
        fileStream.pipe(res);
        return;
      }
    }

    // If we get here, we couldn't find the PDF file
    console.log("PDF file not found for any of the tried paths");
    return res.status(404).json({
      error: "PDF file not found",
      originalPath: originalPath,
      checkedPaths: allCheckedPaths,
    });
  };

  app.post(
    "/document/create-folder/:slug",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (request, response) => {
      try {
        const { slug } = request.params;
        const user = await userFromSession(request, response);
        const { name } = reqBody(request);
        const documentsPathUser = getUserDocumentPath(user, false, slug);
        const storagePath = path.join(documentsPathUser, name);
        if (
          !isWithin(path.resolve(documentsPathUser), path.resolve(storagePath))
        )
          throw new Error("Invalid folder name.");

        if (fs.existsSync(storagePath)) {
          response.status(500).json({
            success: false,
            message: "Folder by that name already exists",
          });
          return;
        }

        fs.mkdirSync(storagePath, { recursive: true });
        response.status(200).json({ success: true, message: null });
      } catch (e) {
        console.error(e);
        response.status(500).json({
          success: false,
          message: `Failed to create folder: ${e.message} `,
        });
      }
    }
  );

  app.post(
    "/document/move-files/:slug",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (request, response) => {
      try {
        const { slug } = request.params;
        const { files } = reqBody(request);
        const docpaths = files.map(({ from }) => from);
        const documents = await Document.where({ docpath: { in: docpaths } });

        const embeddedFiles = documents.map((doc) => doc.docpath);
        const moveableFiles = files.filter(
          ({ from }) => !embeddedFiles.includes(from)
        );

        const movePromises = moveableFiles.map(({ from, to }) => {
          const sourcePath = path.join(
            documentsPath,
            slug,
            normalizePath(from)
          );
          const destinationPath = path.join(
            documentsPath,
            slug,
            normalizePath(to)
          );

          return new Promise((resolve, reject) => {
            if (
              !isWithin(documentsPath, sourcePath) ||
              !isWithin(documentsPath, destinationPath)
            )
              return reject("Invalid file location");
            fs.rename(sourcePath, destinationPath, (err) => {
              if (err) {
                console.error(`Error moving file ${from} to ${to}:`, err);
                reject(err);
              } else {
                resolve();
              }
            });
          });
        });

        Promise.all(movePromises)
          .then(() => {
            const unmovableCount = files.length - moveableFiles.length;
            if (unmovableCount > 0) {
              response.status(200).json({
                success: true,
                message: `${unmovableCount}/${files.length} files not moved. Unembed them from all workspaces.`,
              });
            } else {
              response.status(200).json({
                success: true,
                message: null,
              });
            }
          })
          .catch((err) => {
            console.error("Error moving files:", err);
            response
              .status(500)
              .json({ success: false, message: "Failed to move some files." });
          });
      } catch (e) {
        console.error(e);
        response
          .status(500)
          .json({ success: false, message: "Failed to move files." });
      }
    }
  );

  app.post(
    "/document/attachment-process",
    handleFileUpload,
    async (request, response) => {
      try {
        const file = request.file;
        if (!file) {
          return response.status(400).json({
            success: false,
            error: "No file provided",
          });
        }

        // Get skip embedding flag from headers
        const skipEmbedding = request.headers["x-skip-embedding"] === "true";
        console.log("Skip embedding flag:", skipEmbedding);

        // Validate file type
        const fileExt = path.extname(file.originalname).toLowerCase();
        const isPDF = file.mimetype === "application/pdf" || fileExt === ".pdf";

        if (isPDF && (!file.size || file.size === 0)) {
          return response.status(400).json({
            success: false,
            error: "PDF file appears to be empty or corrupted",
          });
        }

        // Ensure storage directories exist
        const attachmentsPath = path.join(documentsPath, "attachments");
        if (!fs.existsSync(attachmentsPath)) {
          fs.mkdirSync(attachmentsPath, { recursive: true });
        }

        // Create a new instance of CollectorApi
        const collector = new CollectorApi();

        // Process the file using collector's processDocument method
        const result = await collector.processDocument(
          file.path,
          "attachments",
          {
            isTemporary: true,
            attachmentMode: true,
            skipEmbedding: skipEmbedding,
            metadata: {
              isAttachment: true,
              skipEmbedding: true,
            },
          }
        );

        // Always clean up the original file regardless of processing result
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }

        if (!result.success) {
          return response.status(400).json({
            success: false,
            error: result.reason || "Failed to process attachment",
          });
        }

        // Ensure the document location is relative to documentsPath
        const location = result.documents[0].location;
        const relativePath = location.replace(documentsPath + path.sep, "");
        const documentPath = path.join(documentsPath, relativePath);

        // Validate the path is within documentsPath
        if (!isWithin(documentsPath, documentPath)) {
          console.error("Invalid document path:", {
            documentsPath,
            location,
            documentPath,
          });
          return response.status(400).json({
            success: false,
            error: "Invalid document location",
          });
        }

        // Read the processed document content
        if (!fs.existsSync(documentPath)) {
          return response.status(400).json({
            success: false,
            error: "Processed document not found",
          });
        }

        const documentContent = fs.readFileSync(documentPath, "utf8");
        let document;
        try {
          document = JSON.parse(documentContent);
        } catch (error) {
          console.error("Error parsing document content:", error);
          return response.status(400).json({
            success: false,
            error: "Failed to parse processed document",
          });
        }

        // For PDFs, ensure we have valid content
        if (
          isPDF &&
          (!document.pageContent || document.pageContent.length < 10)
        ) {
          return response.status(400).json({
            success: false,
            error:
              "Failed to extract content from PDF. The file may be password protected or corrupted.",
          });
        }

        return response.status(200).json({
          success: true,
          document: {
            id: document.id,
            location: location,
            content: document.pageContent || document.content || "",
          },
        });
      } catch (error) {
        // Clean up the file in case of any error
        if (request.file && fs.existsSync(request.file.path)) {
          fs.unlinkSync(request.file.path);
        }
        console.error("Error processing attachment:", error);
        return response.status(500).json({
          success: false,
          error: error.message || "Internal server error processing attachment",
        });
      }
    }
  );

  app.post("/document/attachment-cleanup", async (request, response) => {
    try {
      const { location } = reqBody(request);
      if (!location) {
        return response.status(400).json({
          success: false,
          error: "No location provided",
        });
      }

      // Define paths - note that __dirname is in server/endpoints/api/document
      const frontendHotdir = path.resolve(
        __dirname,
        "../../../frontend/public/hotdir"
      );
      const serverDocsPath = path.join(documentsPath, "attachments");

      // Remove attachments/ prefix but keep the full filename for now
      const cleanLocation = location.replace(/^attachments\//, "");

      // Get the base filename without .json if it exists
      const baseFilename = cleanLocation.replace(/\.json$/, "");

      // Define all possible filenames
      const possibleFiles = [
        baseFilename, // e.g. doc.pdf
        baseFilename + ".json", // e.g. doc.pdf.json
        cleanLocation, // Original filename as-is
      ];

      let deleted = false;
      for (const filename of possibleFiles) {
        const frontendPath = path.resolve(frontendHotdir, filename);
        const serverPath = path.resolve(serverDocsPath, filename);

        // Check and delete from frontend hotdir
        if (fs.existsSync(frontendPath)) {
          fs.unlinkSync(frontendPath);
          deleted = true;
        }

        // Check and delete from server docs
        if (fs.existsSync(serverPath)) {
          fs.unlinkSync(serverPath);
          deleted = true;
        }
      }

      if (!deleted) {
        console.warn("No files found to cleanup:", {
          location,
          possibleFiles,
          frontendHotdir,
          serverDocsPath,
        });
      }

      return response.status(200).json({ success: true });
    } catch (error) {
      console.error("Error cleaning up attachment:", error);
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  });

  app.post(
    "/document/rename-folder/:slug",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager, ROLES.superuser]),
    ],
    async (request, response) => {
      try {
        const { slug } = request.params;
        const { oldName, newName } = reqBody(request);
        const user = await userFromSession(request, response);
        const documentsPathUser = getUserDocumentPath(user, false, slug);
        const oldFolderPath = path.join(documentsPathUser, oldName);
        const newFolderPath = path.join(documentsPathUser, newName);

        if (!fs.existsSync(oldFolderPath)) {
          return response.status(404).json({
            success: false,
            message: "Folder not found",
          });
        }

        if (fs.existsSync(newFolderPath)) {
          return response.status(400).json({
            success: false,
            message: "A folder with the new name already exists",
          });
        }
        await Document.updateDocpathAndMetadata(oldFolderPath, newFolderPath);
        fs.renameSync(oldFolderPath, newFolderPath);
        response.status(200).json({ success: true, message: null });
      } catch (e) {
        console.error(e);
        response.status(500).json({
          success: false,
          message: `Failed to rename folder: ${e.message}`,
        });
      }
    }
  );

  app.get("/document/contents", async (req, res) => {
    let { path: docPath } = req.query;
    console.log("Document contents request for path:", docPath);

    // Get the content blob
    try {
      if (!docPath) {
        console.log("Missing path parameter");
        return res.status(400).json({ error: "path parameter is required" });
      }

      if (docPath.startsWith("/hotdir/")) {
        docPath = docPath.substring("/hotdir/".length);
        console.log("Adjusted path from hotdir:", docPath);
      }

      console.log(
        "Looking up document with filename ending with:",
        docPath + ".json"
      );
      const document = await Document.contents(docPath);
      if (!document) {
        console.log("Document not found for path:", docPath);
        return res.status(404).json({ error: "Document not found" });
      }

      console.log("Document found, returning content for path:", docPath);

      // Check if path ends with .pdf and this is a direct file request
      if (docPath.toLowerCase().endsWith(".pdf")) {
        console.log(
          "PDF file requested, checking if we should return raw file"
        );
        const { documentsPath } = require("../utils/files");
        const filePath = path.join(documentsPath, docPath);

        // Check if this file exists and if so, serve the actual PDF instead of JSON
        if (fs.existsSync(filePath)) {
          console.log("Serving raw PDF file from:", filePath);
          return res.sendFile(filePath);
        }
      }

      res.json({
        content: document.content,
        path: docPath,
      });
    } catch (error) {
      console.error("Error reading document contents:", error);
      res.status(500).json({ error: "Error reading document contents" });
    }
  });

  app.get("/document/pdf-contents", async (req, res) => {
    try {
      let { path: requestPath } = req.query;
      console.log("PDF document request for path:", requestPath);
      console.log("Request headers:", req.headers);

      if (!requestPath) {
        console.log("Missing path parameter");
        return res.status(400).json({ error: "path parameter is required" });
      }

      // Clean up the path if it includes problematic prefixes
      if (requestPath.startsWith("/hotdir/")) {
        requestPath = requestPath.substring("/hotdir/".length);
        console.log("Cleaned path from /hotdir/ prefix:", requestPath);
      }

      // Also clean up document prefix
      if (requestPath.startsWith("/document/")) {
        requestPath = requestPath.substring("/document/".length);
        console.log("Cleaned path from /document/ prefix:", requestPath);
      }

      return findAndServePdfFile(req, res, requestPath);
    } catch (error) {
      console.error("Error serving PDF document:", error);
      res.status(500).json({ error: "Error serving PDF document" });
    }
  });

  // Simple endpoint for PDF files to avoid query parameter encoding issues
  app.get("/document/pdf/:filename", async (req, res) => {
    try {
      const { filename } = req.params;
      console.log("PDF direct document request for filename:", filename);

      if (!filename) {
        console.log("Missing filename parameter");
        return res
          .status(400)
          .json({ error: "filename parameter is required" });
      }

      return findAndServePdfFile(req, res, filename);
    } catch (error) {
      console.error("Error serving PDF document:", error);
      return res.status(500).json({ error: "Internal server error" });
    }
  });

  // Direct PDF contents endpoint to avoid query parameter encoding issues
  app.get("/document/pdf-contents/:filename", async (req, res) => {
    try {
      const { filename } = req.params;
      console.log("PDF contents request with direct filename:", filename);

      if (!filename) {
        console.log("Missing filename parameter");
        return res
          .status(400)
          .json({ error: "filename parameter is required" });
      }

      return findAndServePdfFile(req, res, filename);
    } catch (error) {
      console.error("Error serving PDF file:", error);
      res.status(500).json({ error: "Error serving PDF file" });
    }
  });

  // Endpoint to resolve PDF paths
  app.get("/document/resolve-pdf-path", async (req, res) => {
    const { path: documentPath } = req.query;
    console.log("Resolving PDF path for:", documentPath);

    if (!documentPath) {
      return res
        .status(400)
        .json({ success: false, error: "Missing path parameter" });
    }

    // Extract filename from path
    const filename = path.basename(documentPath);
    console.log("Extracted filename:", filename);

    const pathsToCheck = [];

    // Get the first segment as the workspace
    let workspace = null;
    if (documentPath.includes("/")) {
      workspace = documentPath.split("/")[0];
      console.log("Extracted workspace from first segment:", workspace);
    }

    if (workspace) {
      // Log to verify the extracted workspace
      console.log(`Working with workspace: ${workspace}`);

      // 1. Original path with full structure
      const originalPath = path.join(documentsPath, documentPath);
      pathsToCheck.push({
        path: originalPath,
        description: `Full original path (${documentPath})`,
        relativePath: documentPath,
      });

      // 2. Check in workspace root
      const workspaceRootPath = path.join(documentsPath, workspace, filename);
      pathsToCheck.push({
        path: workspaceRootPath,
        description: `Workspace root (${workspace}/${filename})`,
        relativePath: `${workspace}/${filename}`,
      });

      // 3. Check in custom-documents
      const customDocsPath = path.join(
        documentsPath,
        workspace,
        "custom-documents",
        filename
      );
      pathsToCheck.push({
        path: customDocsPath,
        description: `Workspace custom-documents (${workspace}/custom-documents/${filename})`,
        relativePath: `${workspace}/custom-documents/${filename}`,
      });

      // 4. Extract subdirectory if present
      const pathParts = documentPath.split("/");
      if (pathParts.length > 2) {
        // We have subdirectories between workspace and filename
        const subDirParts = pathParts.slice(1, -1); // Skip workspace and filename
        const subDir = subDirParts.join("/");

        if (subDir) {
          console.log(`Found subdirectory: ${subDir}`);

          const subDirPath = path.join(
            documentsPath,
            workspace,
            subDir,
            filename
          );
          pathsToCheck.push({
            path: subDirPath,
            description: `Subdirectory path (${workspace}/${subDir}/${filename})`,
            relativePath: `${workspace}/${subDir}/${filename}`,
          });
        }
      }
    } else {
      // If we can't identify the workspace, use the full path
      console.log("Could not identify workspace from path");

      if (!documentPath) {
        return res.status(400).json({
          error: "Invalid document path",
          details: "Document path is undefined or invalid",
        });
      }

      const directPath = path.join(documentsPath, documentPath);
      pathsToCheck.push({
        path: directPath,
        description: "Direct path (no workspace identified)",
        relativePath: documentPath,
      });

      // Also try checking each workspace directory
      try {
        const potentialWorkspaces = fs
          .readdirSync(documentsPath)
          .filter((item) => {
            try {
              return fs.statSync(path.join(documentsPath, item)).isDirectory();
            } catch (e) {
              return false;
            }
          })
          .filter((dir) => !["attachments", "temp", "logs"].includes(dir));

        console.log(
          `Checking ${potentialWorkspaces.length} potential workspaces`
        );

        for (const ws of potentialWorkspaces) {
          // Check workspace root
          const wsPath = path.join(documentsPath, ws, filename);
          pathsToCheck.push({
            path: wsPath,
            description: `Potential workspace root (${ws}/${filename})`,
            relativePath: `${ws}/${filename}`,
          });

          // Check custom-documents
          const wsCustomPath = path.join(
            documentsPath,
            ws,
            "custom-documents",
            filename
          );
          pathsToCheck.push({
            path: wsCustomPath,
            description: `Potential workspace custom-documents (${ws}/custom-documents/${filename})`,
            relativePath: `${ws}/custom-documents/${filename}`,
          });
        }
      } catch (err) {
        console.warn("Error scanning workspace directories:", err.message);
      }
    }

    console.log(`Checking ${pathsToCheck.length} paths for PDF`);

    // Create arrays to track the paths we've checked
    const checkedPaths = [];
    let resolvedPdfPath = null;
    let resolvedServerPath = null;
    let resolvedFrontendPath = null;
    let successfulRelativePath = null; // Variable to store the correct relativePath

    // Check each path for the PDF file
    for (const { path: pdfPath, description, relativePath } of pathsToCheck) {
      console.log(`Checking ${description}:`, pdfPath);
      checkedPaths.push({ path: pdfPath, description });

      // Make sure the path is within our documents directory for security
      if (!isWithin(documentsPath, pdfPath)) {
        console.warn("Path is outside documents directory:", pdfPath);
        continue;
      }

      // Check if the file exists
      if (fs.existsSync(pdfPath)) {
        console.log("PDF file found at:", pdfPath);

        // Check if it's actually a PDF
        try {
          const fd = fs.openSync(pdfPath, "r");
          const buffer = Buffer.alloc(5);
          fs.readSync(fd, buffer, 0, 5, 0);
          fs.closeSync(fd);

          if (!buffer.toString().startsWith("%PDF-")) {
            console.warn("File exists but is not a PDF:", pdfPath);
            continue;
          }
        } catch (err) {
          console.warn("Error checking if file is PDF:", err.message);
          continue;
        }

        // Save the resolved paths
        resolvedPdfPath = pdfPath;
        resolvedServerPath = pdfPath;
        successfulRelativePath = relativePath; // Capture the relativePath from the successful check

        // For frontend path, try to determine the correct URL
        // The frontend path should be /document/PDF_FILENAME or similar service path
        resolvedFrontendPath = `/document/${successfulRelativePath}`;

        break;
      } else {
        console.log("PDF file not found at:", pdfPath);
      }
    }

    // If we found a PDF, return success response
    if (resolvedPdfPath) {
      if (!successfulRelativePath) {
        // This case should ideally not be hit if resolvedPdfPath is set,
        // but as a fallback, try to derive it if somehow missed.
        console.warn(
          "successfulRelativePath was not set, attempting to derive from resolvedServerPath and documentsPath."
        );
        if (
          resolvedServerPath &&
          documentsPath &&
          resolvedServerPath.startsWith(documentsPath)
        ) {
          successfulRelativePath = resolvedServerPath
            .substring(documentsPath.length)
            .replace(/^\\|^\//, "");
        } else {
          // Ultimate fallback: use basename. This might be incorrect for nested structures.
          console.error(
            "Could not derive successfulRelativePath. Using basename as a potentially incorrect fallback."
          );
          successfulRelativePath = path.basename(
            resolvedServerPath || "unknown.pdf"
          );
        }
      }

      return res.json({
        success: true,
        serverPath: successfulRelativePath, // Use the captured/derived relative path
        frontendPath: resolvedFrontendPath, // This uses successfulRelativePath prefixed with /document/
        checkedPaths: checkedPaths,
      });
    } else {
      console.log("PDF not found in any expected location");
      return res.status(404).json({
        success: false,
        error: "PDF not found in any expected location",
        checkedPaths: checkedPaths,
      });
    }
  });
}

module.exports = {
  documentEndpoints,
  getUserDocumentPathName,
};
