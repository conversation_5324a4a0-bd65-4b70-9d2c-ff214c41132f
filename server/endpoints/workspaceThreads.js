const {
  multiUserMode,
  userFromSession,
  reqBody,
  safeJsonParse,
} = require("../utils/http");
const { validatedRequest } = require("../utils/middleware/validatedRequest");
const { Telemetry } = require("../models/telemetry");
const {
  flexUserRoleValid,
  ROLES,
} = require("../utils/middleware/multiUserProtected");
const { EventLogs } = require("../models/eventLogs");
const { WorkspaceThread } = require("../models/workspaceThread");
const {
  validWorkspaceSlug,
  validWorkspaceAndThreadSlug,
} = require("../utils/middleware/validWorkspace");
const { WorkspaceChats } = require("../models/workspaceChats");
const { users } = require("../models/user");
const { convertToChatHistory } = require("../utils/helpers/chat/responses");
const { t } = require("../utils/i18n");
const { Workspace } = require("../models/workspace");

function workspaceThreadEndpoints(app) {
  if (!app) return;

  app.post(
    "/workspace/:slug/thread/new",
    [validatedRequest, flexUserRoleValid([ROLES.all]), validWorkspaceSlug],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);
        const { slug = null } = request.params;
        const workspace = response.locals.workspace;
        const { thread, message } = await WorkspaceThread.new(
          workspace,
          user?.id
        );

        let LLMSelection;
        let Embedder;
        let VectorDbSelection;
        if (slug === "document-drafting") {
          LLMSelection = process.env.LLM_PROVIDER_DD;
          Embedder = process.env.EMBEDDING_ENGINE_DD;
          VectorDbSelection = process.env.VECTOR_DB_DD;
        } else {
          LLMSelection = process.env.LLM_PROVIDER;
          Embedder = process.env.EMBEDDING_ENGINE;
          VectorDbSelection = process.env.VECTOR_DB;
        }

        await Telemetry.sendTelemetry(
          "workspace_thread_created",
          {
            multiUserMode: multiUserMode(response),
            LLMSelection: LLMSelection || "openai",
            Embedder: Embedder || "inherit",
            VectorDbSelection: VectorDbSelection || "lancedb",
            TTSSelection: process.env.TTS_PROVIDER || "native",
          },
          user?.id
        );

        await EventLogs.logEvent(
          "workspace_thread_created",
          {
            workspaceName: workspace?.name || "Unknown Workspace",
          },
          user?.id
        );
        response.status(200).json({ thread, message });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.get(
    "/workspace/:slug/threads",
    [validatedRequest, flexUserRoleValid([ROLES.all]), validWorkspaceSlug],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);
        const workspace = response.locals.workspace;

        if (!user) {
          return response.status(401).json({ error: "Unauthorized" });
        }

        const { User } = require("../models/user");
        const { ThreadShare } = require("../models/threadShare");
        const userInfo = await User.getWithOrg({ id: user.id });

        // Always start with threads the user owns
        const userThreads = await WorkspaceThread.where({
          workspace_id: workspace.id,
          user_id: user.id,
        });

        // Get threads explicitly shared with this user
        const sharedThreadIds = await ThreadShare.getThreadIdsSharedWithUser(
          user.id
        );
        let sharedThreads = [];
        if (sharedThreadIds.length > 0) {
          sharedThreads = await WorkspaceThread.where({
            workspace_id: workspace.id,
            id: { in: sharedThreadIds },
            user_id: { not: user.id }, // Exclude threads the user already owns
          });
        }

        // Get threads shared with the user's organization
        let orgThreads = [];
        if (userInfo?.organizationId) {
          const orgUsers = await User.where({
            organizationId: userInfo.organizationId,
          });

          if (orgUsers?.users?.length > 0) {
            const orgUserIds = orgUsers.users.map((u) => u.id);
            orgThreads = await WorkspaceThread.where({
              workspace_id: workspace.id,
              sharedWithOrg: true,
              user_id: { in: orgUserIds },
              id: {
                notIn: [
                  ...userThreads.map((t) => t.id),
                  ...sharedThreads.map((t) => t.id),
                ],
              },
            });
          }
        }

        // Get workspace owner's threads if workspace is shared with the user's organization
        let workspaceOwnerThreads = [];
        const { WorkspaceShare } = require("../models/workspaceShare");
        const isWorkspaceOwner = workspace.user_id === user.id;
        const hasWorkspaceAccess =
          isWorkspaceOwner ||
          (await WorkspaceShare.hasAccess(workspace.id, user.id));

        if (hasWorkspaceAccess && !isWorkspaceOwner) {
          workspaceOwnerThreads = await WorkspaceThread.where({
            workspace_id: workspace.id,
            user_id: workspace.user_id,
            id: {
              notIn: [
                ...userThreads.map((t) => t.id),
                ...sharedThreads.map((t) => t.id),
                ...orgThreads.map((t) => t.id),
              ],
            },
          });
        }

        // Combine all threads, removing duplicates
        const allThreads = [
          ...userThreads,
          ...sharedThreads,
          ...orgThreads,
          ...workspaceOwnerThreads,
        ];

        const uniqueThreads = [];
        const seenThreads = new Set();

        for (const thread of allThreads) {
          if (!seenThreads.has(thread.id)) {
            seenThreads.add(thread.id);
            uniqueThreads.push(thread);
          }
        }

        response.status(200).json({ threads: uniqueThreads });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.delete(
    "/workspace/:slug/thread/:threadSlug",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (_, response) => {
      try {
        const thread = response.locals.thread;
        await WorkspaceThread.delete({ id: thread.id });
        response.sendStatus(200).end();
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.delete(
    "/workspace/:slug/thread-bulk-delete",
    [validatedRequest, flexUserRoleValid([ROLES.all]), validWorkspaceSlug],
    async (request, response) => {
      try {
        const { slugs = [] } = reqBody(request);
        if (slugs.length === 0) return response.sendStatus(200).end();

        const user = await userFromSession(request, response);
        const workspace = response.locals.workspace;
        await WorkspaceThread.delete({
          slug: { in: slugs },
          user_id: user?.id ?? null,
          workspace_id: workspace.id,
        });
        response.sendStatus(200).end();
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.get(
    "/workspace/:slug/thread/:threadSlug/chats",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);
        const workspace = response.locals.workspace;
        const thread = response.locals.thread;

        // Check if the user has access to this thread
        const isThreadOwner = thread.user_id === user?.id;
        const { ThreadShare } = require("../models/threadShare");
        const hasThreadAccess =
          isThreadOwner || (await ThreadShare.hasAccess(thread.id, user?.id));

        if (!hasThreadAccess) {
          return response
            .status(403)
            .json({ error: "You don't have access to this thread" });
        }

        // If thread is shared or user is the owner, get all history from this thread
        const history = await WorkspaceChats.where(
          {
            workspaceId: workspace.id,
            thread_id: thread.id,
            api_session_id: null, // Do not include API session chats.
            include: true,
          },
          null,
          { id: "asc" }
        );

        response
          .status(200)
          .json({ history: await convertToChatHistory(request, history) });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/workspace/:slug/thread/:threadSlug/update",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (request, response) => {
      try {
        const data = reqBody(request);
        const currentThread = response.locals.thread;
        const { thread, message } = await WorkspaceThread.update(
          currentThread,
          data
        );
        response.status(200).json({ thread, message });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.delete(
    "/workspace/:slug/thread/:threadSlug/delete-edited-chats",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (request, response) => {
      try {
        const { startingId } = reqBody(request);
        const user = await userFromSession(request, response);
        const workspace = response.locals.workspace;
        const thread = response.locals.thread;

        await WorkspaceChats.delete({
          workspaceId: Number(workspace.id),
          thread_id: Number(thread.id),
          user_id: user?.id,
          id: { gte: Number(startingId) },
        });

        response.sendStatus(200).end();
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/workspace/:slug/thread/:threadSlug/update-chat",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (request, response) => {
      try {
        const { chatId, newText = null } = reqBody(request);
        if (!newText || !String(newText).trim())
          throw new Error("Cannot save empty response");

        const user = await userFromSession(request, response);
        const workspace = response.locals.workspace;
        const thread = response.locals.thread;
        const existingChat = await WorkspaceChats.get({
          workspaceId: workspace.id,
          thread_id: thread.id,
          user_id: user?.id,
          id: Number(chatId),
        });
        if (!existingChat) throw new Error("Invalid chat.");

        const chatResponse = safeJsonParse(existingChat.response, null);
        if (!chatResponse) throw new Error("Failed to parse chat response");

        await WorkspaceChats.update(existingChat.id, {
          response: JSON.stringify({
            ...chatResponse,
            text: String(newText),
          }),
        });

        response.sendStatus(200).end();
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/workspace/:slug/thread/:threadSlug/insert-history",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (request, response) => {
      try {
        const { messages } = reqBody(request);
        const workspace = response.locals.workspace;
        const thread = response.locals.thread;
        const user = await userFromSession(request, response);

        if (!Array.isArray(messages) || messages.length === 0) {
          return response
            .status(400)
            .json({ error: "Invalid message history." });
        }

        console.log(
          `Attempting to insert ${messages.length} messages into history for thread ${thread.id} by user ${user?.id || "anonymous"}`
        );

        let userMessage = null;
        let successCount = 0;

        for (let i = 0; i < messages.length; i++) {
          const message = messages[i];
          // Validate required message fields
          if (!message.role || typeof message.content !== "string") {
            console.warn(`Skipping invalid message:`, message);
            continue; // Skip this message and continue with the next one
          }

          try {
            if (message.role === "user") {
              // For user messages, we have two options:
              // 1. If there's an assistant message after this one, we'll pair them
              // 2. If this is the last message or not followed by an assistant, save it alone

              const nextMessage =
                i + 1 < messages.length ? messages[i + 1] : null;

              if (nextMessage && nextMessage.role === "assistant") {
                // Case 1: User message followed by assistant message - pair them
                await WorkspaceChats.new({
                  workspaceId: workspace.id,
                  threadId: thread.id,
                  prompt: message.content,
                  response: { text: nextMessage.content },
                  user:
                    user || (message.userId ? { id: message.userId } : null),
                  include: true,
                });
                successCount += 2; // Count both messages
                i++; // Skip the next message since we've processed it
              } else {
                // Case 2: User message with no assistant reply
                await WorkspaceChats.new({
                  workspaceId: workspace.id,
                  threadId: thread.id,
                  prompt: message.content,
                  response: { text: "" }, // Empty response
                  user:
                    user || (message.userId ? { id: message.userId } : null),
                  include: true,
                });
                successCount++;
              }
            } else if (
              message.role === "assistant" &&
              i > 0 &&
              messages[i - 1].role !== "user"
            ) {
              // This is an assistant message not preceded by a user message
              // We need to create a synthetic pair with an empty user message
              await WorkspaceChats.new({
                workspaceId: workspace.id,
                threadId: thread.id,
                prompt: "", // Empty prompt
                response: { text: message.content },
                user: user || (message.userId ? { id: message.userId } : null),
                include: true,
              });
              successCount++;
            }
            // Skip assistant messages that follow user messages as they're handled in the user message case
          } catch (insertError) {
            console.error(
              `Error inserting individual message:`,
              insertError,
              message
            );
            // Continue with other messages instead of failing the entire operation
          }
        }

        // Log success information
        console.log(
          `Successfully inserted ${successCount} of ${messages.length} messages`
        );

        response.status(200).json({
          success: true,
          message: `Successfully imported ${successCount} messages into the thread.`,
        });
      } catch (e) {
        console.error("Error inserting message history:", e);
        response
          .status(500)
          .json({ error: e.message || "Internal server error" });
      }
    }
  );

  // Get thread share status
  app.get(
    "/workspace/:slug/thread/:threadSlug/share-status",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (request, response) => {
      try {
        const thread = response.locals.thread;
        const { ThreadShare } = require("../models/threadShare");
        const { shares, error } = await ThreadShare.getByThread(thread.id);

        if (error) {
          return response.status(500).json({
            success: false,
            error,
          });
        }

        const sharedUserIds = shares.map((share) => share.userId);

        response.status(200).json({
          success: true,
          sharedUserIds,
          sharedWithOrg: thread.sharedWithOrg || false,
        });
      } catch (e) {
        console.error(e.message, e);
        response.status(500).json({
          success: false,
          error: await t("errors.thread.getStatusFailed"),
        });
      }
    }
  );

  /**
   * @swagger
   * /workspace/{slug}/thread/{threadSlug}/share:
   *   post:
   *     summary: Share a thread with users or the organization
   *     parameters:
   *       - in: path
   *         name: slug
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: threadSlug
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       description: User IDs and optional org sharing flag
   *       required: true
   *     responses:
   *       200:
   *         description: Success
   *       403:
   *         description: Only thread owners can perform this action
   *       500:
   *         description: Server error
   */
  // Share thread with users or organization
  app.post(
    "/workspace/:slug/thread/:threadSlug/share",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (request, response) => {
      try {
        const thread = response.locals.thread;
        const { userIds = [], shareWithOrg = undefined } = reqBody(request);
        const user = await userFromSession(request, response);
        const { ThreadShare } = require("../models/threadShare");

        // Check if user is the owner of the thread
        if (thread.user_id !== user.id) {
          return response.status(403).json({
            success: false,
            error: await t("errors.thread.onlyOwnerShare"),
          });
        }

        // Handle organization-wide sharing
        if (shareWithOrg !== undefined) {
          await WorkspaceThread.update(thread, {
            sharedWithOrg: !!shareWithOrg,
          });
        }

        // Handle user-specific sharing
        if (userIds && userIds.length > 0) {
          for (const userId of userIds) {
            await ThreadShare.create(thread.id, userId);
          }
        }

        response.status(200).json({ success: true });
      } catch (e) {
        console.error(e.message, e);
        response.status(500).json({
          success: false,
          error: await t("errors.thread.shareFailed"),
        });
      }
    }
  );

  /**
   * @swagger
   * /workspace/{slug}/thread/{threadSlug}/revoke-share:
   *   post:
   *     summary: Revoke thread sharing for a specific user
   *     parameters:
   *       - in: path
   *         name: slug
   *         required: true
   *         schema:
   *           type: string
   *       - in: path
   *         name: threadSlug
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       description: User ID to revoke
   *       required: true
   *     responses:
   *       200:
   *         description: Success
   *       403:
   *         description: Only thread owners can perform this action
   *       500:
   *         description: Server error
   */
  // Revoke thread share for a specific user
  app.post(
    "/workspace/:slug/thread/:threadSlug/revoke-share",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.all]),
      validWorkspaceAndThreadSlug,
    ],
    async (request, response) => {
      try {
        const thread = response.locals.thread;
        const { userId } = reqBody(request);
        const user = await userFromSession(request, response);
        const { ThreadShare } = require("../models/threadShare");

        // Check if user is the owner of the thread
        if (thread.user_id !== user.id) {
          return response.status(403).json({
            success: false,
            error: await t("errors.thread.onlyOwnerRevoke"),
          });
        }

        const { success, error } = await ThreadShare.delete(thread.id, userId);

        if (!success) {
          return response.status(500).json({
            success: false,
            error: await t("errors.thread.revokeFailed"),
          });
        }

        response.status(200).json({ success: true });
      } catch (e) {
        console.error(e.message, e);
        response.status(500).json({
          success: false,
          error: await t("errors.thread.revokeFailed"),
        });
      }
    }
  );

  app.get(
    "/threads/:threadId",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async (request, response) => {
      try {
        const { threadId } = request.params;
        const numericThreadId = Number(threadId);

        if (isNaN(numericThreadId)) {
          return response.status(400).json({ error: "Invalid thread ID." });
        }

        const user = await userFromSession(request, response);

        const thread = await WorkspaceThread.getById(numericThreadId);

        if (!thread) {
          return response.status(404).json({ error: "Thread not found." });
        }

        if (thread.user_id !== null && thread.user_id !== user?.id) {
          return response.status(403).json({ error: "Forbidden." });
        }

        if (!thread.workspaceSlug) {
          console.warn(
            `Workspace slug missing for thread ${threadId}. Fetching workspace...`
          );
          const workspace = await Workspace.get({ id: thread.workspace_id });
          if (!workspace) {
            console.error(
              `Could not find workspace with ID ${thread.workspace_id} for thread ${threadId}`
            );
            return response
              .status(404)
              .json({ error: "Associated workspace not found for thread." });
          }
          thread.workspaceSlug = workspace.slug;
        }

        response.status(200).json(thread);
      } catch (e) {
        console.error(`Error fetching thread ${request.params.threadId}:`, e);
        response.status(500).json({
          error: "Internal server error occurred while fetching thread.",
        });
      }
    }
  );
}

module.exports = { workspaceThreadEndpoints };
