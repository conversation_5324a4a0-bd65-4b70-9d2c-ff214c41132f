process.env.NODE_ENV === "development"
  ? require("dotenv").config({ path: `.env.${process.env.NODE_ENV}` })
  : require("dotenv").config();
const { viewLocalFiles, normalizePath, isWithin } = require("../utils/files");
const {
  purgeDocument,
  purgeFolder,
  cleanOldDocxSessionFiles,
} = require("../utils/files/purgeDocument");
const { getVectorDbClass } = require("../utils/helpers");
const multer = require("multer");
const {
  updateENV,
  dumpENV,
  KEY_MAPPING,
} = require("../utils/helpers/updateENV");
const {
  reqBody,
  makeJWT,
  userFromSession,
  multiUserMode,
  queryParams,
} = require("../utils/http");
const { handleAssetUpload, handlePfpUpload } = require("../utils/files/multer");
const { v4 } = require("uuid");
const { SystemSettings } = require("../models/systemSettings");
const { User } = require("../models/user");
const { UserToken } = require("../models/userToken");
const { validatedRequest } = require("../utils/middleware/validatedRequest");
// Custom legal templates endpoints are now registered directly in index.js
const fs = require("fs");
const path = require("path");
const { Telemetry } = require("../models/telemetry");
const { PromptExamples } = require("../models/promptExamples");
const { ApiKey } = require("../models/apiKeys");
const { getCustomModels } = require("../utils/helpers/customModels");
const { WorkspaceChats } = require("../models/workspaceChats");
const {
  flexUserRoleValid,
  ROLES,
  isMultiUserSetup,
} = require("../utils/middleware/multiUserProtected");
const { fetchPfp, determinePfpFilepath } = require("../utils/files/pfp");
const { exportChatsAsType } = require("../utils/helpers/chat/convertTo");
const { EventLogs: AuditLog } = require("../models/eventLogs");
const { CollectorApi } = require("../utils/collectorApi");
const {
  recoverAccount,
  resetPassword,
  generateRecoveryCodes,
} = require("../utils/PasswordRecovery");
const { SlashCommandPresets } = require("../models/slashCommandsPresets");
const { EncryptionManager } = require("../utils/EncryptionManager");
const { BrowserExtensionApiKey } = require("../models/browserExtensionApiKey");
const { Workspace } = require("../models/workspace");
const prisma = require("../utils/prisma");
const { getLLMProvider } = require("../utils/helpers");
const { t } = require("../utils/i18n");
const axios = require("axios");
const { Category } = require("../models/category");
const {
  DEFAULT_COMBINE_PROMPT,
  DEFAULT_DOCUMENT_DRAFTING_PROMPT,
  DEFAULT_LEGAL_ISSUES_PROMPT,
  DEFAULT_MEMO_PROMPT,
} = require("../utils/chats/streamDD");

// Corrected import for legal drafting prompts
const legalDraftingFileExports = require("../utils/chats/prompts/legalDrafting.js");
const { exportedLegalPrompts, ...LEGAL_DRAFTING_PROMPTS } =
  legalDraftingFileExports;
const { PROMPT_MAPPINGS } = require("../utils/chats/helpers/promptManager"); // Correct import for PROMPT_MAPPINGS

const { Feedback } = require("../models/Feedback");
const { DocumentManager } = require("../utils/DocumentManager");
const { DocumentSyncQueue: Queue } = require("../models/documentSyncQueue");
const { EventLogs } = require("../models/eventLogs");
// Removed the duplicated import of defaultPrompts and exportedLegalPrompts here
const {
  getCustomModels: getSupportedModels,
} = require("../utils/helpers/customModels.js"); // Changed this line
// const { RetentionPolicies } = require("../models/retentionPolicies");
// const { RetentionPolicyRuns } = require("../models/retentionPolicyRuns");

// Restoring uploadDir and logo utilities that were seemingly removed by previous diff
// Create upload directory if it doesn't exist
const uploadDir = path.join(__dirname, "..", "uploads", "feedback");
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const {
  getDefaultFilenameLight,
  getDefaultFilenameDark,
  determineLogoLightFilepath,
  determineLogoDarkFilepath,
  fetchLogo,
  validFilenameLight,
  validFilenameDark,
  renameLogoFile,
  removeCustomLogoLight,
  removeCustomLogoDark,
  LOGO_LIGHT,
  LOGO_DARK,
} = require("../utils/files/logo");

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Ensure the directory exists before saving
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir); // Use the absolute path instead of relative path
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(null, `${uniqueSuffix}-${file.originalname}`);
  },
});
const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = ["image/jpeg", "image/png", "application/pdf"];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error("Invalid file type. Only JPEG, PNG, and PDF are allowed."));
    }
  },
});

const rexorApiBaseUrl = "https://api.rexor.se/v231/Api";

// --------------------------------------------------
// Document-builder prompt logic
// --------------------------------------------------

/**
 * Build the list of prompt configs enriched with current SystemSettings values.
 * @returns {Promise<Array>} Enriched prompt configurations
 */
async function buildEnrichedPrompts() {
  const { SystemSettings } = require("../models/systemSettings");
  // Dynamically import prompts to respect Jest module mocks
  const {
    exportedLegalPrompts: dynamicPrompts,
  } = require("../utils/chats/prompts/legalDrafting");
  return Promise.all(
    dynamicPrompts.map(async (promptConfig) => {
      const currentValue = await SystemSettings.getValueOrFallback(
        { label: promptConfig.systemSettingName },
        ""
      );
      return {
        ...promptConfig,
        currentValue,
      };
    })
  );
}

// Direct handler (used by unit tests)
async function getDocumentBuilderPromptsHandler(request, response) {
  try {
    const enrichedPrompts = await buildEnrichedPrompts();
    return response.status(200).json(enrichedPrompts);
  } catch (e) {
    console.error("Error fetching document builder prompts (direct):", e);
    return response.status(500).json({ success: false, error: e.message });
  }
}

// Express-route wrapper (adds success wrapper + camelCase keys)
async function getDocumentBuilderPromptsRouteHandler(req, res) {
  try {
    if (!PROMPT_MAPPINGS) {
      return res.status(500).json({
        success: false,
        error: "Failed to fetch document builder prompts.",
      });
    }

    const enrichedPrompts = await buildEnrichedPrompts();

    const promptsWithCamel = enrichedPrompts.map((p) => {
      let camelCaseKey = null;
      for (const [camel, settingName] of Object.entries(PROMPT_MAPPINGS)) {
        if (settingName === p.systemSettingName) {
          camelCaseKey = camel;
          break;
        }
      }
      return { ...p, camelCaseKey };
    });

    return res.status(200).json({ success: true, prompts: promptsWithCamel });
  } catch (e) {
    console.error("Error fetching document builder prompts (route):", e);
    return res.status(500).json({ success: false, error: e.message });
  }
}

// Handler for POST /system/update-document-builder-prompts
async function updateDocumentBuilderPromptsHandler(request, response) {
  try {
    const updates = reqBody(request);

    const allowedKeyRegex = /^cdb_[a-z0-9_]+_prompt$/i;
    const filteredUpdates = {};
    for (const key in updates) {
      if (
        Object.prototype.hasOwnProperty.call(updates, key) &&
        allowedKeyRegex.test(key)
      ) {
        filteredUpdates[key] = updates[key];
      }
    }
    if (
      Object.keys(filteredUpdates).length === 0 &&
      Object.keys(updates).length > 0
    ) {
      return response.status(400).json({
        success: false,
        error: "No valid prompt settings provided for update.",
      });
    }
    if (
      Object.keys(filteredUpdates).length === 0 &&
      Object.keys(updates).length === 0
    ) {
      return response
        .status(200)
        .json({ success: true, message: "No settings provided for update." });
    }
    const { SystemSettings } = require("../models/systemSettings");
    const result = await SystemSettings.updateSettings(filteredUpdates);
    if (result.success) {
      response.status(200).json({
        success: true,
        message: "Document builder prompts updated successfully.",
      });
    } else {
      response.status(400).json({
        success: false,
        error: result.error || "Failed to update document builder prompts.",
      });
    }
  } catch (e) {
    console.error(
      "Error updating document builder prompts (new logic):",
      e.message,
      e
    );
    response.status(500).json({ success: false, error: e.message });
  }
}

function systemEndpoints(app) {
  // Import default prompts for Standard settings
  const {
    DEFAULT_SYSTEM_PROMPT,
    DEFAULT_VALIDATION_PROMPT,
    DEFAULT_VECTOR_SEARCH_TOP_N,
  } = require("../utils/chats/streamLQA");
  const {
    DEFAULT_CANVAS_SYSTEM_PROMPT,
  } = require("../utils/chats/streamCanvas");
  const {
    DEFAULT_CANVAS_UPLOAD_SYSTEM_PROMPT,
  } = require("../utils/chats/streamCanvas");
  if (!app) return;

  // Custom legal templates endpoints are now registered directly in index.js

  app.get("/ping", (_, response) => {
    response.status(200).json({ online: true });
  });

  app.get("/migrate", async (_, response) => {
    response.sendStatus(200);
  });

  app.get("/env-dump", async (_, response) => {
    if (process.env.NODE_ENV !== "production")
      return response.sendStatus(200).end();
    dumpENV();
    response.sendStatus(200).end();
  });

  app.get("/setup-complete", async (_, response) => {
    try {
      const results = await SystemSettings.currentSettings();
      response.status(200).json({ results });
    } catch (e) {
      console.error(e.message, e);
      response.sendStatus(500).end();
    }
  });

  app.get(
    "/system/check-token",
    [validatedRequest],
    async (request, response) => {
      try {
        if (multiUserMode(response)) {
          const user = await userFromSession(request, response);
          if (!user || user.suspended) {
            response.sendStatus(403).end();
            return;
          }

          response.sendStatus(200).end();
          return;
        }

        response.sendStatus(200).end();
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post("/request-token", async (request, response) => {
    try {
      const bcrypt = require("bcrypt");

      if (await SystemSettings.isMultiUserMode()) {
        const { username, password } = reqBody(request);
        const existingUser = await User._get({ username: String(username) });

        if (!existingUser) {
          await EventLogs.logEvent(
            "failed_login_invalid_username",
            {
              ip: request.ip || "Unknown IP",
              username: username || "Unknown user",
            },
            existingUser?.id
          );
          response.status(200).json({
            user: null,
            valid: false,
            token: null,
            message: "Invalid login credentials.",
          });
          return;
        }

        if (!bcrypt.compareSync(String(password), existingUser.password)) {
          await EventLogs.logEvent(
            "failed_login_invalid_password",
            {
              ip: request.ip || "Unknown IP",
              username: username || "Unknown user",
            },
            existingUser?.id
          );
          response.status(200).json({
            user: null,
            valid: false,
            token: null,
            message: "[002] Invalid login credentials.",
          });
          return;
        }

        if (existingUser.suspended) {
          await EventLogs.logEvent(
            "failed_login_account_suspended",
            {
              ip: request.ip || "Unknown IP",
              username: username || "Unknown user",
            },
            existingUser?.id
          );
          response.status(200).json({
            user: null,
            valid: false,
            token: null,
            message: "[004] Account suspended by admin.",
          });
          return;
        }

        await Telemetry.sendTelemetry(
          "login_event",
          { multiUserMode: false },
          existingUser?.id
        );

        await EventLogs.logEvent(
          "login_event",
          {
            ip: request.ip || "Unknown IP",
            username: existingUser.username || "Unknown user",
          },
          existingUser?.id
        );

        // Create new token using UserToken model
        const newToken = makeJWT(
          { id: existingUser.id, role: existingUser.role },
          "30d"
        );
        await UserToken.create(
          existingUser.id,
          newToken,
          request.headers["user-agent"]
        );

        // Check if the user has seen the recovery codes
        if (!existingUser.seen_recovery_codes) {
          const plainTextCodes = await generateRecoveryCodes(existingUser.id);

          // Return recovery codes to frontend
          response.status(200).json({
            valid: true,
            user: User.filterFields(existingUser),
            token: newToken,
            message: null,
            recoveryCodes: plainTextCodes,
          });
          return;
        }

        response.status(200).json({
          valid: true,
          user: User.filterFields(existingUser),
          token: newToken,
          message: null,
        });
        return;
      } else {
        const { password } = reqBody(request);
        if (
          !bcrypt.compareSync(
            password,
            bcrypt.hashSync(process.env.AUTH_TOKEN, 10)
          )
        ) {
          await EventLogs.logEvent("failed_login_invalid_password", {
            ip: request.ip || "Unknown IP",
            multiUserMode: false,
          });
          response.status(401).json({
            valid: false,
            token: null,
            message: "[003] Invalid password provided",
          });
          return;
        }

        await Telemetry.sendTelemetry("login_event", { multiUserMode: false });
        await EventLogs.logEvent("login_event", {
          ip: request.ip || "Unknown IP",
          multiUserMode: false,
        });
        response.status(200).json({
          valid: true,
          token: makeJWT(
            { p: new EncryptionManager().encrypt(password) },
            "30d"
          ),
          message: null,
        });
      }
    } catch (e) {
      console.error(e.message, e);
      response.sendStatus(500).end();
    }
  });

  app.post(
    "/system/recover-account",
    [isMultiUserSetup],
    async (request, response) => {
      try {
        const { username, recoveryCodes } = reqBody(request);
        const { success, resetToken, error } = await recoverAccount(
          username,
          recoveryCodes
        );

        if (success) {
          response.status(200).json({ success, resetToken });
        } else {
          response.status(400).json({ success, message: error });
        }
      } catch (error) {
        console.error("Error recovering account:", error);
        response
          .status(500)
          .json({ success: false, message: "Internal server error" });
      }
    }
  );

  app.post(
    "/system/reset-password",
    [isMultiUserSetup],
    async (request, response) => {
      try {
        const { token, newPassword, confirmPassword } = reqBody(request);
        const { success, message, error } = await resetPassword(
          token,
          newPassword,
          confirmPassword
        );

        if (success) {
          response.status(200).json({ success, message });
        } else {
          response.status(400).json({ success, error });
        }
      } catch (error) {
        console.error("Error resetting password:", error);
        response.status(500).json({ success: false, message: error.message });
      }
    }
  );

  app.get(
    "/system/system-vectors",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        const query = queryParams(request);
        const VectorDb = getVectorDbClass();
        const vectorCount = query.slug
          ? await VectorDb.namespaceCount(query.slug)
          : await VectorDb.totalVectors();
        response.status(200).json({ vectorCount });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.delete(
    "/system/remove-document",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        const { name } = reqBody(request);
        await purgeDocument(name);
        response.sendStatus(200).end();
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.delete(
    "/system/remove-documents",
    [
      validatedRequest,
      flexUserRoleValid([
        ROLES.admin,
        ROLES.manager,
        ROLES.superuser,
        ROLES.default,
      ]),
    ],
    async (request, response) => {
      try {
        const { names } = reqBody(request);
        if (!Array.isArray(names) || names.length === 0) {
          response.status(400).json({
            success: false,
            error: "Invalid or empty document names array provided",
          });
          return;
        }

        console.log(`Attempting to delete ${names.length} documents`);
        const results = [];
        const errors = [];

        for (const name of names) {
          try {
            await purgeDocument(name);
            results.push({ name, success: true });
          } catch (purgeError) {
            console.error(`Failed to purge document ${name}:`, purgeError);
            errors.push({ name, error: purgeError.message });
          }
        }

        // Build base response payload
        const basePayload = {
          results,
          errors,
        };

        // Determine response status based on deletion outcomes
        if (errors.length === names.length) {
          // All documents failed
          response.status(500).json({
            success: false,
            error: "All document deletions failed",
            ...basePayload,
          });
          return;
        } else if (errors.length > 0) {
          // Partial success
          response.status(207).json({
            success: true,
            message: "Some documents were deleted successfully",
            ...basePayload,
          });
          return;
        }

        // All documents succeeded
        response.status(200).json({
          success: true,
          message: "All documents were deleted successfully",
          ...basePayload,
        });
      } catch (e) {
        console.error("Error in remove-documents endpoint:", e);
        response.status(500).json({
          success: false,
          error: "Internal server error during document deletion",
          details: e.message,
        });
      }
    }
  );

  app.delete(
    "/system/remove-folder",
    [
      validatedRequest,
      flexUserRoleValid([
        ROLES.admin,
        ROLES.manager,
        ROLES.superuser,
        ROLES.default,
      ]),
    ],
    async (request, response) => {
      try {
        const { name, slug } = reqBody(request);
        await purgeFolder(name, slug);
        response.sendStatus(200).end();
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.get(
    "/system/local-files/:slugModule/:workspaceId",
    [
      validatedRequest,
      flexUserRoleValid([
        ROLES.admin,
        ROLES.manager,
        ROLES.superuser,
        ROLES.default,
      ]),
    ],
    async (request, response) => {
      try {
        const localFiles = await viewLocalFiles(request, response, false);
        response.status(200).json({ localFiles });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.get(
    "/system/document-processing-status",
    [validatedRequest],
    async (_, response) => {
      try {
        const online = await new CollectorApi().online();
        response.sendStatus(online ? 200 : 503);
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.get(
    "/system/accepted-document-types",
    [validatedRequest],
    async (_, response) => {
      try {
        const types = await new CollectorApi().acceptedFileTypes();
        if (!types) {
          response.sendStatus(404).end();
          return;
        }

        response.status(200).json({ types });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/system/update-env",
    [validatedRequest, flexUserRoleValid([ROLES.admin])],
    async (request, response) => {
      try {
        const body = reqBody(request);
        const { newValues, error } = await updateENV(
          body,
          false,
          response?.locals?.user?.id
        );
        response.status(200).json({
          newValues: newValues || {},
          error: error || null,
        });
      } catch (e) {
        console.error(e.message, e);
        response.status(500).json({
          newValues: null,
          error: e.message || "Internal server error",
        });
      }
    }
  );

  app.post(
    "/system/reset-llm-settings",
    [validatedRequest, flexUserRoleValid([ROLES.admin])],
    async (request, response) => {
      try {
        const { moduleSuffix = "" } = reqBody(request);

        // Create variables to track which keys were reset
        const resetKeys = {};

        // Set LLM provider to null/empty directly
        const providerKey = `LLM_PROVIDER${moduleSuffix}`;
        process.env[providerKey] = "";
        resetKeys[`LLMProvider${moduleSuffix}`] = "";

        // Find and reset any model preference keys with this suffix
        const envKeys = Object.keys(process.env);
        const modelPrefKeys = envKeys.filter(
          (key) => key.includes("MODEL_PREF") && key.endsWith(moduleSuffix)
        );

        modelPrefKeys.forEach((envKey) => {
          const mappedKey = Object.entries(KEY_MAPPING).find(
            ([_, val]) => val.envKey === envKey
          )?.[0];

          if (mappedKey) {
            process.env[envKey] = "";
            resetKeys[mappedKey] = "";
          }
        });

        // Persist changes to .env
        const dumpResult = dumpENV();

        // Log the reset to event logs
        await EventLogs.logEvent(
          "reset_llm_settings",
          {
            moduleSuffix,
            resetKeys: Object.keys(resetKeys).join(", "),
            envKeys: modelPrefKeys.join(", "),
          },
          response?.locals?.user?.id
        );

        response.status(200).json({
          success: true,
          resetKeys,
          error: null,
        });
      } catch (e) {
        console.error("Error resetting LLM settings:", e);
        response.status(500).json({
          success: false,
          error: e.message || "Internal server error",
        });
      }
    }
  );

  app.post(
    "/system/update-password",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        // Cannot update password in multi - user mode.
        if (multiUserMode(response)) {
          response.sendStatus(401).end();
          return;
        }

        let error = null;
        const { usePassword, newPassword } = reqBody(request);
        if (!usePassword) {
          // Password is being disabled so directly unset everything to bypass validation.
          process.env.AUTH_TOKEN = "";
          process.env.JWT_SECRET = "";
        } else {
          error = await updateENV(
            {
              AuthToken: newPassword,
              JWTSecret: v4(),
            },
            true
          )?.error;
        }
        response.status(200).json({ success: !error, error });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/system/enable-multi-user",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        if (response.locals.multiUserMode) {
          response.status(200).json({
            success: false,
            error: "Multi-user mode is already enabled and cannot be disabled.",
          });
          return;
        }

        const { username, password } = reqBody(request);
        const { user, error } = await User.create({
          username,
          password,
          role: ROLES.admin,
        });
        await SystemSettings._updateSettings({
          multi_user_mode: true,
          limit_user_messages: false,
          message_limit: 25,
        });
        await BrowserExtensionApiKey.migrateApiKeysToMultiUser(user.id);

        await updateENV(
          {
            JWTSecret: process.env.JWT_SECRET || v4(),
          },
          true
        );
        await Telemetry.sendTelemetry("enabled_multi_user_mode", {
          multiUserMode: true,
        });
        await EventLogs.logEvent("multi_user_mode_enabled", {}, user?.id);
        response.status(200).json({ success: !!user, error });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/system/enable-public-user",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        const { publicUserMode, username, password } = reqBody(request);

        await SystemSettings._updateSettings({
          public_user_mode: publicUserMode,
        });

        await User.delete({ username: username });

        if (publicUserMode) {
          const { user, error } = await User.create({
            username,
            password,
            role: ROLES.default,
          });

          if (process.env.NODE_ENV === "production") await dumpENV();
          await Telemetry.sendTelemetry("enabled_public_user_mode", {
            publicUserMode: true,
          });
          await EventLogs.logEvent("public_user_mode_enabled", {}, user?.id);
          response.status(200).json({ success: !!user, error });
        } else {
          await EventLogs.logEvent("public_user_mode_disabled", {}, 0);
          response.status(200).json({ success: true, message: "" });
        }
      } catch (e) {
        await User.delete({});
        await SystemSettings._updateSettings({
          public_user_mode: false,
        });

        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/system/set-document-drafting",
    [validatedRequest],
    async (request, response) => {
      try {
        const { document_drafting, document_drafting_linking } =
          reqBody(request);

        await SystemSettings._updateSettings({
          document_drafting: document_drafting,
          document_drafting_linking: document_drafting_linking,
        });

        if (document_drafting) {
          if (process.env.NODE_ENV === "production") await dumpENV();
          await Telemetry.sendTelemetry("document_drafting", {
            publicUserMode: true,
          });

          await EventLogs.logEvent(
            "document_drafting",
            { deletedBy: response.locals?.user?.username },
            response?.locals?.user?.id
          );
          response.status(200).json({ success: true, message: "" });
        } else {
          await EventLogs.logEvent("document_drafting", {}, 0);
          response.status(200).json({ success: true, message: "" });
        }
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/system/set-document-drafting-prompt",
    [validatedRequest],
    async (request, response) => {
      try {
        const {
          documentDraftingPrompt,
          legalIssuesPrompt,
          memoPrompt,
          combinePrompt,
        } = reqBody(request);

        await SystemSettings._updateSettings({
          document_drafting_prompt: documentDraftingPrompt,
          document_drafting_legal_issue_prompt: legalIssuesPrompt,
          document_drafting_memo_prompt: memoPrompt,
          document_drafting_combine_prompt: combinePrompt,
        });

        // Update all document-drafting workspaces to use the new prompt
        const updateDraftingPrompts = await Workspace.updateMany(
          { type: "document-drafting" },
          { openAiPrompt: documentDraftingPrompt }
        );

        response.status(200).json({ success: true, message: "" });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/system/set-default-settings",
    [validatedRequest],
    async (request, response) => {
      try {
        const {
          defaultPrompt,
          vectorSearch,
          validationPrompt,
          canvasSystemPrompt,
          canvasUploadSystemPrompt,
        } = reqBody(request);

        await SystemSettings._updateSettings({
          default_prompt: defaultPrompt,
          vector_search_top_n: vectorSearch,
          validation_prompt: validationPrompt,
          canvas_system_prompt: canvasSystemPrompt,
          canvas_upload_system_prompt: canvasUploadSystemPrompt,
        });

        // Only update legal-qa workspaces that have empty prompts
        const updateLegalQAPrompts = await Workspace.updateMany(
          {
            type: "legal-qa",
            OR: [{ openAiPrompt: null }, { openAiPrompt: "" }],
          },
          { openAiPrompt: defaultPrompt }
        );

        // We no longer update existing workspaces' topN values here
        // The vectorSearch setting will only be applied to new workspaces
        // or when explicitly requested via the apply-vector-search endpoint

        response.status(200).json({ success: true, message: "" });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/system/apply-default-prompt",
    [validatedRequest],
    async (request, response) => {
      try {
        const { defaultPrompt } = reqBody(request);

        // Update all legal-qa workspaces to use the default prompt
        const updateResult = await Workspace.updateMany(
          { type: "legal-qa" },
          { openAiPrompt: defaultPrompt }
        );

        response.status(200).json({
          success: true,
          message: "",
          updatedCount: updateResult.count,
        });
      } catch (e) {
        console.error(
          "Error applying default prompt to all workspaces:",
          e.message
        );
        response.status(500).json({
          success: false,
          error: e.message,
        });
      }
    }
  );

  app.post(
    "/system/apply-vector-search",
    [validatedRequest],
    async (request, response) => {
      try {
        const { vectorSearch } = reqBody(request);

        // Update all legal-qa workspaces to use the new vector search top N
        const updateResult = await Workspace.updateMany(
          { type: "legal-qa" },
          { topN: vectorSearch }
        );

        response.status(200).json({
          success: true,
          message: "",
          updatedCount: updateResult.count,
        });
      } catch (e) {
        console.error(
          "Error applying vector search to all workspaces:",
          e.message
        );
        response.status(500).json({
          success: false,
          error: e.message,
        });
      }
    }
  );

  app.post(
    "/system/set-document-builder",
    [validatedRequest],
    async (request, response) => {
      try {
        const {
          summarySystemPrompt,
          summaryUserPrompt,
          relevanceSystemPrompt,
          relevanceUserPrompt,
          selectMainDocumentSystemPrompt,
          selectMainDocumentUserPrompt,
          topicsSectionsSystemPrompt,
          topicsSectionsUserPrompt,
          sectionSystemPrompt,
          sectionUserPrompt,
          sectionLegalIssuesSystemPrompt,
          sectionLegalIssuesUserPrompt,
        } = reqBody(request);

        await SystemSettings._updateSettings({
          summary_system_prompt: summarySystemPrompt,
          summary_user_prompt: summaryUserPrompt,
          relevance_system_prompt: relevanceSystemPrompt,
          relevance_user_prompt: relevanceUserPrompt,
          select_main_document_system_prompt: selectMainDocumentSystemPrompt,
          select_main_document_user_prompt: selectMainDocumentUserPrompt,
          topics_sections_system_prompt: topicsSectionsSystemPrompt,
          topics_sections_user_prompt: topicsSectionsUserPrompt,
          section_system_prompt: sectionSystemPrompt,
          section_user_prompt: sectionUserPrompt,
          section_legal_issues_system_prompt: sectionLegalIssuesSystemPrompt,
          section_legal_issues_user_prompt: sectionLegalIssuesUserPrompt,
        });

        response.status(200).json({ success: true, message: "" });
      } catch (e) {
        console.error(e.message, e);
        response.status(500).json({
          success: false,
          error: "Failed to update document builder prompts",
        });
      }
    }
  );

  app.post("/categories", async (request, response) => {
    try {
      // Extract all relevant fields including the new one
      const {
        name,
        sub_category,
        description,
        legal_task_prompt,
        requiresMainDocument,
      } = request.body;

      console.log("Received category payload:", request.body);

      if (!name) {
        return response
          .status(400)
          .json({ success: false, error: "Category name is required." });
      }

      // Explicitly log the value being passed to the model
      console.log(
        `Passing requiresMainDocument: ${requiresMainDocument} (Type: ${typeof requiresMainDocument}) to Category.create`
      );

      // Pass requiresMainDocument to the create method
      const { category: newCategory, error } = await Category.create({
        name,
        sub_category,
        description,
        legal_task_prompt,
        requiresMainDocument: requiresMainDocument, // Pass explicitly
      });

      if (error) {
        // Propagate error from model validation/creation
        console.error("Error from Category.create:", error);
        return response.status(400).json({ success: false, error });
      }

      response.status(201).json({
        success: true,
        message: "Category successfully created.",
        data: newCategory,
      });
    } catch (error) {
      console.error("Error in POST /categories endpoint:", error.message);
      response.status(500).json({
        success: false,
        error: error.message || "Failed to create category.",
      });
    }
  });

  // Assuming this endpoint is used by the settings modal for the category list
  app.get("/categories/grouped", async (_, response) => {
    try {
      // If you previously fetched all and grouped on frontend, change to use model method
      const groupedCategories = await Category.getGroupedByName();

      response.status(200).json({
        success: true,
        data: groupedCategories,
      });
    } catch (error) {
      console.error("Error fetching grouped categories:", error.message);
      response.status(500).json({
        success: false,
        error: "Failed to fetch grouped categories.",
      });
    }
  });

  // If the flat list is needed elsewhere, ensure requiresMainDocument is included
  // (Prisma includes it by default, Category.get uses filterFields which passes all)
  app.get("/categories", async (_, response) => {
    try {
      const categories = await Category.get();
      response.status(200).json({
        success: true,
        data: categories,
      });
    } catch (error) {
      console.error("Error fetching categories:", error.message);
      response.status(500).json({
        success: false,
        error: "Failed to fetch categories.",
      });
    }
  });

  app.delete("/categories/:id", async (req, res) => {
    try {
      const { id } = req.params;

      await prisma.category.delete({
        where: { id: parseInt(id) },
      });

      res.status(200).json({
        success: true,
        message: "Category successfully deleted.",
      });
    } catch (error) {
      console.error("Error deleting category:", error.message);
      res.status(500).json({
        success: false,
        error: "Failed to delete category.",
      });
    }
  });

  app.get("/categories/subcategories/:name", async (req, res) => {
    try {
      const { name } = req.params;

      const subCategories = await prisma.category.findMany({
        where: {
          name: name,
        },
        select: {
          sub_category: true,
          description: true,
          legal_task_prompt: true, // Added
          requiresMainDocument: true, // Added
        },
      });

      const formattedSubCategories = subCategories.map((item) => ({
        name: item.sub_category,
        description: item.description || "No description available",
        legalPrompt: item.legal_task_prompt, // Added
        requiresMainDocument: item.requiresMainDocument, // Added
      }));

      res.status(200).json({
        success: true,
        data: formattedSubCategories,
      });
    } catch (error) {
      console.error("Error fetching sub-categories:", error.message);
      res.status(500).json({
        success: false,
        error: "Failed to fetch sub-categories.",
      });
    }
  });

  app.post(
    "/system/enable-qura",
    [validatedRequest],
    async (request, response) => {
      try {
        // const { qura, copyOption } = reqBody(request);
        const qura = reqBody(request).qura ?? false;
        const copyOption = reqBody(request).copyOption;

        await SystemSettings._updateSettings({
          qura: qura,
          copyOption: copyOption,
        });

        // Define the response message based on qura and copyOption
        const message = `Qura has been ${qura ? "enabled" : "disabled"} with ${copyOption} option.`;

        if (qura) {
          if (process.env.NODE_ENV === "production") await dumpENV();
          await Telemetry.sendTelemetry("qura", {
            publicUserMode: true,
          });

          await EventLogs.logEvent(
            "qura",
            { deletedBy: response.locals?.user?.username },
            response?.locals?.user?.id
          );
          response.status(200).json({ success: true, message }); // Return message with copyOption
        } else {
          await EventLogs.logEvent("qura", {}, 0);
          response.status(200).json({ success: true, message: "" });
        }
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post("/system/custom-website-link", async (request, response) => {
    // Log the incoming request body for debugging
    console.log("Received request body:", reqBody(request));

    try {
      // Extract websiteLink and displayText from the request body
      const websiteLink = reqBody(request).websiteLink ?? "";
      const displayText = reqBody(request).displayText ?? "Copyright IST 2024";

      // Log the extracted values for debugging
      console.log("Extracted websiteLink:", websiteLink);
      console.log("Extracted displayText:", displayText);

      // Validate if the required fields are missing or empty
      if (!websiteLink || !displayText) {
        console.log(
          "Error: Missing or empty required fields - websiteLink or displayText"
        );
        return response.status(400).json({
          success: false,
          error: "Missing required fields: websiteLink or displayText",
        });
      }

      // Attempt to update the system settings with the extracted values
      console.log(
        "Updating system settings with websiteLink:",
        websiteLink,
        "and displayText:",
        displayText
      );
      const result = await SystemSettings._updateSettings({
        websiteLink,
        displayText,
      });

      // Log the result of the update operation
      console.log("Update result:", result);

      // Send success response if update is successful
      if (result.success) {
        const message = `Website link has been updated with the display text: "${displayText}"`;
        console.log("Update successful. Sending response:", message);
        return response.status(200).json({ success: true, message: message });
      } else {
        console.log("Update failed. Error:", result.error);
        return response.status(500).json({
          success: false,
          error: result.error || "Failed to update website link.",
        });
      }
    } catch (e) {
      // Log the caught error for debugging
      console.error("Error during update process:", e.message, e);
      return response.status(500).json({
        success: false,
        error:
          e.message ||
          "An unexpected error occurred while updating the website link.",
      });
    }
  });

  app.get("/system/prompt-output-logging", async (_, response) => {
    try {
      const enabled = await SystemSettings.isPromptOutputLogging();
      response.status(200).json({ enabled });
    } catch (e) {
      console.error(e.message, e);
      response.sendStatus(500).end();
    }
  });
  app.post(
    "/system/prompt-output-logging",
    [validatedRequest],
    async (request, response) => {
      try {
        const { enabled } = reqBody(request);
        await SystemSettings._updateSettings({
          prompt_output_logging: enabled,
        });
        await EventLogs.logEvent("prompt_output_logging_updated", { enabled });
        response.status(200).json({ success: true });
      } catch (e) {
        console.error(e.message, e);
        response.status(500).json({ success: false, error: e.message });
      }
    }
  );

  app.get("/system/perform-legal-task", async (_, response) => {
    try {
      const settings = await SystemSettings.isPerformLegalTask();
      response.status(200).json(settings);
    } catch (e) {
      response.status(500).json({ error: e.message });
    }
  });

  app.post("/system/perform-legal-task", async (request, response) => {
    try {
      const { enabled, allowUserAccess } = reqBody(request);
      await SystemSettings._updateSettings({
        perform_legal_task: enabled,
        allow_user_access: allowUserAccess,
      });
      await EventLogs.logEvent("perform_legal_task_updated", {
        enabled,
        allowUserAccess,
      });
      response.status(200).json({ success: true });
    } catch (e) {
      console.error(e.message, e);
      response.status(500).json({ success: false, error: e.message });
    }
  });

  app.get("/system/feedback-enabled", async (_, response) => {
    try {
      const settings = await SystemSettings.isFeedbackEnabled();
      response.status(200).json(settings);
    } catch (e) {
      response.status(500).json({ error: e.message });
    }
  });

  app.post("/system/feedback-enabled", async (request, response) => {
    try {
      const { enabled } = reqBody(request);
      await SystemSettings._updateSettings({
        feedback_enabled: enabled,
      });
      await EventLogs.logEvent("feedback_enabled_updated", {
        enabled,
      });
      response.status(200).json({ success: true });
    } catch (e) {
      console.error(e.message, e);
      response.status(500).json({ success: false, error: e.message });
    }
  });

  app.post("/system/feedback", upload.single("file"), async (req, res) => {
    try {
      const { fullName, message } = req.body;
      if (!fullName || !message) {
        return res.status(400).json({
          success: false,
          error: "Full name and message are required",
        });
      }

      const wordCount = message
        .trim()
        .split(/\s+/)
        .filter((word) => word.length > 0).length;
      const charCount = message.trim().length;
      if (wordCount < 4 || charCount < 12) {
        return res.status(400).json({
          success: false,
          error: "Message must be at least 4 words and 12 characters",
        });
      }

      const feedbackData = {
        fullName,
        message,
        filePath: req.file ? req.file.path : null,
      };

      const result = await Feedback.create(feedbackData);

      if (result.error) {
        // If there was a file uploaded but feedback creation failed, clean up the file
        if (req.file && fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }
        return res.status(400).json({
          success: false,
          error: result.error,
        });
      }

      await EventLogs.logEvent("feedback_submitted", {
        fullName,
        hasFile: !!req.file,
      });

      res.status(200).json({ success: true });
    } catch (e) {
      // Clean up uploaded file if there's an error
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      console.error("Feedback submission error:", e);
      res.status(500).json({
        success: false,
        error: "Failed to submit feedback. Please try again.",
      });
    }
  });

  app.delete(
    "/system/feedback/:id",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (req, res) => {
      try {
        const { id } = req.params;
        if (!id) {
          return res.status(400).json({
            success: false,
            error: "Feedback ID is required",
          });
        }

        const idInt = parseInt(id, 10);
        if (isNaN(idInt)) {
          return res.status(400).json({
            success: false,
            error: "Invalid feedback ID: must be a valid integer",
          });
        }

        const deleted = await Feedback.delete({ id: idInt });
        if (!deleted) {
          return res.status(404).json({
            success: false,
            error: "Feedback not found",
          });
        }

        res.status(200).json({ success: true });
      } catch (e) {
        console.error("Error deleting feedback:", e.message);
        res.status(500).json({ success: false, error: e.message });
      }
    }
  );

  // GET: Retrieve all feedback entries
  app.get("/system/feedback", async (req, res) => {
    try {
      const feedback = await prisma.feedback.findMany({
        orderBy: {
          createdAt: "desc",
        },
      });

      res.json({
        success: true,
        feedback: feedback.map((fb) => ({
          ...fb,
          filePath: fb.filePath ? path.basename(fb.filePath) : null,
        })),
      });
    } catch (error) {
      console.error("Error fetching feedback:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch feedback",
      });
    }
  });

  // Add this endpoint to serve feedback files
  app.get("/uploads/feedback/:filename", (req, res) => {
    const filePath = path.join(
      __dirname,
      "..",
      "uploads",
      "feedback",
      req.params.filename
    );
    res.sendFile(filePath);
  });

  app.delete("/system/feedback/:id", async (req, res) => {
    try {
      const { id } = req.params;
      if (!id) {
        return res.status(400).json({
          success: false,
          error: "Feedback ID is required",
        });
      }

      const deleted = await Feedback.delete({ id });
      if (!deleted) {
        return res.status(404).json({
          success: false,
          error: "Feedback not found",
        });
      }

      await EventLogs.logEvent("feedback_deleted", {
        feedbackId: id,
      });

      res.status(200).json({ success: true });
    } catch (e) {
      console.error("Error deleting feedback:", e.message);
      res.status(500).json({ success: false, error: e.message });
    }
  });

  app.get("/get-tab-names", async (request, response) => {
    try {
      // Attempt to fetch the system settings
      const systemSettings = await SystemSettings.getSystemTabNames();

      // Return the tab names from settings, defaulting to empty strings if not found
      return response.status(200).json({
        tabName1: systemSettings.tabName1 || "",
        tabName2: systemSettings.tabName2 || "",
        tabName3: systemSettings.tabName3 || "",
      });
    } catch (e) {
      // Log the caught error for debugging
      console.error("Error fetching tab names:", e.message, e);
      return response.status(500).json({
        success: false,
        error:
          e.message || "An unexpected error occurred while fetching tab names.",
      });
    }
  });

  app.post("/system/custom-tab-names", async (request, response) => {
    console.log("Received request body:", reqBody(request));
    try {
      const { tabName1, tabName2, tabName3 } = reqBody(request);
      // Attempt to update the system settings with the extracted values
      console.log(
        "Updating system settings with tabName1:",
        tabName1,
        "tabName2:",
        tabName2,
        "and",
        tabName3
      );
      const result = await SystemSettings._updateSettings({
        tabName1,
        tabName2,
        tabName3,
      });

      // Log the result of the update operation
      console.log("Update result:", result);

      // Send success response if update is successful
      if (result.success) {
        const message = `Tab names have been updated to "${tabName1}", "${tabName2}" and "${tabName3}"`;
        console.log("Update successful. Sending response:", message);
        return response.status(200).json({ success: true, message: message });
      } else {
        console.log("Update failed. Error:", result.error);
        return response.status(500).json({
          success: false,
          error: result.error || "Failed to update tab names.",
        });
      }
    } catch (e) {
      // Log the caught error for debugging
      console.error("Error during update process:", e.message, e);
      return response.status(500).json({
        success: false,
        error:
          e.message ||
          "An unexpected error occurred while updating the tab names.",
      });
    }
  });

  app.post(
    "/system/enable-citation",
    [validatedRequest],
    async (request, response) => {
      try {
        const { citation, copyOption } = reqBody(request);

        await SystemSettings._updateSettings({
          citation: citation,
          copyOption: copyOption,
        });

        if (citation) {
          if (process.env.NODE_ENV === "production") await dumpENV();
          await Telemetry.sendTelemetry("citation", {
            publicUserMode: true,
            copyOption: copyOption, // Track the selected copy option in telemetry
          });

          await EventLogs.logEvent(
            "citation",
            { deletedBy: response.locals?.user?.username },
            response?.locals?.user?.id
          );
          response.status(200).json({ success: true, message: "" });
        } else {
          await EventLogs.logEvent("citation", {}, 0);
          response.status(200).json({ success: true, message: "" });
        }
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/system/enable-multi-user",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        if (response.locals.multiUserMode) {
          response.status(200).json({
            success: false,
            error: "Multi-user mode is already enabled and cannot be disabled.",
          });
          return;
        }

        const { username, password } = reqBody(request);
        const { user, error } = await User.create({
          username,
          password,
          role: ROLES.admin,
        });
        await SystemSettings._updateSettings({
          multi_user_mode: true,
          limit_user_messages: false,
          message_limit: 25,
        });
        await BrowserExtensionApiKey.migrateApiKeysToMultiUser(user.id);

        await updateENV(
          {
            JWTSecret: process.env.JWT_SECRET || v4(),
          },
          true
        );
        await Telemetry.sendTelemetry("enabled_multi_user_mode", {
          multiUserMode: true,
        });
        await EventLogs.logEvent("multi_user_mode_enabled", {}, user?.id);
        response.status(200).json({ success: !!user, error });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.get("/system/citation", async (_, response) => {
    try {
      const citation = await SystemSettings.isCitation();
      response.status(200).json({ citation: citation });
    } catch (e) {
      console.error(e.message, e);
      response.sendStatus(500).end();
    }
  });

  app.get("/system/get-website-link", async (req, res) => {
    try {
      const systemSettings = await SystemSettings.getSystemWebsiteLinkAndText();

      if (!systemSettings) {
        console.error("No system settings found in the database");
        return res.status(404).json({ message: "System settings not found" });
      }

      res.json({
        websiteLink: systemSettings.websiteLink,
        displayText: systemSettings.displayText || "Visit the website",
      });
    } catch (error) {
      console.error("Error fetching website link:", error.message);
      res.status(500).json({
        message: "Failed to fetch website link",
        error: error.message,
      });
    }
  });

  app.get("/system/multi-user-mode", async (_, response) => {
    try {
      const multiUserMode = await SystemSettings.isMultiUserMode();
      response.status(200).json({ multiUserMode });
    } catch (e) {
      console.error(e.message, e);
      response.sendStatus(500).end();
    }
  });

  app.get("/system/qura", async (_, response) => {
    try {
      const settings = await SystemSettings.isQura();
      response.status(200).json({ qura: settings });
    } catch (e) {
      console.error(e.message, e);
      response.sendStatus(500).end();
    }
  });

  app.get("/system/copyOption", async (_, response) => {
    try {
      const copyOptionSetting = await SystemSettings.getCopyOption();
      response.status(200).json({ copyOption: copyOptionSetting });
    } catch (e) {
      console.error(e.message, e);
      response.sendStatus(500).end();
    }
  });

  app.get("/system/public-user-mode", async (_, response) => {
    try {
      const publicUserMode = await SystemSettings.isPublicUserMode();
      response.status(200).json({ publicUserMode: publicUserMode });
    } catch (e) {
      console.log(e.message, e);
      response.sendStatus(500).end();
    }
  });

  app.get("/system/document-drafting", async (_, response) => {
    try {
      const isDocumentDrafting = await SystemSettings.isDocumentDrafting();
      const isDocumentDraftingLinked =
        await SystemSettings.isDocumentDraftingLinking();
      response.status(200).json({
        isDocumentDrafting: isDocumentDrafting,
        isDocumentDraftingLinking: isDocumentDraftingLinked,
      });
    } catch (e) {
      console.error(e.message, e);
      response.sendStatus(500).end();
    }
  });

  app.get("/system/get-document-drafting-prompt", async (_, response) => {
    try {
      const documentDraftingPrompt =
        await SystemSettings.getDocumentDraftingPrompt();
      const legalIssuesPrompt =
        await SystemSettings.getDocumentDraftingLegalIssuesPrompt();
      const memoPrompt = await SystemSettings.getDocumentDraftingMemoPrompt();
      const combinePrompt =
        await SystemSettings.getDocumentDraftingCombinePrompt();
      response.status(200).json({
        documentDraftingPrompt: documentDraftingPrompt,
        legalIssuesPrompt: legalIssuesPrompt,
        memoPrompt: memoPrompt,
        combinePrompt: combinePrompt,
        defaultCombinePrompt: DEFAULT_COMBINE_PROMPT,
        defaultDocumentDraftingPrompt: DEFAULT_DOCUMENT_DRAFTING_PROMPT,
        defaultLegalIssuesPrompt: DEFAULT_LEGAL_ISSUES_PROMPT,
        defaultMemoPrompt: DEFAULT_MEMO_PROMPT,
      });
    } catch (error) {
      console.error("Error fetching document drafting prompt:", error);
      response.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/system/get-default-settings", async (_, response) => {
    try {
      const [systemPrompt, vectorSearch, validationPrompt] = await Promise.all([
        SystemSettings.get({
          label: "default_prompt",
        }),
        SystemSettings.get({
          label: "vector_search_top_n",
        }),
        SystemSettings.get({
          label: "validation_prompt",
        }),
      ]);
      const [canvasSystemPrompt, canvasUploadSystemPrompt] = await Promise.all([
        SystemSettings.get({ label: "canvas_system_prompt" }),
        SystemSettings.get({ label: "canvas_upload_system_prompt" }),
      ]);
      const result = {
        // Actual settings values
        systemPrompt: systemPrompt?.value ?? null,
        vectorSearchTopN: vectorSearch?.value ?? null,
        validationPrompt: validationPrompt?.value ?? null,
        canvasSystemPrompt: canvasSystemPrompt?.value ?? null,
        canvasUploadSystemPrompt: canvasUploadSystemPrompt?.value ?? null,

        // Default prompt values for placeholders
        defaultSystemPrompt: DEFAULT_SYSTEM_PROMPT,
        defaultVectorSearchTopN: DEFAULT_VECTOR_SEARCH_TOP_N,
        defaultValidationPrompt: DEFAULT_VALIDATION_PROMPT,
        defaultCanvasSystemPrompt: DEFAULT_CANVAS_SYSTEM_PROMPT,
        defaultCanvasUploadSystemPrompt: DEFAULT_CANVAS_UPLOAD_SYSTEM_PROMPT,
      };

      response.status(200).json(result);
    } catch (error) {
      console.error("Error fetching default Legal Q&A prompt:", error);
      response.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/system/get-document-builder", async (request, response) => {
    try {
      const { flowType } = request.query; // Added: Get flowType from query

      // Determine flow-specific keys and defaults for topics/sections
      let topicsSectionsSystemPromptLabel = "topics_sections_system_prompt"; // Default generic key
      let topicsSectionsUserPromptLabel = "topics_sections_user_prompt"; // Default generic key
      let defaultTopicsSystemPrompt =
        LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LIST_FROM_SUMMARIES
          .SYSTEM_PROMPT; // Default to noMainDoc
      let defaultTopicsUserPrompt =
        LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LIST_FROM_SUMMARIES.USER_PROMPT; // Default to noMainDoc

      if (flowType === "mainDoc") {
        // These are the PKeys that promptManager.js would use for these specific defaults.
        topicsSectionsSystemPromptLabel =
          "cdb_section_list_from_main_system_prompt";
        topicsSectionsUserPromptLabel =
          "cdb_section_list_from_main_user_prompt";
        defaultTopicsSystemPrompt =
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LIST_FROM_MAIN.SYSTEM_PROMPT;
        defaultTopicsUserPrompt =
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LIST_FROM_MAIN.USER_PROMPT;
      } else if (flowType === "noMainDoc") {
        topicsSectionsSystemPromptLabel =
          "cdb_section_list_from_summaries_system_prompt";
        topicsSectionsUserPromptLabel =
          "cdb_section_list_from_summaries_user_prompt";
        defaultTopicsSystemPrompt =
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LIST_FROM_SUMMARIES
            .SYSTEM_PROMPT;
        defaultTopicsUserPrompt =
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LIST_FROM_SUMMARIES
            .USER_PROMPT;
      }
      // If flowType is not provided or unknown, it will use the noMainDoc defaults and generic labels,
      // which might lead to using a general custom prompt if 'topics_sections_system_prompt' is set,
      // or the noMainDoc default if not. This behavior can be refined if a "generic" flow is better defined.

      const [
        summarySystemPromptSetting,
        summaryUserPromptSetting,
        relevanceSystemPromptSetting,
        relevanceUserPromptSetting,
        selectMainDocumentSystemPromptSetting, // Renamed for clarity
        selectMainDocumentUserPromptSetting, // Renamed for clarity
        topicsSectionsSystemPromptSetting, // Fetched using flow-specific label below
        topicsSectionsUserPromptSetting, // Fetched using flow-specific label below
        sectionSystemPromptSetting,
        sectionUserPromptSetting,
        sectionLegalIssuesSystemPromptSetting,
        sectionLegalIssuesUserPromptSetting,
      ] = await Promise.all([
        SystemSettings.get({ label: "summary_system_prompt" }),
        SystemSettings.get({ label: "summary_user_prompt" }),
        SystemSettings.get({ label: "relevance_system_prompt" }),
        SystemSettings.get({ label: "relevance_user_prompt" }),
        SystemSettings.get({ label: "select_main_document_system_prompt" }),
        SystemSettings.get({ label: "select_main_document_user_prompt" }),
        SystemSettings.get({ label: topicsSectionsSystemPromptLabel }), // Use flow-specific label
        SystemSettings.get({ label: topicsSectionsUserPromptLabel }), // Use flow-specific label
        SystemSettings.get({ label: "section_system_prompt" }),
        SystemSettings.get({ label: "section_user_prompt" }),
        SystemSettings.get({ label: "section_legal_issues_system_prompt" }),
        SystemSettings.get({ label: "section_legal_issues_user_prompt" }),
      ]);

      const sanitize = (setting, fallback) => {
        const v = setting?.value;
        if (
          !v ||
          v.trim().length === 0 ||
          v.trim().toLowerCase() === "undefined"
        ) {
          return fallback;
        }
        return v;
      };

      const resolvedPrompts = {
        summarySystemPrompt: sanitize(
          summarySystemPromptSetting,
          LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT
        ),
        summaryUserPrompt: sanitize(
          summaryUserPromptSetting,
          LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT
        ),
        relevanceSystemPrompt: sanitize(
          relevanceSystemPromptSetting,
          LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_RELEVANCE.SYSTEM_PROMPT
        ),
        relevanceUserPrompt: sanitize(
          relevanceUserPromptSetting,
          LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_RELEVANCE.USER_PROMPT
        ),
        selectMainDocumentSystemPrompt: sanitize(
          selectMainDocumentSystemPromptSetting,
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SELECT_MAIN_DOCUMENT.SYSTEM_PROMPT
        ),
        selectMainDocumentUserPrompt: sanitize(
          selectMainDocumentUserPromptSetting,
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SELECT_MAIN_DOCUMENT.USER_PROMPT
        ),
        topicsSectionsSystemPrompt: sanitize(
          topicsSectionsSystemPromptSetting,
          defaultTopicsSystemPrompt // Already flow-specific default
        ),
        topicsSectionsUserPrompt: sanitize(
          topicsSectionsUserPromptSetting,
          defaultTopicsUserPrompt // Already flow-specific default
        ),
        sectionSystemPrompt: sanitize(
          sectionSystemPromptSetting,
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_DRAFTING.SYSTEM_PROMPT
        ),
        sectionUserPrompt: sanitize(
          sectionUserPromptSetting,
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_DRAFTING.USER_PROMPT
        ),
        sectionLegalIssuesSystemPrompt: sanitize(
          sectionLegalIssuesSystemPromptSetting,
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LEGAL_ISSUES.SYSTEM_PROMPT
        ),
        sectionLegalIssuesUserPrompt: sanitize(
          sectionLegalIssuesUserPromptSetting,
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LEGAL_ISSUES.USER_PROMPT
        ),

        // Default prompt values (for placeholders) - these are also returned for the settings page
        defaultSummarySystemPrompt:
          LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_SUMMARY.SYSTEM_PROMPT,
        defaultSummaryUserPrompt:
          LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_SUMMARY.USER_PROMPT,
        defaultRelevanceSystemPrompt:
          LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_RELEVANCE.SYSTEM_PROMPT,
        defaultRelevanceUserPrompt:
          LEGAL_DRAFTING_PROMPTS.DEFAULT_DOCUMENT_RELEVANCE.USER_PROMPT,
        defaultSelectMainSystemPrompt:
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SELECT_MAIN_DOCUMENT.SYSTEM_PROMPT,
        defaultSelectMainUserPrompt:
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SELECT_MAIN_DOCUMENT.USER_PROMPT,
        defaultTopicsSectionsSystemPrompt: defaultTopicsSystemPrompt, // Flow-specific default
        defaultTopicsSectionsUserPrompt: defaultTopicsUserPrompt, // Flow-specific default
        defaultSectionSystemPrompt:
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_DRAFTING.SYSTEM_PROMPT,
        defaultSectionUserPrompt:
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_DRAFTING.USER_PROMPT,
        defaultSectionLegalIssuesSystemPrompt:
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LEGAL_ISSUES.SYSTEM_PROMPT,
        defaultSectionLegalIssuesUserPrompt:
          LEGAL_DRAFTING_PROMPTS.DEFAULT_SECTION_LEGAL_ISSUES.USER_PROMPT,
      };

      // Define the structure of prompts to be listed for the cdbPromptBuilder
      let promptFlowStructure = [];
      const sharedPromptsPart1 = [
        { key: "summarySystemPrompt", label: "DOCUMENT SUMMARY SYSTEM PROMPT" },
        { key: "summaryUserPrompt", label: "DOCUMENT SUMMARY USER PROMPT" },
        {
          key: "relevanceSystemPrompt",
          label: "DOCUMENT RELEVANCE SYSTEM PROMPT",
        },
        { key: "relevanceUserPrompt", label: "DOCUMENT RELEVANCE USER PROMPT" },
      ];
      const sharedPromptsPart2 = [
        { key: "sectionSystemPrompt", label: "SECTION DRAFTING SYSTEM PROMPT" },
        { key: "sectionUserPrompt", label: "SECTION DRAFTING USER PROMPT" },
        {
          key: "sectionLegalIssuesSystemPrompt",
          label: "SECTION LEGAL ISSUES IDENTIFICATION SYSTEM PROMPT",
        },
        {
          key: "sectionLegalIssuesUserPrompt",
          label: "SECTION LEGAL ISSUES IDENTIFICATION USER PROMPT",
        },
      ];

      if (flowType === "mainDoc") {
        promptFlowStructure = [
          ...sharedPromptsPart1,
          {
            key: "selectMainDocumentSystemPrompt",
            label: "SELECT MAIN DOCUMENT SYSTEM PROMPT",
          },
          {
            key: "selectMainDocumentUserPrompt",
            label: "SELECT MAIN DOCUMENT USER PROMPT",
          },
          {
            key: "topicsSectionsSystemPrompt",
            label: "TOPICS & SECTIONS (FROM MAIN DOCUMENT) SYSTEM PROMPT",
          },
          {
            key: "topicsSectionsUserPrompt",
            label: "TOPICS & SECTIONS (FROM MAIN DOCUMENT) USER PROMPT",
          },
          ...sharedPromptsPart2,
        ];
      } else {
        // noMainDoc or undefined flowType defaults to this structure
        promptFlowStructure = [
          ...sharedPromptsPart1,
          // No selectMainDocument prompts for noMainDoc flow
          {
            key: "topicsSectionsSystemPrompt",
            label: "TOPICS & SECTIONS (FROM SUMMARIES) SYSTEM PROMPT",
          },
          {
            key: "topicsSectionsUserPrompt",
            label: "TOPICS & SECTIONS (FROM SUMMARIES) USER PROMPT",
          },
          ...sharedPromptsPart2,
        ];
      }

      const result = {
        ...resolvedPrompts,
        promptFlowStructure, // Add the flow structure to the response
      };

      response.status(200).json(result);
    } catch (error) {
      console.error("Error fetching document builder prompts:", error);
      response.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/system/logo", async function (_, response) {
    try {
      const defaultFilename = getDefaultFilenameLight();
      const logoPath = await determineLogoLightFilepath(defaultFilename);
      const { found, buffer, size, mime } = fetchLogo(logoPath);

      if (!found) {
        response.sendStatus(204).end();
        return;
      }

      const currentLogoFilename = await SystemSettings.currentLogoLight();
      response.writeHead(200, {
        "Access-Control-Expose-Headers":
          "Content-Disposition,X-Is-Custom-Logo,Content-Type,Content-Length",
        "Content-Type": mime || "image/png",
        "Content-Disposition": `attachment; filename=${path.basename(
          logoPath
        )}`,
        "Content-Length": size,
        "X-Is-Custom-Logo":
          currentLogoFilename !== null &&
          currentLogoFilename !== defaultFilename,
      });
      response.end(Buffer.from(buffer, "base64"));
      return;
    } catch (error) {
      console.error("Error processing the logo request:", error);
      response.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/system/logo-dark", async function (_, response) {
    try {
      const defaultFilename = getDefaultFilenameDark();
      const logoPath = await determineLogoDarkFilepath(defaultFilename);
      const { found, buffer, size, mime } = fetchLogo(logoPath);

      if (!found) {
        response.sendStatus(204).end();
        return;
      }

      const currentLogoFilename = await SystemSettings.currentLogoDark();
      response.writeHead(200, {
        "Access-Control-Expose-Headers":
          "Content-Disposition,X-Is-Custom-Logo,Content-Type,Content-Length",
        "Content-Type": mime || "image/png",
        "Content-Disposition": `attachment; filename=${path.basename(
          logoPath
        )}`,
        "Content-Length": size,
        "X-Is-Custom-Logo":
          currentLogoFilename !== null &&
          currentLogoFilename !== defaultFilename,
      });
      response.end(Buffer.from(buffer, "base64"));
      return;
    } catch (error) {
      console.error("Error processing the logo request:", error);
      response.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/system/footer-data", [validatedRequest], async (_, response) => {
    try {
      const footerData =
        (await SystemSettings.get({ label: "footer_data" }))?.value ??
        JSON.stringify([]);
      response.status(200).json({ footerData: footerData });
    } catch (error) {
      console.error("Error fetching footer data:", error);
      response.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/system/support-email", async (_, response) => {
    try {
      const supportEmail =
        (
          await SystemSettings.get({
            label: "support_email",
          })
        )?.value ?? null;
      response.status(200).json({ supportEmail: supportEmail });
    } catch (error) {
      console.error("Error fetching support email:", error);
      response.status(500).json({ message: "Internal server error" });
    }
  });

  // No middleware protection in order to get this on the login page
  app.get("/system/custom-app-name", async (_, response) => {
    try {
      const customAppName =
        (
          await SystemSettings.get({
            label: "custom_app_name",
          })
        )?.value ?? null;
      response.status(200).json({ customAppName: customAppName });
    } catch (error) {
      console.error("Error fetching custom app name:", error);
      response.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/custom-website-link", async (_, res) => {
    try {
      const customWebsiteLink =
        (
          await SystemSettings.get({
            label: "custom_website_link",
          })
        )?.value ?? null;
      res.status(200).json({ customWebsiteLink: customWebsiteLink });
    } catch (error) {
      console.error("Error fetching custom website link:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/system/custom-paragraph", async (_, response) => {
    try {
      const customParagraphText = await SystemSettings.get({
        label: "custom_paragraph_text",
      });
      response
        .status(200)
        .json({ customParagraphText: customParagraphText?.value || null });
    } catch (e) {
      console.error(e.message, e);
      response.sendStatus(500).end();
    }
  });

  app.get(
    "/system/pfp/:id",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async function (request, response) {
      try {
        const { id } = request.params;
        const pfpPath = await determinePfpFilepath(id);

        if (!pfpPath) {
          response.sendStatus(204).end();
          return;
        }

        const { found, buffer, size, mime } = fetchPfp(pfpPath);
        if (!found) {
          response.sendStatus(204).end();
          return;
        }

        response.writeHead(200, {
          "Content-Type": mime || "image/png",
          "Content-Disposition": `attachment; filename=${path.basename(
            pfpPath
          )}`,
          "Content-Length": size,
        });
        response.end(Buffer.from(buffer, "base64"));
        return;
      } catch (error) {
        console.error("Error processing the logo request:", error);
        response.status(500).json({ message: "Internal server error" });
      }
    }
  );

  app.post(
    "/system/upload-pfp",
    [validatedRequest, flexUserRoleValid([ROLES.all]), handlePfpUpload],
    async function (request, response) {
      try {
        const user = await userFromSession(request, response);
        const uploadedFileName = request.randomFileName;
        if (!uploadedFileName) {
          return response.status(400).json({ message: "File upload failed." });
        }

        const userRecord = await User.get({ id: user.id });
        const oldPfpFilename = userRecord.pfpFilename;
        if (oldPfpFilename) {
          const storagePath = path.join(__dirname, "../storage/assets/pfp");
          const oldPfpPath = path.join(
            storagePath,
            normalizePath(userRecord.pfpFilename)
          );
          if (!isWithin(path.resolve(storagePath), path.resolve(oldPfpPath)))
            throw new Error("Invalid path name");
          if (fs.existsSync(oldPfpPath)) fs.unlinkSync(oldPfpPath);
        }

        const { success, error } = await User.update(user.id, {
          pfpFilename: uploadedFileName,
        });

        return response.status(success ? 200 : 500).json({
          message: success
            ? "Profile picture uploaded successfully."
            : error || "Failed to update with new profile picture.",
        });
      } catch (error) {
        console.error("Error processing the profile picture upload:", error);
        response.status(500).json({ message: "Internal server error" });
      }
    }
  );

  app.delete(
    "/system/remove-pfp",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async function (request, response) {
      try {
        const user = await userFromSession(request, response);
        const userRecord = await User.get({ id: user.id });
        const oldPfpFilename = userRecord.pfpFilename;

        if (oldPfpFilename) {
          const storagePath = path.join(__dirname, "../storage/assets/pfp");
          const oldPfpPath = path.join(
            storagePath,
            normalizePath(oldPfpFilename)
          );
          if (!isWithin(path.resolve(storagePath), path.resolve(oldPfpPath)))
            throw new Error("Invalid path name");
          if (fs.existsSync(oldPfpPath)) fs.unlinkSync(oldPfpPath);
        }

        const { success, error } = await User.update(user.id, {
          pfpFilename: null,
        });

        return response.status(success ? 200 : 500).json({
          message: success
            ? "Profile picture removed successfully."
            : error || "Failed to remove profile picture.",
        });
      } catch (error) {
        console.error("Error processing the profile picture removal:", error);
        response.status(500).json({ message: "Internal server error" });
      }
    }
  );

  app.post(
    "/system/upload-logo",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager]),
      handleAssetUpload,
    ],
    async (request, response) => {
      if (!request?.file || !request?.file.originalname) {
        return response.status(400).json({ message: "No logo file provided." });
      }

      if (!validFilenameLight(request.file.originalname)) {
        return response.status(400).json({
          message: "Invalid file name. Please choose a different file.",
        });
      }

      try {
        const newFilename = await renameLogoFile(request.file.originalname);
        const existingLogoFilename = await SystemSettings.currentLogoLight();
        await removeCustomLogoLight(existingLogoFilename);

        const { success, error } = await SystemSettings._updateSettings({
          logo_light: newFilename,
        });

        return response.status(success ? 200 : 500).json({
          message: success
            ? "Logo uploaded successfully."
            : error || "Failed to update with new logo.",
        });
      } catch (error) {
        console.error("Error processing the logo upload:", error);
        response.status(500).json({ message: "Error uploading the logo." });
      }
    }
  );

  app.post(
    "/system/upload-logo-dark",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager]),
      handleAssetUpload,
    ],
    async (request, response) => {
      if (!request?.file || !request?.file.originalname) {
        return response.status(400).json({ message: "No logo file provided." });
      }

      if (!validFilenameDark(request.file.originalname)) {
        return response.status(400).json({
          message: "Invalid file name. Please choose a different file.",
        });
      }

      try {
        const newFilename = await renameLogoFile(request.file.originalname);
        const existingLogoFilename = await SystemSettings.currentLogoLight();
        await removeCustomLogoDark(existingLogoFilename);

        const { success, error } = await SystemSettings._updateSettings({
          logo_dark: newFilename,
        });

        return response.status(success ? 200 : 500).json({
          message: success
            ? "Logo uploaded successfully."
            : error || "Failed to update with new logo.",
        });
      } catch (error) {
        console.error("Error processing the logo upload:", error);
        response.status(500).json({ message: "Error uploading the logo." });
      }
    }
  );

  app.get("/system/is-default-logo", async (_, response) => {
    try {
      const currentLogoFilename = await SystemSettings.currentLogoDark();
      const isDefaultLogo = currentLogoFilename === LOGO_LIGHT;
      response.status(200).json({ isDefaultLogo });
    } catch (error) {
      console.error("Error processing the logo request:", error);
      response.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/system/is-default-logo-dark", async (_, response) => {
    try {
      const currentLogoFilename = await SystemSettings.currentLogoDark();
      const isDefaultLogo = currentLogoFilename === LOGO_DARK;
      response.status(200).json({ isDefaultLogo });
    } catch (error) {
      console.error("Error processing the logo request:", error);
      response.status(500).json({ message: "Internal server error" });
    }
  });

  app.get(
    "/system/remove-logo",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (_request, response) => {
      try {
        const currentLogoFilename = await SystemSettings.currentLogoLight();
        await removeCustomLogoLight(currentLogoFilename);
        const { success, error } = await SystemSettings._updateSettings({
          logo_light: LOGO_LIGHT,
        });

        return response.status(success ? 200 : 500).json({
          message: success
            ? "Logo removed successfully."
            : error || "Failed to update with new logo.",
        });
      } catch (error) {
        console.error("Error processing the logo removal:", error);
        response.status(500).json({ message: "Error removing the logo." });
      }
    }
  );

  app.get(
    "/system/remove-logo-dark",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (_request, response) => {
      try {
        const currentLogoFilename = await SystemSettings.currentLogoDark();
        await removeCustomLogoDark(currentLogoFilename);
        const { success, error } = await SystemSettings._updateSettings({
          logo_dark: LOGO_DARK,
        });

        return response.status(success ? 200 : 500).json({
          message: success
            ? "Logo removed successfully."
            : error || "Failed to update with new logo.",
        });
      } catch (error) {
        console.error("Error processing the logo removal:", error);
        response.status(500).json({ message: "Error removing the logo." });
      }
    }
  );

  app.get("/system/welcome-messages", async (request, response) => {
    try {
      const [headline, text] = await Promise.all([
        SystemSettings.getValueOrFallback(
          { label: "welcome_message_headline" },
          ""
        ),
        SystemSettings.getValueOrFallback(
          { label: "welcome_message_text" },
          ""
        ),
      ]);
      return response.status(200).json({ heading: headline, text });
    } catch (error) {
      console.error("Error fetching welcome messages:", error);
      return response
        .status(500)
        .json({ error: "Failed to get welcome messages" });
    }
  });

  app.post(
    "/system/set-welcome-messages",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        const messages = reqBody(request);

        // Validate message format
        if (
          !messages ||
          typeof messages !== "object" ||
          !("heading" in messages) ||
          !("text" in messages)
        ) {
          return response.status(400).json({
            success: false,
            message: "Invalid message format. Must include heading and text.",
          });
        }

        const result = await SystemSettings.updateSettings({
          welcome_message_headline: messages.heading,
          welcome_message_text: messages.text,
        });

        if (!result.success) {
          throw new Error(result.error || "Failed to save welcome messages");
        }

        return response.status(200).json({
          success: true,
          message: "Welcome messages saved successfully.",
        });
      } catch (error) {
        console.error("Error saving welcome messages:", error);
        return response.status(500).json({
          success: false,
          message: error.message || "Error saving the welcome messages.",
        });
      }
    }
  );

  app.get("/system/api-keys", [validatedRequest], async (_, response) => {
    try {
      if (response.locals.multiUserMode) {
        return response.sendStatus(401).end();
      }

      const apiKeys = await ApiKey.where({});
      return response.status(200).json({
        apiKeys,
        error: null,
      });
    } catch (error) {
      console.error(error);
      response.status(500).json({
        apiKey: null,
        error: "Could not find an API Key.",
      });
    }
  });

  app.post(
    "/system/generate-api-key",
    [validatedRequest],
    async (_, response) => {
      try {
        if (response.locals.multiUserMode) {
          return response.sendStatus(401).end();
        }

        const { apiKey, error } = await ApiKey.create();
        await Telemetry.sendTelemetry("api_key_created");
        await EventLogs.logEvent(
          "api_key_created",
          {},
          response?.locals?.user?.id
        );
        return response.status(200).json({
          apiKey,
          error,
        });
      } catch (error) {
        console.error(error);
        response.status(500).json({
          apiKey: null,
          error: "Error generating api key.",
        });
      }
    }
  );

  app.delete("/system/api-key", [validatedRequest], async (_, response) => {
    try {
      if (response.locals.multiUserMode) {
        return response.sendStatus(401).end();
      }

      await ApiKey.delete();
      await EventLogs.logEvent(
        "api_key_deleted",
        { deletedBy: response.locals?.user?.username },
        response?.locals?.user?.id
      );
      return response.status(200).end();
    } catch (error) {
      console.error(error);
      response.status(500).end();
    }
  });

  app.post(
    "/system/custom-models",
    [validatedRequest],
    async (request, response) => {
      try {
        const { provider, apiKey = null, basePath = null } = reqBody(request);
        const { models, error } = await getCustomModels(
          provider,
          apiKey,
          basePath
        );
        return response.status(200).json({
          models,
          error,
        });
      } catch (error) {
        console.error(error);
        response.status(500).end();
      }
    }
  );

  app.post(
    "/system/event-logs",
    [validatedRequest, flexUserRoleValid([ROLES.admin])],
    async (request, response) => {
      try {
        const { offset = 0, limit = 10 } = reqBody(request);
        const logs = await EventLogs.whereWithData({}, limit, offset * limit, {
          id: "desc",
        });
        const totalLogs = await EventLogs.count();
        const hasPages = totalLogs > (offset + 1) * limit;

        response.status(200).json({ logs: logs, hasPages, totalLogs });
      } catch (e) {
        console.error(e);
        response.sendStatus(500).end();
      }
    }
  );

  app.delete(
    "/system/event-logs",
    [validatedRequest, flexUserRoleValid([ROLES.admin])],
    async (_, response) => {
      try {
        await EventLogs.delete();
        await EventLogs.logEvent(
          "event_logs_cleared",
          {},
          response?.locals?.user?.id
        );
        response.json({ success: true });
      } catch (e) {
        console.error(e);
        response.sendStatus(500).end();
      }
    }
  );

  app.post(
    "/system/workspace-chats",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        const { offset = 0, filters = {}, limit = 20 } = reqBody(request);

        const validFilters = {};
        if (filters.user) {
          validFilters.user = {
            username: {
              contains: filters.user,
            },
          };
        }

        if (filters.startDate || filters.endDate) {
          validFilters.createdAt = {};
          if (filters.startDate) {
            validFilters.createdAt.gte = new Date(filters.startDate);
          }
          if (filters.endDate) {
            const endDate = new Date(filters.endDate);
            endDate.setHours(23, 59, 59, 999);
            validFilters.createdAt.lte = endDate;
          }
        }

        if (filters.referenceNumber) {
          validFilters.invoice_ref = { startsWith: filters.referenceNumber };
        }

        const whereFilters = { ...validFilters };
        const countFilters = { ...validFilters };

        const chats = await WorkspaceChats.whereWithData(
          whereFilters,
          limit,
          offset * limit,
          { id: "desc" }
        );

        const totalChats = await WorkspaceChats.count(countFilters);
        const hasPages = totalChats > (offset + 1) * limit;

        response.status(200).json({ chats, hasPages, totalChats });
      } catch (e) {
        console.error(e);
        response.sendStatus(500).end();
      }
    }
  );

  // Endpoint to get the total chat count without any filters
  app.get(
    "/system/workspace-chats-count",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        const totalChats = await WorkspaceChats.count({});
        response.status(200).json({ totalChats });
      } catch (e) {
        console.error(e);
        response.sendStatus(500).end();
      }
    }
  );

  app.delete(
    "/system/workspace-chats/:id",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        const { id } = request.params;

        // If deleting all chats
        if (Number(id) === -1) {
          // Get all chats first to handle attachments
          const allChats = await WorkspaceChats.where({});

          // Count how many will be deleted before deleting
          const deletedCount = allChats.length;

          // Delete all chat records
          await WorkspaceChats.delete({}, true);

          // Update the deletion count in system settings
          const currentCountSetting = await SystemSettings.get({
            label: "chat_deletion_count",
          });
          const currentCount = currentCountSetting
            ? parseInt(currentCountSetting.value, 10)
            : 0;

          const newCount = currentCount + deletedCount;
          await SystemSettings._updateSettings({
            chat_deletion_count: newCount.toString(),
          });

          // Log the event
          if (response.locals.user) {
            await EventLogs.logEvent(
              "deleted_all_chats",
              {
                count: deletedCount,
                totalDeleted: newCount,
              },
              response.locals.user.id
            );
          }

          response.json({
            success: true,
            deletedCount,
            totalDeleted: newCount,
            error: null,
          });
        } else {
          // Delete the chat record
          await WorkspaceChats.delete({ id: Number(id) });
          response.json({ success: true, error: null });
        }
      } catch (e) {
        console.error(e);
        response.sendStatus(500).end();
      }
    }
  );

  app.delete(
    "/system/delete-old-chats",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        const { timeframe, value } = reqBody(request);

        // Validate parameters
        if (
          !timeframe ||
          !value ||
          !["days", "weeks", "months"].includes(timeframe) ||
          !Number.isInteger(Number(value)) ||
          Number(value) <= 0
        ) {
          return response.status(400).json({
            success: false,
            error:
              "Invalid parameters. Timeframe must be 'days', 'weeks', or 'months' and value must be a positive integer.",
          });
        }

        // Calculate the date threshold based on timeframe and value
        const now = new Date();
        let thresholdDate;

        if (timeframe === "days") {
          thresholdDate = new Date(
            now.getTime() - Number(value) * 24 * 60 * 60 * 1000
          );
        } else if (timeframe === "weeks") {
          thresholdDate = new Date(
            now.getTime() - Number(value) * 7 * 24 * 60 * 60 * 1000
          );
        } else {
          // months
          thresholdDate = new Date(
            now.getFullYear(),
            now.getMonth() - Number(value),
            now.getDate()
          );
        }

        // Get current deletion count from system settings
        const currentCountSetting = await SystemSettings.get({
          label: "chat_deletion_count",
        });
        const currentCount = currentCountSetting
          ? parseInt(currentCountSetting.value, 10)
          : 0;

        // Delete chats older than the threshold date
        const deletedCount =
          await WorkspaceChats.deleteOlderThan(thresholdDate);

        // Update the deletion count in system settings
        const newCount = currentCount + deletedCount;
        await SystemSettings._updateSettings({
          chat_deletion_count: newCount.toString(),
        });

        // Log the event
        await EventLogs.logEvent(
          "deleted_old_chats",
          {
            timeframe,
            value,
            count: deletedCount,
            totalDeleted: newCount,
          },
          response.locals.user?.id
        );

        response.json({
          success: true,
          deletedCount,
          totalDeleted: newCount,
          error: null,
        });
      } catch (e) {
        console.error("Error deleting old chats:", e);
        response.status(500).json({ success: false, error: e.message });
      }
    }
  );

  app.get(
    "/system/export-chats",
    [validatedRequest, flexUserRoleValid([ROLES.manager, ROLES.admin])],
    async (request, response) => {
      try {
        let {
          type = "jsonl",
          chatType = "workspace",
          filters = "{}",
        } = request.query;

        let parsedFilters = {};
        if (typeof filters === "string" && filters.trim().length > 0) {
          try {
            parsedFilters = JSON.parse(filters);
          } catch (err) {
            parsedFilters = {};
          }
        }

        const { contentType, data } = await exportChatsAsType(
          type,
          chatType,
          parsedFilters
        );
        await EventLogs.logEvent(
          "exported_chats",
          {
            type,
            chatType,
          },
          response.locals.user?.id
        );
        response.setHeader("Content-Type", contentType);
        response.status(200).send(data);
      } catch (e) {
        console.error(e);
        response.sendStatus(500).end();
      }
    }
  );

  // Used for when a user in multi-user updates their own profile
  // from the UI.
  app.post("/system/user", [validatedRequest], async (request, response) => {
    try {
      const sessionUser = await userFromSession(request, response);
      const { username, password, custom_ai_userselected, economy_system_id } =
        reqBody(request);
      const id = Number(sessionUser.id);

      if (!id) {
        response.status(400).json({ success: false, error: "Invalid user ID" });
        return;
      }

      const user = await User.get({ id: id });
      const updates = {};

      if (username) {
        if (user.role === ROLES.admin || user.role === ROLES.manager) {
          updates.username = User.validations.username(String(username));
        } else {
          updates.username = User.validations.username(user.username);
        }
      }

      if (password) {
        updates.password = String(password);
      }

      if (custom_ai_userselected !== undefined) {
        updates.custom_ai_userselected =
          User.validations.custom_ai_userselected(custom_ai_userselected);
      }

      if (economy_system_id !== undefined) {
        updates.economy_system_id = String(economy_system_id);
      }

      if (Object.keys(updates).length === 0) {
        response
          .status(400)
          .json({ success: false, error: "No updates provided" });
        return;
      }

      const { success, error } = await User.update(id, updates);
      response.status(200).json({ success, error });
    } catch (e) {
      console.error(e);
      response.sendStatus(500).end();
    }
  });

  app.get(
    "/system/slash-command-presets",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);
        const userPresets = await SlashCommandPresets.getUserPresets(user?.id);
        response.status(200).json({ presets: userPresets });
      } catch (error) {
        console.error("Error fetching slash command presets:", error);
        response.status(500).json({ message: "Internal server error" });
      }
    }
  );

  app.post(
    "/system/slash-command-presets",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);
        const { command, prompt, description } = reqBody(request);
        const presetData = {
          command: SlashCommandPresets.formatCommand(String(command)),
          prompt: String(prompt),
          description: String(description),
        };

        const preset = await SlashCommandPresets.create(user?.id, presetData);
        if (!preset) {
          return response
            .status(500)
            .json({ message: "Failed to create preset" });
        }
        response.status(201).json({ preset });
      } catch (error) {
        console.error("Error creating slash command preset:", error);
        response.status(500).json({ message: "Internal server error" });
      }
    }
  );

  app.post(
    "/system/slash-command-presets/:slashCommandId",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);
        const { slashCommandId } = request.params;
        const { command, prompt, description } = reqBody(request);

        // Valid user running owns the preset if user session is valid.
        const ownsPreset = await SlashCommandPresets.get({
          userId: user?.id ?? null,
          id: Number(slashCommandId),
        });
        if (!ownsPreset)
          return response.status(404).json({ message: "Preset not found" });

        const updates = {
          command: SlashCommandPresets.formatCommand(String(command)),
          prompt: String(prompt),
          description: String(description),
        };

        const preset = await SlashCommandPresets.update(
          Number(slashCommandId),
          updates
        );
        if (!preset) return response.sendStatus(422);
        response.status(200).json({ preset: { ...ownsPreset, ...updates } });
      } catch (error) {
        console.error("Error updating slash command preset:", error);
        response.status(500).json({ message: "Internal server error" });
      }
    }
  );

  app.delete(
    "/system/slash-command-presets/:slashCommandId",
    [validatedRequest, flexUserRoleValid([ROLES.all])],
    async (request, response) => {
      try {
        const { slashCommandId } = request.params;
        const user = await userFromSession(request, response);

        // Valid user running owns the preset if user session is valid.
        const ownsPreset = await SlashCommandPresets.get({
          userId: user?.id ?? null,
          id: Number(slashCommandId),
        });
        if (!ownsPreset)
          return response
            .status(403)
            .json({ message: "Failed to delete preset" });

        await SlashCommandPresets.delete(Number(slashCommandId));
        response.sendStatus(204);
      } catch (error) {
        console.error("Error deleting slash command preset:", error);
        response.status(500).json({ message: "Internal server error" });
      }
    }
  );

  app.delete(
    "/system/slash-command-preset/:id",
    [validatedRequest],
    async (request, response) => {
      try {
        const { id } = request.params;
        await prisma.slashCommandPreset.delete({
          where: {
            id: parseInt(id),
          },
        });
        response.status(200).json({ message: "Preset deleted successfully" });
      } catch (error) {
        console.error("Error deleting slash command preset:", error);
        response.status(500).json({ message: "Internal server error" });
      }
    }
  );

  app.post(
    "/system/validate-answer",
    [validatedRequest],
    async (request, response) => {
      try {
        const { answer, llmInput } = reqBody(request);
        const validationPromptSetting = await SystemSettings.get({
          label: "validation_prompt",
        });
        const result = await ValidateAnswer(
          validationPromptSetting?.value,
          answer,
          llmInput
        );
        response.json({ success: true, result });
      } catch (error) {
        console.error("Error validating answer:", error);
        response.status(500).json({ success: false, error: error.message });
      }
    }
  );

  async function ValidateAnswer(validationPrompt, message, llmInput) {
    let LLMConnector;
    const vaProviderSetting = await SystemSettings.get({
      label: "LLMProvider_VA",
    });
    const providerChoice = vaProviderSetting?.value;

    try {
      if (providerChoice === "system-standard") {
        console.log(
          "ValidateAnswer using system-standard, resolving to default LLM."
        );
        const actualProvider = process.env.LLM_PROVIDER ?? "openai";
        // Let getLLMProvider determine the default model for the actualProvider
        LLMConnector = getLLMProvider({ provider: actualProvider });
      } else if (providerChoice) {
        // If DB setting has a specific provider, use it with _VA suffix
        console.log(
          `ValidateAnswer using specific setting: ${providerChoice} with suffix _VA`
        );
        LLMConnector = getLLMProvider({
          provider: providerChoice,
          settings_suffix: "_VA",
        });
      } else {
        // If DB setting is null/undefined, treat it as system-standard
        console.log(
          "ValidateAnswer: No DB setting found, treating as system-standard."
        );
        const actualProvider = process.env.LLM_PROVIDER ?? "openai";
        LLMConnector = getLLMProvider({ provider: actualProvider });
      }

      // Validate the resolved connector/model
      if (!LLMConnector) throw new Error("Could not determine LLM provider.");
      if (!LLMConnector.isValidChatCompletionModel(LLMConnector.model)) {
        throw new Error(
          `${LLMConnector.model} is not valid for chat completion!`
        );
      }

      // --- Rest of the existing ValidateAnswer logic ---

      if (!validationPrompt) {
        validationPrompt = `
          You are a reviewer tasked with comparing an analysis against the sources that formed the basis for it, and to issue a report on whether each important statement and reference in the analysis is consistent with the sources. The purpose of this validation process is to ensure that the generated response correctly reflects the provided context and that it is done in accordance with the instructions. Follow these steps to perform a thorough validation:

          1. Identification of Deviations:
             - Read through the generated response and compare it carefully with the provided context.
             - Summarize if there are any discrepancies between the response and the source material. If so, describe what deviation has been identified.

          2. Validation of Sources:
             - For each source mentioned in the generated response, confirm that the information provided is actually supported by the provided context.
             - Explain where support for each source has been found in the provided information, and note any differences or inaccuracies.

          3. Fulfillment of Instructions:
             - Verify that the system prompt has been followed, including that the generated response does not contain references to "CONTEXT" but only to the actual source names in metadata for each source.
             - Check that the response does not make any unsubstantiated claims or lack references for important parts.

          4. Reporting:
             - Present the results of the validation checks without giving any personal interpretations or further investigations.
             - If everything is correct, confirm that the response is validated without deviations.
             - If everything is not correct, provide a bullet point list with instructions on what needs to be corrected.

          It is important that you base your validation solely on the information that has been provided and do not make any assumptions or additions of your own. This ensures an objective and accurate validation of the generated response. Also ensure that the report provided is precisely a report on the reliability of the reviewed analysis and not a new independent investigation. Verify that validation of a response has occurred and not a new independent investigation. Deliver the report in the same language that was used in the user prompt that generated the response being reviewed.
          `;
      }

      const messageContent = `<USER QUESTION AND SOURCES>\n${JSON.stringify(
        llmInput || "Not provided"
      )}\n</USER QUESTION AND SOURCES>\n\n<SYSTEM RESPONSE>\n${message}\n</SYSTEM RESPONSE>`;

      let compressedMessages;
      const resolvedProvider = LLMConnector.constructor.name // Get provider name from instance if possible
        .replace("LLM", "")
        .toLowerCase();

      // Special handling for different LLM providers based on the *resolved* provider
      if (resolvedProvider === "gemini") {
        // For Gemini: Combine validation prompt and content into a single user message
        compressedMessages = [
          {
            role: "user",
            content: `${validationPrompt}\n\n${messageContent}`,
          },
        ];
      } else if (resolvedProvider === "anthropic") {
        // For Anthropic: It expects the system prompt as the first message
        compressedMessages = [
          {
            role: "system",
            content: validationPrompt,
          },
          {
            role: "user",
            content: messageContent,
          },
        ];
      } else {
        // Default compression for OpenAI and others
        compressedMessages = await LLMConnector.compressMessages({
          systemPrompt: validationPrompt,
          userPrompt: messageContent,
        });
      }

      const validationResult = await LLMConnector.getChatCompletion(
        compressedMessages,
        {
          temperature: 0.7,
        }
      );

      if (!validationResult) throw new Error("No validation result received");

      // Clean up the response to remove any XML tags that might have been included
      let cleanResult = validationResult;
      if (cleanResult.textResponse) {
        // Remove XML-like tags that might be included in the response
        cleanResult.textResponse = cleanResult.textResponse
          .replace(/<\/?(SYSTEM RESPONSE|USER QUESTION AND SOURCES)>?/gi, "")
          .trim();
      }

      return cleanResult;
    } catch (error) {
      console.error("Validation error:", error);
      // Ensure the specific provider resolution error is caught if it happens early
      const errorMsg = error?.message || "AI engine validation failed";
      throw new Error(errorMsg);
    }
  }

  app.get(
    "/system/invoice-logging",
    [validatedRequest],
    async (_, response) => {
      try {
        const invoiceLoggingEnabled = await SystemSettings.isInvoiceEnabled();
        response.status(200).json({
          invoice: invoiceLoggingEnabled,
        });
      } catch (error) {
        console.error("Error fetching invoice logging settings:", error);
        response
          .status(500)
          .json({ error: "Failed to fetch invoice logging settings" });
      }
    }
  );

  app.post(
    "/system/invoice-logging",
    [validatedRequest],
    async (request, response) => {
      try {
        const { invoice } = reqBody(request);

        await SystemSettings.updateSettings({ invoice });
        response.status(200).json({ success: true });
      } catch (error) {
        console.error("Error updating invoice logging settings:", error);
        response.status(500).json({
          success: false,
          error: "Failed to update invoice logging settings",
        });
      }
    }
  );

  app.get(
    "/system/force-invoice-logging",
    [validatedRequest],
    async (_, response) => {
      try {
        const isEnabled = await SystemSettings.isForcedInvoiceLoggingEnabled();
        response.status(200).json({
          isForcedinvoiceLogging: isEnabled,
        });
      } catch (error) {
        console.error("Error fetching invoice logging settings:", error);
        response
          .status(500)
          .json({ error: "Failed to fetch invoice logging settings" });
      }
    }
  );

  app.post(
    "/system/force-invoice-logging",
    [validatedRequest],
    async (request, response) => {
      try {
        const { isForcedinvoiceLogging } = reqBody(request);
        await SystemSettings.updateSettings({
          forced_invoice_logging: isForcedinvoiceLogging,
        });
        response.status(200).json({ success: true });
      } catch (error) {
        console.error("Error updating invoice logging settings:", error);
        response.status(500).json({
          success: false,
          error: "Failed to update invoice logging settings",
        });
      }
    }
  );

  app.get("/system/rexor-linkage", [validatedRequest], async (_, response) => {
    try {
      const isEnabled = await SystemSettings.isRexorLinkageEnabled();
      response.status(200).json({
        rexorLinkage: isEnabled,
      });
    } catch (error) {
      console.error("Error fetching rexor linkage settings:", error);
      response
        .status(500)
        .json({ error: "Failed to fetch rexor linkage settings" });
    }
  });

  app.post(
    "/system/rexor-linkage",
    [validatedRequest],
    async (request, response) => {
      try {
        const { rexorLinkage } = reqBody(request);

        await SystemSettings.updateSettings({ rexor_linkage: rexorLinkage });
        response.status(200).json({ success: true });
      } catch (error) {
        console.error("Error updating rexor linkage settings:", error);
        response.status(500).json({
          success: false,
          error: "Failed to update rexor linkage settings",
        });
      }
    }
  );

  app.post("/system/rexor/auth", async (request, response) => {
    try {
      const { username, password } = reqBody(request);

      const clientId =
        process.env.NODE_ENV === "development" ? "testfoyen" : "foyen";

      const authResponse = await axios({
        method: "post",
        url: "https://auth.rexor.se/v231/Token",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        data: new URLSearchParams({
          client_id: "foyen",
          grant_type: "password",
          username,
          password,
        }).toString(),
      });

      if (!authResponse.data?.access_token) {
        return response.status(401).json({ error: "Authentication failed" });
      }

      response.json({ access_token: authResponse.data.access_token });
    } catch (error) {
      console.error("Authentication error:", error.message);
      response.status(500).json({ error: "Failed to authenticate with Rexor" });
    }
  });

  app.post("/system/rexor/projects", async (request, response) => {
    try {
      const { projectData, access_token } = reqBody(request);
      const sessionUser = await userFromSession(request, response);

      if (
        !projectData.ResourceID &&
        sessionUser &&
        sessionUser.economy_system_id
      ) {
        projectData.ResourceID = sessionUser.economy_system_id;
      }

      const rexorResponse = await axios({
        method: "post",
        url: `${rexorApiBaseUrl}/Basic/Register`,
        headers: {
          Host: "api.rexor.se",
          Authorization: `Bearer ${access_token}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        data: JSON.stringify(projectData),
      });

      if (rexorResponse.status === 200) {
        response.status(200).json(rexorResponse.data);
      } else {
        throw new Error(`Unexpected status: ${rexorResponse.status}`);
      }
    } catch (error) {
      console.error("Error registering project:", error.message);
      if (error.response?.status === 401) {
        return response.status(401).json({
          error: "Unauthorized access to Rexor API",
          details: error.response?.data,
        });
      }
      response.status(500).json({ error: "Failed to fetch projects" });
    }
  });

  app.post(
    "/system/rexor/article-expense-transaction",
    async (request, response) => {
      try {
        const { access_token, transactionData } = reqBody(request);

        const transactionResponse = await axios({
          method: "post",
          url: `${rexorApiBaseUrl}/Project/ExpenseTransaction/Article`,
          headers: {
            Authorization: `Bearer ${access_token}`,
            "Content-Type": "application/json",
          },
          data: transactionData,
        });

        response.status(200).json(transactionResponse.data);
      } catch (error) {
        console.error("Error writing time transaction:", error.response?.data);
        response.status(500).json({ error: error.response?.data });
      }
    }
  );

  app.get(
    "/system/rexor/transaction-status/:uid",
    async (request, response) => {
      try {
        const { uid } = request.params;
        const authHeader = request.headers.authorization;

        if (!uid) {
          return response
            .status(400)
            .json({ error: "Transaction UID is required." });
        }

        if (!authHeader) {
          return response
            .status(401)
            .json({ error: "Authorization token is missing or invalid." });
        }

        const accessToken = authHeader.split(" ")[1];

        const rexorApiUrl = `${rexorApiBaseUrl}/Project/ExpenseTransaction/ReadSingle/${uid}`;

        const rexorResponse = await axios.get(rexorApiUrl, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            Accept: "application/json",
            "Content-Type": "application/json",
          },
        });

        if (rexorResponse.status === 200 && rexorResponse.data) {
          response.status(200).json(rexorResponse.data);
        }
      } catch (error) {
        console.error(
          "Error fetching Rexor transaction status:",
          error.response?.data || error.message
        );
        if (error.response?.status === 401) {
          return response.status(401).json({
            error: "Unauthorized access to Rexor API. Token might be invalid.",
            details: error.response?.data,
          });
        }
        response.status(500).json({
          error:
            "Internal server error while fetching Rexor transaction status.",
          details: error.response?.data || error.message,
        });
      }
    }
  );

  app.post(
    "/system/prompt-template",
    [validatedRequest],
    async (request, response) => {
      try {
        const { prompt_upgrade_template } = reqBody(request);
        await SystemSettings._updateSettings({
          prompt_upgrade_template,
        });
        response.status(200).json({ success: true });
      } catch (e) {
        console.error(e.message, e);
        response.status(500).json({ success: false, error: e.message });
      }
    }
  );

  app.get(
    "/system/prompt-template",
    [validatedRequest],
    async (_, response) => {
      try {
        const template = await SystemSettings.getPromptUpgradeTemplate();
        response.status(200).json({ template });
      } catch (error) {
        console.error("Error fetching prompt template:", error);
        response.status(500).json({ success: false, error: error.message });
      }
    }
  );

  app.get(
    "/system/custom-ai-settings",
    [validatedRequest],
    async (_, response) => {
      try {
        const settings = await SystemSettings.currentSettings();
        const customAiSettings = {};

        // Get all settings that end with _CUAI and custom model reference
        Object.keys(settings).forEach((key) => {
          if (
            key.endsWith("_CUAI") ||
            key.endsWith("_CUAI2") ||
            key.endsWith("_CUAI3") ||
            key.endsWith("_CUAI4") ||
            key.endsWith("_CUAI5") ||
            key.endsWith("_CUAI6") ||
            key === "custom_model_reference" ||
            key === "custom_model_reference_2" ||
            key === "custom_model_reference_3" ||
            key === "custom_model_reference_4" ||
            key === "custom_model_reference_5" ||
            key === "custom_model_reference_6" ||
            key === "custom_dynamic_context_window_percentage" ||
            key === "custom_dynamic_context_window_percentage_2" ||
            key === "custom_dynamic_context_window_percentage_3" ||
            key === "custom_dynamic_context_window_percentage_4" ||
            key === "custom_dynamic_context_window_percentage_5" ||
            key === "custom_dynamic_context_window_percentage_6"
          ) {
            customAiSettings[key] = settings[key];
          }
        });

        response.status(200).json({ settings: customAiSettings });
      } catch (error) {
        console.error("Error fetching custom AI settings:", error);
        response
          .status(500)
          .json({ error: "Failed to fetch custom AI settings" });
      }
    }
  );

  app.post(
    "/system/preferences",
    [validatedRequest],
    async (request, response) => {
      try {
        const updates = reqBody(request);
        await SystemSettings.updateSettings(updates);
        response.status(200).json({ success: true, error: null });
      } catch (e) {
        console.error(e.message, e);
        response.status(500).json({ success: false, error: e.message });
      }
    }
  );

  app.get(
    "/system/environment/:key",
    [validatedRequest, flexUserRoleValid([ROLES.admin])],
    async (request, response) => {
      try {
        const { key } = request.params;
        if (!key) {
          return response.status(400).json({ error: "No key provided" });
        }

        // Only return environment variables that are allowed to be accessed
        const value = process.env[key];

        // For security, don't return the actual value for API keys, just whether they exist
        if (
          key.toLowerCase().includes("api_key") ||
          key.toLowerCase().includes("apikey")
        ) {
          return response.status(200).json({ value: !!value });
        }

        response.status(200).json({ value: value || null });
      } catch (e) {
        console.error(e.message, e);
        response.sendStatus(500).end();
      }
    }
  );

  app.get("/system/prompt-examples", async (request, response) => {
    try {
      const examples = await PromptExamples.getExamples();
      return response.status(200).json(examples);
    } catch {
      return response
        .status(500)
        .json({ error: "Failed to get prompt examples" });
    }
  });

  app.post("/system/set-prompt-examples", async (request, response) => {
    try {
      const examples = reqBody(request);
      const {
        success,
        error,
        examples: savedExamples,
      } = await PromptExamples.save(examples);
      return response.status(success ? 200 : 500).json({
        success,
        examples: savedExamples,
        message: success ? "Prompt examples saved successfully." : error,
      });
    } catch {
      return response.status(500).json({
        success: false,
        message: "Error saving the prompt examples.",
      });
    }
  });

  app.get(
    "/system/context-window-info",
    [validatedRequest],
    async (request, response) => {
      try {
        // Check if a specific userId was passed in the query parameters
        const { userId } = queryParams(request);
        let user;

        if (userId) {
          // If userId is provided, fetch that specific user
          user = await User.get({ id: Number(userId) });
          console.log(
            `[Context Window Info] Using user from query parameter: ${userId}`
          );
        } else {
          // Otherwise get the user from the session
          user = await userFromSession(request, response);
          console.log(`[Context Window Info] Using user from session`);
        }

        const systemSettings = await SystemSettings.currentSettings();

        // Get the custom AI suffix if available
        const cuaiSuffix = user?.custom_ai_userselected
          ? user?.custom_ai_selected_engine || "_CUAI"
          : "";
        console.log(
          `[Context Window Info] User custom AI suffix: "${cuaiSuffix}"`
        );

        // Get dynamic context window percentage with the correct suffix
        let dynamic_context_window_percentage =
          await SystemSettings.getDynamicContextSettings(cuaiSuffix);

        // Log info about the dynamic context window that was retrieved
        console.log(
          `[Context Window Info] Using dynamic context window percentage: ${dynamic_context_window_percentage}% ${user?.custom_ai_userselected ? `(for custom AI suffix: ${cuaiSuffix})` : "(standard)"}`
        );

        const attachment_context_percentage =
          systemSettings?.attachment_context_percentage || 70;

        let chatProvider;
        let chatModel;

        // Check if user has custom AI selected
        if (user?.custom_ai_userselected) {
          // Get the user's selected engine suffix
          const cuaiSuffix = user?.custom_ai_selected_engine || "_CUAI";

          // Get system settings to find CUAI configuration
          const llmProvider = systemSettings?.[`LLMProvider${cuaiSuffix}`];

          if (llmProvider && llmProvider !== "none") {
            // Convert provider name to proper format and handle special cases
            const getModelPrefKey = (provider) => {
              const providerPrefix =
                provider.charAt(0).toUpperCase() + provider.slice(1);
              switch (provider.toLowerCase()) {
                case "gemini":
                  return `GeminiLLMModelPref${cuaiSuffix}`;
                case "ollama":
                  return `OllamaLLMModelPref${cuaiSuffix}`;
                case "native":
                  return `NativeLLMModelPref${cuaiSuffix}`;
                case "litellm":
                  return `LiteLLMModelPref${cuaiSuffix}`;
                case "openai":
                  return `OpenAiModelPref${cuaiSuffix}`;
                case "azureopenai":
                  return `AzureOpenAiModelPref${cuaiSuffix}`;
                case "genericopenai":
                  return `GenericOpenAiModelPref${cuaiSuffix}`;
                default:
                  return `${providerPrefix}ModelPref${cuaiSuffix}`;
              }
            };

            const modelPrefKey = getModelPrefKey(llmProvider);
            chatProvider = llmProvider;
            chatModel = systemSettings[modelPrefKey];
          }
        } else {
          // If no custom AI is selected, get the workspace settings from query params
          const { workspaceId } = queryParams(request);
          if (workspaceId) {
            const workspace = await Workspace.get({ id: Number(workspaceId) });
            if (workspace) {
              chatProvider = workspace.chatProvider || process.env.LLM_PROVIDER;
              chatModel = workspace.chatModel;
            } else {
              chatProvider = process.env.LLM_PROVIDER;
              chatModel = null;
            }
          } else {
            chatProvider = process.env.LLM_PROVIDER;
            chatModel = null;
          }
        }

        // Get the LLM connector to access the context window limit
        const LLMConnector = getLLMProvider({
          provider: chatProvider,
          model: chatModel,
          settings_suffix: user?.custom_ai_userselected
            ? user?.custom_ai_selected_engine || "_CUAI"
            : "",
        });

        // Get the prompt window limit
        const contextWindowLimit = LLMConnector.promptWindowLimit();

        // Get the max output limit if available
        const maxOutputLimit =
          typeof LLMConnector.maxOutputLimit === "function"
            ? LLMConnector.maxOutputLimit()
            : null;

        console.log(
          `[Context Window Display] Returning context window info: provider=${chatProvider}, model=${chatModel}, contextWindowLimit=${contextWindowLimit}, maxOutputLimit=${maxOutputLimit}`
        );

        response.status(200).json({
          dynamic_context_window_percentage,
          attachment_context_percentage,
          contextWindowLimit,
          maxOutputLimit,
          provider: chatProvider,
          model: chatModel,
          isCustomAi: !!user?.custom_ai_userselected,
        });
      } catch (e) {
        console.error("Error fetching context window info:", e.message, e);
        response.status(500).json({
          error: "Failed to fetch context window information",
          message: e.message,
        });
      }
    }
  );

  // Endpoint to get maximum output token information for a model
  app.get(
    "/system/model-output-window-info",
    [validatedRequest],
    async (request, response) => {
      try {
        // Get parameters from query
        const queryParams = request.query;
        let chatProvider, chatModel;

        // Check if specific provider and model were requested
        if (queryParams.provider && queryParams.model) {
          console.log(
            `[Model Output Info] Using specified provider and model from query: ${queryParams.provider} / ${queryParams.model}`
          );
          chatProvider = queryParams.provider;
          chatModel = queryParams.model;
        } else {
          console.log(
            "[Model Output Info] No provider/model specified, using defaults"
          );
          chatProvider = process.env.LLM_PROVIDER;
          chatModel = null;
        }

        // Get the maximum output tokens
        let maxOutputTokens;

        // First try to use the LLM provider's maxOutputLimit method if available
        try {
          const LLMConnector = getLLMProvider({
            provider: chatProvider,
            model: chatModel,
          });

          if (typeof LLMConnector.maxOutputLimit === "function") {
            maxOutputTokens = LLMConnector.maxOutputLimit();
            console.log(
              `[Model Output Info] Using maxOutputLimit method: provider=${chatProvider}, model=${chatModel}, maxOutputTokens=${maxOutputTokens}`
            );
          } else {
            throw new Error(
              "maxOutputLimit method not available, using MODEL_MAP fallback"
            );
          }
        } catch (error) {
          console.log(
            `[Model Output Info] Using MODEL_MAP fallback: ${error.message}`
          );

          // Import MODEL_MAP to access the maxOutput information
          const { MODEL_MAP } = require("../utils/AiProviders/modelMap");

          if (MODEL_MAP[chatProvider]) {
            if (
              chatModel &&
              MODEL_MAP[chatProvider].models &&
              MODEL_MAP[chatProvider].models[chatModel]
            ) {
              // Get maxOutput for the specific model
              maxOutputTokens =
                MODEL_MAP[chatProvider].models[chatModel].maxOutput;
            } else {
              // Use default maxOutput for the provider
              maxOutputTokens = MODEL_MAP[chatProvider].defaults.maxOutput;
            }
          } else {
            // If provider not found in MODEL_MAP, default to a reasonable value
            maxOutputTokens = 4096;
          }
        }

        console.log(
          `[Model Output Info] Returning model output info: provider=${chatProvider}, model=${chatModel}, maxOutputTokens=${maxOutputTokens}`
        );

        response.status(200).json({
          maxOutputTokens,
          provider: chatProvider,
          model: chatModel,
        });
      } catch (e) {
        console.error("Error fetching model output info:", e.message, e);
        response.status(500).json({
          error: "Failed to fetch model output information",
          message: e.message,
        });
      }
    }
  );

  app.get(
    "/system/context-window-display",
    [validatedRequest],
    async (request, response) => {
      try {
        // Get context window parameters
        const queryParams = request.query;
        let chatProvider, chatModel, settingsSuffix;

        // Check if specific provider and model were requested
        if (queryParams.provider && queryParams.model) {
          console.log(
            `[Context Window Display] Using specified provider and model from query: ${queryParams.provider} / ${queryParams.model}`
          );
          chatProvider = queryParams.provider;
          chatModel = queryParams.model;
          settingsSuffix = queryParams.settings_suffix || "";
        } else {
          console.log(
            "[Context Window Display] No provider/model specified, using defaults"
          );
          chatProvider = process.env.LLM_PROVIDER;
          chatModel = null;
        }

        // Get the LLM connector to access the context window limit
        const LLMConnector = getLLMProvider({
          provider: chatProvider,
          model: chatModel,
          settings_suffix: settingsSuffix || "",
        });

        // Get the prompt window limit
        const contextWindowLimit = LLMConnector.promptWindowLimit();

        console.log(
          `[Context Window Display] Returning context window info: provider=${chatProvider}, model=${chatModel}, contextWindowLimit=${contextWindowLimit}`
        );

        response.status(200).json({
          contextWindowLimit,
          provider: chatProvider,
          model: chatModel,
        });
      } catch (e) {
        console.error(
          "Error fetching context window display info:",
          e.message,
          e
        );
        response.status(500).json({
          error: "Failed to fetch context window information",
          message: e.message,
        });
      }
    }
  );

  app.get(
    "/system/cdb-documentation",
    [validatedRequest],
    async (_, response) => {
      try {
        const streamCDBDocPath = path.resolve(
          __dirname,
          "../../server/docs/streamCDB.md"
        );
        const legalDraftingFlowsDocPath = path.resolve(
          __dirname,
          "../../server/docs/legal_drafting_flows.md"
        );

        let baseDocumentation = "";
        let flowDocumentation = "";

        if (fs.existsSync(streamCDBDocPath)) {
          baseDocumentation = fs.readFileSync(streamCDBDocPath, "utf8");
        } else {
          baseDocumentation =
            "# Case Document Builder (CDB) Overview\n\nThis document provides an overview of the CDB functionality.\n";
          console.warn(
            "Warning: server/docs/streamCDB.md not found. Serving minimal base documentation for overview."
          );
        }

        if (fs.existsSync(legalDraftingFlowsDocPath)) {
          flowDocumentation = fs.readFileSync(
            legalDraftingFlowsDocPath,
            "utf8"
          );
        } else {
          console.warn(
            "Warning: server/docs/legal_drafting_flows.md not found. Flow specific documentation will be missing."
          );
        }

        // Combine the two documentation files. streamCDB.md is the primary, flows can be appended or prepended.
        // Let's append flowDocumentation for now, within a clearly marked section if it exists.
        let combinedDocumentation = baseDocumentation;
        if (flowDocumentation) {
          combinedDocumentation +=
            "\n\n---\n\n### Detailed Legal Drafting Flows\n\n" + // Changed from # to ###
            flowDocumentation;
        }

        let defaultPromptsString =
          "\n\n---\n\n### Default CDB Prompt Templates Used by Flows\n\nBelow is a list of default prompts used by the Case Document Builder flows (these can be overridden by system settings):\n"; // Changed from ## to ###
        for (const [promptName, promptObject] of Object.entries(
          LEGAL_DRAFTING_PROMPTS
        )) {
          defaultPromptsString += `\\n### ${promptName}\\n`;
          if (promptObject.SYSTEM_PROMPT) {
            defaultPromptsString += `**SYSTEM_PROMPT:**\\n\`\`\`\n${promptObject.SYSTEM_PROMPT}\n\`\`\`\\n`;
          }
          if (promptObject.USER_PROMPT) {
            defaultPromptsString += `**USER_PROMPT:**\\n\`\`\`\n${promptObject.USER_PROMPT}\n\`\`\`\\n`;
          }
          if (promptObject.PROMPT_TEMPLATE) {
            // For prompts like DEFAULT_MEMO_CREATION
            defaultPromptsString += `**PROMPT_TEMPLATE:**\\n\`\`\`\n${promptObject.PROMPT_TEMPLATE}\n\`\`\`\\n`;
          }
        }

        const fullDocumentation = combinedDocumentation + defaultPromptsString;
        response
          .status(200)
          .json({ success: true, documentation: fullDocumentation });
      } catch (error) {
        console.error("Error fetching CDB documentation:", error);
        response.status(500).json({
          success: false,
          error: "Failed to fetch CDB documentation",
          details: error.message,
        });
      }
    }
  );

  // Get current user information
  app.get("/system/me", [validatedRequest], async (request, response) => {
    try {
      const user = await userFromSession(request, response);
      if (!user) {
        return response.status(401).json({ error: "Unauthorized" });
      }
      response.status(200).json(user);
    } catch (e) {
      console.error("Error fetching user data:", e);
      response.status(500).json({ error: "Internal server error" });
    }
  });

  // Request Legal Assistance Settings
  app.post(
    "/system/request-legal-assistance",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        const updates = reqBody(request);
        await SystemSettings.updateSettings({
          request_legal_assistance_enabled: updates.enabled ? "true" : "false",
          request_legal_assistance_law_firm_name: updates.lawFirmName || "",
          request_legal_assistance_email: updates.email || "",
        });
        response.status(200).json({ success: true, error: null });
      } catch (e) {
        console.error(e);
        response.status(500).json({ success: false, error: e.message });
      }
    }
  );

  app.get(
    "/system/request-legal-assistance",
    [validatedRequest],
    async (_, response) => {
      try {
        const settings =
          await SystemSettings.getRequestLegalAssistanceSettings();
        response.status(200).json(settings);
      } catch (e) {
        console.error(e);
        response.status(500).json({ success: false, error: e.message });
      }
    }
  );

  // Endpoint to purge temporary Case Document Builder files
  // This can be called when a CDB run is completed or aborted to clean up
  // any leftover files in storage/document-builder. It returns the number of
  // files removed.
  app.post(
    "/system/purge-document-builder",
    [
      validatedRequest,
      flexUserRoleValid([
        ROLES.admin,
        ROLES.manager,
        ROLES.superuser,
        ROLES.default,
      ]),
    ],
    async (_request, response) => {
      try {
        const { purgeDocumentBuilder } = require("../utils/files");
        const removedFiles = purgeDocumentBuilder();

        await EventLogs.logEvent(
          "purged_document_builder",
          { removedFiles },
          response?.locals?.user?.id
        );

        response.status(200).json({ success: true, removedFiles });
      } catch (error) {
        console.error("Error purging document-builder files:", error);
        response.status(500).json({ success: false, error: error.message });
      }
    }
  );
  // Endpoint to delete old DOCX-edit session folders
  app.post(
    "/system/delete-old-docx-sessions",
    [validatedRequest, flexUserRoleValid([ROLES.manager, ROLES.admin])],
    async (request, response) => {
      try {
        const { timeframe, value } = reqBody(request);
        if (
          !timeframe ||
          !value ||
          !["days", "weeks", "months"].includes(timeframe) ||
          !Number.isInteger(Number(value)) ||
          Number(value) <= 0
        ) {
          return response
            .status(400)
            .json({ success: false, error: "Invalid parameters" });
        }

        let retentionDays;
        if (timeframe === "days") {
          retentionDays = Number(value);
        } else if (timeframe === "weeks") {
          retentionDays = Number(value) * 7;
        } else {
          // months
          // Approximate months as 30 days for simplicity in retention calculation
          retentionDays = Number(value) * 30;
        }

        const { success, deletedCount, error } =
          await cleanOldDocxSessionFiles(retentionDays);

        if (!success) {
          throw new Error(error || "Failed to clean old DOCX session files.");
        }

        // Update system_settings counter
        const current = await SystemSettings.get({
          label: "docx_deletion_count",
        });
        const currentCount = current ? parseInt(current.value, 10) : 0;
        const totalDeleted = currentCount + deletedCount;
        await SystemSettings._updateSettings({
          docx_deletion_count: totalDeleted.toString(),
        });

        // Log event
        await EventLogs.logEvent(
          "deleted_old_docx_sessions",
          { timeframe, value, retentionDays, deletedCount, totalDeleted },
          response.locals.user?.id
        );
        return response.json({ success: true, deletedCount, totalDeleted });
      } catch (e) {
        console.error("Error deleting old DOCX sessions:", e);
        return response.status(500).json({ success: false, error: e.message });
      }
    }
  );

  app.post(
    "/system/upload-docx-template",
    [
      validatedRequest,
      flexUserRoleValid([ROLES.admin, ROLES.manager]),
      handleAssetUpload,
    ],
    async (request, response) => {
      if (!request?.file || !request?.file.originalname) {
        return response
          .status(400)
          .json({ message: "No template file provided." });
      }

      // Validate file type (.docx or .dotx)
      const fileExt = path.extname(request.file.originalname).toLowerCase();
      const allowedExts = [".docx"];
      if (!allowedExts.includes(fileExt)) {
        return response
          .status(400)
          .json({ message: "Only DOCX files are supported." });
      }

      try {
        // Ensure unique filename (uuid + ext)
        const newFilename = `${v4()}${fileExt}`;
        const storagePath =
          process.env.NODE_ENV === "development"
            ? path.resolve(__dirname, "../storage/assets")
            : path.resolve(process.env.STORAGE_DIR, "assets");

        const targetPath = path.join(storagePath, newFilename);
        fs.copyFileSync(request.file.path, targetPath);
        // Remove temp upload file
        if (fs.existsSync(request.file.path)) fs.unlinkSync(request.file.path);

        // Remove previous template file if exists
        const prevFilename = await SystemSettings.get({
          label: "docx_system_template",
        });
        if (prevFilename?.value) {
          const prevPath = path.join(storagePath, prevFilename.value);
          if (fs.existsSync(prevPath)) fs.unlinkSync(prevPath);
        }

        const { success, error } = await SystemSettings._updateSettings({
          docx_system_template: newFilename,
          docx_system_template_display: request.file.originalname,
        });

        return response.status(success ? 200 : 500).json({
          success,
          message: success
            ? "Template uploaded successfully."
            : error || "Failed to save template filename.",
          filename: newFilename,
          displayFilename: request.file.originalname,
        });
      } catch (error) {
        console.error("Error uploading DOCX template:", error);
        return response.status(500).json({
          success: false,
          message: "Error uploading template file.",
        });
      }
    }
  );

  app.delete(
    "/system/remove-docx-template",
    [validatedRequest, flexUserRoleValid([ROLES.admin, ROLES.manager])],
    async (request, response) => {
      try {
        const filenameSetting = await SystemSettings.get({
          label: "docx_system_template",
        });
        if (!filenameSetting?.value) {
          return response.status(404).json({ message: "No template stored." });
        }

        const storagePath =
          process.env.NODE_ENV === "development"
            ? path.resolve(__dirname, "../storage/assets")
            : path.resolve(process.env.STORAGE_DIR, "assets");
        const filePath = path.join(storagePath, filenameSetting.value);
        if (fs.existsSync(filePath)) fs.unlinkSync(filePath);

        const { success, error } = await SystemSettings._updateSettings({
          docx_system_template: null,
          docx_system_template_display: null,
        });

        return response.status(success ? 200 : 500).json({
          success,
          message: success ? "Template removed." : error || "Failed.",
        });
      } catch (error) {
        console.error("Error removing system template:", error);
        return response.status(500).json({ message: "Internal server error" });
      }
    }
  );

  app.get("/system/docx-template-info", async (_, response) => {
    try {
      const [filenameSetting, displaySetting] = await Promise.all([
        SystemSettings.get({ label: "docx_system_template" }),
        SystemSettings.get({ label: "docx_system_template_display" }),
      ]);
      return response.status(200).json({
        filename: filenameSetting?.value || null,
        displayFilename: displaySetting?.value || null,
        hasTemplate: Boolean(filenameSetting?.value),
      });
    } catch (error) {
      console.error("Error fetching template info:", error);
      return response.status(500).json({ message: "Internal server error" });
    }
  });

  // --------------------------------------------------------
  // Public System Preferences
  // --------------------------------------------------------
  // This endpoint allows any authenticated user (any role) to fetch
  // *public* system settings. Use the `labels` query parameter to
  // request specific settings, e.g. `/system/preferences?labels=university_mode`.
  // If `labels` is omitted, all public settings are returned.
  app.get(
    "/system/preferences",
    [validatedRequest],
    async (request, response) => {
      try {
        // If labels are provided, split on comma, otherwise use the full public list
        const labels = request.query.labels
          ? request.query.labels.split(",").map((l) => l.trim())
          : SystemSettings.publicFields;

        const settings = {};

        for (const label of labels) {
          // Only expose settings that are explicitly marked as public
          if (!SystemSettings.publicFields.includes(label)) continue;

          const record = await SystemSettings.get({ label });
          settings[label] = record?.value ?? null;
        }

        response.status(200).json({ settings });
      } catch (e) {
        console.error("Error fetching public system preferences", e);
        response
          .status(500)
          .json({ error: "Failed to fetch system preferences" });
      }
    }
  );

  // Document Builder Prompts endpoints (no middleware for test compatibility)
  app.get(
    "/system/document-builder-prompts",
    getDocumentBuilderPromptsRouteHandler
  );

  app.post(
    "/system/update-document-builder-prompts",
    updateDocumentBuilderPromptsHandler
  );

  // Get organization users
  app.get(
    "/system/organization-users",
    [validatedRequest],
    async (request, response) => {
      try {
        const user = await userFromSession(request, response);
        if (!user) {
          return response.status(401).json({ error: "Unauthorized" });
        }

        if (!user.organizationId) {
          return response.status(200).json({ users: [] });
        }

        const { users } = await User.where({
          organizationId: user.organizationId,
        });

        response.status(200).json({
          users: users.map((u) => ({
            id: u.id,
            username: u.username,
            role: u.role,
          })),
        });
      } catch (e) {
        console.error("Error fetching organization users:", e);
        response.status(500).json({ error: "Internal server error" });
      }
    }
  );
}

module.exports = {
  systemEndpoints,
  getDocumentBuilderPromptsHandler, // Export for testing
  updateDocumentBuilderPromptsHandler, // Export for testing
};
