{"name": "ist-legal-server", "version": "0.2.0", "description": "Server endpoints to process or create content for chatting", "main": "index.js", "author": "IST Legal", "license": "Copyright", "private": false, "engines": {"node": ">=18.12.1"}, "scripts": {"dev": "cross-env NODE_ENV=development nodemon --ignore documents --ignore vector-cache --ignore storage --ignore swagger --trace-warnings index.js", "start": "NODE_ENV=production node index.js", "lint": "npx prettier --ignore-path ../.prettierignore --check ./endpoints ./models ./utils index.js && npx eslint ./endpoints ./models ./utils index.js package.json --quiet", "swagger": "node ./swagger/init.js", "test": "OPENAI_API_KEY=dummy_key jest"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.32.1", "@aws-sdk/client-ses": "^3.799.0", "@azure/openai": "1.0.0-beta.10", "@datastax/astra-db-ts": "^1.5.0", "@google/genai": "^0.9.0", "@ladjs/graceful": "^3.2.2", "@lancedb/lancedb": "^0.15.0", "@langchain/anthropic": "0.1.16", "@langchain/aws": "^0.0.9", "@langchain/community": "^0.3.22", "@langchain/core": "^0.3.18", "@langchain/openai": "0.0.28", "@langchain/textsplitters": "0.0.0", "@mintplex-labs/bree": "^9.2.5", "@mintplex-labs/express-ws": "^5.0.7", "@modelcontextprotocol/sdk": "^1.11.0", "@pinecone-database/pinecone": "^2.0.1", "@prisma/client": "^5.18.0", "@qdrant/js-client-rest": "^1.9.0", "@xenova/transformers": "^2.14.0", "@zilliz/milvus2-sdk-node": "^2.3.5", "apache-arrow": "19.0.0", "axios": "^1.9.0", "bcrypt": "^5.1.0", "body-parser": "^1.20.2", "chalk": "^4", "check-disk-space": "^3.4.0", "chromadb": "^1.5.2", "cohere-ai": "^7.9.5", "cors": "^2.8.5", "croner": "^9.0.0", "cross-env": "^7.0.3", "diff": "^7.0.0", "docx": "^9.3.0", "docxtemplater": "^3.43.0", "dotenv": "^16.0.3", "elevenlabs": "^0.5.0", "express": "^4.18.2", "extract-json-from-string": "^1.0.1", "graphql": "^16.7.1", "joi": "^17.11.0", "joi-password-complexity": "^5.2.0", "js-tiktoken": "^1.0.7", "jsonrepair": "^3.7.0", "jsonwebtoken": "^9.0.0", "langchain": "^0.3.9", "mammoth": "^1.9.0", "mime": "^3.0.0", "moment": "^2.29.4", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.8", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "node-llama-cpp": "^2.8.0", "officegen": "^0.6.5", "ollama": "^0.5.10", "openai": "4.95.1", "pg": "^8.11.5", "pinecone-client": "^1.1.0", "pizzip": "^3.0.6", "pluralize": "^8.0.0", "posthog-node": "^3.1.1", "prisma": "^5.18.0", "remark-docx": "^0.1.6", "remark-parse": "^11.0.0", "slugify": "^1.6.6", "swagger-autogen": "^2.23.5", "swagger-ui-express": "^5.0.0", "truncate": "^3.0.0", "unified": "^11.0.5", "url-pattern": "^1.0.3", "uuid": "^9.0.0", "uuid-apikey": "^1.5.3", "vectordb": "0.4.11", "weaviate-ts-client": "^1.4.0", "winston": "^3.13.0"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/preset-env": "^7.26.9", "@inquirer/prompts": "^4.3.1", "babel-jest": "^29.7.0", "eslint": "^9.25.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-ft-flow": "^3.0.0", "eslint-plugin-package-json": "^0.31.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "eslint-plugin-unused-imports": "^4.1.4", "flow-bin": "^0.217.0", "flow-remove-types": "^2.217.1", "globals": "^13.21.0", "hermes-eslint": "^0.15.0", "jest": "^29.7.0", "jsonc-eslint-parser": "^2.4.0", "node-html-markdown": "^1.3.0", "nodemon": "^3.1.9", "prettier": "^3.0.3", "supertest": "^7.0.0"}}