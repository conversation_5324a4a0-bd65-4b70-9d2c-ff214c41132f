/* eslint-env jest */
/* eslint-disable no-undef */
const path = require("path");

// Mock environment variables
process.env.NODE_ENV = "test";

// Mock node-fetch
jest.mock("node-fetch", () => jest.fn());

process.env.STORAGE_DIR = path.join(__dirname, "../storage");

// Mock global objects and functions used in tests
jest.spyOn(console, "error").mockImplementation();
jest.spyOn(console, "warn").mockImplementation();
jest.spyOn(console, "log").mockImplementation();

// Mock DocumentManager
jest.mock("../utils/DocumentManager", () => ({
  DocumentManager: jest.fn().mockImplementation(() => ({
    pinnedDocs: jest.fn().mockResolvedValue([]),
    pdrDocs: jest.fn().mockResolvedValue([]),
  })),
}));

// Mock VectorDb
global.VectorDb = {
  hasNamespace: jest.fn().mockResolvedValue(true),
  namespaceCount: jest.fn().mockResolvedValue(100),
  performSimilaritySearch: jest.fn(),
  getAdjacentVectors: jest.fn(),
};

// Mock TokenManager
jest.mock("../utils/helpers/tiktoken", () => ({
  TokenManager: jest.fn().mockImplementation(() => ({
    countFromString: jest.fn((str) => Math.ceil(str.length / 4)),
    statsFrom: jest.fn(() => ({ tokens: 100 })),
    tokensFromString: jest.fn((str) => {
      // Mock tokenization by splitting on words and creating a rough token array
      const words = str.split(/\s+/).filter((word) => word.length > 0);
      const tokens = [];
      for (let i = 0; i < words.length; i++) {
        // Each word gets roughly 1-2 tokens
        tokens.push(i * 2);
        if (words[i].length > 6) tokens.push(i * 2 + 1);
      }
      return tokens;
    }),
    bytesFromTokens: jest.fn((tokens) => {
      // Mock detokenization - just create dummy text based on token count
      return "word ".repeat(Math.ceil(tokens.length / 2)).trim();
    }),
  })),
}));

// Mock SystemSettings
jest.mock("../models/systemSettings", () => ({
  SystemSettings: {
    getPdrSettings: jest.fn().mockResolvedValue({
      pdrToken: 100000,
      responseToken: 1000,
      adjacentVector: 2,
      keepPdrVectors: true,
    }),
  },
}));

// Mock WorkspaceChats
jest.mock("../models/workspaceChats", () => ({
  WorkspaceChats: {
    new: jest.fn().mockResolvedValue({}),
    where: jest.fn().mockResolvedValue([]),
  },
}));
