const legalDrafting = require("../../../utils/chats/prompts/legalDrafting");

describe("Legal Drafting Prompts", () => {
  // Test the main exported constants
  const promptKeys = [
    "DEFAULT_DOCUMENT_SUMMARY",
    "DEFAULT_DOCUMENT_RELEVANCE",
    "DEFAULT_SECTION_DRAFTING",
    "DEFAULT_SECTION_LEGAL_ISSUES",
    "DEFAULT_MEMO_CREATION",
    "DEFAULT_SECTION_INDEX",
    "DEFAULT_SELECT_MAIN_DOCUMENT",
    "DEFAULT_SECTION_LIST_FROM_MAIN",
    "DEFAULT_SECTION_LIST_FROM_SUMMARIES",
  ];

  promptKeys.forEach((key) => {
    test(`should have ${key} defined with correct structure`, () => {
      const prompt = legalDrafting[key];
      expect(prompt).toBeDefined();
      if (key === "DEFAULT_MEMO_CREATION") {
        expect(prompt).toHaveProperty("PROMPT_TEMPLATE");
        expect(typeof prompt.PROMPT_TEMPLATE).toBe("string");
      } else if (key === "DEFAULT_SECTION_INDEX") {
        expect(prompt).toHaveProperty("SYSTEM_PROMPT");
        expect(typeof prompt.SYSTEM_PROMPT).toBe("string");
        // This one does not have a USER_PROMPT
      } else {
        expect(prompt).toHaveProperty("SYSTEM_PROMPT");
        expect(typeof prompt.SYSTEM_PROMPT).toBe("string");
        expect(prompt).toHaveProperty("USER_PROMPT");
        expect(typeof prompt.USER_PROMPT).toBe("string");
      }
    });
  });

  // Test the exportedLegalPrompts array
  describe("exportedLegalPrompts", () => {
    const { exportedLegalPrompts } = legalDrafting;

    test("should be an array and not empty", () => {
      expect(Array.isArray(exportedLegalPrompts)).toBe(true);
      expect(exportedLegalPrompts.length).toBeGreaterThan(0);
    });

    test("each item should have the correct properties and types", () => {
      exportedLegalPrompts.forEach((item) => {
        expect(item).toHaveProperty("promptKey");
        expect(typeof item.promptKey).toBe("string");
        expect(promptKeys).toContain(item.promptKey); // Ensure promptKey refers to an existing default prompt

        expect(item).toHaveProperty("promptField");
        expect(typeof item.promptField).toBe("string");

        const mainPromptObject = legalDrafting[item.promptKey];
        if (
          ["SYSTEM_PROMPT", "USER_PROMPT", "PROMPT_TEMPLATE"].includes(
            item.promptField
          )
        ) {
          // Check that the promptField exists on the corresponding default prompt object
          expect(mainPromptObject).toHaveProperty(item.promptField);
          // Verify defaultContent matches the actual content from the main prompt object
          expect(item.defaultContent).toBe(mainPromptObject[item.promptField]);

          // These fields are only expected for actual prompt entries, not group titles/descriptions
          expect(item).toHaveProperty("systemSettingName");
          expect(typeof item.systemSettingName).toBe("string");
          expect(item.systemSettingName.startsWith("cdb_")).toBe(true);
          expect(
            item.systemSettingName.endsWith(item.promptField.toLowerCase())
          ).toBe(true);

          expect(item).toHaveProperty("description");
          expect(typeof item.description).toBe("string");
        } else if (
          ["GROUP_TITLE", "GROUP_DESCRIPTION"].includes(item.promptField)
        ) {
          // For group titles and descriptions, systemSettingName and description are not expected
          expect(item.systemSettingName).toBeUndefined();
          expect(item.description).toBeUndefined();
        }

        expect(item).toHaveProperty("label");
        expect(typeof item.label).toBe("string");

        expect(item).toHaveProperty("defaultContent");
        expect(typeof item.defaultContent).toBe("string");
      });
    });

    test("systemSettingName should be unique for each prompt field that has one", () => {
      const itemsWithSettings = exportedLegalPrompts.filter(
        (item) => item.systemSettingName
      );
      const settingNames = itemsWithSettings.map(
        (item) => item.systemSettingName
      );
      const uniqueSettingNames = new Set(settingNames);
      expect(uniqueSettingNames.size).toBe(settingNames.length);
    });

    test("generateSystemSettingPKeyForLegalDrafting helper logic consistency", () => {
      // This implicitly tests the helper generateSystemSettingPKeyForLegalDrafting
      // by checking the output structure it produces for systemSettingName.
      exportedLegalPrompts.forEach((item) => {
        if (!item.systemSettingName) return; // Skip items without a systemSettingName

        const expectedBaseName = item.promptKey
          .replace(/^DEFAULT_/, "")
          .toLowerCase();
        const expectedFieldKey = item.promptField.toLowerCase();
        const expectedPKey = `cdb_${expectedBaseName}_${expectedFieldKey}`;
        expect(item.systemSettingName).toBe(expectedPKey);
      });
    });
  });
});
