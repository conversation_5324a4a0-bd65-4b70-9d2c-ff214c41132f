const request = require("supertest");
const express = require("express");
const fs = require("fs");

// Mock necessary modules
const mockGetLLMProvider = jest.fn();
const mockGenerateLegalMemo = jest.fn();
// Mock userFromSession
jest.mock("../../utils/http", () => ({
  ...jest.requireActual("../../utils/http"), // Keep other exports like reqBody
  userFromSession: jest.fn(),
}));
// Mock the actual Prisma client export to prevent engine init issues in tests
jest.mock("../../utils/prisma", () => ({
  // Provide mocks for models if they are accessed directly via prisma.modelName
  // This helps if models aren't 100% encapsulated by their own model files/mocks
  // when prisma instance is passed around or used directly.
  workspace: {
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
    // Add other common Prisma model methods as needed
  },
  user: {
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    // etc.
  },
  system_settings: {
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    // etc.
  },
  workspace_chats: {
    create: jest.fn(),
    findUnique: jest.fn(),
    // etc.
  },
  // Add other models that might be accessed via the prisma instance
  $transaction: jest.fn(async (promises) => {
    // If promises is a function (interactive transaction)
    if (typeof promises === "function") {
      return promises(jest.fn()); // Pass a mock prisma client to the transaction function
    }
    // If promises is an array of operations
    return Promise.all(promises);
  }),
  $connect: jest.fn(),
  $disconnect: jest.fn(),
}));
jest.mock("../../utils/helpers", () => ({
  getLLMProvider: (...args) => mockGetLLMProvider(...args),
  // Add other helpers if needed by the endpoint or streamChatWithWorkspace directly
}));
jest.mock("../../models/workspace", () => ({
  Workspace: {
    get: jest.fn(),
    // Add other Workspace methods if needed
  },
}));
jest.mock("../../models/user", () => ({
  User: {
    get: jest.fn(),
  },
}));
jest.mock("../../models/systemSettings", () => ({
  SystemSettings: {
    get: jest.fn().mockResolvedValue({ value: null }),
    // Add other SystemSettings methods if needed
    getDDSettings: jest.fn().mockResolvedValue({ ddMemoEnabled: true }),
    isDocumentDraftingLinking: jest.fn().mockResolvedValue(true),
  },
}));
jest.mock("../../models/workspaceChats", () => ({
  WorkspaceChats: {
    new: jest.fn().mockResolvedValue({
      chat: { id: "test-chat-id", get: jest.fn().mockReturnThis() },
    }),
    get: jest.fn().mockResolvedValue(null),
    update: jest.fn(),
    markHistoryExecutionAsComplete: jest.fn(),
  },
}));
// Corrected mock for the entire '../../utils/files' module
jest.mock("../../utils/files", () => ({
  purgeDocumentBuilder: jest.fn(),
  getUserDocumentPathName: jest.fn().mockReturnValue("testuser/test-workspace"), // Example return
  // Add other functions from '../../utils/files/index.js' if they are called and need mocking
  // For example, if findDocumentInDocuments, cachedVectorInformation, etc., are indirectly used.
  // For now, keeping it minimal to fix the immediate error.
  findDocumentInDocuments: jest.fn().mockResolvedValue(null),
  cachedVectorInformation: jest
    .fn()
    .mockResolvedValue({ exists: false, chunks: [] }),
  storeVectorResult: jest.fn().mockResolvedValue(null),
  fileData: jest.fn().mockResolvedValue(null),
  normalizePath: jest.fn((p) => p), // Simple pass-through for normalizePath
  isWithin: jest.fn().mockReturnValue(true),
  documentsPath: "mock/documents/path",
  hasVectorCachedFiles: jest.fn().mockReturnValue(false),
  deletePDFFile: jest.fn(),
}));
// Mock fs interactions more granularly for specific path checks
jest.mock("fs", () => ({
  existsSync: jest.fn(),
  mkdirSync: jest.fn(),
  readdirSync: jest.fn(),
  readFileSync: jest.fn(),
  writeFileSync: jest.fn(),
  statSync: jest.fn(() => ({
    isFile: () => false,
    isDirectory: () => true,
    ctimeMs: Date.now(),
  })),
  rmSync: jest.fn(),
  // Add any other fs functions that might be called by the code under test (e.g., constants)
}));
// Mock generateLegalMemo specifically for finer control if needed
jest.mock("../../utils/helpers/legalMemo", () => ({
  generateLegalMemo: (...args) => mockGenerateLegalMemo(...args),
}));
// Import the endpoint router
const { chatEndpoints } = require("../../endpoints/chat"); // Assuming chatEndpoints is the exported router function
const app = express();
app.use(express.json());
// Middleware normally added in server.js (like user extraction) might need to be mocked or added.
// For simplicity, we'll mock req.user directly in tests for now.
chatEndpoints(app); // Mount the chat router
// Mock middleware used by the chat endpoints
jest.mock("../../utils/middleware/validatedRequest", () => ({
  validatedRequest: jest.fn((req, res, next) => next()),
}));

jest.mock("../../utils/middleware/multiUserProtected", () => ({
  flexUserRoleValid: jest.fn(() => (req, res, next) => {
    // This mock assumes req.user will be set by the test setup if auth is needed.
    // For basic testing, allow all if user is present.
    if (req.user) return next();
    // If req.user is not set by a test, it simulates an unauthenticated state for protected routes.
    // However, the actual /workspace/:slug/chat endpoint uses ROLES.all,
    // which might have specific logic in the real middleware if user is null.
    // For these tests, we ensure req.user is set in test cases.
    return next(); // Defaulting to next() to allow endpoint logic to handle user presence or absence if ROLES.all means public access.
  }),
  ROLES: {
    all: "<all>",
    admin: "admin",
    manager: "manager",
    default: "default",
    // Add other roles if they become relevant for specific test cases
  },
}));
jest.mock("../../utils/middleware/validWorkspace", () => ({
  validWorkspaceSlug: jest.fn(async (req, res, next) => {
    const { Workspace } = require("../../models/workspace"); // Use the mocked Workspace
    try {
      const workspace = await Workspace.get({ slug: req.params.slug });
      if (workspace) {
        res.locals.workspace = workspace;
        // Simulate chatServiceId if it's normally set by middleware and used by endpoint
        res.locals.chatId =
          req.query.chatId ||
          req.body.chatId ||
          `test-chat-id-mw-${Date.now()}`;
        return next();
      } else {
        return res
          .status(404)
          .json({ error: "Test Middleware: Workspace not found" });
      }
    } catch (e) {
      return res.status(500).json({
        error: "Test Middleware: Error fetching workspace",
        details: e.message,
      });
    }
  }),
  validWorkspaceAndThreadSlug: jest.fn(async (req, res, next) => {
    const { Workspace } = require("../../models/workspace");
    require("../../models/workspaceThread"); // Assuming this model exists and might be mocked
    try {
      const workspace = await Workspace.get({ slug: req.params.slug });
      if (!workspace)
        return res
          .status(404)
          .json({ error: "Test Middleware: Workspace not found" });
      res.locals.workspace = workspace;
      // Simplified thread mock for now - in a real scenario, this would also hit a mocked Thread.get
      const thread = {
        id: 1,
        slug: req.params.threadSlug,
        workspace_id: workspace.id,
      };
      res.locals.thread = thread;
      res.locals.chatId =
        req.query.chatId || req.body.chatId || `test-chat-id-mw-${Date.now()}`;
      return next();
    } catch (e) {
      return res.status(500).json({
        error: "Test Middleware: Error fetching workspace/thread",
        details: e.message,
      });
    }
  }),
}));
// Test Suite
describe("Document Drafting API Endpoint (/workspace/:slug/chat?cdb=true)", () => {
  let mockLLMInstance;
  beforeEach(() => {
    jest.clearAllMocks();
    // Get a reference to the auto-mocked fs module to configure its functions
    const fs = require("fs");
    mockLLMInstance = {
      compressMessages: jest.fn(async (messages) => messages),
      getChatCompletion: jest.fn(),
      defaultTemp: 0.7,
      model: "mock-model-api",
      metrics: { lastCompletionTokens: 0 },
      promptWindowLimit: jest.fn(() => 128000),
    };
    mockGetLLMProvider.mockReturnValue(mockLLMInstance);
    const defaultUser = { id: 1, username: "testuser", role: "admin" };
    const defaultWorkspace = {
      id: 1,
      slug: "test-workspace",
      type: "document-drafting",
      openAiTemp: 0.7,
      user_id: defaultUser.id, // Assuming flows might need user_id from workspace
    };
    require("../../models/workspace").Workspace.get.mockResolvedValue(
      defaultWorkspace
    );
    require("../../models/user").User.get.mockResolvedValue(defaultUser);
    // Set default mock for getUserDocumentPathName using the default user and workspace
    const filesMock = require("../../utils/files");
    filesMock.getUserDocumentPathName.mockReturnValue(
      `${defaultUser.username}/${defaultWorkspace.slug}`
    );
    filesMock.purgeDocumentBuilder.mockImplementation(() => {}); // Ensure this is also reset/set here
    // Setup default fs mocks
    fs.existsSync.mockReturnValue(true); // Assume paths exist unless specified
    fs.readdirSync.mockReturnValue([]); // Default to empty directory
    fs.readFileSync.mockReturnValue(
      JSON.stringify({ pageContent: "dummy content" })
    );
    fs.mkdirSync.mockImplementation(() => {}); // Mock mkdirSync
    fs.writeFileSync.mockImplementation(() => {}); // Mock writeFileSync
    // Mock purgeDocumentBuilder to do nothing (it's now part of the '../../utils/files' mock)
    // require("../../utils/files").purgeDocumentBuilder.mockImplementation(
    //  () => {}
    // );
    // Accessing the mock correctly:
    // require("../../utils/files").purgeDocumentBuilder.mockImplementation(() => {}); // Already set above
    // require("../../utils/files").getUserDocumentPathName.mockReturnValue(`${user.username}/${workspaceSlug}`); // This line caused error, now handled above
    // Default user for userFromSession mock
    const { userFromSession } = require("../../utils/http");
    userFromSession.mockResolvedValue(defaultUser); // Make defaultUser accessible here or define it earlier
  });
  test("Main Document Flow: successfully drafts with a main document", async () => {
    const user = { id: 1, role: "admin", username: "testuser" };
    const workspaceSlug = "test-workspace";
    const chatId = "api-main-flow-chat-id";
    const mainDocName = "mainDocument.pdf";
    const slugModule = "document-drafting";
    // Set user for this test
    require("../../utils/http").userFromSession.mockResolvedValueOnce(user);
    require("../../models/workspace").Workspace.get.mockResolvedValueOnce({
      id: 1,
      slug: workspaceSlug,
      type: "document-drafting",
      openAiTemp: 0.7,
      user_id: user.id,
    });

    // Reordered LLM mocks based on mainDoc.js flow (7 steps)
    // Step 1: Section List from Main Document
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: JSON.stringify([
        { index_number: 1, title: "Section 1 from Main" },
      ]),
      metrics: { lastCompletionTokens: 20 },
    });
    // Step 2: Process Documents (Descriptions & Relevance)
    // 2a. Describe mainDocument.pdf
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: "Description for mainDocument.pdf",
      metrics: { lastCompletionTokens: 10 },
    });
    // 2b. Describe otherDoc.pdf
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: "Description for otherDoc.pdf",
      metrics: { lastCompletionTokens: 10 },
    });
    // 2c. Relevance mainDocument.pdf (assuming it's relevant)
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: "true", // Or whatever the relevance check expects
      metrics: { lastCompletionTokens: 5 },
    });
    // 2d. Relevance otherDoc.pdf (assuming it's relevant)
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: "true", // Or whatever the relevance check expects
      metrics: { lastCompletionTokens: 5 },
    });
    // Step 3: Map otherDoc to sections
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: JSON.stringify([1]), // e.g., otherDoc maps to section 1
      metrics: { lastCompletionTokens: 5 },
    });
    // Step 4: Identify Legal Issues
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: JSON.stringify([{ Issue: "Issue Alpha" }]),
      metrics: { lastCompletionTokens: 15 },
    });
    // Step 5: Generate Legal Memo
    mockGenerateLegalMemo.mockResolvedValueOnce({
      memo: "Memo for Issue Alpha",
      tokenCount: 30,
      sources: [],
    });
    // Step 6: Draft Section
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: "Drafted Section 1 content.",
      metrics: { lastCompletionTokens: 40 },
    });
    // Step 7: Combine Sections
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: "Final Combined Document - Main Flow.",
      metrics: { lastCompletionTokens: 50 },
    });

    // Mock fs.readdirSync to return the mainDoc and one other relevant doc
    fs.readdirSync.mockReturnValue([
      `${mainDocName}.json`,
      "otherDoc.pdf.json",
    ]);
    fs.readFileSync.mockImplementation((filePath) => {
      if (filePath.includes(mainDocName))
        return JSON.stringify({
          metadata: { title: mainDocName },
          pageContent: "Main document content",
        });
      if (filePath.includes("otherDoc.pdf"))
        return JSON.stringify({
          metadata: { title: "otherDoc.pdf" },
          pageContent: "Other document content",
        });
      return JSON.stringify({ pageContent: "Default dummy content" });
    });

    const response = await request(app)
      .post(`/workspace/${workspaceSlug}/stream-chat/${slugModule}?cdb=true`)
      .send({
        message: "Legal task for main document flow",
        chatId: chatId,
        cdbOptions: ["Legal Task Name", "Custom Instructions", mainDocName],
      })
      .set("Accept", "text/event-stream");

    expect(response.status).toBe(200);
    expect(response.headers["content-type"]).toMatch(/text\/event-stream/);

    const sseEvents = response.text
      .split("\n\n")
      .filter(Boolean)
      .map((e) => JSON.parse(e.replace(/^data: /, "")));

    const progressEvents = sseEvents.filter((e) => e.type === "cdbProgress");
    console.log(
      "[TEST LOG] Received cdbProgress events for Main Document Flow:",
      JSON.stringify(progressEvents, null, 2)
    );
    // Expect at least step 0 (start/complete) + 7 steps (start/complete or message)
    // (2 + 7*1 to 7*2) events. Let's use a reasonable lower bound like 9.
    // If each of 7 steps sends start and complete, plus step 0 start/complete = 16
    expect(progressEvents.length).toBeGreaterThanOrEqual(16);
    expect(progressEvents.every((e) => e.flowType === "main")).toBe(true);

    // Verify each main step (1-7) was progressed through
    for (let i = 1; i <= 7; i++) {
      const stepEvents = progressEvents.filter((e) => e.step === i);
      expect(stepEvents.length).toBeGreaterThanOrEqual(1);
      const hasActivationEvent = stepEvents.some(
        (e) =>
          e.status === "starting" || e.status === "in_progress" || e.message
      );
      expect(hasActivationEvent).toBe(true);
      const hasCompletionEvent = stepEvents.some(
        (e) => e.status === "complete" || e.status === "skipped"
      );
      expect(hasCompletionEvent).toBe(true);
    }

    // Adjusted to look for finalizeResponseStream for successful close
    const finalizationEvent = sseEvents.find(
      (e) =>
        e.type === "finalizeResponseStream" &&
        e.close === true &&
        e.error === false
    );
    expect(finalizationEvent).toBeDefined();

    const expectedDirPattern = new RegExp(`storage/document-builder/`);
    const writeCalls = fs.writeFileSync.mock.calls;
    expect(writeCalls.some((call) => expectedDirPattern.test(call[0]))).toBe(
      true
    );
  });
  test("No Main Document Flow: successfully drafts without a main document", async () => {
    const user = { id: 2, role: "admin", username: "testuser2" };
    const workspaceSlug = "test-workspace-no-main";
    const chatId = "api-no-main-flow-chat-id";
    const slugModule = "document-drafting";
    // Set user for this test
    require("../../utils/http").userFromSession.mockResolvedValueOnce(user);
    require("../../models/workspace").Workspace.get.mockResolvedValueOnce({
      id: 2,
      slug: workspaceSlug,
      type: "document-drafting",
      openAiTemp: 0.7,
      user_id: user.id,
    });
    // The getUserDocumentPathName mock is now set in beforeEach using defaultUser/defaultWorkspace.
    // If a different path is needed for this specific test, it should be re-mocked here.
    // For now, assume the default from beforeEach is sufficient or it's correctly derived inside the endpoint.
    // Actually, the flows themselves call getUserDocumentPathName, so the user for that call matters.
    require("../../utils/files").getUserDocumentPathName.mockReturnValueOnce(
      `${user.username}/${workspaceSlug}`
    );
    // Mock the specific sequence of LLM calls for the no main document flow
    // 1. Descriptions (assuming 2 docs for simplicity)
    mockLLMInstance.getChatCompletion
      .mockResolvedValueOnce({
        textResponse: "Description for docA.pdf",
        metrics: { lastCompletionTokens: 10 },
      })
      .mockResolvedValueOnce({
        textResponse: "Description for docB.pdf",
        metrics: { lastCompletionTokens: 10 },
      });
    // 2. Section List from Summaries
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: JSON.stringify([
        {
          index_number: 1,
          title: "Section 1 from Summaries",
          relevant_documents: ["docA.pdf"],
        },
      ]),
      metrics: { lastCompletionTokens: 20 },
    });
    // 3. Identify Legal Issues
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: JSON.stringify([{ Issue: "Issue Gamma" }]),
      metrics: { lastCompletionTokens: 15 },
    });
    // 4. Generate Legal Memo
    mockGenerateLegalMemo.mockResolvedValueOnce({
      memo: "Memo for Issue Gamma",
      tokenCount: 30,
      sources: [],
    });
    // 5. Draft Section
    mockLLMInstance.getChatCompletion.mockResolvedValueOnce({
      textResponse: "Drafted Section 1 for no-main.",
      metrics: { lastCompletionTokens: 40 },
    });
    // Mock for Combine Sections is removed.

    // Mock fs.readdirSync for this flow
    fs.readdirSync.mockReturnValue(["docA.pdf.json", "docB.pdf.json"]);
    fs.readFileSync.mockImplementation((filePath) => {
      if (filePath.includes("docA.pdf"))
        return JSON.stringify({
          metadata: { title: "docA.pdf" },
          pageContent: "Doc A content",
        });
      if (filePath.includes("docB.pdf"))
        return JSON.stringify({
          metadata: { title: "docB.pdf" },
          pageContent: "Doc B content",
        });
      return JSON.stringify({ pageContent: "Default dummy content" });
    });
    const response = await request(app)
      .post(`/workspace/${workspaceSlug}/stream-chat/${slugModule}?cdb=true`)
      .send({
        message: "Legal task for no main document flow",
        chatId: chatId,
        cdbOptions: [
          "Legal Task Name NoMain",
          "Custom Instructions NoMain",
          null,
        ],
      })
      .set("Accept", "text/event-stream");
    expect(response.status).toBe(200);
    expect(response.headers["content-type"]).toMatch(/text\/event-stream/);
    const sseEvents = response.text
      .split("\n\n")
      .filter(Boolean)
      .map((e) => JSON.parse(e.replace(/^data: /, "")));
    const progressEvents = sseEvents.filter((e) => e.type === "cdbProgress");
    expect(progressEvents.length).toBeGreaterThanOrEqual(7); // No-Main flow has 7+ steps
    expect(progressEvents.every((e) => e.flowType === "noMain")).toBe(true);

    // Find the chunk containing the actual document content
    const documentContentEvent = sseEvents.find(
      (e) =>
        (e.type === "text" || e.type === "textResponseChunk") &&
        typeof e.textResponse === "string" &&
        e.close === false
    );
    expect(documentContentEvent).toBeDefined();
    expect(documentContentEvent.textResponse).toBe(
      "## Section 1 from Summaries\\n\\nDrafted Section 1 for no-main."
    );

    // Find the separate finalization chunk
    const finalizationEvent = sseEvents.find(
      (e) => e.type === "finalizeResponseStream" && e.close === true
    );
    expect(finalizationEvent).toBeDefined();
    expect(finalizationEvent.error).toBe(false); // Assuming no error in this successful test case

    const expectedDirPattern = new RegExp(`storage/document-builder/`);
    const writeCalls = fs.writeFileSync.mock.calls;
    expect(writeCalls.some((call) => expectedDirPattern.test(call[0]))).toBe(
      true
    );
  });
  test("Error Handling: returns 404 if workspace not found", async () => {
    const user = { id: 1, role: "admin", username: "erroruser" };
    const nonExistentWorkspaceSlug = "non-existent-workspace";
    const chatId = "api-error-flow-chat-id";
    const slugModule = "document-drafting";
    // Set user for this test
    require("../../utils/http").userFromSession.mockResolvedValueOnce(user);
    require("../../models/workspace").Workspace.get.mockResolvedValueOnce(null);
    require("../../utils/files").getUserDocumentPathName.mockReturnValueOnce(
      `${user.username}/${nonExistentWorkspaceSlug}`
    );
    const response = await request(app)
      .post(
        `/workspace/${nonExistentWorkspaceSlug}/stream-chat/${slugModule}?cdb=true`
      )
      .send({
        message: "Test task",
        chatId: chatId,
        cdbOptions: ["Task Name", "Instructions", null],
      })
      .set("Accept", "text/event-stream");
    // Depending on how errors are handled (streamed or direct HTTP error response):
    if (
      response.headers["content-type"] &&
      response.headers["content-type"].includes("text/event-stream")
    ) {
      const sseEvents = response.text
        .split("\n\n")
        .filter(Boolean)
        .map((e) => JSON.parse(e.replace(/^data: /, "")));
      const errorEvent = sseEvents.find(
        (e) => e.type === "text" && e.error === true && e.close === true
      );
      expect(errorEvent).toBeDefined();
      expect(errorEvent.text).toMatch(/Workspace not found/i); // Or similar error message
      // For streamed errors, status might still be 200 but event contains error.
      // Adjust based on actual error streaming behavior.
    } else {
      expect(response.status).toBe(404); // Or other appropriate error code
      expect(response.body.error).toMatch(/Workspace not found/i);
    }
  });
  test("Error Handling: LLM error during document description", async () => {
    const user = { id: 1, role: "admin", username: "testuser" };
    const workspaceSlug = "test-workspace";
    const chatId = "api-llm-error-chat-id";
    const mainDocName = "mainDocument.pdf";
    const slugModule = "document-drafting";
    const fs = require("fs");

    require("../../utils/http").userFromSession.mockResolvedValueOnce(user);
    require("../../models/workspace").Workspace.get.mockResolvedValueOnce({
      id: 1,
      slug: workspaceSlug,
      type: "document-drafting",
      openAiTemp: 0.7,
      user_id: user.id,
    });
    require("../../utils/files").getUserDocumentPathName.mockReturnValueOnce(
      `${user.username}/${workspaceSlug}`
    );

    fs.readdirSync.mockReturnValueOnce([`${mainDocName}.json`]);
    fs.readFileSync.mockImplementationOnce((filePath) => {
      if (filePath.includes(mainDocName))
        return JSON.stringify({
          metadata: { title: mainDocName },
          pageContent: "Main document content for LLM error test",
        });
      return JSON.stringify({ pageContent: "Default dummy content" });
    });

    // Simulate LLM error during the first document description call
    // The first LLM call in the new order is for Section List.
    // The second LLM call is for Describe mainDocument.pdf. This is where the error should be.
    mockLLMInstance.getChatCompletion
      .mockResolvedValueOnce({
        textResponse: JSON.stringify([{ title: "mock section" }]),
      }) // Mock for Step 1 (Section List)
      .mockRejectedValueOnce(
        new Error("Simulated LLM API Error during description")
      ); // Error for Step 2 (Describe mainDoc)

    const response = await request(app)
      .post(`/workspace/${workspaceSlug}/stream-chat/${slugModule}?cdb=true`)
      .send({
        message: "Legal task, expecting LLM error",
        chatId: chatId,
        cdbOptions: ["Legal Task Name", "Custom Instructions", mainDocName],
      })
      .set("Accept", "text/event-stream");

    expect(response.status).toBe(200);
    expect(response.headers["content-type"]).toMatch(/text\/event-stream/);
    const sseEvents = response.text
      .split("\n\n")
      .filter(Boolean)
      .map((e) => JSON.parse(e.replace(/^data: /, "")));

    let llmErrorSignaled = false;
    let errorMessageDetail = "";

    const progressErrorEvent = sseEvents.find(
      (e) => e.type === "cdbProgress" && e.status === "error"
    );
    if (progressErrorEvent) {
      expect(progressErrorEvent.message).toMatch(
        /Simulated LLM API Error during description/i
      );
      llmErrorSignaled = true;
      errorMessageDetail = progressErrorEvent.message;
    }

    if (!llmErrorSignaled) {
      const textErrorEvent = sseEvents.find(
        (e) => e.type === "text" && e.error === true
      );
      if (textErrorEvent) {
        expect(textErrorEvent.text).toMatch(
          /Simulated LLM API Error during description/i
        );
        llmErrorSignaled = true;
        errorMessageDetail = textErrorEvent.text;
      }
    }

    if (!llmErrorSignaled) {
      const finalizeErrorEvent = sseEvents.find(
        (e) => e.type === "finalizeResponseStream" && e.error === true
      );
      if (finalizeErrorEvent) {
        errorMessageDetail =
          finalizeErrorEvent.errorMessage ||
          finalizeErrorEvent.text ||
          "Error in finalizeResponseStream";
        expect(errorMessageDetail).toMatch(
          /Simulated LLM API Error during description/i
        );
        llmErrorSignaled = true;
      }
    }

    // Re-add check for type: "abort"
    if (!llmErrorSignaled) {
      const abortEvent = sseEvents.find((e) => e.type === "abort");
      if (abortEvent) {
        // Abort events might carry the error message in various fields
        errorMessageDetail =
          abortEvent.error ||
          abortEvent.message ||
          abortEvent.text ||
          "Abort event occurred";
        expect(errorMessageDetail).toMatch(
          /Simulated LLM API Error during description/i
        );
        llmErrorSignaled = true;
      }
    }

    if (!llmErrorSignaled) {
      console.log(
        "[Error Handling Test] SSE Events on failure to find LLM error signal:",
        JSON.stringify(sseEvents, null, 2)
      );
      throw new Error(
        `Expected an LLM error to be signaled via cdbProgress, text, finalizeResponseStream, or abort event, but none matched. SSE Events: ${JSON.stringify(sseEvents)}`
      );
    }

    // Ensure the stream eventually closes, regardless of how the error was signaled
    const closeEvent = sseEvents.find((e) => e.close === true);
    expect(closeEvent).toBeDefined();

    const successTextEvent = sseEvents.find(
      (e) =>
        e.type === "text" &&
        e.text &&
        e.text.includes("processing complete") &&
        e.error !== true
    );
    expect(successTextEvent).toBeUndefined();
  });
  // TODO: Add test for LLM error during a later step (e.g., section drafting)
});
