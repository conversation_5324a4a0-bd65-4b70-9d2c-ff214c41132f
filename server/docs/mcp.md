# Multi-Component Processing (MCP) System

## Overview

The Multi-Component Processing (MCP) system allows ISTLegal to manage and interact with external compute processes, referred to as MCP servers. This enables the integration of specialized tools and services that adhere to the Model Context Protocol specification.

The `MCPHypervisor` class (`server/utils/MCP/hypervisor/index.js`) is responsible for managing these external processes. It reads a configuration file, starts the defined servers as child processes, and facilitates communication using the `@modelcontextprotocol/sdk`.

Functionality provided by MCP servers can be exposed within ISTLegal, often as agent tools usable in chat via the `@agent` directive.

## Configuration

MCP servers are defined in a JSON configuration file located at:

```fileref
server/storage/plugins/istlegal_mcp_servers.json
```

If this file does not exist upon server startup, the `MCPHypervisor` will automatically create it with an empty configuration structure.

### Configuration Structure

The JSON file must contain a top-level object named `mcpServers`. Each key within this object represents a unique identifier for an MCP server. The value associated with each key is an object defining how to launch that server process:

```json
{
  "mcpServers": {
    "unique-server-name": {
      "command": "executable_command",
      "args": ["argument1", "argument2"],
      "env": {
        "EXAMPLE_VAR": "value"
      }
    },
    "another-server": {
      // ... configuration ...
    }
  }
}
```

- `unique-server-name`: A unique string identifier for this MCP server. This name is used internally to manage the process.
- `command`: (Required) The executable command to run to start the server process (e.g., `npx`, `python`, `uvx`, path to a script). The command must be accessible from the environment where the main ISTLegal server process is running.
- `args`: (Required) An array of strings representing the arguments to pass to the `command`.
- `env`: (Optional) An object containing key-value pairs representing environment variables to set specifically for this MCP server's child process.

### Adding a New MCP Server

To add a new MCP server connection, edit the `server/storage/plugins/istlegal_mcp_servers.json` file and add a new entry to the `mcpServers` object.

**Example:**

To add servers named `face-generator` (run via `npx`) and `mcp-youtube` (run via `uvx`), you would modify the JSON file as follows:

```json
{
  "mcpServers": {
    "face-generator": {
      "command": "npx",
      "args": ["@dasheck0/face-generator"]
    },
    "mcp-youtube": {
      "command": "uvx",
      "args": ["mcp-youtube"]
    }
    // ... any other existing servers ...
  }
}
```

**Important Considerations:**

- **Dependencies**: The `MCPHypervisor` does _not_ check for or install dependencies required by the MCP servers. Ensure that the specified `command` and any underlying tools or packages are installed and accessible in the execution environment of the ISTLegal server. For example, if a server requires `npx`, then `npx` must be available in the `PATH`.
- **Restart**: After modifying the `istlegal_mcp_servers.json` file, you typically need to restart the ISTLegal server for the changes to take effect and for the `MCPHypervisor` to load the new configuration and attempt to start the new server process(es). Alternatively, administrative endpoints might exist to reload the configuration without a full server restart.
- **Communication**: MCP servers are expected to communicate using the Model Context Protocol, typically via standard input/output (stdio) as managed by the `@modelcontextprotocol/sdk`.

## Management

The `MCPHypervisor` handles the lifecycle of these processes. It attempts to start all configured servers on boot and provides mechanisms (potentially via API endpoints or internal functions) to start, stop, and reload individual servers or the entire configuration. Loading results (success/failure) for each server are tracked in `mcpLoadingResults`.
