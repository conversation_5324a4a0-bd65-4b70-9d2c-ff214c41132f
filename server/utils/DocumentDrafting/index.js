const { DocumentManager } = require("../DocumentManager");
const { getVectorDbClass } = require("../helpers");
const { TokenManager } = require("../helpers/tiktoken");

/**
 * DocumentDrafting class for document generation with AI assistance
 * This class manages memo generation and document operations for workspaces
 */
class DocumentDrafting {
  /**
   * Create a new DocumentDrafting instance
   * @param {Object} workspace - The workspace configuration
   * @param {Object} LLMConnector - Existing LLM connector to reuse (prevents multiple initializations)
   */
  constructor(workspace, LLMConnector) {
    this.workspace = workspace;
    // Reuse the existing LLMConnector instead of creating a new one
    this.LLMConnector = LLMConnector;
    this.VectorDb = getVectorDbClass();
    this.tokenManager = new TokenManager();

    // Avoid logging constructor messages as these will duplicate in the logs
    // Only log workspace slug once in actual method calls
  }

  /**
   * Generates a memo for a workspace based on legal issues
   * @param {Object} params - The parameters for memo generation
   * @param {string} params.legalIssues - The legal issues to analyze
   * @param {string} params.memoPrompt - The prompt for memo generation
   * @param {number} params.temperature - The temperature for LLM responses
   * @param {number} params.tokenLimit - Maximum tokens allowed for the memo
   * @returns {Promise<{memo: string, tokenCount: number, sources: Array, tokenUsage: Object}>}
   */
  async generateMemoForWorkspace({
    legalIssues,
    memoPrompt,
    temperature,
    tokenLimit,
  }) {
    console.log(
      `\x1b[36m[MEMO GENERATION STARTED]\x1b[0m Workspace: ${this.workspace.slug}, Reusing provider: ${this.LLMConnector.constructor.name}, Token limit: ${tokenLimit || "unlimited"}`
    );

    const startTime = Date.now();
    try {
      if (!this.workspace.slug || !legalIssues) {
        console.log(
          `\x1b[31m[MEMO GENERATION FAILED]\x1b[0m Invalid workspace slug or legal issues`
        );
        throw new Error("Invalid workspace slug or legal issues");
      }

      const tokenUsage = {
        vectorSearch: 0,
        pdrDocs: 0,
        memoGeneration: 0,
        total: 0,
      };

      // If skipContext is enabled, generate memo directly without context
      if (this._settings?.skipContext === true) {
        const messages = [
          { role: "system", content: memoPrompt },
          { role: "user", content: legalIssues },
        ];
        const result = await this.LLMConnector.getChatCompletion(messages, {
          temperature,
        });
        const memo = result?.textResponse || result;
        const tokenCount = this.tokenManager.countFromString(memo);
        const tokenUsage = {
          vectorSearch: 0,
          pdrDocs: 0,
          memoGeneration: tokenCount,
          total: tokenCount,
        };
        // Log generation success without context
        console.log(
          `\x1b[32m[MEMO GENERATION][SUCCESS]\x1b[0m Generated memo (${tokenCount} tokens) for workspace: ${this.workspace.slug}`
        );
        this.LLMConnector.metrics = {
          ...(this.LLMConnector.metrics || {}),
          lastCompletionTokens: tokenCount,
        };
        return { memo, tokenCount, sources: [], tokenUsage };
      }

      // Get vector search results
      const sources = await this.VectorDb.performSimilaritySearch({
        namespace: this.workspace.slug,
        input: legalIssues,
        LLMConnector: this.LLMConnector,
        similarityThreshold: this.workspace?.similarityThreshold,
        topN: this.workspace?.topN,
        workspace: this.workspace,
      });

      // Calculate vector search tokens
      tokenUsage.vectorSearch = sources.sources.reduce(
        (total, source) =>
          total + this.tokenManager.countFromString(source.text),
        0
      );

      // Get PDR docs
      const docManager = new DocumentManager({
        workspace: this.workspace,
        maxTokens: this.LLMConnector.promptWindowLimit(),
      });

      // Add the necessary settings for linked workspaces
      docManager._settings = {
        isLinkedWorkspaceImpact: true, // Always enable for memo generation
      };

      console.log(
        `\x1b[36m[MEMO GENERATION]\x1b[0m Getting PDR docs for workspace: ${this.workspace.slug} with isLinkedWorkspaceImpact=true`
      );

      const docStartTime = Date.now();
      const pdrDocs = await docManager.pdrDocs();
      const docRetrievalTime = Date.now() - docStartTime;

      console.log(
        `\x1b[36m[MEMO GENERATION]\x1b[0m Retrieved ${pdrDocs.length} PDR documents in ${docRetrievalTime}ms`
      );

      tokenUsage.pdrDocs = pdrDocs.reduce(
        (total, doc) =>
          total + this.tokenManager.countFromString(doc.pageContent),
        0
      );

      console.log(
        `\x1b[36m[MEMO GENERATION]\x1b[0m PDR docs token usage: ${tokenUsage.pdrDocs}`
      );

      const context = {
        systemPrompt: memoPrompt,
        sources: sources.sources,
        pdrDocs: pdrDocs.map((doc) => doc.pageContent),
      };

      // Pre-check total tokens
      const estimatedTokens =
        this.tokenManager.countFromString(memoPrompt) +
        this.tokenManager.countFromString(legalIssues) +
        tokenUsage.vectorSearch +
        tokenUsage.pdrDocs;

      if (estimatedTokens > this.LLMConnector.promptWindowLimit()) {
        // Compress messages if we might exceed limits
        const messages = await this.LLMConnector.compressMessages({
          systemPrompt: context.systemPrompt,
          userPrompt: legalIssues,
          sources: context.sources.map((source) => source.text),
          contextTexts: context.pdrDocs,
          chatHistory: [],
          attachments: [],
        });

        const result = await this.LLMConnector.getChatCompletion(messages, {
          temperature: temperature,
        });

        // Extract textResponse from the result - LLM providers return an object with textResponse property
        const memo = result?.textResponse || result;

        // Verify memo quality
        if (!memo || typeof memo !== "string") {
          console.log(
            `\x1b[31m[MEMO GENERATION FAILED]\x1b[0m Generated memo is ${memo === null ? "null" : typeof memo}, not a string`
          );
          throw new Error(
            `Generated memo is not valid: ${JSON.stringify(memo)}`
          );
        }

        const tokenCount = this.tokenManager.countFromString(memo);

        // Check if memo is too short to be useful
        if (tokenCount < 20) {
          console.log(
            `\x1b[33m[MEMO GENERATION WARNING]\x1b[0m Generated memo is very short (${tokenCount} tokens): "${memo}"`
          );

          // If it's under 10 tokens, consider it a failure
          if (tokenCount < 10) {
            console.log(
              `\x1b[31m[MEMO GENERATION FAILED]\x1b[0m Memo is too short to be useful (${tokenCount} tokens)`
            );
            throw new Error(
              `Generated memo is too short to be useful (${tokenCount} tokens): "${memo}"`
            );
          }
        }

        tokenUsage.memoGeneration = tokenCount;
        tokenUsage.total = tokenCount + estimatedTokens;

        // Check final token limit
        if (tokenLimit && tokenCount > tokenLimit) {
          console.log(
            `\x1b[31m[MEMO GENERATION FAILED]\x1b[0m Memo exceeds token limit of ${tokenLimit} (actual: ${tokenCount})`
          );
          throw new Error(`Memo exceeds token limit of ${tokenLimit}`);
        }

        console.log(
          `\x1b[32m[MEMO GENERATION SUCCEEDED]\x1b[0m Generated memo with ${tokenCount} tokens using compressed messages`
        );

        this.LLMConnector.metrics = {
          ...(this.LLMConnector.metrics || {}),
          lastCompletionTokens: tokenCount,
        };
        return {
          memo,
          tokenCount,
          sources: sources.sources,
          tokenUsage,
        };
      } else {
        // Direct generation for smaller contexts
        const result = await this.LLMConnector.getChatCompletion(
          [
            { role: "system", content: memoPrompt },
            { role: "user", content: legalIssues },
          ],
          { temperature }
        );

        // Extract textResponse from the result - LLM providers return an object with textResponse property
        const memo = result?.textResponse || result;

        // Verify memo quality
        if (!memo || typeof memo !== "string") {
          console.log(
            `\x1b[31m[MEMO GENERATION FAILED]\x1b[0m Generated memo is ${
              memo === null ? "null" : typeof memo
            }, not a string`
          );
          throw new Error(
            `Generated memo is not valid: ${JSON.stringify(memo)}`
          );
        }

        const tokenCount = this.tokenManager.countFromString(memo);

        // Check if memo is too short to be useful
        if (tokenCount < 20) {
          console.log(
            `\x1b[33m[MEMO GENERATION WARNING]\x1b[0m Generated memo is very short (${tokenCount} tokens): "${memo}"`
          );

          // If it's under 10 tokens, consider it a failure
          if (tokenCount < 10) {
            console.log(
              `\x1b[31m[MEMO GENERATION FAILED]\x1b[0m Memo is too short to be useful (${tokenCount} tokens)`
            );
            throw new Error(
              `Generated memo is too short to be useful (${tokenCount} tokens): "${memo}"`
            );
          }
        }

        tokenUsage.memoGeneration = tokenCount;
        tokenUsage.total = tokenCount + estimatedTokens;

        if (tokenLimit && tokenCount > tokenLimit) {
          console.log(
            `\x1b[31m[MEMO GENERATION FAILED]\x1b[0m Memo exceeds token limit of ${tokenLimit} (actual: ${tokenCount}) (direct generation)`
          );
          throw new Error(`Memo exceeds token limit of ${tokenLimit}`);
        }

        console.log(
          `\x1b[32m[MEMO GENERATION SUCCEEDED]\x1b[0m Generated memo with ${tokenCount} tokens using direct generation`
        );

        this.LLMConnector.metrics = {
          ...(this.LLMConnector.metrics || {}),
          lastCompletionTokens: tokenCount,
        };
        return {
          memo,
          tokenCount,
          sources: sources.sources,
          tokenUsage,
        };
      }
    } catch (error) {
      console.log(
        `\x1b[31m[MEMO GENERATION FAILED]\x1b[0m Error: ${error.message}`
      );
      throw error;
    }
  }
}

module.exports = DocumentDrafting;
