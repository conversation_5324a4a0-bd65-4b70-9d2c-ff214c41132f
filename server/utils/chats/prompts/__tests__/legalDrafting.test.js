const { exportedLegalPrompts } = require("../legalDrafting");
// const { SystemSettings } = require("../../../../models/systemSettings"); // Commenting out due to loading issues in this test file

// console.log(
//   "[TEST_LOG] Raw imported systemSettings module:",
//   rawSystemSettingsModule
// );

// console.log("[TEST_LOG] Destructured SystemSettings:", SystemSettings);

// if (SystemSettings) {
//   console.log(
//     "[TEST_LOG] typeof SystemSettings.currentSettings:",
//     typeof SystemSettings.currentSettings
//   );
// } else {
//   console.log(
//     "[TEST_LOG] SystemSettings is undefined or null after destructuring."
//   );
// }

// Mock Prisma, as systemSettings.js depends on it heavily.
// An issue with Prisma client init in this test context could cause systemSettings to load incompletely.
jest.mock("../../../../utils/prisma", () => ({ system_settings: {} })); // Minimal prisma
jest.mock("../../../../utils/i18n", () => ({ t: jest.fn((key) => key) })); // Mock t function
jest.mock("../../../../utils/http", () => ({
  isValidUrl: jest.fn().mockReturnValue(true),
  safeJsonParse: jest.fn((val, fallback) => {
    try {
      return JSON.parse(val);
    } catch {
      return fallback;
    }
  }),
}));
jest.mock("../../../../utils/boot/MetaGenerator", () => ({
  MetaGenerator: jest
    .fn()
    .mockImplementation(() => ({ clearConfig: jest.fn() })),
}));
// Assuming systemSettings.js might also use ../utils/files directly or indirectly via other models.
// This is a broad mock; if specific functions are needed, they should be added.
jest.mock("../../../../utils/files", () => ({
  hasVectorCachedFiles: jest.fn().mockReturnValue(false),
  // Add any other functions from utils/files that systemSettings might call during init
}));

// CRITICAL MOCK: The SystemSettings module is mocked here with static copies of its
// protectedFields and supportedFields arrays from the actual server/models/systemSettings.js file.
// This is a workaround for persistent module loading issues that prevent the actual SystemSettings
// module from being reliably imported and used in this specific test file.
//
// !! IMPORTANT !!
// IF YOU MODIFY SystemSettings.protectedFields OR SystemSettings.supportedFields in
// server/models/systemSettings.js, YOU MUST MANUALLY UPDATE THE ARRAYS BELOW IN THIS MOCK
// TO MATCH, OTHERWISE THIS TEST WILL PRODUCE FALSE POSITIVES OR NEGATIVES.
// Consider this technical debt; a more robust solution would dynamically access these arrays
// or use a script to generate this mock data.
jest.mock("../../../../models/systemSettings", () => ({
  SystemSettings: {
    protectedFields: [
      "multi_user_mode",
      "public_user_mode",
      "document_drafting",
      "document_drafting_linking",
      "qura",
      "copyOption",
      "citation",
      "document_drafting_prompt",
      "default_prompt",
      "websiteLink",
      "displayText",
      "vector_search_top_n",
      "keep_pdr_vectors",
      "tabName1",
      "tabName2",
      "tabName3",
      "prompt_output_logging",
      "max_tokens_per_user",
      "adjacent_vector_limit",
      "validation_prompt",
      "dd_vector_enabled",
      "dd_memo_enabled",
      "dd_base_enabled",
      "dd_linked_workspace_impact",
      "dd_vector_token_limit",
      "dd_memo_token_limit",
      "dd_base_token_limit",
      "performLegalTask",
      "feedback_enabled",
      "global_pdr_override",
      "requiresMainDocument",
      "canvas_system_prompt",
      "canvas_upload_system_prompt",
      "document_drafting_combine_prompt",
      "document_drafting_memo_prompt",
      "cdb_document_summary_system_prompt",
      "cdb_document_summary_user_prompt",
      "cdb_document_relevance_system_prompt",
      "cdb_document_relevance_user_prompt",
      "cdb_section_drafting_system_prompt",
      "cdb_section_drafting_user_prompt",
      "cdb_memo_creation_prompt",
      "cdb_memo_creation_prompt_template",
      "cdb_section_legal_issues_system_prompt",
      "cdb_section_legal_issues_user_prompt",
      "cdb_select_main_document_system_prompt",
      "cdb_select_main_document_user_prompt",
      "cdb_section_list_from_main_system_prompt",
      "cdb_section_list_from_main_user_prompt",
      "cdb_section_list_from_summaries_system_prompt",
      "cdb_section_list_from_summaries_user_prompt",
      "cdb_section_index_system_prompt",
      "dynamic_context_window_percentage",
      "custom_dynamic_context_window_percentage",
      "welcome_message_headline",
      "welcome_message_text",
      "login_ui",
      "request_legal_assistance_enabled",
      "request_legal_assistance_law_firm_name",
      "request_legal_assistance_email",
    ],
    supportedFields: [
      "limit_user_messages",
      "message_limit",
      "logo_light",
      "logo_dark",
      "telemetry_id",
      "footer_data",
      "support_email",
      "text_splitter_chunk_size",
      "text_splitter_chunk_overlap",
      "text_splitter_method",
      "text_splitter_jina_max_tokens",
      "text_splitter_jina_return_tokens",
      "text_splitter_jina_return_chunks",
      "agent_search_provider",
      "default_agent_skills",
      "agent_sql_connections",
      "custom_app_name",
      "language",
      "palette",
      "experimental_live_file_sync",
      "meta_page_title",
      "dynamic_context_window_percentage", // Note: also in protectedFields, Set will handle duplication
      "meta_page_favicon",
      "max_tokens_per_user", // Note: also in protectedFields
      "invoice",
      "forced_invoice_logging",
      "rerank_vector_count",
      "rexor_linkage",
      "enable_lancedb_rerank",
      "docx_system_template",
      "docx_system_template_display",
      "deep_search_provider",
      "deep_search_model_id",
      "deep_search_api_key",
      "deep_search_enabled",
      "deep_search_context_percentage",
      "custom_model_reference",
      "custom_model_reference_2",
      "custom_model_reference_3",
      "custom_model_reference_4",
      "custom_model_reference_5",
      "custom_model_reference_6",
      "custom_dynamic_context_window_percentage", // Note: also in protectedFields
      "custom_dynamic_context_window_percentage_2",
      "custom_dynamic_context_window_percentage_3",
      "custom_dynamic_context_window_percentage_4",
      "custom_dynamic_context_window_percentage_5",
      "custom_dynamic_context_window_percentage_6",
      "CustomUserAiStatus_1",
      "CustomUserAiStatus_2",
      "CustomUserAiStatus_3",
      "CustomUserAiStatus_4",
      "CustomUserAiStatus_5",
      "CustomUserAiStatus_6",
      "custom_legal_templates",
      "disableValidationPrompt",
      "university_mode",
    ],
    // Add other methods or properties if the test starts depending on them, but keep minimal for now.
  },
}));

const { SystemSettings } = require("../../../../models/systemSettings");

describe("Legal Drafting Prompts", () => {
  let knownSystemSettingKeys = [];
  // SystemSettings will be populated by the require inside isolateModulesAsync
  let SystemSettingsLDS_Module;

  beforeAll(async () => {
    await jest.isolateModulesAsync(async () => {
      jest.doMock("../../../../utils/prisma", () => ({
        system_settings: {
          findMany: jest.fn().mockResolvedValue([]),
          findFirst: jest.fn().mockResolvedValue(null),
          upsert: jest.fn().mockResolvedValue({}),
          create: jest.fn().mockResolvedValue({}),
          update: jest.fn().mockResolvedValue({}),
        },
      }));
      jest.doMock("../../../../utils/i18n", () => ({
        t: jest.fn((key) => key),
      }));
      jest.doMock("../../../../utils/http", () => ({
        isValidUrl: jest.fn().mockReturnValue(true),
        safeJsonParse: jest.fn((val, fallback) => {
          try {
            return JSON.parse(val);
          } catch {
            return fallback;
          }
        }),
      }));
      jest.doMock("../../../../utils/boot/MetaGenerator", () => ({
        MetaGenerator: jest
          .fn()
          .mockImplementation(() => ({ clearConfig: jest.fn() })),
      }));
      jest.doMock("../../../../utils/files", () => ({
        hasVectorCachedFiles: jest.fn().mockReturnValue(false),
      }));

      // Require SystemSettings *after* its dependencies are mocked for this scope
      SystemSettingsLDS_Module = require("../../../../models/systemSettings");
      const ActualSystemSettings = SystemSettingsLDS_Module.SystemSettings;

      if (
        ActualSystemSettings &&
        Array.isArray(ActualSystemSettings.protectedFields) &&
        Array.isArray(ActualSystemSettings.supportedFields)
      ) {
        const settingKeys = new Set([
          ...ActualSystemSettings.protectedFields,
          ...ActualSystemSettings.supportedFields,
        ]);
        knownSystemSettingKeys = Array.from(settingKeys);
      } else {
        console.error(
          "[ISOLATED_TEST_LOG] FreshSystemSettings or its key arrays (protectedFields, supportedFields) are undefined/not arrays."
        );
        knownSystemSettingKeys = [];
      }
    });
  });

  test("all systemSettingNames in exportedLegalPrompts should correspond to a known system setting key", () => {
    if (knownSystemSettingKeys.length === 0) {
      console.warn(
        "[TEST_LOG] Test for systemSettingName validity is not effective as knownSystemSettingKeys is empty. This indicates SystemSettings did not load correctly."
      );
      expect(knownSystemSettingKeys.length).toBeGreaterThan(0);
    }

    const missingKeys = [];
    exportedLegalPrompts.forEach((prompt) => {
      if (prompt.systemSettingName) {
        if (!knownSystemSettingKeys.includes(prompt.systemSettingName)) {
          missingKeys.push({
            promptKey: prompt.promptKey,
            promptField: prompt.promptField,
            systemSettingName: prompt.systemSettingName,
          });
        }
      }
    });

    if (missingKeys.length > 0) {
      console.error(
        "[TEST_LOG] Missing systemSettingName keys (not found in dynamically loaded SystemSettings.protectedFields or SystemSettings.supportedFields):",
        JSON.stringify(missingKeys, null, 2)
      );
    }
    expect(missingKeys).toEqual([]);
  });
});
