/**
 * The model name and context window for all know model windows
 * that are available through providers which has discrete model options.
 * This file should be aligned with the frontend/src/utils/AiProviders/modelMap.js file.
 *
 * Each model entry has:
 * - context: The maximum context window size in tokens
 * - maxOutput: The maximum number of output tokens the model can generate
 */
const MODEL_MAP = {
  anthropic: {
    models: {
      "claude-3-haiku-20240307": { context: 200_000, maxOutput: 4_096 },
      "claude-3-opus-latest": { context: 200_000, maxOutput: 4_096 },
      "claude-3-sonnet-20240229": { context: 200_000, maxOutput: 4_096 },
      "claude-3-5-haiku-latest": { context: 200_000, maxOutput: 4_096 },
      "claude-3-5-sonnet-20240620": { context: 200_000, maxOutput: 4_096 },
      "claude-3-5-sonnet-20241022": { context: 200_000, maxOutput: 4_096 },
      "claude-3-7-sonnet-20250219": { context: 200_000, maxOutput: 4_096 },
      "claude-3-7-sonnet-latest": { context: 200_000, maxOutput: 4_096 },
    },
    defaults: {
      contextWindow: 200_000,
      temperature: 0.7,
      maxOutput: 4_096,
    },
  },
  cohere: {
    models: {
      "command-r": { context: 128_000, maxOutput: 4_096 },
      "command-r-plus": { context: 128_000, maxOutput: 4_096 },
      command: { context: 4_096, maxOutput: 2_048 },
      "command-light": { context: 4_096, maxOutput: 2_048 },
      "command-nightly": { context: 8_192, maxOutput: 4_096 },
      "command-light-nightly": { context: 8_192, maxOutput: 4_096 },
    },
    defaults: {
      contextWindow: 128_000,
      temperature: 0.7,
      maxOutput: 4_096,
    },
  },
  gemini: {
    models: {
      "gemini-pro": { context: 30_720, maxOutput: 8_192 },
      "gemini-1.0-pro": { context: 30_720, maxOutput: 8_192 },
      "gemini-1.5-flash-latest": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-1.5-flash-002": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-1.5-flash-8b": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-1.5-flash-8b-latest": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-1.5-flash-8b-001": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-1.5-pro-latest": { context: 2_097_152, maxOutput: 16_384 },
      "gemini-1.5-pro-002": { context: 2_097_152, maxOutput: 16_384 },
      "gemini-1.5-flash-8b-exp-0827": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-2.0-flash": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-2.0-flash-exp": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-2.0-flash-lite": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-2.0-flash-thinking-exp": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-2.0-pro-exp": { context: 1_048_576, maxOutput: 16_384 },
      "gemini-exp-1206": { context: 1_048_576, maxOutput: 16_384 },
      "gemini-2.5-pro-exp-03-25": { context: 1_048_576, maxOutput: 65_536 },
      "gemini-2.5-pro-preview-03-25": { context: 1_048_576, maxOutput: 65_536 },
      "gemini-2.5-pro-preview-05-06": { context: 1_048_576, maxOutput: 65_536 },
      "gemini-2.0-flash-001": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-2.0-flash-exp-image-generation": {
        context: 1_048_576,
        maxOutput: 8_192,
      },
      "gemini-2.0-flash-thinking-exp-01-21": {
        context: 1_048_576,
        maxOutput: 8_192,
      },
      "gemini-2.0-flash-lite-001": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-1.5-flash": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-1.5-flash-001": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-1.5-pro": { context: 2_097_152, maxOutput: 8_192 },
      "gemini-1.5-pro-001": { context: 2_097_152, maxOutput: 8_192 },
      "gemini-2.0-flash-live-001": { context: 1_048_576, maxOutput: 8_192 },
      "gemini-2.5-flash-preview-04-17": {
        context: 1_048_576,
        maxOutput: 64_000,
      },
      "gemini-2.5-flash-preview-05-20": {
        context: 1_048_576,
        maxOutput: 64_000,
      },
    },
    defaults: {
      contextWindow: 30_720,
      temperature: 0.7,
      maxOutput: 8_192,
    },
  },
  groq: {
    models: {
      "gemma2-9b-it": { context: 8192, maxOutput: 4_096 },
      "gemma-7b-it": { context: 8192, maxOutput: 4_096 },
      "llama3-70b-8192": { context: 8192, maxOutput: 4_096 },
      "llama3-8b-8192": { context: 8192, maxOutput: 4_096 },
      "llama-3.1-70b-versatile": { context: 8000, maxOutput: 4_000 },
      "llama-3.1-8b-instant": { context: 8000, maxOutput: 4_000 },
      "mixtral-8x7b-32768": { context: 32768, maxOutput: 16_384 },
    },
    defaults: {
      contextWindow: 8192,
      temperature: 0.7,
      maxOutput: 4_096,
    },
  },
  openai: {
    models: {
      "chatgpt-4o-latest": { context: 128_000, maxOutput: 16_384 },
      "gpt-4o": { context: 128_000, maxOutput: 16_384 },
      "gpt-4o-2024-11-20": { context: 128_000, maxOutput: 16_384 },
      "gpt-4o-mini": { context: 128_000, maxOutput: 16_384 },
      "gpt-4o-mini-2024-07-18": { context: 128_000, maxOutput: 16_384 },
      o3: { context: 200_000, maxOutput: 100_000 },
      "o3-mini": { context: 200_000, maxOutput: 100_000 },
      "o4-mini": { context: 128_000, maxOutput: 16_384 },
      "o1-2024-12-17": { context: 200_000, maxOutput: 100_000 },
      "gpt-4.1": { context: 1_047_576, maxOutput: 32_768 },
      "gpt-4.1-mini": { context: 1_047_576, maxOutput: 32_768 },
      "gpt-4.1-nano": { context: 1_047_576, maxOutput: 32_768 },
    },
    defaults: {
      contextWindow: 128_000,
      temperature: 0.7,
      maxOutput: 16_384,
    },
  },
  deepseek: {
    models: {
      "deepseek-chat": { context: 128_000, maxOutput: 8_192 },
      "deepseek-coder": { context: 128_000, maxOutput: 8_192 },
    },
    defaults: {
      contextWindow: 128_000,
      temperature: 0.7,
      maxOutput: 8_192,
    },
  },
  xai: {
    models: {
      "grok-beta": { context: 131_072, maxOutput: 8_192 },
    },
    defaults: {
      contextWindow: 131_072,
      temperature: 0.7,
      maxOutput: 8_192,
    },
  },
  localAi: {
    defaults: {
      contextWindow: 8_192,
      temperature: 0.7,
      maxOutput: 4_096,
    },
  },
  lmStudio: {
    defaults: {
      contextWindow: 8_192,
      temperature: 0.7,
      maxOutput: 4_096,
    },
  },
  perplexity: {
    defaults: {
      contextWindow: 32_000,
      temperature: 0.7,
      maxOutput: 8_192,
    },
  },
  liteLLM: {
    defaults: {
      contextWindow: 8_192,
      temperature: 0.7,
      maxOutput: 4_096,
    },
  },
  textGenWebUI: {
    defaults: {
      contextWindow: 8_192,
      temperature: 0.7,
      maxOutput: 4_096,
    },
  },
};
module.exports = { MODEL_MAP };
