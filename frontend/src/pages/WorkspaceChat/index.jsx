import React, { useEffect, useState } from "react";
import { default as WorkspaceChatContainer } from "@/components/WorkspaceChat";
import Sidebar from "@/components/Sidebar";
import { useParams, useLocation } from "react-router-dom";
import Workspace from "@/models/workspace";
import PasswordModal, { usePasswordModal } from "@/components/Modals/Password";
import { FullScreenLoader } from "@/components/Preloader";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { useSetSelectedFeatureCard } from "@/stores/userStore";
import useQuery from "@/hooks/useQuery";

export default function WorkspaceChat() {
  const { loading, requiresAuth, mode } = usePasswordModal();
  const { slug } = useParams();

  if (loading) return <FullScreenLoader />;
  if (requiresAuth !== false) {
    return <>{requiresAuth !== null && <PasswordModal mode={mode} />}</>;
  }

  return <ShowWorkspaceChat key={slug} />;
}

function ShowWorkspaceChat() {
  const { slug } = useParams();
  const [workspace, setWorkspace] = useState(null);
  const [loading, setLoading] = useState(true);
  const query = useQuery();
  const setSelectedFeatureCard = useSetSelectedFeatureCard();

  // Check for the openModal query parameter
  useEffect(() => {
    const openModal = query.get("openModal");
    if (openModal === "performLegalTask") {
      // Set the selectedFeatureCard to trigger the PerformLegalTaskModal to open
      setSelectedFeatureCard("complex-document-builder");
    }
  }, [query, setSelectedFeatureCard]);

  useEffect(() => {
    async function getWorkspace() {
      if (!slug) return;
      const _workspace = await Workspace.bySlug(slug);
      if (!_workspace) {
        setLoading(false);
        return;
      }
      const suggestedMessages = await Workspace.getSuggestedMessages(slug);
      const pfpUrl = await Workspace.fetchPfp(slug);
      setWorkspace({
        ..._workspace,
        suggestedMessages,
        pfpUrl,
      });
      setLoading(false);
    }
    getWorkspace();
  }, [slug]);

  return (
    <>
      <div className="overflow-hidden w-full h-full flex flex-col">
        <HeaderWorkspace />
        <div className="flex overflow-hidden h-[calc(100vh-var(--header-height))] bg-background">
          <Sidebar />
          <WorkspaceChatContainer loading={loading} workspace={workspace} />
        </div>
      </div>
    </>
  );
}
