import { useEffect, useState } from "react";
import paths from "@/utils/paths";
import Admin from "@/models/admin";
import System from "@/models/system";
import showToast from "@/utils/toast";
import { Link } from "react-router-dom";
import PreLoader from "@/components/Preloader";
import { useTranslation } from "react-i18next";
import { ArrowLeft } from "@phosphor-icons/react";
import Sidebar from "@/components/SettingsSidebar";
import Toggle from "@/components/ui/Toggle";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import UsageRegistration from "./UsageRegistration";
import {
  useInvoiceLogging,
  useForceInvoiceLogging,
  useRexorLinkage,
  useRequestLegalAssistance,
  useUniversityMode,
} from "@/stores/settingsStore";
import useSystemSettingsStore from "@/stores/settingsStore";
import { Button } from "@/components/Button";

const AdminSystem = () => {
  const { t } = useTranslation();
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  const [useQura, setUseQura] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [useCitation, setUseCitation] = useState(false);
  const [citationChanges, setCitationChanges] = useState(false);
  const [hasQuraChanges, setHasQuraChanges] = useState(false);
  const [copyOption, setCopyOption] = useState("question");
  const [citationEnabled, setCitationEnabled] = useState(false);
  const [messageLimit, setMessageLimit] = useState({
    enabled: false,
    limit: 10,
  });
  const [usePromptOutput, setUsePromptOutput] = useState(false);
  const [promptOutputChange, setPromptOutputChange] = useState(false);
  const [promptOutputEnabled, setPromptOutputEnabled] = useState(false);
  const [maxTokensPerUser, setMaxTokensPerUser] = useState(1);
  const [usePerformLegalTask, setUsePerformLegalTask] = useState(false);
  const [allowUserAccess, setAllowUserAccess] = useState(false);
  const [performLegalTaskChange, setPerformLegalTaskChange] = useState(false);
  const [invoiceLogging, setInvoiceLogging] = useState(false);
  const [forcedInvoiceLogging, setForcedInvoiceLogging] = useState(false);
  const [rexorLinkage, setRexorLinkage] = useState(false);
  const [hasInvoiceLoggingChanges, setHasInvoiceLoggingChanges] =
    useState(false);
  const [hasForcedInvoiceLoggingChanges, setHasForcedInvoiceLoggingChanges] =
    useState(false);
  const [hasRexorLinkageChanges, setHasRexorLinkageChanges] = useState(false);
  const [contextWindowPercentage, setContextWindowPercentage] = useState(70);
  const [hasContextWindowChanges, setHasContextWindowChanges] = useState(false);
  const [attachmentContextPercentage, setAttachmentContextPercentage] =
    useState(70);
  const [hasAttachmentContextChanges, setHasAttachmentContextChanges] =
    useState(false);
  const [requestLegalAssistance, setRequestLegalAssistance] = useState({
    enabled: false,
    lawFirmName: "",
    email: "",
  });
  const [
    hasRequestLegalAssistanceChanges,
    setHasRequestLegalAssistanceChanges,
  ] = useState(false);
  const [universityMode, setUniversityMode] = useState(false);
  const [hasUniversityModeChanges, setHasUniversityModeChanges] =
    useState(false);

  const [useFeedback, setUseFeedback] = useState(false);
  const [feedbackChange, setFeedbackChange] = useState(false);

  const { invoice: storeInvoiceLogging } = useInvoiceLogging();
  const { isForcedinvoiceLogging: storeForcedInvoiceLogging } =
    useForceInvoiceLogging();
  const { rexorLinkage: storeRexorLinkage } = useRexorLinkage();
  const requestLegalAssistanceStore = useRequestLegalAssistance();
  const storeUniversityMode = useUniversityMode();

  useEffect(() => {
    setInvoiceLogging(storeInvoiceLogging);
    setForcedInvoiceLogging(storeForcedInvoiceLogging);
    setRexorLinkage(storeRexorLinkage);
    setUniversityMode(
      storeUniversityMode === true || storeUniversityMode === "true"
    );
  }, [
    storeInvoiceLogging,
    storeForcedInvoiceLogging,
    storeRexorLinkage,
    storeUniversityMode,
  ]);

  useEffect(() => {
    if (requestLegalAssistanceStore) {
      setRequestLegalAssistance({
        enabled: requestLegalAssistanceStore.enabled || false,
        lawFirmName: requestLegalAssistanceStore.lawFirmName || "",
        email: requestLegalAssistanceStore.email || "",
      });
    }
  }, [requestLegalAssistanceStore]);

  const handlePerformLegalTaskChange = (isChecked) => {
    setUsePerformLegalTask(isChecked);
    setPerformLegalTaskChange(true);
  };

  const handleCopyOptionChange = (option) => {
    setCopyOption(option);
    setHasQuraChanges(true);
    localStorage.setItem("copyOption", JSON.stringify(option));
  };

  const handleCitationSubmit = async (e) => {
    if (e) e.preventDefault();

    setSaving(true);
    setCitationChanges(false);

    const data = {
      citation: useCitation,
      copyOption: copyOption,
    };

    try {
      const { success, error } = await System.setCitationButton(data);
      if (success) {
        showToast(
          t("system.source-highlighting.toast-success", {
            state: useCitation ? "enabled" : "disabled",
          }),
          "success"
        );
        setCitationEnabled(useCitation);
      } else {
        console.error("Failed to update citation setting:", error);
        showToast(t("system.source-highlighting.toast-error"), "error");
        setUseCitation(citationEnabled);
      }
    } catch {
      showToast(t("system.source-highlighting.toast-error"), "error");
      setUseCitation(citationEnabled);
    } finally {
      setSaving(false);
    }
  };

  const handleQuraSubmit = async (e) => {
    if (e) e.preventDefault();

    setSaving(true);
    setHasQuraChanges(false);

    const data = {
      qura: useQura,
      copyOption: copyOption,
    };

    try {
      const response = await System.setQuraButton(data);
      const { success, message, error } = response;

      if (success) {
        showToast(
          message || `Qura has been ${useQura ? "enabled" : "disabled"}`,
          "success"
        );

        localStorage.setItem("quraStatus", JSON.stringify({ qura: useQura }));
        localStorage.setItem("copyOption", JSON.stringify(copyOption));
      } else {
        console.error("Failed to update Qura setting:", error);
        showToast(t("show-toast.qura-setting-update-failed"), "error");
      }
    } catch (error) {
      console.error("Error while submitting Qura settings:", error);
      showToast(t("show-toast.qura-settings-submission-error"), "error");
    } finally {
      setSaving(false);
    }
  };

  const handleRequestLegalAssistanceChange = (field) => (value) => {
    setRequestLegalAssistance((prev) => ({
      ...prev,
      [field]: value,
    }));
    setHasRequestLegalAssistanceChanges(true);
  };

  const handleRequestLegalAssistanceSubmit = async () => {
    try {
      const success = await useSystemSettingsStore
        .getState()
        .updateSetting("request-legal-assistance", requestLegalAssistance);

      if (success) {
        showToast(t("request-legal-assistance.settings-saved"), "success");
        setHasRequestLegalAssistanceChanges(false);
      } else {
        showToast(t("request-legal-assistance.save-error"), "error");
      }
    } catch (error) {
      console.error("Failed to save request legal assistance settings", error);
      showToast(t("request-legal-assistance.save-error"), "error");
    }
  };

  const handleUniversityModeChange = (newCheckedState) => {
    setUniversityMode(newCheckedState);
    setHasUniversityModeChanges(true);
  };

  const handleUniversityModeSubmit = async () => {
    if (!hasUniversityModeChanges) return;
    setSaving(true);
    try {
      const success = await useSystemSettingsStore
        .getState()
        .updateSetting("university-mode", universityMode);

      if (success) {
        showToast(t("admin.system.universityMode.saved"), "success");
        setHasUniversityModeChanges(false);
      } else {
        showToast(t("admin.system.universityMode.error"), "error");
      }
    } catch (error) {
      console.error("Failed to save university mode setting:", error);
      showToast(t("admin.system.universityMode.error"), "error");
    } finally {
      setSaving(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    try {
      if (hasQuraChanges) {
        await handleQuraSubmit();
      }
      if (hasChanges) {
        await Admin.updateSystemPreferences({
          limit_user_messages: messageLimit.enabled,
          message_limit: messageLimit.limit,
          max_tokens_per_user: maxTokensPerUser,
        });
        showToast(t("show-toast.message-limit-updated"), "success");
      }
      if (citationChanges) {
        await handleCitationSubmit();
      }
      if (promptOutputChange) {
        await handlePromptOutputSubmit();
      }
      if (performLegalTaskChange) {
        await handlePerformLegalTaskSubmit();
      }
      if (hasRequestLegalAssistanceChanges) {
        await handleRequestLegalAssistanceSubmit();
      }
      if (hasUniversityModeChanges) {
        await handleUniversityModeSubmit();
      }

      if (hasContextWindowChanges) {
        try {
          // Validate the percentage at save time
          const numValue = Number(contextWindowPercentage);
          if (isNaN(numValue) || numValue < 10 || numValue > 100) {
            showToast(t("system.context_window.validation-error"), "error");
            const settings = (await Admin.systemPreferences())?.settings;
            setContextWindowPercentage(
              settings?.dynamic_context_window_percentage || 70
            );
            setHasContextWindowChanges(false);
            return; // Exit early if validation fails
          }

          const response = await Admin.updateSystemPreferences({
            dynamic_context_window_percentage: numValue,
          });

          if (response.success) {
            showToast(t("system.context_window.toast-success"), "success");
            setContextWindowPercentage(numValue); // Ensure we display the validated number
            setHasContextWindowChanges(false);
          } else {
            showToast(t("system.context_window.toast-error"), "error");
            const settings = (await Admin.systemPreferences())?.settings;
            setContextWindowPercentage(
              settings?.dynamic_context_window_percentage || 70
            );
          }
        } catch {
          showToast(t("setting.context_window.toast-error"), "error");
          const settings = (await Admin.systemPreferences())?.settings;
          setContextWindowPercentage(
            settings?.dynamic_context_window_percentage || 70
          );
        }
      }
      if (hasAttachmentContextChanges) {
        try {
          // Validate the percentage at save time
          const numValue = Number(attachmentContextPercentage);
          if (isNaN(numValue) || numValue < 10 || numValue > 80) {
            showToast(t("system.attachment_context.validation-error"), "error");
            const settings = (await Admin.systemPreferences())?.settings;
            setAttachmentContextPercentage(
              settings?.attachment_context_percentage || 70
            );
            setHasAttachmentContextChanges(false);
            return; // Exit early if validation fails
          }

          const response = await Admin.updateSystemPreferences({
            attachment_context_percentage: numValue,
          });

          if (response.success) {
            showToast(t("system.attachment_context.toast-success"), "success");
            setAttachmentContextPercentage(numValue); // Ensure we display the validated number
            setHasAttachmentContextChanges(false);
          } else {
            showToast(t("system.attachment_context.toast-error"), "error");
            const settings = (await Admin.systemPreferences())?.settings;
            setAttachmentContextPercentage(
              settings?.attachment_context_percentage || 70
            );
          }
        } catch {
          showToast(t("system.attachment_context.toast-error"), "error");
          const settings = (await Admin.systemPreferences())?.settings;
          setAttachmentContextPercentage(
            settings?.attachment_context_percentage || 70
          );
        }
      }
      if (hasInvoiceLoggingChanges) {
        await handleInvoiceLoggingSubmit();
      }
      if (hasForcedInvoiceLoggingChanges) {
        await handleForceInvoiceLoggingSubmit();
      }
      if (hasRexorLinkageChanges) {
        await handleRexorLinkageSubmit();
      }
      if (feedbackChange) {
        await handleFeedbackSubmit();
      }
      setHasChanges(false);
      setHasQuraChanges(false);
      setFeedbackChange(false);
      setCitationChanges(false);
      setPromptOutputChange(false);
      setHasInvoiceLoggingChanges(false);
      setHasForcedInvoiceLoggingChanges(false);
      setHasRexorLinkageChanges(false);
      setHasRequestLegalAssistanceChanges(false);
      setHasUniversityModeChanges(false);
    } catch (error) {
      console.error("Error while saving preferences:", error);
      showToast(t("show-toast.preferences-save-error"), "error");
    } finally {
      setSaving(false);
    }
  };

  const handleInvoiceLoggingChange = () => {
    setInvoiceLogging(!invoiceLogging);
    setHasInvoiceLoggingChanges(true);
  };

  const handleForcedInvoiceLoggingChange = () => {
    setForcedInvoiceLogging(!forcedInvoiceLogging);
    setHasForcedInvoiceLoggingChanges(true);
  };

  const handleRexorLinkageChange = () => {
    setRexorLinkage(!rexorLinkage);
    setHasRexorLinkageChanges(true);
  };

  const handleFeedbackChange = (e) => {
    const isChecked = e.target.checked;
    setUseFeedback(isChecked);
    setFeedbackChange(true);
  };

  const handlePromptOutputChange = () => {
    setUsePromptOutput(!usePromptOutput);
    setPromptOutputChange(true);
  };

  const handleMaxTokensPerUserChange = (e) => {
    const value = Number(e.target.value) || 1;
    setMaxTokensPerUser(value);
    setHasChanges(true);
  };

  useEffect(() => {
    const fetchSettings = async () => {
      setLoading(true);
      try {
        const storedQuraStatus = localStorage.getItem("quraStatus");
        if (storedQuraStatus) {
          const parsedQuraStatus = JSON.parse(storedQuraStatus);
          setUseQura(parsedQuraStatus.qura);
        } else {
          const quraStatus = await System.isQura();
          if (quraStatus && typeof quraStatus === "object") {
            setUseQura(quraStatus.qura);
            localStorage.setItem("quraStatus", JSON.stringify(quraStatus));
          } else {
            console.warn("Qura Status is not an object:", quraStatus);
            setUseQura(false);
          }
        }

        const storedCopyOption = localStorage.getItem("copyOption");
        if (storedCopyOption) {
          setCopyOption(JSON.parse(storedCopyOption));
        } else {
          const copyOption = (await System.getCopyOption()) || "question";
          setCopyOption(copyOption);
          localStorage.setItem("copyOption", JSON.stringify(copyOption));
        }
      } catch {
        console.error("Error fetching settings:");
        showToast(t("show-toast.error-fetching-settings"), "error");
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [t]);

  useEffect(() => {
    async function fetchSettings() {
      try {
        const response = await Admin.systemPreferences();
        if (!response || !response.settings) {
          showToast(t("show-toast.error-fetching-settings"), "error");
          return;
        }
        const { settings } = response;

        // Convert string "true"/"false" to boolean for limit_user_messages
        setMessageLimit({
          enabled: settings.limit_user_messages === "true",
          limit: Number(settings.message_limit) || 10,
        });
        setMaxTokensPerUser(settings.max_tokens_per_user || 1);
        setContextWindowPercentage(
          settings.dynamic_context_window_percentage || 70
        );
        setAttachmentContextPercentage(
          settings.attachment_context_percentage || 70
        );
      } catch {
        showToast(t("show-toast.error-fetching-settings"), "error");
      }
    }
    fetchSettings();
  }, [t]);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const enabled = await System.isPromptOutputLoggingEnabled();
        const citationEnabled = await System.isCitationButton();
        setPromptOutputEnabled(enabled);
        setUsePromptOutput(enabled);
        setUseCitation(citationEnabled);
      } catch {
        showToast(t("show-toast.error-fetching-prompt-logging"), "error");
      }
    };
    fetchSettings();
  }, [t]);

  useEffect(() => {
    async function fetchSettings() {
      try {
        // Using already fetched store values or falling back to API calls if needed
        const [citationEnabled] = await Promise.all([
          System.isCitationButton(),
        ]);

        setCitationEnabled(citationEnabled);
        setUseCitation(citationEnabled);
      } catch {
        showToast(t("show-toast.error-loading-settings"), "error");
      }
    }
    fetchSettings();
  }, [t]);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const performLegalTaskResponse = await System.getPerformLegalTask();
        const performLegalTaskEnabled = performLegalTaskResponse?.enabled;
        const userAccessEnabled = performLegalTaskResponse?.allowUserAccess;

        setUsePerformLegalTask(performLegalTaskEnabled);
        setAllowUserAccess(userAccessEnabled);
      } catch {
        showToast(t("performLegalTask.failureUpdateMessage"), "error");
      }
    };

    fetchSettings();
  }, [t]);

  useEffect(() => {
    const fetchFeedbackStatus = async () => {
      try {
        await useSystemSettingsStore.getState().fetchAllSettings(true);
        const feedbackValue = useSystemSettingsStore
          .getState()
          .getSetting("feedback-enabled");
        setUseFeedback(feedbackValue?.enabled ?? feedbackValue ?? false);
      } catch (error) {
        console.error("Error fetching feedback status:", error);
        showToast(t("feedback.error-fetching"), "error");
      }
    };
    fetchFeedbackStatus();
  }, [t]);

  const handleInvoiceLoggingSubmit = async (e) => {
    if (e) e.preventDefault();

    setSaving(true);

    try {
      const success = await useSystemSettingsStore
        .getState()
        .updateSetting("invoice-logging", { invoice: invoiceLogging });

      if (success) {
        showToast(
          t("show-toast.invoice-logging-state-updated", {
            state: `${invoiceLogging ? "enabled" : "disabled"}`,
          }),
          "success"
        );
      } else {
        showToast(t("show-toast.invoice-logging-state-update-error"), "error");
      }
    } catch (error) {
      showToast(
        t("show-toast.invoice-logging-state-update-error", { error }),
        "error"
      );
    } finally {
      setSaving(false);
    }
  };

  const handleForceInvoiceLoggingSubmit = async (e) => {
    if (e) e.preventDefault();

    setSaving(true);

    try {
      const success = await useSystemSettingsStore
        .getState()
        .updateSetting("force-invoice-logging", {
          isForcedinvoiceLogging: forcedInvoiceLogging,
        });

      if (success) {
        showToast(
          t("show-toast.invoice-logging-state-updated", {
            state: `${forcedInvoiceLogging ? "enabled" : "disabled"}`,
          }),
          "success"
        );
      } else {
        showToast(t("show-toast.invoice-logging-state-update-error"), "error");
      }
    } catch (error) {
      showToast(
        t("show-toast.invoice-logging-state-update-error", { error }),
        "error"
      );
    } finally {
      setSaving(false);
    }
  };

  const handleRexorLinkageSubmit = async (e) => {
    if (e) e.preventDefault();

    setSaving(true);

    try {
      const success = await useSystemSettingsStore
        .getState()
        .updateSetting("rexor-linkage", { rexorLinkage });

      if (success) {
        showToast(
          t("show-toast.rexor-linkage-state-updated", {
            state: t(`statuses.${rexorLinkage ? "enabled" : "disabled"}`),
          }),
          "success"
        );
      } else {
        showToast(t("show-toast.rexor-linkage-state-update-error"), "error", {
          clear: true,
        });
      }
    } catch (error) {
      showToast(
        t("show-toast.rexor-linkage-state-update-error", { error }),
        "error",
        { clear: true }
      );
    } finally {
      setSaving(false);
    }
  };

  const handleFeedbackSubmit = async (e) => {
    if (e) e.preventDefault();

    setSaving(true);
    setFeedbackChange(false);

    const data = {
      enabled: useFeedback,
    };

    try {
      const success = await useSystemSettingsStore
        .getState()
        .updateSetting("feedback-enabled", data);

      if (success) {
        showToast(t("feedback-settings.successMessage"), "success");
      } else {
        console.error("Failed to update feedback setting");
        showToast(t("feedback-settings.failureUpdateMessage"), "error");
        setUseFeedback(!useFeedback);
      }
    } catch (error) {
      console.error("Error updating feedback setting:", error);
      showToast(t("feedback-settings.errorSubmitting"), "error");
      setUseFeedback(!useFeedback);
    } finally {
      setSaving(false);
    }
  };

  const handlePromptOutputSubmit = async (e) => {
    if (e) e.preventDefault();

    setSaving(true);
    setPromptOutputChange(false);

    const data = {
      enabled: usePromptOutput,
    };

    try {
      const { success, error } = await System.setPromptOutputLogging(data);
      if (success) {
        showToast(
          t(
            `toast.prompt-output-logging.${usePromptOutput ? "enabled" : "disabled"}`
          ),
          "success"
        );
        setPromptOutputEnabled(usePromptOutput);
      } else {
        console.error("Failed to update prompt output logging setting:", error);
        showToast(t("toast.prompt-output-logging.error"), "error");
        setUsePromptOutput(promptOutputEnabled);
      }
    } catch {
      showToast(t("toast.prompt-output-logging.error"), "error");
      setUsePromptOutput(promptOutputEnabled);
    } finally {
      setSaving(false);
    }
  };

  const handlePerformLegalTaskSubmit = async (e) => {
    if (e) e.preventDefault();

    setSaving(true);
    setPerformLegalTaskChange(false);

    const data = {
      enabled: usePerformLegalTask,
      allowUserAccess: allowUserAccess,
    };

    try {
      const { success, error } = await System.setPerformLegalTask(data);
      if (!success && error) {
        console.error("Failed to update perform legal task setting:", error);
      }
      const statusKey = usePerformLegalTask
        ? "statuses.enabled"
        : "statuses.disabled";
      if (success) {
        showToast(
          t("performLegalTask.successMessage", { status: t(statusKey) }),
          "success"
        );
      } else {
        showToast(t("performLegalTask.failureUpdateMessage"), "error");
        setUsePerformLegalTask(!usePerformLegalTask);
        setAllowUserAccess(!allowUserAccess);
      }
    } catch {
      showToast(t("performLegalTask.errorSubmitting"), "error");
      setUsePerformLegalTask(!usePerformLegalTask);
      setAllowUserAccess(!allowUserAccess);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="transition-all duration-500 relative md:ml-[2px] md:mr-[8px] md:my-[16px] md:rounded-[26px] p-[18px] overflow-y-scroll">
        <div className="h-full flex justify-center items-center">
          <PreLoader />
        </div>
      </div>
    );
  }

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <form
            onSubmit={handleSubmit}
            className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4"
          >
            {/* Header Section */}
            <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-4">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col">
                <div className="items-center">
                  <p className="text-lg leading-6 font-bold text-foreground">
                    {t("system.attachment_context.title")}
                  </p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("system.attachment_context.desc")}
                </p>
              </div>
            </div>

            {/* Save Changes Button */}
            <div className="w-full justify-end flex absolute right-5">
              {(hasChanges ||
                citationChanges ||
                feedbackChange ||
                hasQuraChanges ||
                promptOutputChange ||
                performLegalTaskChange ||
                hasInvoiceLoggingChanges ||
                hasForcedInvoiceLoggingChanges ||
                hasRexorLinkageChanges ||
                hasContextWindowChanges ||
                hasAttachmentContextChanges ||
                hasRequestLegalAssistanceChanges ||
                hasUniversityModeChanges) && (
                <Button type="submit" className="mr-0 -mb-14">
                  {saving ? t("common.saving") : t("common.save")}
                </Button>
              )}
            </div>

            {/* Message Limit Section */}
            <div className="mb-4 border-bottom border-top pt-4 pb-4">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("system.limit.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("system.limit.desc-limit")}
                </p>

                <Toggle
                  label={`${t("system.limit.label")}: ${t(
                    `statuses.${messageLimit.enabled ? "enabled" : "disabled"}`
                  )}`}
                  checked={Boolean(messageLimit.enabled)}
                  onCheckedChange={(checked) => {
                    setMessageLimit((prev) => ({
                      ...prev,
                      enabled: checked,
                    }));
                    setHasChanges(true);
                  }}
                  disabled={saving}
                  className="mt-3"
                />
              </div>
              {messageLimit.enabled && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-foreground">
                    {t("system.limit.per-day")}
                  </label>
                  <div className="relative mt-2">
                    <input
                      type="number"
                      name="message_limit"
                      onScroll={(e) => e.target.blur()}
                      onChange={(e) => {
                        setMessageLimit({
                          enabled: true,
                          limit: Number(e.target.value) || 0,
                        });
                        setHasChanges(true);
                      }}
                      value={messageLimit.limit}
                      min={1}
                      max={300}
                      className="dark-input-mdl w-1/3 rounded-md bg-transparent py-2 pl-6 pr-10 text-foreground outline-none focus:outline-none"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Dynamic Context Window Percentage Section */}
            <div className="mb-4 border-bottom pb-3">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("system.context_window.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("system.context_window.desc")}
                </p>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-foreground">
                    {t("system.context_window.label")}
                  </label>
                  <div className="relative mt-2">
                    <input
                      type="number"
                      name="context_window_percentage"
                      onScroll={(e) => e.target.blur()}
                      onChange={(e) => {
                        const value = e.target.value;
                        // Only allow numbers and empty string
                        if (value === "" || /^\d*$/.test(value)) {
                          setContextWindowPercentage(value);
                          setHasContextWindowChanges(true);
                        }
                      }}
                      value={
                        contextWindowPercentage === ""
                          ? ""
                          : contextWindowPercentage
                      }
                      min={10}
                      max={100}
                      className="dark-input-mdl w-1/3 rounded-md bg-transparent py-2 pl-6 pr-10 text-foreground outline-none focus:outline-none"
                    />
                    <span className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      %
                    </span>
                  </div>
                  <small className="text-[12px] text-foreground mt-1 block">
                    {t("system.context_window.help")}
                  </small>
                </div>
              </div>
            </div>

            {/* Attachment Context Window Percentage Section */}
            <div className="mb-4 border-bottom pb-3">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("system.attachment_context.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("system.attachment_context.desc")}
                </p>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-foreground">
                    {t("system.attachment_context.label")}
                  </label>
                  <div className="relative mt-2">
                    <input
                      type="number"
                      name="attachment_context_percentage"
                      onScroll={(e) => e.target.blur()}
                      onChange={(e) => {
                        const value = e.target.value;
                        // Only allow numbers and empty string
                        if (value === "" || /^\d*$/.test(value)) {
                          setAttachmentContextPercentage(value);
                          setHasAttachmentContextChanges(true);
                        }
                      }}
                      value={
                        attachmentContextPercentage === ""
                          ? ""
                          : attachmentContextPercentage
                      }
                      min={10}
                      max={80}
                      className="dark-input-mdl w-1/3 rounded-md bg-transparent py-2 pl-6 pr-10 text-foreground outline-none focus:outline-none"
                    />
                    <span className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      %
                    </span>
                  </div>
                  <small className="text-[12px] text-foreground mt-1 block">
                    {t("system.attachment_context.help")}
                  </small>
                </div>
              </div>
            </div>

            {/* Max Tokens Per User Section */}
            <div className="mb-4 border-bottom pb-3">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("system.max_tokens.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("system.max_tokens.desc")}
                </p>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-foreground">
                    {t("system.max_tokens.label")}
                  </label>
                  <div className="relative mt-2">
                    <input
                      type="number"
                      name="max_tokens_per_user"
                      onScroll={(e) => e.target.blur()}
                      onChange={handleMaxTokensPerUserChange}
                      value={maxTokensPerUser}
                      min={1}
                      className="dark-input-mdl w-1/3 rounded-md bg-transparent py-2 pl-6 pr-10 text-foreground outline-none focus:outline-none"
                    />
                  </div>
                  <small className="text-[12px] text-foreground mt-1 block">
                    {t("system.max_tokens.help")}
                  </small>
                </div>
              </div>
            </div>

            {/* Show citation Section */}
            <div className="mb-4">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("system.source-highlighting.title")}
                </h2>
                <p className="text-foreground text-xs">
                  {t("system.source-highlighting.description")}
                </p>
                <Toggle
                  label={`${t("system.source-highlighting.label")}${t(useCitation ? "statuses.enabled" : "statuses.disabled")}`}
                  checked={useCitation}
                  onCheckedChange={(checked) => {
                    setUseCitation(checked);
                    setCitationChanges(true);
                  }}
                  disabled={saving}
                  className="mt-3"
                />
              </div>
            </div>

            {/* Prompt output logging */}
            <div className="mb-4 border-top pt-4">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("promptLogging.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("promptLogging.description")}
                </p>

                <Toggle
                  label={`${t("promptLogging.label")}: ${t(
                    `statuses.${usePromptOutput ? "enabled" : "disabled"}`
                  )}`}
                  checked={Boolean(usePromptOutput)}
                  onCheckedChange={handlePromptOutputChange}
                  disabled={saving}
                  className="mt-3"
                />
              </div>
            </div>

            {/* Perform legal task */}
            <div className="mb-4 border-top pt-4">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("performLegalTask.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("performLegalTask.description")}
                </p>
              </div>

              <Toggle
                label={`${t("performLegalTask.title")}: ${t(
                  `statuses.${usePerformLegalTask ? "enabled" : "disabled"}`
                )}`}
                checked={Boolean(usePerformLegalTask)}
                onCheckedChange={(e) => handlePerformLegalTaskChange(e)}
                disabled={saving}
                className="mt-3"
              />

              {usePerformLegalTask && (
                <div className="mt-4 ml-4 border-l-2 border-gray-200 pl-4">
                  <div className="flex flex-col gap-y-1">
                    <h3 className="text-sm leading-6 font-semibold text-foreground">
                      {t("userAccess.title")}
                    </h3>
                    <p className="text-xs leading-[18px] font-base text-foreground">
                      {t("userAccess.description")}
                    </p>
                    <Toggle
                      label={`${t("userAccess.label")}: ${t(
                        `statuses.${allowUserAccess ? "enabled" : "disabled"}`
                      )}`}
                      checked={Boolean(allowUserAccess)}
                      onCheckedChange={(checked) => {
                        setAllowUserAccess(checked);
                        setPerformLegalTaskChange(true);
                      }}
                      disabled={saving}
                      className="mt-3"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* User feedback */}
            <div className="mb-4 border-top pt-4">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("feedback-settings.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("feedback-settings.description")}
                </p>
                <div className="mt-2">
                  <Toggle
                    checked={Boolean(useFeedback)}
                    onChange={handleFeedbackChange}
                    disabled={saving}
                  />
                </div>
              </div>
              <small className="text-[12px] text-foreground">
                {t("feedback-settings.title")}:{" "}
                <b>
                  {t(
                    `promptLogging.state.${useFeedback ? "enabled" : "disabled"}`
                  )}
                </b>
              </small>
            </div>

            {/* Qura Section */}
            <div className="relative w-full max-h-full border-top border-bottom pb-3 pt-4">
              <div className="relative rounded-lg">
                <div className="space-y-6 flex h-full w-full">
                  <div className="w-full flex flex-col gap-y-4">
                    <div className="flex flex-col gap-y-1">
                      <h2 className="text-base leading-6 font-bold text-foreground">
                        {t("qura.qura-status")}{" "}
                        {t(`statuses.${useQura ? "enabled" : "disabled"}`)}.
                      </h2>
                      <p className="text-foreground text-xs">
                        {t("qura.role-description")}{" "}
                        <a
                          href="https://www.qura.law/"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="font-thin text-blue-400"
                        >
                          Qura.law
                        </a>
                      </p>
                      <Toggle
                        checked={useQura}
                        onCheckedChange={(checked) => {
                          setUseQura(checked);
                          setHasQuraChanges(true);
                        }}
                        disabled={saving}
                        className="mt-3"
                      />

                      {/* Radio buttons for Copy Option */}
                      {useQura && (
                        <div className="flex flex-col gap-y-2 mt-4">
                          <label className="text-foreground font-medium text-sm">
                            {t("qura.copy-option")}
                          </label>
                          <div className="flex items-center gap-x-4">
                            <label className="flex items-center gap-x-2">
                              <input
                                type="radio"
                                name="copyOption"
                                value="question"
                                checked={copyOption === "question"}
                                onChange={() =>
                                  handleCopyOptionChange("question")
                                }
                                className="form-radio"
                              />
                              <span className="text-foreground">
                                {t("qura.option-quest")}
                              </span>
                            </label>
                            <label className="flex items-center gap-x-2">
                              <input
                                type="radio"
                                name="copyOption"
                                value="response"
                                checked={copyOption === "response"}
                                onChange={() =>
                                  handleCopyOptionChange("response")
                                }
                                className="form-radio"
                              />
                              <span className="text-foreground">
                                {t("qura.option-resp")}
                              </span>
                            </label>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Request Legal Assistance Section */}
            <div className="mb-4 border-bottom pb-3">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("request-legal-assistance.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("request-legal-assistance.description")}
                </p>
              </div>
              <Toggle
                label={`${t("request-legal-assistance.title")}: ${t(requestLegalAssistance.enabled ? "statuses.enabled" : "statuses.disabled")}`}
                checked={requestLegalAssistance.enabled}
                onCheckedChange={(checked) => {
                  handleRequestLegalAssistanceChange("enabled")(checked);
                }}
                disabled={saving}
                className="mt-2"
              />
              {requestLegalAssistance.enabled && (
                <div className="mt-4 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-foreground">
                      {t("request-legal-assistance.law-firm-name")}
                    </label>
                    <div className="relative mt-2">
                      <input
                        type="text"
                        value={requestLegalAssistance.lawFirmName}
                        onChange={(e) =>
                          handleRequestLegalAssistanceChange("lawFirmName")(
                            e.target.value
                          )
                        }
                        placeholder={t(
                          "request-legal-assistance.law-firm-placeholder"
                        )}
                        className="dark-input-mdl w-full rounded-md bg-transparent py-2 pl-6 pr-10 text-foreground outline-none focus:outline-none"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-foreground">
                      {t("request-legal-assistance.email")}
                    </label>
                    <div className="relative mt-2">
                      <input
                        type="email"
                        value={requestLegalAssistance.email}
                        onChange={(e) =>
                          handleRequestLegalAssistanceChange("email")(
                            e.target.value
                          )
                        }
                        placeholder={t(
                          "request-legal-assistance.email-placeholder"
                        )}
                        className="dark-input-mdl w-full rounded-md bg-transparent py-2 pl-6 pr-10 text-foreground outline-none focus:outline-none"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* University Mode Section - Adjusted Styling */}
            <div className="mb-4 border-top pt-4">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("admin.system.universityMode.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("admin.system.universityMode.description")}
                </p>
              </div>
              <Toggle
                label={`${t("admin.system.universityMode.enable")}: ${t(universityMode ? "statuses.enabled" : "statuses.disabled")}`}
                checked={universityMode}
                onCheckedChange={handleUniversityModeChange}
                disabled={saving}
                className="mt-3"
              />
            </div>

            <UsageRegistration
              invoiceLogging={invoiceLogging}
              forcedInvoiceLogging={forcedInvoiceLogging}
              rexorLinkage={rexorLinkage}
              handleInvoiceLoggingChange={handleInvoiceLoggingChange}
              handleForcedInvoiceLoggingChange={
                handleForcedInvoiceLoggingChange
              }
              handleRexorLinkageChange={handleRexorLinkageChange}
            />
          </form>
        </div>
      </div>
    </div>
  );
};

export default AdminSystem;
