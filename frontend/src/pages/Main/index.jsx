import React from "react";
import Sidebar from "@/components/Sidebar";
import PasswordModal, { usePasswordModal } from "@/components/Modals/Password";
import { FullScreenLoader } from "@/components/Preloader";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import WelcomeMessages from "@/components/MainPage/WelcomeMessages";
import PopulatedWorkspaces from "@/components/MainPage/PopulatedWorkspaces";
import ExamplePrompts from "@/components/MainPage/ExamplePrompts";
import FeatureCards from "@/components/MainPage/FeatureCards";

export default function Main() {
  const { loading, requiresAuth, mode } = usePasswordModal();

  if (loading) return <FullScreenLoader />;
  if (requiresAuth !== false) {
    return <>{requiresAuth !== null && <PasswordModal mode={mode} />}</>;
  }

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="flex flex-col w-full p-6 md:p-16 md:pb-24 overflow-y-auto bg-background">
          <WelcomeMessages />
          <FeatureCards />
          <PopulatedWorkspaces />
          <ExamplePrompts />
        </div>
      </div>
    </div>
  );
}
