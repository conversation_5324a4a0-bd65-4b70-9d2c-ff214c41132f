import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import Admin from "@/models/admin";
import Toggle from "@/components/ui/Toggle";

export default function ChatPdrSetting({ workspace, setHasChanges }) {
  const { t } = useTranslation();
  const [globalPdrEnabled, setGlobalPdrEnabled] = useState(false);

  useEffect(() => {
    async function checkGlobalPdr() {
      const settings = await Admin.getPdrSettings();
      setGlobalPdrEnabled(settings?.globalPdrOverride ?? true);
    }
    checkGlobalPdr();
  }, []);

  return (
    <div className="flex flex-col gap-y-2 mb-6">
      <div className="flex flex-col">
        <label className="block input-label text-foreground">
          {t("chat.dynamic-pdr.title")}
        </label>
        <p className="text-foreground text-opacity-60 text-xs font-medium">
          {globalPdrEnabled
            ? t("chat.dynamic-pdr.global-enabled")
            : t("chat.dynamic-pdr.description")}
        </p>
      </div>
      <div className="flex items-center">
        <div
          className={`relative inline-flex items-center ${globalPdrEnabled ? "cursor-not-allowed opacity-60" : "cursor-pointer"}`}
        >
          <Toggle
            checked={globalPdrEnabled || workspace?.pdr || false}
            onChange={(e) => {
              if (!globalPdrEnabled) {
                workspace.pdr = e.target.checked;
                setHasChanges(true);
              }
            }}
            disabled={globalPdrEnabled}
          />
        </div>
      </div>
    </div>
  );
}
