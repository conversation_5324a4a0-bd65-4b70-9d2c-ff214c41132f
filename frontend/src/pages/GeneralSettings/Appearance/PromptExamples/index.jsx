import System from "@/models/system";
import showToast from "@/utils/toast";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import { Plus } from "@phosphor-icons/react";
import { icons } from "@/utils/examplePromptIcons";
import WorkspaceSelector from "./WorkspaceSelector";

export default function PromptExamples() {
  const [hasChanges, setHasChanges] = useState(false);
  const [examples, setExamples] = useState([]);
  const { t } = useTranslation();

  useEffect(() => {
    async function fetchExamples() {
      const examples = await System.getPromptExamples();

      if (examples.length === 0) {
        setExamples([
          { title: "", area: "", prompt: "", icon: "", workspaceSlug: "" },
        ]);
      } else {
        setExamples(examples);
      }
    }
    fetchExamples();
  }, []);

  const handleExampleChange = (index, field, value) => {
    setHasChanges(true);
    setExamples((prev) => {
      const newExamples = [...prev];
      if (!newExamples[index]) {
        newExamples[index] = {};
      }
      newExamples[index] = {
        ...newExamples[index],
        [field]: value,
      };
      return newExamples;
    });
  };

  const validateExamples = (examples) => {
    const requiredFields = ["title", "area", "prompt", "icon", "workspaceSlug"];

    for (let i = 0; i < examples.length; i++) {
      const example = examples[i];
      const missingFields = requiredFields.filter((field) => !example[field]);

      if (missingFields.length > 0) {
        return {
          isValid: false,
          error: t("toast.settings.prompt-examples-validation", {
            number: i + 1,
            fields: missingFields.join(", "),
          }),
        };
      }
    }

    return { isValid: true };
  };

  const handleExampleSave = async () => {
    const validation = validateExamples(examples);
    if (!validation.isValid) {
      showToast(validation.error, "error");
      return;
    }

    const { success, error } = await System.setPromptExamples(examples);
    if (!success) {
      showToast(t("toast.settings.prompt-examples-failed", { error }), "error");
      return;
    }
    showToast(t("toast.settings.prompt-examples-success"), "success");
    setHasChanges(false);
  };

  const addExample = () => {
    setExamples((prev) => [
      ...prev,
      { title: "", area: "", prompt: "", icon: "", workspaceSlug: "" },
    ]);
  };

  const removeExample = (index) => {
    setHasChanges(true);
    setExamples((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="border-bottom py-6 mb-6">
      <div className="flex flex-col gap-y-1 w-full">
        <h2 className="text-base leading-6 font-bold text-foreground">
          {t("appearance.prompt-examples.title")}
        </h2>
        <p className="text-xs leading-[18px] font-base text-foreground">
          {t("appearance.prompt-examples.description")}
        </p>
      </div>
      <div className="flex flex-col items-start gap-y-6 w-full md:w-[54%] pt-4">
        {examples.map((example, index) => (
          <div
            key={index}
            className="flex flex-col gap-y-4 w-full border p-4 rounded-lg"
          >
            <div className="flex justify-between items-center">
              <h3 className="font-medium text-foreground">
                {t("appearance.prompt-examples.example", { number: index + 1 })}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => removeExample(index)}
                className="text-red-500"
              >
                {t("appearance.prompt-examples.remove")}
              </Button>
            </div>
            <div className="flex flex-col gap-y-2">
              <label className="text-sm font-medium text-foreground">
                {t("appearance.prompt-examples.title-field")}
              </label>
              <input
                type="text"
                value={example.title || ""}
                onChange={(e) =>
                  handleExampleChange(index, "title", e.target.value)
                }
                className="w-full p-2 rounded-md dark-input-mdl text-foreground"
              />
            </div>
            <div className="flex flex-col gap-y-2">
              <label className="text-sm font-medium text-foreground">
                {t("appearance.prompt-examples.area")}
              </label>
              <input
                type="text"
                value={example.area || ""}
                onChange={(e) =>
                  handleExampleChange(index, "area", e.target.value)
                }
                className="w-full p-2 rounded-md dark-input-mdl text-foreground"
              />
            </div>
            <div className="flex flex-col gap-y-2">
              <label className="text-sm font-medium text-foreground">
                {t("appearance.prompt-examples.prompt")}
              </label>
              <textarea
                value={example.prompt || ""}
                onChange={(e) =>
                  handleExampleChange(index, "prompt", e.target.value)
                }
                rows={3}
                className="w-full p-2 rounded-md dark-input-mdl text-foreground resize-none overflow-y-auto"
              />
            </div>
            <WorkspaceSelector
              value={example.workspaceSlug}
              onChange={(value) =>
                handleExampleChange(index, "workspaceSlug", value)
              }
            />
            <div className="flex flex-col gap-y-2">
              <label className="text-sm font-medium text-foreground">
                {t("appearance.prompt-examples.icon")}
              </label>
              <div className="flex flex-wrap justify-between">
                {icons.map(({ icon: Icon, name }) => (
                  <Button
                    key={name}
                    variant="outline"
                    size="icon"
                    onClick={() => handleExampleChange(index, "icon", name)}
                    className={`${
                      example.icon === name
                        ? "border-2 border-primary bg-secondary"
                        : ""
                    }`}
                  >
                    <Icon className="!size-6" />
                  </Button>
                ))}
              </div>
            </div>
          </div>
        ))}
        <div className="flex gap-4">
          <Button variant="outline" size="sm" onClick={addExample}>
            <Plus />
            {t("appearance.prompt-examples.add")}
          </Button>

          <Button
            size="sm"
            className="duration-300 gap-x-2"
            onClick={handleExampleSave}
            disabled={!hasChanges}
          >
            {t("appearance.prompt-examples.save")}
          </Button>
        </div>
      </div>
    </div>
  );
}
