import React, { useState, useEffect } from "react";
import Appearance from "@/models/appearance";
import Toggle from "@/components/ui/Toggle";
export default function ShowScrollbar() {
  const [saving, setSaving] = useState(false);
  const [showScrollbar, setShowScrollbar] = useState(false);
  const handleChange = async (e) => {
    const newValue = e.target.checked;
    setShowScrollbar(newValue);
    setSaving(true);
    try {
      Appearance.updateSettings({ showScrollbar: newValue });
    } catch (error) {
      console.error("Failed to update appearance settings:", error);
      setShowScrollbar(!newValue);
    }
    setSaving(false);
  };
  useEffect(() => {
    function fetchSettings() {
      const settings = Appearance.getSettings();
      setShowScrollbar(settings.showScrollbar);
    }
    fetchSettings();
  }, []);
  return (
    <div className="flex flex-col w-full gap-y-4 mt-6 border-bottom pb-2">
      <div className="flex flex-col gap-y-1">
        <h2 className="text-base leading-6 font-bold text-foreground">
          Show chat window scrollbar
        </h2>
        <p className="text-xs leading-[18px] font-base text-foreground">
          Enable or disable the scrollbar in the chat window
        </p>
        <div className="mt-2">
          <Toggle
            id="show_scrollbar"
            checked={showScrollbar}
            onChange={handleChange}
            disabled={saving}
          />
        </div>
      </div>
    </div>
  );
}
