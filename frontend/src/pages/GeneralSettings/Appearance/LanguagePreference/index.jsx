import { useLanguageOptions } from "@/hooks/useLanguageOptions";
import { useTranslation } from "react-i18next";

export default function LanguagePreference() {
  const { t } = useTranslation();
  const {
    currentLanguage,
    supportedLanguages,
    getLanguageName,
    changeLanguage,
  } = useLanguageOptions();

  return (
    <div className="w-full">
      <div className="flex flex-col gap-y-1">
        <h2 className="text-base leading-6 font-bold text-foreground">
          {t("appearance.display.title")}
        </h2>
        <p className="text-xs leading-[18px] font-base text-foreground">
          {t("appearance.display.description")}
        </p>
      </div>
      <div className="flex items-center gap-x-4">
        <select
          name="userLang"
          className="w-full mt-2 px-2 dark-input-mdl text-foreground text-sm rounded-md block py-2"
          value={currentLanguage || "en"}
          onChange={(e) => changeLanguage(e.target.value)}
        >
          {supportedLanguages.map((lang) => {
            return (
              <option key={lang} value={lang} className="text-black">
                {getLanguageName(lang)}
              </option>
            );
          })}
        </select>
      </div>
    </div>
  );
}
