import { useEffect, useState } from "react";
import Sidebar from "@/components/SettingsSidebar";
import showToast from "@/utils/toast";
import System from "@/models/system";
import paths from "@/utils/paths";
import PreLoader from "@/components/Preloader";
import {
  getEmbeddingEnginePrivacy,
  getLLMSelectionPrivacy,
  getVectorDbPrivacy,
} from "@/pages/OnboardingFlow/Steps/DataHandling";
import { Link } from "react-router-dom";
import { ArrowLeft } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import Toggle from "@/components/ui/Toggle";

export default function PrivacyAndDataHandling() {
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation();

  useEffect(() => {
    async function fetchSettings() {
      setLoading(true);
      const settings = await System.keys();
      setSettings(settings);
      setLoading(false);
    }
    fetchSettings();
  }, []);

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
            <div className="w-full flex flex-row items-start gap-3.5 gap-y-1 pb-3 border-bottom">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col">
                <div className="items-center flex gap-x-4">
                  <p className="text-lg leading-6 font-bold text-foreground">
                    {t("privacy.title")}
                  </p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("privacy.description")}
                </p>
              </div>
            </div>
            {loading ? (
              <div className="h-1/2 transition-all duration-500 relative md:ml-[2px] md:mr-[8px] md:my-[16px] md:rounded-[26px] p-[18px] overflow-y-scroll">
                <div className="w-full h-full flex justify-center items-center">
                  <PreLoader />
                </div>
              </div>
            ) : (
              <>
                <ThirdParty settings={settings} />
                <TelemetryLogs settings={settings} />
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function ThirdParty({ settings }) {
  const { t } = useTranslation();
  const llmChoice = settings?.LLMProvider || "openai";
  const embeddingEngine = settings?.EmbeddingEngine || "openai";
  const vectorDb = settings?.VectorDB || "lancedb";

  const LLM_SELECTION_PRIVACY = getLLMSelectionPrivacy(t);
  const VECTOR_DB_PRIVACY = getVectorDbPrivacy(t);
  const EMBEDDING_ENGINE_PRIVACY = getEmbeddingEnginePrivacy(t);

  return (
    <div className="py-3 w-full flex items-start justify-center flex-col gap-y-2 border-bottom">
      <div className="flex flex-col gap-8">
        <div className="flex flex-col gap-y-2 border-bottom pb-3">
          <div className="text-foreground text-base font-bold">
            {t("privacy.llm")}
          </div>
          <div className="flex items-center gap-2.5">
            <img
              src={LLM_SELECTION_PRIVACY[llmChoice].logo}
              alt="LLM Logo"
              className="w-8 h-8 rounded"
            />
            <p className="text-foreground text-sm font-bold">
              {LLM_SELECTION_PRIVACY[llmChoice].name}
            </p>
          </div>
          <ul className="flex flex-col list-disc">
            {LLM_SELECTION_PRIVACY[llmChoice].description.map((desc, index) => (
              <li key={index} className="text-foreground text-sm">
                {desc}
              </li>
            ))}
          </ul>
        </div>
        <div className="flex flex-col gap-y-2 border-bottom pb-4">
          <div className="text-foreground text-base font-bold">
            {t("privacy.embedding")}
          </div>
          <div className="flex items-center gap-2.5">
            <img
              src={EMBEDDING_ENGINE_PRIVACY[embeddingEngine].logo}
              alt="LLM Logo"
              className="w-8 h-8 rounded"
            />
            <p className="text-foreground text-sm font-bold">
              {EMBEDDING_ENGINE_PRIVACY[embeddingEngine].name}
            </p>
          </div>
          <ul className="flex flex-col list-disc">
            {EMBEDDING_ENGINE_PRIVACY[embeddingEngine].description.map(
              (desc, index) => (
                <li key={index} className="text-foreground text-sm">
                  {desc}
                </li>
              )
            )}
          </ul>
        </div>

        <div className="flex flex-col gap-y-2 pb-4">
          <div className="text-foreground text-base font-bold">
            {t("privacy.vector")}
          </div>
          <div className="flex items-center gap-2.5">
            <img
              src={VECTOR_DB_PRIVACY[vectorDb].logo}
              alt="LLM Logo"
              className="w-8 h-8 rounded"
            />
            <p className="text-foreground text-sm font-bold">
              {VECTOR_DB_PRIVACY[vectorDb].name}
            </p>
          </div>
          <ul className="flex flex-col list-disc">
            {VECTOR_DB_PRIVACY[vectorDb].description.map((desc, index) => (
              <li key={index} className="text-foreground text-sm">
                {desc}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}

function TelemetryLogs({ settings }) {
  const { t } = useTranslation();
  const [telemetry, setTelemetry] = useState(
    settings?.DisableTelemetry !== "true"
  );
  async function toggleTelemetry() {
    await System.updateSystem({
      DisableTelemetry: !telemetry ? "false" : "true",
    });
    setTelemetry(!telemetry);
    showToast(
      `Anonymous Telemetry has been ${!telemetry ? "enabled" : "disabled"}.`,
      "info",
      { clear: true }
    );
  }

  return (
    <div className="relative w-full max-h-full">
      <div className="relative rounded-lg">
        <div className="flex items-start justify-between px-6 py-2"></div>
        <div className="space-y-6 flex h-full w-full">
          <div className="w-full flex flex-col gap-y-4">
            <div className="">
              <label className="mb-2.5 block font-medium text-foreground">
                {t("privacy.anonymous")}
              </label>
              <Toggle checked={telemetry} onChange={toggleTelemetry} />
            </div>
          </div>
        </div>
        <div className="flex flex-col items-left space-y-2">
          <p className="text-foreground text-xs rounded-lg w-96">
            {t("privacy.desc-event")} <b>{t("privacy.desc-id")}</b>{" "}
            {t("privacy.desc-cont")}{" "}
            <a
              href="https://github.com/search?q=repo%3AI%2rahswe%20istlegal.sendTelemetry(&type=code"
              className="underline text-blue-400"
              target="_blank"
              rel="noreferrer"
            >
              {t("privacy.desc-git")}
            </a>
            .
          </p>
          <p className="text-foreground text-xs rounded-lg w-96">
            {t("privacy.desc-end")}{" "}
            <a
              href="mailto:<EMAIL>"
              className="underline text-blue-400"
              target="_blank"
              rel="noreferrer"
            >
              <EMAIL>
            </a>
            .
          </p>
        </div>
      </div>
    </div>
  );
}
