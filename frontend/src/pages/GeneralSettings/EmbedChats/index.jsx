import { useEffect, useState, useRef } from "react";
import Sidebar from "@/components/SettingsSidebar";
import * as Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import useQuery from "@/hooks/useQuery";
import ChatRow from "./ChatRow";
import Embed from "@/models/embed";
import paths from "@/utils/paths";
import { ArrowLeft, ArrowRight } from "@phosphor-icons/react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { CaretDown, Download } from "@phosphor-icons/react";
import showToast from "@/utils/toast";
import { saveAs } from "file-saver";
import System from "@/models/system";
import { Button } from "@/components/Button";

const exportOptions = {
  csv: {
    name: "CSV",
    mimeType: "text/csv",
    fileExtension: "csv",
    filenameFunc: () => {
      return `istlegal-embed-chats-${new Date().toLocaleDateString()}`;
    },
  },
  json: {
    name: "JSO<PERSON>",
    mimeType: "application/json",
    fileExtension: "json",
    filenameFunc: () => {
      return `istlegal-embed-chats-${new Date().toLocaleDateString()}`;
    },
  },
  jsonl: {
    name: "JSONL",
    mimeType: "application/jsonl",
    fileExtension: "jsonl",
    filenameFunc: () => {
      return `istlegal-embed-chats-${new Date().toLocaleDateString()}-lines`;
    },
  },
  jsonAlpaca: {
    name: "JSON (Alpaca)",
    mimeType: "application/json",
    fileExtension: "json",
    filenameFunc: () => {
      return `istlegal-embed-chats-${new Date().toLocaleDateString()}-alpaca`;
    },
  },
};

export default function EmbedChats() {
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef();
  const openMenuButton = useRef();
  const { t } = useTranslation();

  const handleDumpChats = async (exportType) => {
    const chats = await System.exportChats(exportType, "embed");
    if (chats) {
      const { name, mimeType, fileExtension, filenameFunc } =
        exportOptions[exportType];
      const blob = new Blob([chats], { type: mimeType });
      saveAs(blob, `${filenameFunc()}.${fileExtension}`);
      showToast(t("toast.export.embed-chats-success", { name }), "success");
    } else {
      showToast(t("toast.export.embed-chats-failed"), "error");
    }
  };
  const toggleMenu = () => {
    setShowMenu(!showMenu);
  };
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        !openMenuButton.current.contains(event.target)
      ) {
        setShowMenu(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[86px] md:py-4">
            <div className="w-full flex flex-row items-start gap-3.5 gap-y-1 pb-3 border-bottom border-opacity-10">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col">
                <div className="flex gap-x-4 items-center">
                  <p className="text-lg leading-6 font-bold text-foreground">
                    {t("embed-chats.title")}
                  </p>
                  <div className="absolute right-3">
                    <Button
                      ref={openMenuButton}
                      onClick={toggleMenu}
                      className="gap-x-2 py-1 primary-bg text-white text-xs font-semibold shadow-[0_4px_14px_rgba(0,0,0,0.25)] h-[30px] w-fit"
                    >
                      <Download size={18} weight="bold" />
                      {t("embed-chats.export")}
                      <CaretDown size={18} weight="bold" />
                    </Button>
                    <div
                      ref={menuRef}
                      className={`${
                        showMenu ? "slide-down" : "slide-up hidden"
                      } z-20 w-fit rounded-lg absolute top-full right-0 bg-secondary mt-2 shadow-md`}
                    >
                      <div className="py-2">
                        {Object.entries(exportOptions).map(([key, data]) => (
                          <Button
                            key={key}
                            onClick={() => {
                              handleDumpChats(key);
                              setShowMenu(false);
                            }}
                            className="w-full text-left px-4 py-2 text-white text-sm hover:bg-[#3D4147] justify-start"
                            variant="ghost"
                          >
                            {data.name}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("embed-chats.description")}
                </p>
              </div>
            </div>
            <ChatsContainer />
          </div>
        </div>
      </div>
    </div>
  );
}

function ChatsContainer() {
  const { t } = useTranslation();
  const query = useQuery();
  const [loading, setLoading] = useState(true);
  const [chats, setChats] = useState([]);
  const [offset, setOffset] = useState(Number(query.get("offset") || 0));
  const [canNext, setCanNext] = useState(false);

  const handlePrevious = () => {
    setOffset(Math.max(offset - 1, 0));
  };
  const handleNext = () => {
    setOffset(offset + 1);
  };

  const handleDeleteChat = (chatId) => {
    setChats((prevChats) => prevChats.filter((chat) => chat.id !== chatId));
  };

  useEffect(() => {
    async function fetchChats() {
      const { chats: _chats, hasPages = false } = await Embed.chats(offset);
      setChats(_chats);
      setCanNext(hasPages);
      setLoading(false);
    }
    fetchChats();
  }, [offset]);

  if (loading) {
    return (
      <Skeleton.default
        height="80vh"
        width="100%"
        highlightColor="#fff"
        baseColor="#e2f3fa"
        count={1}
        className="w-full p-4 rounded-b-2xl rounded-tr-2xl rounded-tl-sm mt-6"
        containerClassName="flex w-full"
      />
    );
  }

  return (
    <>
      <table className="w-full text-sm text-left rounded-lg mt-5">
        <thead className="text-foreground text-opacity-80 text-sm font-bold uppercase border-white border-b border-opacity-60">
          <tr>
            <th scope="col" className="px-6 py-3 rounded-tl-lg">
              {t("embed-chats.table.embed")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("embed-chats.table.sender")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("embed-chats.table.message")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("embed-chats.table.response")}
            </th>
            <th scope="col" className="px-6 py-3">
              {t("embed-chats.table.at")}
            </th>
            <th scope="col" className="px-6 py-3 rounded-tr-lg">
              {" "}
            </th>
          </tr>
        </thead>
        <tbody>
          {!!chats &&
            chats.map((chat) => (
              <ChatRow key={chat.id} chat={chat} onDelete={handleDeleteChat} />
            ))}
        </tbody>
      </table>
      <div className="flex w-full justify-between items-center">
        <Button
          onClick={handlePrevious}
          className="primary-bg text-white gap-x-2 disabled:invisible"
          disabled={offset === 0}
        >
          {" "}
          <ArrowLeft size={20} />
          {t("common.previous")}
        </Button>
        <Button
          onClick={handleNext}
          className="primary-bg text-white gap-x-2 disabled:invisible"
          disabled={!canNext}
        >
          {t("common.next")}
          <ArrowRight size={20} />
        </Button>
      </div>
    </>
  );
}
