import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, CopySimple, X } from "@phosphor-icons/react";
import showToast from "@/utils/toast";
import hljs from "highlight.js";
import "highlight.js/styles/github-dark-dimmed.min.css";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";

export default function CodeSnippetModal({ embed, isOpen, closeModal }) {
  const { t } = useTranslation();
  return (
    <Modal
      isOpen={isOpen}
      onClose={closeModal}
      title="Copy your embed code"
      footer={
        <Button variant="secondary" onClick={closeModal}>
          {t("button.cancel")}
        </Button>
      }
    >
      <div className="w-full flex flex-col gap-y-6">
        <ScriptTag embed={embed} />
      </div>
    </Modal>
  );
}

function createScriptTagSnippet(embed, scriptHost, serverHost) {
  return `<!--
Paste this script at the bottom of your HTML before the </body> tag.
See more style and config options on our docs
https://github.com/rahswe/istlegal/tree/master/embed/README.md
-->
<script
  data-embed-id="${embed.uuid}"
  data-base-api-url="${serverHost}/api/embed"
  src="${scriptHost}/embed/istlegal-chat-widget.min.js">
</script>
<!-- IST Legal (https://istlegal.com) -->
`;
}

const ScriptTag = ({ embed }) => {
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);
  const scriptHost = import.meta.env.DEV
    ? "http://localhost:3000"
    : window.location.origin;
  const serverHost = import.meta.env.DEV
    ? "http://localhost:3001"
    : window.location.origin;
  const snippet = createScriptTagSnippet(embed, scriptHost, serverHost);

  const handleClick = () => {
    window.navigator.clipboard.writeText(snippet);
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 2500);
    showToast(t("show-toast.snippet-copied"), "success", { clear: true });
  };

  return (
    <div>
      <div className="flex flex-col mb-2">
        <label className="block text-sm font-medium text-foreground">
          HTML Script Tag Embed Code
        </label>
        <p className="text-foreground text-xs">
          Have your workspace chat embed function like a help desk chat bottom
          in the corner of your website.
        </p>
        <a
          href="https://github.com/ISTLegal/ISTLegal/tree/master/embed/README.md"
          target="_blank"
          className="text-blue-600 hover:underline"
          rel="noreferrer"
        >
          View all style and configuration options &rarr;
        </a>
      </div>
      <Button
        disabled={copied}
        onClick={handleClick}
        className="disabled:border disabled:border-gray-300 border border-transparent relative w-full font-mono text-white p-2.5"
      >
        <div
          className="flex w-full text-left flex-col gap-y-1 pr-6 pl-4 whitespace-pre-line"
          dangerouslySetInnerHTML={{
            __html: hljs.highlight(snippet, {
              language: "html",
              ignoreIllegals: true,
            }).value,
          }}
        />
        {copied ? (
          <CheckCircle
            size={14}
            className="text-green-300 absolute top-2 right-2"
          />
        ) : (
          <CopySimple size={14} className="absolute top-2 right-2" />
        )}
      </Button>
    </div>
  );
};
