import { useEffect, useState } from "react";
import Sidebar from "@/components/SettingsSidebar";
import showToast from "@/utils/toast";
import Admin from "@/models/admin";
import paths from "@/utils/paths";
import PreLoader from "@/components/Preloader";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ArrowLeft } from "@phosphor-icons/react";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import Toggle from "@/components/ui/Toggle";
import useSystemSettingsStore from "@/stores/settingsStore";
import { useDocumentDraftingEnabled } from "@/stores/settingsStore";
import { Button } from "@/components/Button";

function isNullOrNaN(value) {
  if (value === null) return true;
  return isNaN(value);
}

// This from the document drafting page. the page content should start here.
export default function DocumentDrafting() {
  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <Sidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <DocumentDraftingPage />
        </div>
      </div>
    </div>
  );
}

function DocumentDraftingPage() {
  const { t } = useTranslation();
  const [saving, setSaving] = useState(false);
  const { isDocumentDrafting, isDocumentDraftingLinking } =
    useDocumentDraftingEnabled();
  const [isDocumentDraftingChecked, setIsDocumentDraftingChecked] =
    useState(false);
  const [isDocumentLinkingChecked, setIsDocumentLinkingChecked] =
    useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [loading, setLoading] = useState(true);
  const [ddSettings, setDDSettings] = useState({});

  const handleSubmit = async (e) => {
    if (e) e.preventDefault();
    setSaving(true);
    setHasChanges(false);

    try {
      const success = await useSystemSettingsStore
        .getState()
        .updateSetting("document-drafting", {
          isDocumentDrafting: isDocumentDraftingChecked,
          isDocumentDraftingLinking: isDocumentLinkingChecked,
        });
      if (!success) {
        throw new Error("Failed to save document drafting settings");
      }

      // Save DD settings
      const form = new FormData(e.target);
      await Admin.updateDDSettings({
        dd_vector_enabled: form.get("dd_vector_enabled") === "on",
        dd_memo_enabled: form.get("dd_memo_enabled") === "on",
        dd_base_enabled: form.get("dd_base_enabled") === "on",
        dd_linked_workspace_impact:
          form.get("dd_linked_workspace_impact") === "on",
        dd_vector_token_limit: isNullOrNaN(form.get("dd_vector_token_limit"))
          ? 5000
          : Number(form.get("dd_vector_token_limit")),
        dd_memo_token_limit: isNullOrNaN(form.get("dd_memo_token_limit"))
          ? 3000
          : Number(form.get("dd_memo_token_limit")),
        dd_base_token_limit: isNullOrNaN(form.get("dd_base_token_limit"))
          ? 2000
          : Number(form.get("dd_base_token_limit")),
      });

      showToast(t("dd-settings.toast-success"), "success");
      setIsDocumentDraftingChecked(isDocumentDrafting);

      // Reload to reflect all changes
      setTimeout(() => window.location.reload(), 1000);
    } catch (error) {
      console.error(error);
      showToast(error.message || t("dd-settings.toast-fail"), "error");
    } finally {
      setSaving(false);
    }
  };

  useEffect(() => {
    async function fetchSettings() {
      try {
        setIsDocumentDraftingChecked(isDocumentDrafting);
        setIsDocumentLinkingChecked(isDocumentDraftingLinking);

        const ddSettings = await Admin.getDDSettings();
        setDDSettings(ddSettings ?? {});
      } catch (error) {
        console.error("Failed to fetch settings:", error);
      }
    }

    setLoading(true);
    fetchSettings().then(() => setLoading(false));
  }, []);

  if (loading) {
    return (
      <div className="transition-all duration-500 relative md:ml-[2px] md:mr-[8px] md:my-[16px] md:rounded-[26px] p-[18px] overflow-y-scroll">
        <div className="h-full flex justify-center items-center">
          <PreLoader />
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[86px] md:py-4 py-16">
      <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3">
        <Link to={paths.home()}>
          <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
        </Link>
        <div className="flex flex-col">
          <div className="flex gap-x-4 items-center">
            <p className="text-lg leading-6 font-bold text-foreground">
              {t("document-drafting.title")}
            </p>
          </div>
          <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
            {t("document-drafting.description")}
          </p>
        </div>
      </div>

      {/* Combined Settings Form */}
      <form
        onSubmit={handleSubmit}
        onChange={() => setHasChanges(true)}
        className="relative w-full max-h-full"
      >
        {/* Document Drafting Settings */}
        <div className="relative rounded-lg mb-8">
          <div className="flex items-start justify-between px-6 py-2"></div>
          <div className="space-y-6 flex h-full w-full">
            <div className="w-full flex flex-col gap-y-4">
              <div className="">
                <label className="mb-2.5 block font-medium text-foreground">
                  {isDocumentDraftingChecked
                    ? t("document-drafting.enabled")
                    : t("document-drafting.disabled")}
                </label>
                <Toggle
                  name="document_drafting"
                  checked={isDocumentDraftingChecked}
                  onChange={(e) => {
                    setIsDocumentDraftingChecked(e.target.checked);
                    if (!e.target.checked) {
                      setIsDocumentLinkingChecked(false);
                    }
                    setHasChanges(true);
                  }}
                />
                <div className="flex items-center justify-between space-x-14">
                  <p className="text-foreground text-xs rounded-lg w-96">
                    {t("document-drafting.desc-settings")}
                  </p>
                </div>
              </div>
              <div className="">
                <label className="mb-2.5 block font-medium text-foreground">
                  {t("document-drafting.linking")}
                </label>
                <Toggle
                  name="document_drafting_linking"
                  checked={
                    isDocumentDraftingChecked && isDocumentLinkingChecked
                  }
                  disabled={!isDocumentDraftingChecked}
                  onChange={(e) => {
                    if (isDocumentDraftingChecked) {
                      setIsDocumentLinkingChecked(e.target.checked);
                    }
                    setHasChanges(true);
                  }}
                />
                <div className="flex items-center justify-between space-x-14">
                  <p className="text-foreground text-xs rounded-lg w-96">
                    {t("document-drafting.desc-linkage")}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Workspace Linking Settings */}
        <div
          className={`flex flex-col gap-y-4 pt-4 border-t ${!isDocumentLinkingChecked ? "opacity-50 pointer-events-none" : ""}`}
        >
          <h2 className="text-lg leading-6 font-bold text-foreground mb-4">
            {t("workspace-linking.title")}
          </h2>

          {/* Feature Toggles */}
          <div className="flex flex-col gap-y-4 mb-6">
            <div className="flex flex-col gap-y-1">
              <h2 className="text-base leading-6 font-bold text-foreground">
                {t("dd-settings.vector-search.title")}
              </h2>
              <p className="text-xs leading-[18px] font-base text-foreground">
                {t("dd-settings.vector-search.description")}
              </p>
              <div className="mt-2">
                <Toggle
                  checked={ddSettings?.ddVectorEnabled ?? false}
                  onChange={(e) => {
                    setDDSettings({
                      ...ddSettings,
                      ddVectorEnabled: e.target.checked,
                    });
                    setHasChanges(true);
                  }}
                  disabled={!isDocumentLinkingChecked}
                  id="dd_vector_enabled"
                />
              </div>
            </div>

            <div className="flex flex-col gap-y-1">
              <h2 className="text-base leading-6 font-bold text-foreground">
                {t("dd-settings.memo-generation.title")}
              </h2>
              <p className="text-xs leading-[18px] font-base text-foreground">
                {t("dd-settings.memo-generation.description")}
              </p>
              <div className="mt-2">
                <Toggle
                  checked={ddSettings?.ddMemoEnabled ?? true}
                  onChange={(e) => {
                    setDDSettings({
                      ...ddSettings,
                      ddMemoEnabled: e.target.checked,
                    });
                    setHasChanges(true);
                  }}
                  disabled={!isDocumentLinkingChecked}
                  id="dd_memo_enabled"
                />
              </div>
            </div>

            <div className="flex flex-col gap-y-1">
              <h2 className="text-base leading-6 font-bold text-foreground">
                {t("dd-settings.base-generation.title")}
              </h2>
              <p className="text-xs leading-[18px] font-base text-foreground">
                {t("dd-settings.base-generation.description")}
              </p>
              <div className="mt-2">
                <Toggle
                  checked={ddSettings?.ddBaseEnabled ?? true}
                  onChange={(e) => {
                    setDDSettings({
                      ...ddSettings,
                      ddBaseEnabled: e.target.checked,
                    });
                    setHasChanges(true);
                  }}
                  disabled={!isDocumentLinkingChecked}
                  id="dd_base_enabled"
                />
              </div>
            </div>

            <div className="flex flex-col gap-y-1">
              <h2 className="text-base leading-6 font-bold text-foreground">
                {t("dd-settings.linked-workspace-impact.title")}
              </h2>
              <p className="text-xs leading-[18px] font-base text-foreground">
                {t("dd-settings.linked-workspace-impact.description")}
              </p>
              <div className="mt-2">
                <Toggle
                  checked={ddSettings?.ddLinkedWorkspaceImpact ?? true}
                  onChange={(e) => {
                    setDDSettings({
                      ...ddSettings,
                      ddLinkedWorkspaceImpact: e.target.checked,
                    });
                    setHasChanges(true);
                  }}
                  disabled={!isDocumentLinkingChecked}
                  id="dd_linked_workspace_impact"
                />
              </div>
            </div>
          </div>

          {/* Token Limits */}
          <div className="flex flex-col gap-y-6">
            <div className="flex flex-col">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("dd-settings.vector-token-limit.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("dd-settings.vector-token-limit.description")}
                </p>
              </div>
              <div className="relative mt-4">
                <input
                  type="number"
                  name="dd_vector_token_limit"
                  min={0}
                  onScroll={(e) => e.target.blur()}
                  className="dark-input-mdl w-1/3 rounded-md bg-transparent py-2 pl-6 pr-10 text-foreground outline-none focus:outline-none"
                  defaultValue={ddSettings?.ddVectorTokenLimit ?? 5000}
                  required={true}
                  disabled={!isDocumentLinkingChecked}
                />
              </div>
            </div>

            <div className="flex flex-col">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("dd-settings.memo-token-limit.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("dd-settings.memo-token-limit.description")}
                </p>
              </div>
              <div className="relative mt-4">
                <input
                  type="number"
                  name="dd_memo_token_limit"
                  min={0}
                  onScroll={(e) => e.target.blur()}
                  className="dark-input-mdl w-1/3 rounded-md bg-transparent py-2 pl-6 pr-10 text-foreground outline-none focus:outline-none"
                  defaultValue={ddSettings?.ddMemoTokenLimit ?? 3000}
                  required={true}
                  disabled={!isDocumentLinkingChecked}
                />
              </div>
            </div>

            <div className="flex flex-col">
              <div className="flex flex-col gap-y-1">
                <h2 className="text-base leading-6 font-bold text-foreground">
                  {t("dd-settings.base-token-limit.title")}
                </h2>
                <p className="text-xs leading-[18px] font-base text-foreground">
                  {t("dd-settings.base-token-limit.description")}
                </p>
              </div>
              <div className="relative mt-4">
                <input
                  type="number"
                  name="dd_base_token_limit"
                  min={0}
                  onScroll={(e) => e.target.blur()}
                  className="dark-input-mdl w-1/3 rounded-md bg-transparent py-2 pl-6 pr-10 text-foreground outline-none focus:outline-none"
                  defaultValue={ddSettings?.ddBaseTokenLimit ?? 2000}
                  required={true}
                  disabled={!isDocumentLinkingChecked}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Single Save Button */}
        <div className="w-full justify-end flex absolute right-5">
          {hasChanges && (
            <Button type="submit" className="mr-0 -mb-14">
              {saving ? t("common.saving") : t("common.save")}
            </Button>
          )}
        </div>
      </form>
    </div>
  );
}
