import React, { useState, useEffect } from "react";
import Appearance from "@/models/appearance";
import { useTranslation } from "react-i18next";
import Toggle from "@/components/ui/Toggle";

export default function AutoSubmit() {
  const [saving, setSaving] = useState(false);
  const [autoSubmitSttInput, setAutoSubmitSttInput] = useState(true);
  const { t } = useTranslation();

  const handleChange = async (e) => {
    const newValue = e.target.checked;
    setAutoSubmitSttInput(newValue);
    setSaving(true);
    try {
      Appearance.updateSettings({ autoSubmitSttInput: newValue });
    } catch (error) {
      console.error("Failed to update appearance settings:", error);
      setAutoSubmitSttInput(!newValue);
    }
    setSaving(false);
  };

  useEffect(() => {
    function fetchSettings() {
      const settings = Appearance.getSettings();
      setAutoSubmitSttInput(settings.autoSubmitSttInput ?? true);
    }
    fetchSettings();
  }, []);

  return (
    <div className="flex flex-col gap-y-0.5 my-4">
      <h2 className="text-sm leading-6 font-semibold text-foreground">
        {t("chat-ui-settings.auto_submit.title")}
      </h2>
      <p className="text-xs text-foreground text-opacity-60">
        {t("chat-ui-settings.auto_submit.description")}
      </p>
      <div className="flex items-center gap-x-4">
        <Toggle
          id="auto_submit"
          name="auto_submit"
          checked={autoSubmitSttInput}
          onChange={handleChange}
          disabled={saving}
        />
      </div>
    </div>
  );
}
