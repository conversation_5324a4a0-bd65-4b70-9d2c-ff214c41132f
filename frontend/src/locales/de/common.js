const TRANSLATIONS = {
  // =========================
  // COMMON STRINGS & PLACEHOLDERS
  // =========================
  common: {
    examples: "Beispiele",
    "workspaces-name": "Arbeitsbereich Name",
    ok: "OK",
    error: "Fehler",
    confirm: "Bestätigen",
    confirmstart: "Bestätigen und starten",
    savesuccess: "Einstellungen erfolgreich gespeichert",
    saveerror: "<PERSON><PERSON> beim Speichern der Einstellungen",
    success: "Erfolg",
    user: "Benutzer",
    selection: "Modellauswahl",
    saving: "Speichern...",
    save: "Änderungen speichern",
    previous: "Vorherige Seite",
    next: "Nächste Seite",
    cancel: "Abbrechen",
    "search-placeholder": "Suchen...",
    "more-actions": "Weitere Aktionen",
    "delete-message": "Na<PERSON><PERSON>t löschen",
    copy: "<PERSON><PERSON>ren",
    edit: "Bearbeiten",
    regenerate: "Neu generieren",
    "export-word": "Nach Word exportieren",
    "stop-generating": "Generierung stoppen",
    "attach-file": "Eine Datei an diesen Chat anhängen",
    home: "Startseite",
    settings: "Einstellungen",
    support: "Support",
    "clear-reference": "Referenz löschen",
    "send-message": "Nachricht senden",
    "ask-legal": "Nach rechtlichen Informationen fragen",
    "stop-response": "Antwortgenerierung stoppen",
    "contact-support": "Support kontaktieren",
    "copy-connection": "Verbindungszeichenfolge kopieren",
    "auto-connect": "Automatisch mit Erweiterung verbinden",
    back: "Zurück",
    "back-to-workspaces": "Zurück zu den Arbeitsbereichen",
    off: "Aus",
    on: "Ein",
    continue: "Fortfahren",
    rename: "Umbenennen",
    delete: "Löschen",
    "default-skill":
      "Diese Fähigkeit ist standardmäßig aktiviert und kann nicht deaktiviert werden.",
    placeholder: {
      username: "Mein Benutzername",
      password: "Dein Passwort",
      email: "Gib deine E-Mail-Adresse ein",
      "support-email": "<EMAIL>",
      website: "https://www.beispiel.de",
      "site-name": "IST Legal",
      "search-llm": "Nach spezifischem LLM-Anbieter suchen",
      "search-providers": "Verfügbare Anbieter durchsuchen",
      "message-heading": "Nachrichtenüberschrift",
      "message-content": "Nachricht",
      "token-limit": "4096",
      "max-tokens": "Maximale Tokens pro Anfrage (z.B.: 1024)",
      "api-key": "API-Schlüssel",
      "base-url": "Basis-URL",
      endpoint: "API-Endpunkt",
    },
    tooltip: {
      copy: "In die Zwischenablage kopieren",
      delete: "Dieses Element löschen",
      edit: "Dieses Element bearbeiten",
      save: "Änderungen speichern",
      cancel: "Änderungen abbrechen",
      search: "Elemente durchsuchen",
      add: "Neues Element hinzufügen",
      remove: "Element entfernen",
      upload: "Datei hochladen",
      download: "Datei herunterladen",
      refresh: "Daten aktualisieren",
      settings: "Einstellungen öffnen",
      more: "Weitere Optionen",
    },
    "default.message": "Gib hier deine Nachricht ein",
    preview: "Vorschau",
    prompt: "Prompt",
    loading: "Lädt...",
    download: "Download",
    open_in_new_tab: "In neuem Tab öffnen",
    timeframes: "Zeiträume",
    other: "Andere Optionen",
    close: "Schließen",
    note: "Hinweis",
  },

  // =========================
  // RECENT UPLOADS COMPONENT
  // =========================

  // moved to recentUploads.js

  // =========================
  // CHAT BOX DRAG & DROP COMPONENT
  // =========================
  chatboxdnd: {
    title: "Füge dieser Eingabeaufforderung eine Datei hinzu",
    description:
      "Ziehe deine Datei hierher, um sie dieser Eingabeaufforderung hinzuzufügen. Sie wird nicht dauerhaft im Arbeitsbereich gespeichert.",
    "file-prefix": "Datei:",
    "attachment-tooltip":
      "Diese Datei wird an deine Nachricht angehängt. Sie wird nicht dauerhaft im Arbeitsbereich gespeichert.",
    "uploaded-file-tag": "HOCHGELADENE BENUTZERDATEI",
  },

  // =========================
  // TEXTGENWEBUI COMPONENT
  // =========================
  textgenwebui: {
    "base-url": "Basis-URL",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "token-window": "Token-Kontextfenster",
    "token-window-placeholder": "Inhaltsfensterlimit (z.B.: 4096)",
    "api-key": "API-Schlüssel (Optional)",
    "api-key-placeholder": "TextGen Web UI API-Schlüssel",
    "max-tokens": "Maximale Tokens",
    "max-tokens-placeholder": "Maximale Tokens pro Anfrage (z.B.: 1024)",
  },

  // =========================
  // LMSTUDIO COMPONENT
  // =========================

  // =========================
  // LOCALAI COMPONENT
  // =========================

  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Confluence Space Key",
    "space-key-desc":
      "Dies ist der Space-Key deiner Confluence-Instanz, der verwendet wird. Beginnt üblicherweise mit ~",
    "space-key-placeholder": "z.B.: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "z.B.: https://example.atlassian.net, http://localhost:8211, etc...",
    "token-tooltip": "Du kannst ein API-Token erstellen",
    "token-tooltip-here": "hier",
  },

  // =========================
  // CONFIRMATION MESSAGES
  // =========================
  deleteWorkspaceConfirmation:
    "Bist du sicher, dass du {{name}} löschen möchtest?\nDanach ist es in dieser Instanz nicht mehr verfügbar.\n\nDiese Aktion kann nicht rückgängig gemacht werden.",
  deleteConfirmation:
    "Bist du sicher, dass du ${user.username} löschen möchtest?\nDanach wird der Benutzer ausgeloggt und kann diese Instanz nicht mehr nutzen.\n\nDiese Aktion kann nicht rückgängig gemacht werden.",
  suspendConfirmation:
    "Bist du sicher, dass du {{username}} sperren möchtest?\nDanach wird der Benutzer ausgeloggt und kann sich nicht mehr einloggen, bis ein Admin ihn entsperrt.",
  flushVectorCachesWorkspaceConfirmation:
    "Bist du sicher, dass du den Vektor-Cache für diesen Arbeitsbereich leeren möchtest?",
  apiKeys: {
    "deactivate-title": "API-Schlüssel deaktivieren",
    "deactivate-message":
      "Bist du sicher, dass du diesen API-Schlüssel deaktivieren möchtest?\nDanach wird er nicht mehr verwendet werden können.\n\nDiese Aktion kann nicht rückgängig gemacht werden.",
  },

  // =========================
  // SETTINGS SIDEBAR MENU ITEMS
  // =========================

  // Settings section moved to settings.js

  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Chat-UI-Einstellungen",
    description: "Konfiguriere die Chat-Einstellungen.",
    auto_submit: {
      title: "Automatisches Absenden von Spracheingaben",
      description:
        "Spracheingaben nach einer Stille-Periode automatisch absenden",
    },
    auto_speak: {
      title: "Automatisches Vorlesen von Antworten",
      description: "Antworten der KI automatisch vorlesen",
    },
  },

  // =========================
  // QURA BUTTONS
  // =========================
  qura: {
    "copy-to-cora": "Qura Quellencheck",
    "qura-status": "Qura-Button ist ",
    "copy-option": "Option kopieren",
    "option-quest": "Frage",
    "option-resp": "Antwort",
    "role-description":
      "Füge einen Qura-Button hinzu, um auf Qura.law abzustimmen",
  },

  // =========================
  // LOGIN & SIGN-IN PAGES
  // =========================
  login: {
    "multi-user": {
      welcome: "Willkommen bei",
      "placeholder-username": "E-Mail-Adresse",
      "placeholder-password": "Passwort",
      login: "Anmelden",
      validating: "Wird validiert...",
      "forgot-pass": "Passwort vergessen",
      "back-to-login": "Zurück zur Anmeldung",
      "reset-password": "Passwort zurücksetzen",
      reset: "Zurücksetzen",
      "reset-password-info":
        "Geben Sie unten die erforderlichen Informationen ein, um Ihr Passwort zurückzusetzen.",
    },
    "sign-in": {
      start: "Melde dich an bei deinem Konto",
      end: "Konto.",
    },
    button: "anmelden",
    password: {
      forgot: "Passwort vergessen?",
      contact: "Bitte wende dich an den Systemadministrator.",
    },
    publicMode: "Ohne Konto ausprobieren",
    logging: "Anmeldung läuft...",
  },

  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Binäre LLM-Auswahl",
    "secondary-llm-toggle-description":
      "Aktiviere diese Option, um Administratoren die Möglichkeit zu geben, zwischen zwei LLM-Modellen im Dokumenten-Entwurfsmodul zu wählen.",
    "secondary-llm-toggle-status": "Status: ",
    "secondary-llm-user-level": "Sekundäre LLM-Benutzerstufe",
    "secondary-llm-user-level-description":
      "Aktiviere diese Option, um ALLEN Benutzern die Möglichkeit zu geben, im Dokumenten-Entwurfsarbeitsbereich zwischen zwei LLM-Modellen zu wählen.",
  },

  // =========================
  // NEW WORKSPACE
  // =========================
  "new-workspace": {
    title: "Neuer Arbeitsbereich",
    placeholder: "Mein Arbeitsbereich",
    "legal-areas": "Rechtsgebiete",
    create: {
      title: "Neuen Arbeitsbereich erstellen",
      description:
        "Nach der Erstellung wird nur den Administratoren der Zugriff angezeigt. Du kannst nachträglich Benutzer hinzufügen.",
      error: "Fehler: ",
      cancel: "Abbrechen",
      "create-workspace": "Arbeitsbereich erstellen",
    },
  },

  // =========================
  // WORKSPACE CHATS
  // =========================
  "workspace-chats": {
    welcome: "Willkommen in Ihrem neuen Arbeitsbereich.",
    "desc-start": "Um zu beginnen, können Sie entweder",
    "desc-mid": "ein Dokument hochladen",
    "desc-or": "oder",
    start: "Um zu beginnen",
    "desc-end": "eine Nachricht senden.",
    "attached-file": "Angehängte Datei",
    "attached-files": "Angehängte Dateien",
    "token-count": "Token-Anzahl",
    "total-tokens": "Gesamtanzahl Tokens",
    "context-window": "Kontextfenster",
    "remaining-tokens": "Verbleibend",
    "view-files": "Angehängte Dateien anzeigen",
    prompt: {
      send: "Senden",
      "send-message": "Nachricht senden",
      placeholder: "Rechtliche Informationen anfragen",
      "change-size": "Textgröße ändern",
      reset: "Chat zurücksetzen",
      clear: "Chatverlauf löschen und neu beginnen",
      command: "Befehl",
      description: "Beschreibung",
      save: "speichern",
      small: "Klein",
      normal: "Normal",
      large: "Groß",
      larger: "Größer",
      attach: "Datei an diesen Chat anhängen",
      upgrade: "Prompt aktualisieren",
      upgrading: "Prompt wird aktualisiert...",
      "original-prompt": "Original Prompt:",
      "upgraded-prompt": "Aktualisierter Prompt:",
      "edit-prompt": "Du kannst den neuen Prompt vor dem Absenden bearbeiten",
      "shortcut-tip":
        "Tipp: Drücke Enter um Änderungen zu übernehmen. Verwende Shift+Enter für neue Zeilen.",
      "speak-prompt": "Sprich deinen Prompt",
      "view-agents": "Alle verfügbaren Agenten anzeigen",
      "ability-tag": "Fähigkeit",
      "deep-search": "Websuche",
      "deep-search-tooltip":
        "Im Web nach Informationen suchen, um Antworten zu verbessern",
      "workspace-chats.prompt.view-agents": "Agenten anzeigen",
      "workspace-chats.prompt.ability-tag": "Fähigkeit",
      "workspace-chats.prompt.speak-prompt": "Sprich deinen Prompt",
    },
  },

  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Deep Search",
    description:
      "Konfiguriere Websuche-Fähigkeiten für Chat-Antworten. Wenn aktiviert, kann das System im Web nach Informationen suchen, um Antworten zu verbessern.",
    enable: "Deep Search aktivieren",
    enable_description:
      "Erlaube dem System, bei der Beantwortung von Anfragen im Web nach Informationen zu suchen.",
    provider_settings: "Anbieter-Einstellungen",
    provider: "Suchanbieter",
    model: "Modell",
    api_key: "API-Schlüssel",
    api_key_placeholder: "Gib deinen API-Schlüssel ein",
    api_key_placeholder_set:
      "API-Schlüssel ist gesetzt (neuen Schlüssel eingeben, um zu ändern)",
    api_key_help:
      "Dein API-Schlüssel wird sicher gespeichert und nur für Websuche-Anfragen verwendet.",
    context_percentage: "Kontext-Prozentsatz",
    context_percentage_help:
      "Prozentsatz des LLM-Kontextfensters, der für Websuche-Ergebnisse reserviert wird (5-20%).",
    fetch_error: "Deep Search-Einstellungen konnten nicht abgerufen werden",
    save_success: "Deep Search-Einstellungen erfolgreich gespeichert",
    save_error:
      "Fehler beim Speichern der Deep Search-Einstellungen: {{error}}",
    toast_success: "Deep Search-Einstellungen erfolgreich gespeichert",
    toast_error:
      "Fehler beim Speichern der Deep Search-Einstellungen: {{error}}",
    brave_recommended:
      "Brave Search ist derzeit die empfohlene und zuverlässigste Anbieter-Option.",
  },

  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Kontextuelles Embedding",
      hint: "Aktiviere kontextuelles Embedding, um den Einbettungsprozess mit zusätzlichen Parametern zu verbessern",
    },
    systemPrompt: {
      label: "System Prompt",
      placeholder: "Gib einen Wert ein...",
      description:
        "Beispiel: Bitte gib einen kurzen, prägnanten Kontext an, der diesen Abschnitt im Gesamtdokument einordnet – ausschließlich der Kontext, sonst nichts.",
    },
    userPrompt: {
      label: "User Prompt",
      placeholder: "Gib einen Wert ein...",
      description:
        "Beispiel: <document>\n{file}\n</document>\nHier ist der Abschnitt, den wir im Gesamtdokument einordnen möchten\n<chunk>\n{chunk}\n</chunk>",
    },
  },

  // =========================
  // HEADER
  // =========================
  header: {
    account: "Konto",
    login: "Anmelden",
    "sign-out": "Abmelden",
  },

  // =========================
  // WORKSPACE OVERVIEW
  // =========================
  workspace: {
    title: "Arbeitsbereiche der Instanz",
    description:
      "Dies sind alle Arbeitsbereiche, die in dieser Instanz existieren. Das Entfernen eines Arbeitsbereichs löscht alle zugehörigen Chats und Einstellungen.",
    "new-workspace": "Neuer Arbeitsbereich",
    name: "Name",
    link: "Link",
    users: "Benutzer",
    type: "Typ",
    "created-on": "Erstellt am",
    save: "Änderungen speichern",
    cancel: "Abbrechen",
    "sort-by-name": "Nach Namen sortieren",
    sort: "Alphabetisch sortieren",
    unsort: "Ursprüngliche Reihenfolge wiederherstellen",
    deleted: {
      title: "Arbeitsbereich nicht gefunden!",
      description:
        "Es scheint, dass ein Arbeitsbereich mit diesem Namen nicht verfügbar ist.",
      homepage: "Zur Startseite zurückkehren",
    },
    "no-workspace": {
      title: "Kein Arbeitsbereich verfügbar",
      description: "Sie haben noch keinen Zugriff auf Arbeitsbereiche.",
      "contact-admin":
        "Bitte kontaktieren Sie Ihren Administrator für Zugriff.",
      "learn-more": "Mehr über Arbeitsbereiche erfahren",
    },
    "no-workspaces":
      "Sie haben noch keine Arbeitsbereiche. Wählen Sie links einen Rechtsbereich aus, um zu beginnen.",
    "my-workspaces": "Meine Arbeitsbereiche",
    "show-my": "Meine Arbeitsbereiche anzeigen",
    "show-all": "Alle Arbeitsbereiche anzeigen",
    "creator-id": "Erstellt von: {{id}}",
    "loading-username": "Benutzername wird geladen...",
    "cloud-ai": "Cloud-basierte KI",
    "local-ai": "Lokale KI",
    "welcome-mobile":
      "Drücken Sie die Schaltfläche oben links, um einen Rechtsbereich auszuwählen.",
    "today-time": "Heute, {{time}}",
    "date-time": "{{day}} {{month}}, {{time}}",
    "ai-type": "Modul",
    "latest-activity": "Letzte Aktivität",
  },

  // =========================
  // WORKSPACES SETTINGS MENU
  // =========================
  "workspaces-settings": {
    general: "Allgemeine Einstellungen",
    chat: "Chat-Einstellungen",
    vector: "Vektor-Datenbank",
    members: "Mitglieder",
    agent: "Agenten-Konfiguration",
    "general-settings": {
      "workspace-name": "Arbeitsbereichsname",
      "desc-name": "Dies ändert nur den Anzeigenamen deines Arbeitsbereichs.",
      "assistant-profile": "Profilbild des Assistenten",
      "assistant-image":
        "Passe das Profilbild des Assistenten für diesen Arbeitsbereich an.",
      "workspace-image": "Arbeitsbereichsbild",
      "remove-image": "Arbeitsbereichsbild entfernen",
      delete: "Arbeitsbereich löschen",
      deleting: "Arbeitsbereich wird gelöscht...",
      update: "Arbeitsbereich aktualisieren",
      updating: "Arbeitsbereich wird aktualisiert...",
    },
    "chat-settings": {
      type: "Chat-Typ",
      private: "Privat",
      standard: "Standard",
      "private-desc-start": "gewährt manuell den Zugriff an",
      "private-desc-mid": "nur",
      "private-desc-end": "bestimmte Benutzer.",
      "standard-desc-start": "gewährt automatisch den Zugriff an",
      "standard-desc-mid": "alle",
      "standard-desc-end": "neuen Benutzer.",
    },
    users: {
      manage: "Benutzer verwalten",
      "workspace-member": "Keine Arbeitsbereichsmitglieder",
      username: "E-Mail-Adresse",
      role: "Rolle",
      date: "Hinzugefügt am",
      users: "Benutzer",
      search: "Nach einem Benutzer suchen",
      "no-user": "Kein Benutzer gefunden",
      select: "Alle auswählen",
      unselect: "Auswahl aufheben",
      save: "Speichern",
    },
    "linked-workspaces": {
      title: "Verknüpfte Arbeitsbereiche",
      description:
        "Wenn Arbeitsbereiche verknüpft sind, werden rechtlich relevante Daten für den Prompt automatisch aus jedem verknüpften Rechtsgebiet abgerufen. Beachte, dass verknüpfte Arbeitsbereiche die Verarbeitungszeit erhöhen können.",
      "linked-workspace": "Keine verknüpften Arbeitsbereiche",
      manage: "Arbeitsbereiche verwalten",
      name: "Name",
      slug: "Slug",
      date: "Hinzugefügt am",
      workspaces: "Arbeitsbereiche",
      search: "Nach einem Arbeitsbereich suchen",
      "no-workspace": "Kein Arbeitsbereich gefunden",
      select: "Alle auswählen",
      unselect: "Auswahl aufheben",
      save: "Speichern",
    },
    "delete-workspace": "Arbeitsbereich löschen",
    "delete-workspace-message":
      "Du bist dabei, deinen gesamten {{workspace}} Arbeitsbereich zu löschen. Dies entfernt alle eingebetteten Vektor-Daten aus der Datenbank.\n\nDie Original-Dateien bleiben unverändert. Diese Aktion ist unwiderruflich.",
    "vector-database": {
      reset: {
        title: "Vektor-Datenbank zurücksetzen",
        message:
          "Du bist dabei, die Vektor-Datenbank dieses Arbeitsbereichs zurückzusetzen. Dabei werden alle aktuell eingebetteten Vektor-Embeddings entfernt.\n\nDie Original-Dateien bleiben unverändert. Diese Aktion ist unwiderruflich.",
      },
    },
  },

  // =========================
  // GENERAL APPEARANCE & CUSTOMIZATION
  // =========================
  general: {
    vector: {
      title: "Vektoranzahl",
      description: "Gesamte Anzahl an Vektoren in deiner Vektor-Datenbank.",
      vectors: "Anzahl Vektoren",
    },
    names: {
      description: "Dies ändert nur den Anzeigenamen deines Arbeitsbereichs.",
    },
    message: {
      title: "Vorgeschlagene Chat-Nachrichten",
      description:
        "Passe die Nachrichten an, die deinen Arbeitsbereichsbenutzern vorgeschlagen werden.",
      add: "Neue Nachricht hinzufügen",
      save: "Nachrichten speichern",
      heading: "Erkläre mir",
      body: "die Vorteile der Plattform",
      message: "Nachricht",
      "new-heading": "Überschrift",
    },
    pfp: {
      title: "Profilbild des Assistenten",
      description:
        "Passe das Profilbild des Assistenten für diesen Arbeitsbereich an.",
      image: "Arbeitsbereichsbild",
      remove: "Arbeitsbereichsbild entfernen",
    },
    delete: {
      delete: "Arbeitsbereich löschen",
      deleting: "Arbeitsbereich wird gelöscht...",
      "confirm-start": "Du bist dabei, deinen gesamten",
      "confirm-end":
        "Arbeitsbereich zu löschen. Dies entfernt alle eingebetteten Vektor-Daten aus der Datenbank.\n\nDie Original-Dateien bleiben unverändert. Diese Aktion ist unwiderruflich.",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chat: {
    llm: {
      title: "LLM-Anbieter des Arbeitsbereichs",
      description:
        "Der spezifische LLM-Anbieter & das Modell, das in diesem Arbeitsbereich verwendet wird. Standardmäßig werden die Systemeinstellungen genutzt.",
      search: "Alle LLM-Anbieter durchsuchen",
      "system-standard-name": "System Standard",
      "system-standard-desc":
        "Verwende den primären LLM, der in den Systemeinstellungen definiert ist.",
    },
    "speak-prompt": "Sprich deinen Prompt",
    "view-agents": "Alle verfügbaren Agenten für den Chat anzeigen",
    "ability-tag": "Fähigkeit",
    "change-text-size": "Textgröße ändern",
    "aria-text-size": "Textgröße ändern",
    model: {
      title: "Chatmodell des Arbeitsbereichs",
      description:
        "Das spezifische Chatmodell, das in diesem Arbeitsbereich verwendet wird. Ist kein Modell angegeben, wird die Systemeinstellung genutzt.",
      wait: "-- warte auf Modelle --",
    },
    mode: {
      title: "Chat-Modus",
      chat: {
        title: "Chat",
        "desc-start":
          "liefert Antworten basierend auf allgemeinem Wissen des LLM",
        and: "und",
        "desc-end": "Dokumentkontext, der gefunden wurde.",
      },
      query: {
        title: "Abfrage",
        "desc-start": "liefert Antworten",
        only: "nur",
        "desc-end": "wenn Dokumentkontext vorhanden ist.",
      },
    },
    history: {
      title: "Chatverlauf",
      "desc-start":
        "Die Anzahl der vorherigen Chats, die in das Kurzzeitspeicher der Antwort einfließen.",
      recommend: "Empfohlen: 20. ",
      "desc-end":
        "Mehr als 45 Nachrichten können bei großen Nachrichten zu Ausfällen führen.",
    },
    prompt: {
      title: "Prompt",
      description:
        "Der Prompt, der in diesem Arbeitsbereich genutzt wird. Definiere den Kontext und die Anweisungen, damit die KI eine passende Antwort generiert. Gib einen sorgfältig formulierten Prompt an, damit die Antwort relevant und genau ist.",
    },
    refusal: {
      title: "Ablehnungsantwort im Abfragemodus",
      "desc-start": "Wenn im",
      query: "Abfrage",
      "desc-end":
        "modus, möchtest du möglicherweise eine benutzerdefinierte Ablehnungsantwort zurückgeben, wenn kein Kontext gefunden wird.",
    },
    temperature: {
      title: "LLM-Temperatur",
      "desc-start":
        'Diese Einstellung steuert, wie "kreativ" die Antworten des LLM sein werden.',
      "desc-end":
        "Je höher die Zahl, desto kreativer – bei manchen Modellen kann zu hohe Temperatur zu unzusammenhängenden Antworten führen.",
      hint: "Die meisten LLMs haben akzeptable Wertebereiche. Siehe hierzu die Dokumentation deines LLM-Anbieters.",
    },
    "dynamic-pdr": {
      title: "Dynamisches PDR für den Arbeitsbereich",
      description:
        "Aktiviere oder deaktiviere dynamisches PDR für diesen Arbeitsbereich.",
      "global-enabled":
        "Dynamisches PDR ist global aktiviert und kann nicht für einzelne Arbeitsbereiche deaktiviert werden.",
    },
  },

  // =========================
  // VECTOR DATABASE (WORKSPACE)
  // =========================
  "vector-workspace": {
    identifier: "Bezeichner der Vektor-Datenbank",
    snippets: {
      title: "Maximale Kontext-Snippets",
      description:
        "Diese Einstellung legt fest, wie viele Kontext-Snippets maximal an das LLM pro Chat oder Abfrage gesendet werden.",
      recommend:
        "Empfohlener Wert ist mindestens 30. Die Einstellung deutlich höherer Werte erhöht die Verarbeitungszeit, ohne zwangsläufig die Präzision abhängig von der Kapazität des verwendeten LLM zu verbessern.",
    },
    doc: {
      title: "Dokument-Ähnlichkeitsschwelle",
      description:
        "Der minimale Ähnlichkeitswert, ab dem eine Quelle als dem Chat zugehörig betrachtet wird. Je höher der Wert, desto ähnlicher muss die Quelle dem Chat sein.",
      zero: "Keine Einschränkung",
      low: "Niedrig (Ähnlichkeitswert ≥ 0,25)",
      medium: "Mittel (Ähnlichkeitswert ≥ 0,50)",
      high: "Hoch (Ähnlichkeitswert ≥ 0,75)",
    },
    reset: {
      reset: "Vektor-Datenbank zurücksetzen",
      resetting: "Vektoren werden gelöscht...",
      confirm:
        "Du bist dabei, die Vektor-Datenbank dieses Arbeitsbereichs zurückzusetzen. Dabei werden alle aktuell eingebetteten Vektor-Embeddings entfernt.\n\nDie Original-Dateien bleiben unverändert. Diese Aktion ist unwiderruflich.",
      error:
        "Vektor-Datenbank des Arbeitsbereichs konnte nicht zurückgesetzt werden!",
      success: "Vektor-Datenbank des Arbeitsbereichs wurde zurückgesetzt!",
    },
  },

  // =========================
  // AGENT CONFIGURATION
  // =========================
  agent: {
    "performance-warning":
      "Die Leistung von LLMs, die keine explizite Tool-Anbindung unterstützen, hängt stark von den Fähigkeiten des Modells ab. Einige Funktionen können eingeschränkt oder nicht verfügbar sein.",
    provider: {
      title: "LLM-Anbieter des Agenten im Arbeitsbereich",
      description:
        "Der spezifische LLM-Anbieter & das Modell, das für den @agent Agenten in diesem Arbeitsbereich verwendet wird.",
      "need-setup":
        "To use {{name}} as this workspace's agent LLM you need to set it up first.",
    },
    mode: {
      chat: {
        title: "Chatmodell des Agenten im Arbeitsbereich",
        description:
          "Das spezifische Chatmodell, das für den @agent Agenten in diesem Arbeitsbereich verwendet wird.",
      },
      title: "Agentenmodell des Arbeitsbereichs",
      description:
        "Das spezifische LLM-Modell, das für den @agent Agenten in diesem Arbeitsbereich verwendet wird.",
      wait: "-- warte auf Modelle --",
    },
    skill: {
      title: "Standard-Agenten-Fähigkeiten",
      description:
        "Verbessere die natürlichen Fähigkeiten des Standard-Agenten mit diesen vorgefertigten Skills. Diese Einstellung gilt für alle Arbeitsbereiche.",
      rag: {
        title: "RAG & Langzeitspeicher",
        description:
          'Ermögliche dem Agenten, lokale Dokumente zur Beantwortung von Anfragen zu nutzen oder Inhalte für den Langzeitspeicher "zu merken".',
      },
      configure: {
        title: "Agenten-Fähigkeiten konfigurieren",
        description:
          "Passe die Fähigkeiten des Standard-Agenten an, indem du einzelne Skills aktivierst oder deaktivierst. Diese Einstellungen gelten für alle Arbeitsbereiche.",
      },
      view: {
        title: "Dokumente anzeigen & zusammenfassen",
        description:
          "Ermögliche dem Agenten, die Inhalte der in den Arbeitsbereichen eingebetteten Dateien aufzulisten und zusammenzufassen.",
      },
      scrape: {
        title: "Webseiten durchsuchen",
        description:
          "Ermögliche dem Agenten, Webseiten zu besuchen und deren Inhalte zu extrahieren.",
      },
      generate: {
        title: "Diagramme generieren",
        description:
          "Aktiviere den Standard-Agenten, verschiedene Diagrammtypen aus bereitgestellten Daten oder Chatinhalten zu erstellen.",
      },
      save: {
        title: "Dateien generieren & im Browser speichern",
        description:
          "Ermögliche dem Standard-Agenten, Dateien zu generieren und im Browser zu speichern, sodass sie heruntergeladen werden können.",
      },
      web: {
        title: "Live-Websuche und Browsing",
        "desc-start":
          "Ermögliche deinem Agenten, über einen Web-Such (SERP)-Dienst Fragen zu beantworten.",
        "desc-end":
          "Die Websuche während einer Agenten-Sitzung funktioniert erst, wenn dies eingerichtet wurde.",
      },
    },
  },

  cdbProgress: {
    "close-msg": "Möchten Sie den Vorgang wirklich abbrechen?",
    general: {
      placeholderSubTask: "Element {{index}} wird verarbeitet...",
    },
    main: {
      step1: {
        label: "Liste der Abschnitte generieren",
        desc: "Verwendung des Hauptdokuments zur Erstellung einer initialen Struktur.",
      },
      step2: {
        label: "Dokumente verarbeiten",
        desc: "Generierung von Beschreibungen und Überprüfung der Relevanz.",
      },
      step3: {
        label: "Dokumente den Abschnitten zuordnen",
        desc: "Zuweisung relevanter Dokumente zu jedem Abschnitt.",
      },
      step4: {
        label: "Rechtliche Fragen identifizieren",
        desc: "Extraktion wichtiger rechtlicher Fragen für jeden Abschnitt.",
      },
      step5: {
        label: "Rechtsgutachten generieren",
        desc: "Erstellung von Rechtsgutachten für die identifizierten Fragen.",
      },
      step6: {
        label: "Abschnitte entwerfen",
        desc: "Verfassen des Inhalts für jeden einzelnen Abschnitt.",
      },
      step7: {
        label: "Dokument zusammenführen & finalisieren",
        desc: "Zusammenstellung der Abschnitte zum endgültigen Dokument.",
      },
    },
    noMain: {
      step1: {
        label: "Dokumente verarbeiten",
        desc: "Generierung von Beschreibungen für alle hochgeladenen Dateien.",
      },
      step2: {
        label: "Liste der Abschnitte generieren",
        desc: "Erstellung einer strukturierten Liste von Abschnitten aus Dokumentzusammenfassungen.",
      },
      step3: {
        label: "Dokumentzuordnung finalisieren",
        desc: "Bestätigung der Dokumentrelevanz für jeden geplanten Abschnitt.",
      },
      step4: {
        label: "Rechtliche Fragen identifizieren",
        desc: "Extraktion wichtiger rechtlicher Fragen für jeden Abschnitt.",
      },
      step5: {
        label: "Rechtsgutachten generieren",
        desc: "Erstellung von Rechtsgutachten für die identifizierten Fragen.",
      },
      step6: {
        label: "Abschnitte entwerfen",
        desc: "Verfassen des Inhalts für jeden einzelnen Abschnitt.",
      },
      step7: {
        label: "Dokument zusammenführen & finalisieren",
        desc: "Zusammenstellung aller Abschnitte zum endgültigen Rechtsdokument.",
      },
    },
  },

  // =========================
  // RECORDED WORKSPACE CHATS
  // =========================
  recorded: {
    title: "Arbeitsbereich-Chats",
    description:
      "Dies sind alle aufgezeichneten Chats und Nachrichten, sortiert nach ihrem Erstellungsdatum.",
    export: "Exportieren",
    table: {
      id: "ID",
      by: "Gesendet von",
      workspace: "Arbeitsbereich",
      prompt: "Prompt",
      response: "Antwort",
      at: "Gesendet am",
      invoice: "Rechnungsreferenz",
      "completion-token": "Completion Token",
      "prompt-token": "Prompt Token",
    },
    "clear-chats": "Alle aktuellen Chats löschen",
    "confirm-clear-chats":
      "Bist du sicher, dass du alle Chats löschen möchtest?\n\nDiese Aktion ist unwiderruflich.",
    "fine-tune-modal": "Fine-Tune-Modell bestellen",
    "confirm-delete.chat":
      "Bist du sicher, dass du diesen Chat löschen möchtest?\n\nDiese Aktion ist unwiderruflich.",
    next: "Nächste Seite",
    previous: "Vorherige Seite",
    filters: {
      "by-name": "Nach Benutzername filtern",
      "by-reference": "Referenznummer",
    },
    bulk_delete_title: "Massenhafte Löschung alter Chats",
    bulk_delete_description:
      "Lösche alle Chat-Logs, die älter als der ausgewählte Zeitraum sind.",
    delete_old_chats: "Alte Chats löschen",
    total_logs: "Gesamtzahl der Logs",
    filtered_logs: "Gefilterte Logs",
    reset_filters: "Filter zurücksetzen",
    "no-chats-found": "Keine Chat-Logs gefunden",
    "no-chats-description":
      "Keine Chat-Logs gefunden, die deinen Filtern entsprechen. Versuche, deine Suchkriterien zu ändern oder einen älteren Zeitraum zu löschen.",
    "deleted-old-chats": "{{count}} alte Chat(s) gelöscht",
    two_days: "2 Tage",
    one_week: "1 Woche",
    two_weeks: "2 Wochen",
    one_month: "1 Monat",
    two_months: "2 Monate",
    three_months: "3 Monate",
    total_deleted: "Insgesamt gelöschte Chat-Logs",
  },

  // =========================
  // API KEYS
  // =========================
  api: {
    title: "API-Schlüssel",
    description:
      "API-Schlüssel ermöglichen dem Inhaber, programmgesteuert auf diese Instanz zuzugreifen und sie zu verwalten.",
    link: "Lies die API-Dokumentation",
    generate: "Neuen API-Schlüssel generieren",
    table: {
      key: "API-Schlüssel",
      by: "Erstellt von",
      created: "Erstellt am",
    },
    new: {
      title: "Neuen API-Schlüssel erstellen",
      description:
        "Nach der Erstellung kann der API-Schlüssel programmgesteuert zum Zugriff und zur Konfiguration dieser Instanz verwendet werden.",
      doc: "Lies die API-Dokumentation",
      cancel: "Abbrechen",
      "create-api": "API-Schlüssel erstellen",
    },
  },

  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "Die Standardoption für die meisten nicht-kommerziellen Einsätze.",
    azure: "Die Unternehmensoption von OpenAI, gehostet auf Azure-Diensten.",
    anthropic: "Ein freundlicher KI-Assistent von Anthropic.",
    gemini: "Googles größtes und leistungsstärkstes KI-Modell",
    huggingface:
      "Zugriff auf über 150.000 Open-Source-LLMs und die weltweite KI-Community",
    ollama: "Führe LLMs lokal auf deinem eigenen Rechner aus.",
    lmstudio:
      "Entdecke, lade herunter und starte tausende moderne LLMs mit wenigen Klicks.",
    localai: "Führe LLMs lokal auf deinem eigenen Rechner aus.",
    togetherai: "Führe Open-Source-Modelle von Together AI aus.",
    mistral: "Führe Open-Source-Modelle von Mistral AI aus.",
    perplexityai:
      "Nutze leistungsstarke, internetverbundene Modelle, gehostet von Perplexity AI.",
    openrouter: "Eine einheitliche Schnittstelle für LLMs.",
    groq: "Die schnellste LLM-Inferenz für Echtzeit-KI-Anwendungen.",
    koboldcpp: "Führe lokale LLMs über koboldcpp aus.",
    oobabooga: "Führe lokale LLMs über Oobaboogas Text Generation Web UI aus.",
    cohere: "Nutze Coheres leistungsstarke Command-Modelle.",
    lite: "Nutze LiteLLM's OpenAI-kompatiblen Proxy für verschiedene LLMs.",
    "generic-openai":
      "Verbinde dich mit jedem OpenAI-kompatiblen Service via einer benutzerdefinierten Konfiguration",
    native:
      "Verwende ein heruntergeladenes, benutzerdefiniertes Llama-Modell zum Chatten in dieser Instanz.",
    xai: "Nutze xAI's leistungsstarke LLMs wie Grok-2 und mehr.",
    "aws-bedrock":
      "Nutze leistungsstarke Foundation-Modelle privat mit AWS Bedrock.",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Sprach-zu-Text-Einstellungen",
    provider: "Anbieter",
    "system-native": "System-nativ",
    "desc-speech":
      "Hier kannst du festlegen, welchen Sprach-zu-Text- bzw. Text-zu-Sprache-Anbieter du nutzen möchtest. Standardmäßig verwendet der Browser seine eigene Unterstützung, aber du kannst auch andere wählen.",
    "title-text": "Text-zu-Sprache-Einstellungen",
    "desc-text":
      "Hier kannst du festlegen, welchen Text-zu-Sprache-Anbieter du nutzen möchtest. Standardmäßig verwendet der Browser seine eigene Unterstützung, aber du kannst auch andere wählen.",
    "desc-config":
      "Für den browsereigenen Text-zu-Sprache-Dienst ist keine Konfiguration nötig.",
    "placeholder-stt": "Nach Speech-to-Text-Anbietern suchen",
    "placeholder-tts": "Nach Text-to-Speech-Anbietern suchen",
    "native-stt":
      "Verwendet den eingebauten Speech-to-Text-Dienst des Browsers, falls verfügbar.",
    "native-tts":
      "Verwendet den eingebauten Text-to-Speech-Dienst des Browsers, falls verfügbar.",
    "piper-tts": "Führen Sie TTS-Modelle lokal in Ihrem Browser privat aus.",
    "openai-description":
      "Nutzen Sie OpenAIs Text-zu-Sprache-Stimmen und Technologie.",
    openai: {
      "api-key": "API-Schlüssel",
      "api-key-placeholder": "OpenAI API-Schlüssel",
      "voice-model": "Sprachmodell",
    },
    elevenlabs: "Nutze ElevenLabs' Text-to-Speech-Stimmen und Technologie.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Transkriptionsmodelleinstellung",
    description:
      "Dies sind die Zugangsdaten und Einstellungen für deinen bevorzugten Transkriptionsmodell-Anbieter. Es ist wichtig, dass diese Schlüssel aktuell und korrekt sind, sonst werden Mediendateien und Audio nicht transkribiert.",
    provider: "Transkriptionsanbieter",
    "warn-start":
      "Die Nutzung des lokalen Whisper-Modells auf Maschinen mit wenig RAM oder CPU kann die Plattform bei der Verarbeitung von Mediendateien verlangsamen.",
    "warn-recommend":
      "Wir empfehlen mindestens 2GB RAM und Dateien unter 10 MB.",
    "warn-end":
      "Das eingebaute Modell wird beim ersten Einsatz automatisch heruntergeladen.",
    "search-audio": "Nach Anbietern für Audio-Transkription suchen",
    "api-key": "API-Schlüssel",
    "api-key-placeholder": "OpenAI API-Schlüssel",
    "whisper-model": "Whisper-Modell",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Standard Eingebaut",
    "default-built-in-desc":
      "Führen Sie ein eingebautes Whisper-Modell privat auf dieser Instanz aus.",
    "openai-name": "OpenAI",
    "openai-desc":
      "Nutzen Sie das OpenAI Whisper-large-Modell mit Ihrem API-Schlüssel.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Neuer Modellname
    "model-size-turbo": "(~810mb)", // Neue Modellgröße
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Embedding-Einstellung",
    "desc-start":
      "Wenn du ein LLM verwendest, das keinen nativen Embedding-Dienst unterstützt, musst du eventuell zusätzliche Zugangsdaten angeben, um Text einbetten zu können.",
    "desc-end":
      "Embedding wandelt Text in Vektoren um. Diese Zugangsdaten werden benötigt, um deine Dateien und Prompts in ein für das System verwertbares Format zu bringen.",
    provider: {
      title: "Embedding-Anbieter",
      description:
        "Für den nativen Embedding-Dienst der Plattform ist keine Einrichtung notwendig.",
      "search-embed": "Alle Embedding-Anbieter durchsuchen",
      select: "Embedding-Anbieter auswählen",
      search: "Alle Embedding-Anbieter durchsuchen",
    },
    workspace: {
      title: "Embedding-Einstellung des Arbeitsbereichs",
      description:
        "Der spezifische Embedding-Anbieter & das Modell, das in diesem Arbeitsbereich verwendet wird. Standardmäßig wird die Systemvoreinstellung genutzt.",
      "multi-model":
        "Multi-Modell-Unterstützung ist für diesen Anbieter derzeit nicht verfügbar.",
      "workspace-use": "Dieser Arbeitsbereich verwendet",
      "model-set": "das im System eingestellte Modellset.",
      embedding: "Embedding-Modell des Arbeitsbereichs",
      model:
        "Das spezifische Embedding-Modell, das in diesem Arbeitsbereich verwendet wird. Ist nichts angegeben, wird die Systemvoreinstellung genutzt.",
      wait: "-- warte auf Modelle --",
      setup: "Einrichten",
      use: "Um zu verwenden",
      "need-setup":
        "muss als dieser Arbeitsbereichs-Embedder erst eingerichtet werden.",
      cancel: "Abbrechen",
      save: "speichern",
      settings: "Einstellungen",
      search: "Alle Embedding-Anbieter durchsuchen",
      "need-llm":
        "muss als dieser Arbeitsbereichs-LLM erst eingerichtet werden.",
      "save-error":
        "Fehler beim Speichern der {{provider}}-Einstellungen: {{error}}",
      "system-default": "Systemstandard",
      "system-default-desc":
        "Verwende die System-Embedding-Einstellung für diesen Arbeitsbereich.",
    },
    warning: {
      "switch-model":
        "Das Wechseln des Embedding-Modells zerstört vorher eingebettete Dokumente, die während des Chats funktionieren. Sie müssen aus jedem Arbeitsbereich entfernt und vollständig neu hochgeladen werden, damit sie mit dem neuen Embedding-Modell eingebettet werden können.",
    },
  },

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Textaufteilungs- & Chunking-Einstellungen",
    "desc-start":
      "Manchmal möchtest du vielleicht die Standardmethode ändern, wie neue Dokumente aufgeteilt und in Chunks zerlegt werden, bevor sie in deine Vektor-Datenbank eingefügt werden.",
    "desc-end":
      "Diese Einstellung solltest du nur ändern, wenn du die Funktionsweise des Text-Splittings und seine Nebeneffekte verstehst.",
    "warn-start": "Änderungen hier gelten nur für",
    "warn-center": "neu eingebettete Dokumente",
    "warn-end": ", nicht für bereits bestehende Dokumente.",
    method: {
      title: "Text-Splitter-Methode",
      "native-explain":
        "Verwende lokale Chunk-Größe & Überlappung für die Aufteilung.",
      "jina-explain":
        "Delegiere Chunking/Segmentierung an Jinas eingebaute Methode.",
      size: {
        title: "Chunk-Größe",
        description: "Die maximale Anzahl an Tokens pro Chunk.",
      },
      jina: {
        api_key: "Jina API-Schlüssel",
        api_key_desc:
          "Erforderlich für die Nutzung von Jinas Segmentierungsdienst. Der Schlüssel wird in Ihrer Umgebung gespeichert.",
        max_tokens: "Jina: Maximale Tokens pro Chunk",
        max_tokens_desc:
          "Definiert die maximalen Tokens in jedem Chunk für Jinas Segmentierer (maximal 2000 Tokens).",
        return_tokens: "Token-Informationen zurückgeben",
        return_tokens_desc:
          "Token-Anzahl und Tokenizer-Informationen in der Antwort einschließen.",
        return_chunks: "Chunk-Informationen zurückgeben",
        return_chunks_desc:
          "Detaillierte Informationen über die erzeugten Chunks in der Antwort einschließen.",
      },
      "jina-info": "Jina-Chunking aktiv.",
    },
    size: {
      title: "Größe der Text-Chunks",
      description:
        "Dies ist die maximale Anzahl an Zeichen, die in einem einzelnen Vektor vorkommen dürfen.",
      recommend: "Maximale Länge des Embed-Modells ist",
    },
    overlap: {
      title: "Überlappung der Text-Chunks",
      description:
        "Dies ist die maximale Überlappung (Anzahl Zeichen) zwischen zwei benachbarten Text-Chunks.",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Vektordatenbank",
    description:
      "Dies sind die Anmeldeinformationen und Einstellungen für die Funktionsweise Ihrer Plattforminstanz. Es ist wichtig, dass diese Schlüssel aktuell und korrekt sind.",
    provider: {
      title: "Vektordatenbank-Anbieter",
      description: "Für LanceDB ist keine Konfiguration erforderlich.",
      "search-db": "Alle Vektordatenbank-Anbieter durchsuchen",
    },
    search: {
      title: "Vektorsuche-Modus",
      mode: {
        "globally-enabled":
          "Diese Einstellung wird global in den Systemeinstellungen gesteuert. Besuchen Sie die Systemeinstellungen, um das Neuranking-Verhalten zu ändern.",
        default: "Standardsuche",
        "default-desc": "Standard-Vektorähnlichkeitssuche ohne Neuranking.",
        "accuracy-optimized": "Genauigkeitsoptimiert",
        "accuracy-desc":
          "Ordnet Ergebnisse neu, um die Genauigkeit mithilfe von Kreuzaufmerksamkeit zu verbessern.",
      },
    },
  },

  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Einbettbare Chat-Widgets",
    description:
      "Einbettbare Chat-Widgets sind öffentliche Chat-Oberflächen, die an einen einzelnen Arbeitsbereich gebunden sind. Damit kannst du Arbeitsbereiche erstellen, die du dann der Öffentlichkeit präsentieren kannst.",
    create: "Embed erstellen",
    table: {
      workspace: "Arbeitsbereich",
      chats: "Gesendete Chats",
      Active: "Aktive Domains",
    },
  },

  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Embed-Chats",
    export: "Exportieren",
    description:
      "Dies sind alle aufgezeichneten Chats und Nachrichten von jedem veröffentlichten Embed.",
    table: {
      embed: "Embed",
      sender: "Absender",
      message: "Nachricht",
      response: "Antwort",
      at: "Gesendet am",
    },
    delete: {
      title: "Chat löschen",
      message:
        "Bist du sicher, dass du diesen Chat löschen möchtest?\n\nDiese Aktion ist unwiderruflich.",
    },
    config: {
      "delete-title": "Embed löschen",
      "delete-message":
        "Bist du sicher, dass du dieses Embed löschen möchtest?\n\nDiese Aktion ist unwiderruflich.",
      "disable-title": "Embed deaktivieren",
      "disable-message":
        "Bist du sicher, dass du dieses Embed deaktivieren möchtest?\n\nDiese Aktion ist unwiderruflich.",
      "enable-title": "Embed aktivieren",
      "enable-message":
        "Bist du sicher, dass du dieses Embed aktivieren möchtest?\n\nDiese Aktion ist unwiderruflich.",
    },
  },

  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Multi-User-Modus",
    description:
      "Richte deine Instanz so ein, dass sie dein Team unterstützt, indem du den Multi-User-Modus aktivierst.",
    enable: {
      "is-enable": "Multi-User-Modus ist aktiviert",
      enable: "Multi-User-Modus aktivieren",
      description:
        "Standardmäßig bist du der einzige Administrator. Als Administrator musst du Konten für alle neuen Benutzer oder Administratoren erstellen. Verliere dein Passwort nicht, da nur ein Administrator Passwörter zurücksetzen kann.",
      username: "Admin-Konto E-Mail",
      password: "Admin-Konto Passwort",
    },
    password: {
      title: "Passwortschutz",
      description:
        "Schütze deine Instanz mit einem Passwort. Wenn du dieses vergisst, gibt es keine Wiederherstellungsmöglichkeit – speichere es also sicher ab.",
    },
    instance: {
      title: "Instanz passwortschützen",
      description:
        "Standardmäßig bist du der einzige Administrator. Als Administrator musst du Konten für alle neuen Benutzer oder Administratoren erstellen. Verliere dein Passwort nicht, da nur ein Administrator Passwörter zurücksetzen kann.",
      password: "Instanzpasswort",
    },
  },

  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Ereignisprotokolle",
    description:
      "Sieh alle Aktionen und Ereignisse, die in dieser Instanz stattfinden, zur Überwachung.",
    clear: "Ereignisprotokolle löschen",
    table: {
      type: "Ereignistyp",
      user: "Benutzer",
      occurred: "Aufgetreten am",
    },
  },

  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Datenschutz & Datenverarbeitung",
    description:
      "Dies ist deine Konfiguration, wie verbundene Drittanbieter und unsere Plattform mit deinen Daten umgehen.",
    llm: "LLM-Auswahl",
    embedding: "Embedding-Einstellung",
    vector: "Vektor-Datenbank",
    anonymous: "Anonyme Telemetrie aktiviert",
    "desc-event": "Alle Ereignisse speichern keine IP-Adresse und enthalten",
    "desc-id": "keine identifizierenden",
    "desc-cont":
      "Inhalte, Einstellungen, Chats oder andere nicht nutzungsbezogene Informationen. Die Liste der gesammelten Ereignis-Tags findest du auf",
    "desc-git": "Github hier",
    "desc-end":
      "Als Open-Source-Projekt respektieren wir dein Recht auf Privatsphäre. Solltest du die Telemetrie deaktivieren, bitten wir lediglich um Feedback, damit wir die Plattform weiter verbessern können.",
  },

  // =========================
  // DEFAULT CHAT
  // =========================
  "default-chat": {
    welcome: "Willkommen bei IST Legal.",
    "choose-legal": "Wähle links ein Rechtsgebiet aus.",
  },

  // =========================
  // INVITES
  // =========================
  invites: {
    title: "Einladungen",
    description:
      "Erstelle Einladungslinks, mit denen Personen in deiner Organisation ein Konto erstellen können. Jede Einladung kann nur von einem Benutzer verwendet werden.",
    link: "Einladungslink erstellen",
    accept: "Angenommen von",
    usage: "Verwendung",
    "created-by": "Erstellt von",
    created: "Erstellt am",
    new: {
      title: "Neue Einladung erstellen",
      "desc-start":
        "Nach der Erstellung kannst du die Einladung kopieren und an einen neuen Benutzer senden, der sich als",
      "desc-mid": "Standard",
      "desc-end":
        "Rolle registriert und automatisch zu ausgewählten Arbeitsbereichen hinzugefügt wird.",
      "auto-add":
        "Einzuladenden Benutzer automatisch zu Arbeitsbereichen hinzufügen",
      "desc-add":
        "Optional kannst du den Benutzer auch automatisch den unten ausgewählten Arbeitsbereichen zuweisen. Standardmäßig hat der Benutzer keinen Arbeitsbereich sichtbar. Die Zuweisung kann später erfolgen.",
      cancel: "Abbrechen",
      "create-invite": "Einladung erstellen",
      error: "Fehler: ",
    },
    "link-copied": "Einladungslink kopiert",
    "copy-link": "Einladungslink kopieren",
    "delete-invite-title": "Einladung deaktivieren",
    "delete-invite-confirmation":
      "Bist du sicher, dass du diese Einladung deaktivieren möchtest?\nDanach ist sie nicht mehr nutzbar.\n\nDiese Aktion ist unwiderruflich.",
    status: {
      label: "Status",
      pending: "Ausstehend",
      disabled: "Deaktiviert",
      claimed: "Akzeptiert",
    },
  },

  // =========================
  // USER MENU
  // =========================
  "user-menu": {
    edit: "Konto bearbeiten",
    profile: "Profilbild",
    size: "800 x 800",
    "remove-profile": "Profilbild entfernen",
    username: "E-Mail-Adresse",
    "username-placeholder": "E-Mail-Adresse eingeben",
    "new-password": "Neues Passwort",
    "new-password-placeholder": "Neues Passwort",
    cancel: "Abbrechen",
    update: "Konto aktualisieren",
    language: "Bevorzugte Sprache",
    email: "E-Mail-Adresse",
    "email-placeholder": "E-Mail-Adresse eingeben",
  },

  // =========================
  // SIDEBAR (THREADS)
  // =========================
  sidebar: {
    thread: {
      "load-thread": "Threads werden geladen...",
      delete: "Ausgewählte Threads löschen",
      deleted: "gelöscht",
      "empty-thread": "Neuer Thread",
      default: "Standard",
      "starting-thread": "Thread wird gestartet...",
      thread: "Neuer Thread",
      "rename-message": "Geben Sie einen neuen Namen für den Thread ein:",
      "delete-message":
        "Möchten Sie diesen Thread wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.",
      rename: "Umbenennen",
      "delete-thread": "Thread löschen",
      "rename-thread-title": "Thread umbenennen",
      "new-name-placeholder": "Neuen Thread-Namen eingeben",
      "delete-thread-title": "Thread löschen?",
      "delete-confirmation-message":
        'Sind Sie sicher, dass Sie den Thread "{{name}}" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
    },
  },

  // =========================
  // THREAD NAME ERROR
  // =========================
  thread_name_error:
    "Der Thread-Name muss zwischen 3 und 255 Zeichen lang sein und darf nur Buchstaben, Zahlen, Leerzeichen oder Bindestriche enthalten.",

  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Verwende den eingebauten Embedding-Dienst. Keine Einrichtung nötig!",
    openai: "Die Standardoption für die meisten nicht-kommerziellen Einsätze.",
    azure: "Die Unternehmensoption von OpenAI, gehostet auf Azure-Diensten.",
    localai: "Führe Embedding-Modelle lokal auf deinem Rechner aus.",
    ollama: "Führe Embedding-Modelle lokal auf deinem Rechner aus.",
    lmstudio:
      "Entdecke, lade herunter und starte tausende moderne LLMs mit wenigen Klicks.",
    cohere: "Nutze Coheres leistungsstarke Embedding-Modelle.",
    voyageai: "Nutze die leistungsstarken Embedding-Modelle von Voyage AI.",
    "generic-openai": "Verwende ein generisches OpenAI Embedding-Modell.",
    "default.embedder": "Standard-Embedding-Dienst",
    jina: "Führe leistungsstarke Embedding-Modelle von Jina aus.",
    litellm: "Führe leistungsstarke Embedding-Modelle von LiteLLM aus.",
  },

  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb:
      "100% lokale Vektor-Datenbank, die auf derselben Instanz wie die Plattform läuft.",
    chroma:
      "Open-Source Vektor-Datenbank, die du selbst hosten oder in der Cloud betreiben kannst.",
    pinecone:
      "100% cloud-basierte Vektor-Datenbank für Unternehmensanwendungen.",
    zilliz:
      "Cloud-gehostete Vektor-Datenbank, entwickelt für Unternehmen mit SOC 2 Konformität.",
    qdrant: "Open-Source, lokal und verteilt einsetzbare Vektor-Datenbank.",
    weaviate: "Open-Source Vektor-Datenbank, lokal oder in der Cloud gehostet.",
    milvus: "Open-Source, hochskalierbar und ultraschnell.",
    astra: "Vektorbasierte Suche für reale GenAI-Anwendungen.",
  },

  // =========================
  // SYSTEM PREFERENCES
  // =========================
  system: {
    title: "Systemeinstellungen",
    "desc-start":
      "Dies sind die allgemeinen Einstellungen und Konfigurationen deiner Instanz.",
    context_window: {
      title: "Dynamisches Kontextfenster",
      desc: "Steuern Sie, wie viel des LLM-Kontextfensters für zusätzliche Quellen verwendet wird.",
      label: "Kontextfenster Prozentsatz",
      help: "Prozentsatz des Kontextfensters, der für die Anreicherung verwendet werden kann (10-100%).",
      "toast-success": "Kontextfenster Prozentsatz erfolgreich aktualisiert.",
      "toast-error":
        "Fehler beim Aktualisieren des Kontextfenster Prozentsatzes.",
    },
    "change-login-ui": {
      title: "Standard-Login-UI auswählen",
      status: "Aktuell",
      subtitle:
        "Die Benutzeroberfläche wird als Standard-Login-UI für die Anwendung übernommen",
    },
    dynamic_context_window: {
      title: "Dynamisches Kontextfenster",
      desc: "Steuern Sie, wie viel des LLM-Kontextfensters für zusätzliche Quellen verwendet wird.",
      label: "Kontextfenster Prozentsatz",
      help: "Prozentsatz des Kontextfensters, der für die Anreicherung verwendet werden kann (10-100%).",
      "toast-success": "Kontextfenster Prozentsatz erfolgreich aktualisiert.",
    },
    attachment_context: {
      title: "Anhang-Kontextfenster",
      desc: "Steuern Sie, wie viel des LLM-Kontextfensters für Anhänge verwendet werden kann.",
      label: "Anhang-Kontext Prozentsatz",
      help: "Prozentsatz des Kontextfensters, der für Anhänge verwendet werden kann (10-80%).",
      "toast-success": "Anhang-Kontext Prozentsatz erfolgreich aktualisiert.",
      "toast-error":
        "Fehler beim Aktualisieren des Anhang-Kontext Prozentsatzes.",
      "validation-error":
        "Anhang-Kontext Prozentsatz muss zwischen 10 und 80 liegen.",
    },
    user: "Benutzer können Arbeitsbereiche löschen",
    "desc-delete":
      "Erlaube es Nicht-Administratoren, Arbeitsbereiche zu löschen, in denen sie Mitglied sind. Dies würde den Arbeitsbereich für alle löschen.",
    limit: {
      title: "Nachrichtenlimit",
      "desc-limit":
        "Begrenze die Anzahl der Nachrichten, die ein Benutzer pro Tag senden darf.",
      "per-day": "Nachrichten pro Tag",
      label: "Nachrichtenlimit ist aktuell ",
    },
    max_tokens: {
      title: "Maximale Login-Tokens pro Benutzer",
      desc: "Lege fest, wie viele aktive Authentifizierungstokens jeder Benutzer gleichzeitig haben darf. Werden mehr als erlaubt, werden ältere Tokens automatisch entfernt.",
      label: "Maximale Tokens",
      help: "Der Wert muss größer als 0 sein",
    },
    state: {
      enabled: "Aktiviert",
      disabled: "Deaktiviert",
    },
    "source-highlighting": {
      title: "Quellen-Hervorhebung ein-/ausschalten",
      description:
        "Die Quellen-Hervorhebung für Benutzer ausblenden oder anzeigen.",
      label: "Zitation: ",
      "toast-success": "Quellenhervorhebungs-Einstellung wurde aktualisiert",
      "toast-error":
        "Fehler beim Aktualisieren der Quellenhervorhebungs-Einstellung",
    },
    "usage-registration": {
      title: "Nutzungsregistrierung für die Abrechnung",
      description:
        "Aktiviere oder deaktiviere die Rechnungsprotokollierung zur Systemüberwachung.",
      label: "Rechnungsprotokollierung ist ",
    },
    "forced-invoice-logging": {
      title: "Erzwungene Rechnungsprotokollierung",
      description:
        "Aktiviere dies, um vor der Nutzung der Plattform zwingend eine Rechnungsreferenz anzugeben.",
      label: "Erzwungene Rechnungsprotokollierung ist ",
    },
    "rexor-linkage": {
      title: "Rexor-Verknüpfung",
      description:
        "Aktiviere die Rexor-Verknüpfung, um aktive Fallreferenzen vom Rexor-Dienst zu erhalten.",
      label: "Rexor-Verknüpfung ist ",
      "activity-id": "Aktivitäts-ID",
      "activity-id-description":
        "Geben Sie die Aktivitäts-ID für die Rexor-Integration ein",
    },
    rerank: {
      title: "Reranking-Einstellungen",
      description:
        "Konfigurieren Sie die Reranking-Einstellungen, um die Relevanz der Suchergebnisse mit LanceDB zu verbessern.",
      "enable-title": "Reranking aktivieren",
      "enable-description":
        "Aktivieren Sie Reranking, um die Relevanz der Suchergebnisse durch Berücksichtigung von mehr Kontext zu verbessern.",
      status: "Reranking-Status",
      "vector-count-title": "Zusätzliche Vektoren für Reranking",
      "vector-count-description":
        "Anzahl zusätzlicher Vektoren, die über die Arbeitsbereich-Vektoreinstellung hinaus abgerufen werden. Wenn der Arbeitsbereich beispielsweise auf 30 Vektoren eingestellt ist und dieser Wert auf 50 gesetzt wird, werden insgesamt 80 Vektoren für das Reranking berücksichtigt. Eine höhere Zahl kann die Genauigkeit verbessern, verlängert aber die Verarbeitungszeit.",
      "lancedb-only": "Nur LanceDB",
      "lancedb-notice":
        "Diese Funktion ist nur bei Verwendung von LanceDB als Vektordatenbank verfügbar.",
    },
    save: "Änderungen speichern",
  },

  feedback: {
    thankYou: "Danke! Ihr Feedback wurde erfolgreich übermittelt.",
    emailSendError: "Fehler beim Senden der E-Mail: ",
    submitFeedbackError: "Fehler beim Senden des Feedbacks: ",
    attachFile: "Datei anhängen",
    improvePlatform: "Helfen Sie uns, die Plattform zu verbessern!",
    suggestionOrQuestion: "Irgendwelche Vorschläge? Oder Fragen?",
    clickToWrite: "Bitte klicken Sie, um uns zu schreiben",
    noFeedback: "Keine Rückmeldungen gefunden",
    previewImage: "Bildvorschau",
    filePreview: "Dateivorschau",
    noFile: "Keine Datei angehängt",
    fullName: "Vollständiger Name",
    fullNamePlaceholder: "Geben Sie Ihren vollständigen Namen ein",
    message: "Nachricht",
    messagePlaceholder: "Geben Sie Ihre Rückmeldung ein",
    attachment: "Anhang",
    submit: "Feedback senden",
    submitting: "Wird gesendet...",
    submitSuccess: "Feedback erfolgreich gesendet",
    submitError: "Feedback konnte nicht gesendet werden",
    imageLoadError: "Bild konnte nicht geladen werden",
    unsupportedFile: "Nicht unterstützter Dateityp",
    validation: {
      fullNameMinLength:
        "Vollständiger Name muss mindestens 2 Zeichen lang sein",
      fullNameMaxLength:
        "Vollständiger Name darf nicht länger als 100 Zeichen sein",
      fullNameFormat:
        "Vollständiger Name darf nur Buchstaben, Zahlen, Leerzeichen, Unterstriche (_), Punkte (.) und Bindestriche (-) enthalten",
      messageMinLength: "Nachricht muss mindestens 12 Zeichen lang sein",
      messageMaxLength: "Nachricht darf nicht länger als 1000 Zeichen sein",
      messageMinWords: "Nachricht muss mindestens 4 Wörter enthalten",
      fileType: "Datei muss eine JPEG, PNG oder PDF sein",
      fileSize: "Datei darf nicht größer als 5MB sein",
    },
  },

  "feedback-settings": {
    "delete-feedback": "Rückmeldung erfolgreich gelöscht!",
    "delete-error": "Rückmeldung konnte nicht gelöscht werden",
    "header-title": "Rückmeldungsliste",
    "header-description":
      "Dies ist die vollständige Liste der Rückmeldungen für diese Instanz. Bitte beachten Sie, dass das Löschen von Rückmeldungen endgültig ist und nicht rückgängig gemacht werden kann.",
    title: "Benutzer-Feedback-Schaltfläche",
    description:
      "Aktivieren oder deaktivieren Sie die Feedback-Schaltfläche für Benutzer.",
    successMessage: "Die Benutzer-Feedback-Schaltfläche wurde aktualisiert.",
    failureUpdateMessage:
      "Fehler beim Aktualisieren des Benutzer-Feedback-Schaltflächenstatus.",
    errorSubmitting: "Fehler beim Senden der Feedback-Einstellungen.",
    errorFetching: "Fehler beim Abrufen der Feedback-Einstellungen.",
  },

  // =========================
  // USER SETTINGS (INSTANCE USERS)
  // =========================
  "user-setting": {
    description:
      "Dies sind alle Konten, die in dieser Instanz existieren. Das Entfernen eines Kontos entfernt sofort den Zugang.",
    "add-user": "Benutzer hinzufügen",
    username: "E-Mail-Adresse",
    role: "Rolle",
    "economy-id": "Wirtschafts-ID",
    "economy-id-ph": "Wirtschaftssystem-Kennung eingeben",
    "economy-id-hint":
      "ID für Integrationen mit externen Wirtschaftssystemen (z.B. Rexor)",
    default: "Standard",
    manager: "Manager",
    admin: "Administrator",
    superuser: "Superuser",
    "date-added": "Hinzugefügt am",
    "all-domains": "Alle Domains",
    "other-users": "Andere Benutzer (keine Domain)",
    "sort-username": "Nach Benutzername sortieren",
    "sort-organization": "Nach Organisation sortieren",
    edit: "Bearbeiten: ",
    "new-password": "Neues Passwort",
    "password-rule": "Das Passwort muss mindestens 8 Zeichen lang sein.",
    "update-user": "Benutzer aktualisieren",
    placeholder: "E-Mail-Adresse eingeben",
    cancel: "Abbrechen",
    "remove-user": "Benutzer entfernen",
    "remove-user-title": "Benutzer entfernen",
    "remove-user-confirmation":
      "Bist du sicher, dass du diesen Benutzer entfernen möchtest?",
    error: "Fehler: ",
  },

  "login-ui": {
    "show-toast": {
      "update-failed": "Aktualisierung der Anmeldeoberfläche fehlgeschlagen",
      "updated-login-ui": "Die Anmeldeoberfläche wurde aktualisiert",
    },
    "visit-website": "Website besuchen",
    loading: "Wird geladen ...",
    "rw-login-description":
      "Maximieren Sie die juristische Produktivität mit unserer KI-gestützten Plattform!",
  },

  // =========================
  // SUPPORT EMAIL
  // =========================
  support: {
    title: "Support-E-Mail",
    description:
      "Lege die Support-E-Mail-Adresse fest, die im Benutzermenü angezeigt wird, wenn du in dieser Instanz angemeldet bist.",
    clear: "Löschen",
    save: "Speichern",
  },

  // =========================
  // PUBLIC MODE
  // =========================
  "public-mode": {
    enable: "Öffentlichen Benutzer-Modus aktivieren",
    enabled: "Öffentlicher Benutzer-Modus ist aktiviert",
  },

  // =========================
  // BUTTON LABELS
  // =========================
  button: {
    delete: "Löschen",
    edit: "Bearbeiten",
    suspend: "Sperren",
    unsuspend: "Entsperren",
    save: "Speichern",
    accept: "Akzeptieren",
    decline: "Ablehnen",
    ok: "OK",
    "flush-vector-caches": "Vektor-Cache leeren",
    cancel: "Abbrechen",
    saving: "Wird gespeichert",
    save_llm: "LLM-Auswahl speichern",
    save_template: "Vorlage speichern",
    "reset-to-default": "Auf Standard zurücksetzen",
    create: "Erstellen",
    enable: "Aktivieren",
    disable: "Deaktivieren",
    reset: "Zurücksetzen",
    revoke: "Widerrufen",
  },

  // =========================
  // NEW USER (INSTANCE)
  // =========================
  "new-user": {
    title: "Benutzer zur Instanz hinzufügen",
    username: "E-Mail-Adresse",
    "username-ph": "E-Mail-Adresse eingeben",
    password: "Passwort",
    "password-ph": "Anfangs-Passwort des Benutzers",
    role: "Rolle",
    default: "Standard",
    manager: "Manager",
    admin: "Administrator",
    superuser: "Superuser",
    description:
      "Nach der Erstellung muss sich der Benutzer mit seinem initialen Login anmelden, um Zugriff zu erhalten.",
    cancel: "Abbrechen",
    "add-User": "Benutzer hinzufügen",
    error: "Fehler: ",
    "invalid-email": "Bitte gib eine gültige E-Mail-Adresse ein.",
    permissions: {
      title: "Berechtigungen",
      default: [
        "Kann nur Chats in Arbeitsbereichen senden, zu denen er von Admin oder Managern hinzugefügt wurde.",
        "Kann keine Einstellungen ändern.",
      ],
      manager: [
        "Kann alle Arbeitsbereiche ansehen, erstellen und löschen sowie arbeitsbereichsspezifische Einstellungen ändern.",
        "Kann neue Benutzer zur Instanz einladen, erstellen und aktualisieren.",
        "Kann LLM-, VektorDB-, Embedding- oder andere Verbindungen nicht ändern.",
      ],
      admin: [
        "Höchste Benutzerrechte.",
        "Hat in der gesamten Instanz alle Rechte und Möglichkeiten.",
      ],
      superuser: [
        "Kann auf bestimmte Einstellungsseiten wie Dokumenten-Ersteller und Prompt-Upgrade zugreifen.",
        "Kann systemweite Einstellungen wie LLM-, VektorDB-Konfigurationen nicht ändern.",
        "Kann Chats in Arbeitsbereichen senden, zu denen er von Admin oder Managern hinzugefügt wurde.",
      ],
    },
  },

  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Neues Embed für den Arbeitsbereich erstellen",
    error: "Fehler: ",
    "desc-start":
      "Nach der Erstellung erhältst du einen Link, den du auf deiner Website veröffentlichen kannst – mithilfe eines einfachen",
    script: "Script",
    tag: "Tag.",
    cancel: "Abbrechen",
    "create-embed": "Embed erstellen",
    workspace: "Arbeitsbereich",
    "desc-workspace":
      "Dies ist der Arbeitsbereich, auf dem dein Chatfenster basiert. Alle Standardeinstellungen werden von dort übernommen, sofern nicht explizit anders angegeben.",
    "allowed-chat": "Erlaubte Chat-Methode",
    "desc-query":
      "Lege fest, wie dein Chatbot arbeiten soll. Query bedeutet, dass er nur antwortet, wenn ein Dokument bei der Beantwortung hilft.",
    "desc-chat":
      "Chat ermöglicht Antworten auch auf allgemeine Fragen, selbst wenn kein spezifischer Arbeitsbereichskontext vorhanden ist.",
    "desc-response": "Chat: Beantworte alle Fragen, unabhängig vom Kontext",
    "query-response":
      "Query: Antworte nur, wenn der Chat in Zusammenhang mit Dokumenten steht",
    restrict: "Anfragen von Domains einschränken",
    filter:
      "Dieser Filter blockiert Anfragen von Domains, die nicht in der Liste stehen.",
    "use-embed":
      "Wenn dieses Feld leer bleibt, kann dein Embed von jeder Website genutzt werden.",
    "max-chats": "Max. Chats pro Tag",
    "limit-chats":
      "Begrenze die Anzahl der Chats, die dieser eingebettete Chat innerhalb von 24 Stunden verarbeiten darf. 0 bedeutet unbegrenzt.",
    "chats-session": "Max. Chats pro Sitzung",
    "limit-chats-session":
      "Begrenze die Anzahl der Chats, die ein Sitzungsbenutzer mit diesem Embed innerhalb von 24 Stunden senden darf. 0 bedeutet unbegrenzt.",
    "enable-dynamic": "Dynamische Modellnutzung aktivieren",
    "llm-override":
      "Erlaube es, das bevorzugte LLM-Modell festzulegen, um die Standardeinstellung des Arbeitsbereichs zu überschreiben.",
    "llm-temp": "Dynamische LLM-Temperatur aktivieren",
    "desc-temp":
      "Erlaube es, die LLM-Temperatur festzulegen, um die Standardeinstellung des Arbeitsbereichs zu überschreiben.",
    "prompt-override": "Prompt-Überschreibung aktivieren",
    "desc-override":
      "Erlaube es, den System-Prompt festzulegen, um die Standardeinstellung des Arbeitsbereichs zu überschreiben.",
  },

  // =========================
  // SHOW TOAST MESSAGES
  // =========================

  // Moved to showToast.js

  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    openai: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für OpenAI sichtbar",
      ],
    },
    azure: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Texte und Embedding-Texte sind für OpenAI oder Microsoft nicht sichtbar",
      ],
    },
    anthropic: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für Anthropic sichtbar",
      ],
    },
    gemini: {
      description: [
        "Deine Chats werden anonymisiert und für Trainingszwecke genutzt",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für Google sichtbar",
      ],
    },
    lmstudio: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Server zugänglich, auf dem LMStudio läuft",
      ],
    },
    localai: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Server zugänglich, auf dem LocalAI läuft",
      ],
    },
    ollama: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Rechner zugänglich, auf dem Ollama läuft",
      ],
    },
    native: {
      description: [
        "Dein Modell und deine Chats sind nur in dieser Instanz zugänglich",
      ],
    },
    togetherai: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für TogetherAI sichtbar",
      ],
    },
    mistral: {
      description: [
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für Mistral sichtbar",
      ],
    },
    huggingface: {
      description: [
        "Deine Prompts und Dokumenttexte werden an deinen HuggingFace-Endpunkt gesendet",
      ],
    },
    perplexity: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für Perplexity AI sichtbar",
      ],
    },
    openrouter: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für OpenRouter sichtbar",
      ],
    },
    groq: {
      description: [
        "Deine Chats werden nicht für Trainingszwecke verwendet",
        "Deine Prompts und Dokumenttexte zur Antwortgenerierung sind für Groq sichtbar",
      ],
    },
    koboldcpp: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Server zugänglich, auf dem KoboldCPP läuft",
      ],
    },
    textgenwebui: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Server zugänglich, auf dem Oobabooga Text Generation Web UI läuft",
      ],
    },
    "generic-openai": {
      description: [
        "Daten werden gemäß den Nutzungsbedingungen deines generischen Endpunkt-Anbieters geteilt.",
      ],
    },
    cohere: {
      description: [
        "Daten werden gemäß den Nutzungsbedingungen von cohere.com und den Datenschutzgesetzen deines Standorts geteilt.",
      ],
    },
    litellm: {
      description: [
        "Dein Modell und deine Chats sind nur auf dem Server zugänglich, auf dem LiteLLM läuft",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Deine Vektoren und Dokumenttexte werden auf deiner Chroma-Instanz gespeichert",
        "Der Zugriff auf deine Instanz wird von dir verwaltet",
      ],
    },
    pinecone: {
      description: [
        "Deine Vektoren und Dokumenttexte werden auf den Servern von Pinecone gespeichert",
        "Der Zugriff auf deine Daten wird von Pinecone verwaltet",
      ],
    },
    qdrant: {
      description: [
        "Deine Vektoren und Dokumenttexte werden in deiner Qdrant-Instanz (Cloud oder self–hosted) gespeichert",
      ],
    },
    weaviate: {
      description: [
        "Deine Vektoren und Dokumenttexte werden in deiner Weaviate-Instanz (Cloud oder self–hosted) gespeichert",
      ],
    },
    milvus: {
      description: [
        "Deine Vektoren und Dokumenttexte werden in deiner Milvus-Instanz (Cloud oder self–hosted) gespeichert",
      ],
    },
    zilliz: {
      description: [
        "Deine Vektoren und Dokumenttexte werden in deinem Zilliz-Cloud-Cluster gespeichert.",
      ],
    },
    astra: {
      description: [
        "Deine Vektoren und Dokumenttexte werden in deiner cloudbasierten AstraDB-Datenbank gespeichert.",
      ],
    },
    lancedb: {
      description: [
        "Deine Vektoren und Dokumenttexte werden privat in dieser Instanz der Plattform gespeichert",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: [
        "Dein Dokumenttext wird privat in dieser Instanz der Plattform eingebettet",
      ],
    },
    openai: {
      description: [
        "Dein Dokumenttext wird an OpenAI-Server gesendet",
        "Deine Dokumente werden nicht für Trainingszwecke genutzt",
      ],
    },
    azure: {
      description: [
        "Dein Dokumenttext wird an deinen Microsoft Azure-Dienst gesendet",
        "Deine Dokumente werden nicht für Trainingszwecke genutzt",
      ],
    },
    localai: {
      description: [
        "Dein Dokumenttext wird privat auf dem Server eingebettet, auf dem LocalAI läuft",
      ],
    },
    ollama: {
      description: [
        "Dein Dokumenttext wird privat auf dem Server eingebettet, auf dem Ollama läuft",
      ],
    },
    lmstudio: {
      description: [
        "Dein Dokumenttext wird privat auf dem Server eingebettet, auf dem LMStudio läuft",
      ],
    },
    cohere: {
      description: [
        "Daten werden gemäß den Nutzungsbedingungen von cohere.com und den Datenschutzgesetzen deines Standorts geteilt.",
      ],
    },
    voyageai: {
      description: [
        "Daten, die an die Server von Voyage AI gesendet werden, werden gemäß den Nutzungsbedingungen von voyageai.com geteilt.",
      ],
    },
  },

  // =========================
  // PROMPT VALIDATION
  // =========================
  "prompt-validate": {
    edit: "Bearbeiten",
    response: "Antwort",
    prompt: "Prompt",
    regenerate: "Antwort neu generieren",
    good: "Gute Antwort",
    bad: "Schlechte Antwort",
    copy: "Kopieren",
    more: "Weitere Aktionen",
    fork: "Abzweigen",
    delete: "Löschen",
    cancel: "Abbrechen",
    save: "Speichern & Absenden",
    "export-word": "Als Word exportieren",
    exporting: "Exportiere...",
  },

  // =========================
  // CITATIONS
  // =========================
  citations: {
    show: "Zitate anzeigen",
    hide: "Zitate ausblenden",
    chunk: "Zitatteile",
    pdr: "Hauptdokument",
    "pdr-h": "Dokumenthervorhebung",
    referenced: "Referenziert",
    times: "mal.",
    citation: "Zitat",
    match: "Treffer",
    download:
      "Dieser Browser unterstützt keine PDFs. Bitte laden Sie das PDF herunter, um es anzuzeigen:",
    "download-btn": "PDF herunterladen",
    view: "Zitate anzeigen",
    sources: "Quellen und Zitate anzeigen",
    "pdf-collapse-tip":
      "Tipp: Sie können diese PDF-Registerkarte mit der Schaltfläche in der oberen linken Ecke minimieren",
    "open-in-browser": "Im Browser öffnen",
    "loading-pdf": "-- PDF wird geladen --",
    "error-loading": "Fehler beim Laden des PDFs",
    "no-valid-path": "Kein gültiger PDF-Pfad gefunden",
    "web-search": "Websuche",
    "web-search-summary": "Websuche-Zusammenfassung",
    "web-search-results": "Websuche-Ergebnisse",
    "no-web-search-results": "Keine Websuche-Ergebnisse gefunden",
  },

  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Dokumenten-Entwurf",
    description: "Steuere die Einstellungen für den Dokumenten-Entwurf.",
    configuration: "Konfiguration",
    "drafting-model": "Entwurfs-LLM",
    enabled: "Dokumenten-Entwurf ist aktiviert",
    disabled: "Dokumenten-Entwurf ist deaktiviert",
    "enabled-toast": "Dokumenten-Entwurf aktiviert",
    "disabled-toast": "Dokumenten-Entwurf deaktiviert",
    "desc-settings":
      "Der Administrator kann die Dokumenten-Entwurfseinstellungen für alle Benutzer ändern.",
    "drafting-llm": "Voreinstellung für Entwurfs-LLM",
    saving: "Wird gespeichert...",
    save: "Änderungen speichern",
    "chat-settings": "Chat-Einstellungen",
    "drafting-chat-settings": "Chat-Einstellungen für den Dokumenten-Entwurf",
    "chat-settings-desc":
      "Steuere das Verhalten der Chat-Funktion im Dokumenten-Entwurf.",
    "drafting-prompt": "System-Prompt für Dokumenten-Entwurf",
    "drafting-prompt-desc":
      "Der System-Prompt, der im Dokumenten-Entwurf genutzt wird, unterscheidet sich vom System-Prompt für die rechtliche Q&A. Er definiert den Kontext und die Anweisungen für die KI zur Generierung einer Antwort. Gib einen sorgfältig formulierten Prompt ein, damit die Antwort relevant und präzise ist.",
    linking: "Dokumenten-Verknüpfung",
    "legal-issues-prompt": "Prompt für rechtliche Fragen",
    "legal-issues-prompt-desc": "Gib den Prompt für rechtliche Fragen ein.",
    "memo-prompt": "Memo-Prompt",
    "memo-prompt-desc": "Gib den Prompt für ein Memo ein.",
    "desc-linkage":
      "Aktiviere die zusätzliche rechtliche Kontextsuche mittels Vektor/PDR, basierend auf Memo-Abrufen.",
    message: {
      title: "Vorgeschlagene Nachrichten für den Dokumenten-Entwurf",
      description:
        "Füge Nachrichten hinzu, die Benutzer beim Verfassen von Dokumenten schnell auswählen können.",
      heading: "Standard-Nachrichtenüberschrift",
      body: "Standard-Nachrichtentext",
      "new-heading": "Nachrichtenüberschrift",
      message: "Nachrichtentext",
      add: "Nachricht hinzufügen",
      save: "Nachrichten speichern",
    },
    "combine-prompt": "Kombinationsaufforderung",
    "combine-prompt-desc":
      "Geben Sie die Systemaufforderung für die Kombination mehrerer Antworten in eine einzelne Antwort an. Diese Aufforderung wird sowohl für die Kombination von Antworten und DD Linkage-Memos als auch für die Kombination der verschiedenen Antworten aus der Infinity Context-Verarbeitung verwendet.",
    "page-description":
      "Auf dieser Seite können Sie die verschiedenen Prompts anpassen, die in verschiedenen Funktionen des Dokumenten-Entwurfsmoduls verwendet werden. In jedem Eingabefeld wird der Standard-Prompt angezeigt, der verwendet wird, wenn auf dieser Seite kein benutzerdefinierter Prompt festgelegt wird.",
    "dd-linkage-steps": "Prompts für DD-Verknüpfungsschritte",
    "general-combination-prompt": "Allgemeiner Kombinationsprompt",
    "import-memo": {
      title: "Aus Legal QA importieren",
      "button-text": "Memo importieren",
      "search-placeholder": "Threads durchsuchen...",
      import: "Importieren",
      importing: "Importieren...",
      "no-threads": "Keine Legal QA-Threads gefunden",
      "no-matching-threads": "Keine Threads entsprechen Ihrer Suche",
      "thread-not-found": "Ausgewählter Thread nicht gefunden",
      "empty-thread":
        "Der ausgewählte Thread hat keine Inhalte zum Importieren",
      "import-success": "Thread-Inhalte erfolgreich importiert",
      "import-error": "Fehler beim Importieren der Thread-Inhalte",
      "import-error-details": "Fehler beim Import: {{details}}",
      "fetch-error":
        "Fehler beim Abrufen der Threads. Bitte versuchen Sie es später erneut.",
      "imported-from": "Importiert aus Legal QA-Thread",
      "unnamed-thread": "Unbenannter Thread",
      "unknown-workspace": "Unbekannter Arbeitsbereich",
      "no-threads-available": "Keine Threads zum Importieren verfügbar",
      "create-conversations-first":
        "Erstellen Sie zuerst Konversationen in einem Legal QA-Arbeitsbereich, dann können Sie sie hier importieren.",
      "no-legal-qa-workspaces":
        "Es wurden keine Legal QA-Arbeitsbereiche mit aktiven Threads gefunden. Erstellen Sie zuerst Konversationen in einem Legal QA-Arbeitsbereich, um sie zu importieren.",
      "empty-workspaces-with-names":
        "Legal QA-Arbeitsbereiche gefunden ({{workspaceNames}}), aber sie enthalten noch keine aktiven Threads. Erstellen Sie zuerst Konversationen in diesen Arbeitsbereichen, um sie zu importieren.",
      "import-success-with-name":
        "Thread erfolgreich importiert: {{threadName}}",
    },
    "create-task": {
      title: "Juristische Aufgabe erstellen",
      category: {
        name: "Kategoriename",
        desc: "Geben Sie den Namen der Hauptkategorie an.",
        placeholder: "Kategorienamen eingeben",
        type: "Kategorietyp",
        new: "Neue Kategorie erstellen",
        existing: "Bestehende Kategorie verwenden",
        select: "Kategorie auswählen",
        "select-placeholder": "Wählen Sie eine bestehende Kategorie",
      },
      subcategory: {
        name: "Name der juristischen Aufgabe",
        desc: "Geben Sie den Namen der juristischen Aufgabe an.",
        placeholder: "Namen der juristischen Aufgabe eingeben",
      },
      description: {
        name: "Beschreibung",
        desc: "Geben Sie eine Beschreibung für die Kategorie und die juristische Aufgabe an.",
        placeholder: "Kurze Beschreibung eingeben",
      },
      prompt: {
        name: "Juristischer Aufgabenprompt",
        desc: "Dieser Prompt leitet die KI bei der Erstellung eines spezifischen Aktionsplans basierend auf der ausgewählten Aufgabe.",
        placeholder: "Juristischen Promptnamen eingeben",
      },
      submitting: "Wird gesendet...",
      submit: "Senden",
      validation: {
        "category-required": "Kategoriename ist erforderlich.",
        "subcategory-required":
          "Name der juristischen Aufgabe ist erforderlich.",
        "description-required": "Beschreibung ist erforderlich.",
        "prompt-required": "Juristischer Promptname ist erforderlich.",
      },
    },
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Juristische Aufgabe-Benutzer-Prompt-Generator",
    description:
      "Automatische Vorschlag für einen angepassten Prompt für eine juristische Aufgabe",
    "task-description": "Juristische Aufgabenbeschreibung",
    "task-description-placeholder":
      "Beschreiben Sie die juristische Aufgabe, die Sie erledigen möchten...",
    "suggested-prompt": "Vorgeschlagener Benutzer-Prompt",
    "generation-prompt": "Prompt für die Generierung",
    "create-task":
      "Juristische Aufgabe basierend auf diesem Vorschlag erstellen",
    "specific-instructions": "Spezifische Anweisungen oder Fachkenntnisse",
    "specific-instructions-description":
      "Einschließen Sie alle spezifischen Anweisungen oder Fachkenntnisse, die für diese juristische Aufgabe relevant sind",
    "specific-instructions-placeholder":
      "Fügen Sie spezifische Anweisungen, Fachkenntnisse oder Erfahrungen für die Behandlung dieser juristischen Aufgabe hinzu...",
    generating: "Generiere...",
    generate: "Vorschlag generieren",
    "toast-success": "Erfolgreich generiert",
    "toast-fail": "Fehler beim Generieren des Prompts",
    button: "Prompt generieren",
    success: "Prompt erfolgreich generiert",
    error: "Bitte geben Sie einen Namen oder eine Unterkategorie zuerst ein",
    failed: "Fehler beim Generieren des Prompts",
  },

  // =========================
  // DD SETTINGS (WORKSPACE LINKING SETTINGS)
  // =========================
  "dd-settings": {
    title: "Einstellungen für Workspace-Verknüpfung",
    description: "Token-Limits und Verhalten für verknüpfte Workspaces steuern",
    "vector-search": {
      title: "Vektorsuche",
      description:
        "Wenn diese Funktion aktiviert ist, werden semantische Vektorsuchen über alle verknüpften Workspaces durchgeführt, um relevante juristische Dokumente zu finden. Das System konvertiert Benutzeranfragen in Vektoreinbettungen und gleicht sie mit Dokumentvektoren in der Datenbank jedes verknüpften Workspace ab. Diese Funktion dient als Fallback-Lösung, wenn die Memo-Generierung aktiviert ist, aber keine Ergebnisse liefert. Wenn die Memo-Generierung deaktiviert ist, wird die Vektorsuche zur primären Methode für den Abruf von Informationen aus verknüpften Workspaces. Die Suchtiefe wird durch die Einstellung für das Vektor-Token-Limit gesteuert.",
    },
    "memo-generation": {
      title: "Memo-Generierung",
      description:
        "Diese Funktion generiert automatisch präzise juristische Memos aus Dokumenten, die in verknüpften Workspaces gefunden werden. Wenn aktiviert, analysiert das System abgerufene Dokumente, um strukturierte Zusammenfassungen wichtiger juristischer Punkte, Präzedenzfälle und relevanter Kontexte zu erstellen. Diese Memos dienen als primäre Methode zur Einbindung von Wissen aus verknüpften Workspaces. Wenn die Memo-Generierung fehlschlägt oder keine Ergebnisse liefert, greift das System automatisch auf die Vektorsuche zurück (falls aktiviert), um sicherzustellen, dass relevante Informationen dennoch abgerufen werden. Die Länge und der Detailgrad dieser Memos werden durch die Einstellung für das Memo-Token-Limit gesteuert.",
    },
    "linked-workspace-impact": {
      title: "Token-Auswirkung verknüpfter Workspaces",
      description:
        "Steuert, wie das System sein Token-Budget über mehrere verknüpfte Workspaces hinweg verwaltet. Wenn diese Funktion aktiviert ist, passt das System die verfügbaren Tokens für jeden Workspace dynamisch basierend auf der Gesamtzahl der verknüpften Workspaces an, was eine faire Verteilung der Datenressourcen gewährleistet. Dies verhindert, dass ein einzelner Workspace das Kontextfenster dominiert, während eine umfassende Abdeckung aller relevanten Rechtsbereiche aufrechterhalten wird. Diese Einstellung reserviert Token-Kapazität speziell für Memo-Generierungs- und/oder Vektorsuche-Ergebnisse aus jedem verknüpften Workspace, was die Gesamtzahl der für den primären Workspace verfügbaren Tokens reduzieren kann, wenn viele Workspaces verknüpft sind.",
    },
    "vector-token-limit": {
      title: "Vektor-Token-Limit",
      description:
        "Gibt die maximale Anzahl von Tokens an, die für Vektorsuchergebnisse aus jedem verknüpften Workspace zugewiesen werden. Dieses Limit gilt, wenn die Vektorsuche verwendet wird, entweder als primäre Methode (wenn die Memo-Generierung deaktiviert ist) oder als Fallback-Lösung (wenn die Memo-Generierung fehlschlägt). Höhere Limits ermöglichen einen umfassenderen Dokumentenabruf, reduzieren jedoch die für andere Operationen verfügbaren Tokens.",
    },
    "memo-token-limit": {
      title: "Memo-Token-Limit",
      description:
        "Kontrolliert die maximale Länge der generierten juristischen Memos aus jedem verknüpften Workspace. Als primäre Methode zur Wissensintegration fassen diese Memos die wichtigsten juristischen Punkte aus den Dokumenten des verknüpften Workspace zusammen. Wenn ein Memo dieses Token-Limit überschreitet, wird es abgelehnt und das System greift auf die Vektorsuche zurück (falls aktiviert). Höhere Limits ermöglichen eine detailliertere juristische Analyse, können jedoch die Anzahl der verknüpften Workspaces, die einbezogen werden können, reduzieren.",
    },
    "base-token-limit": {
      title: "Basis-Token-Grenze",
      description:
        "Bestimmt die maximale Token-Länge für den initialen juristischen Analyserahmen. Diese Grenze beeinflusst, wie umfassend die Grundanalyse sein kann, bevor Informationen aus verknüpften Arbeitsbereichen einbezogen werden. Eine höhere Grenze ermöglicht eine detailliertere initiale Analyse, lässt aber weniger Raum für die Einbeziehung von Inhalten aus verknüpften Arbeitsbereichen.",
    },
    "toast-success": "Einstellungen erfolgreich aktualisiert",
    "toast-fail": "Einstellungen konnten nicht aktualisiert werden",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Einstellungen für Workspace-Verknüpfung",
    description: "Token-Limits und Verhalten für verknüpfte Workspaces steuern",
    "vector-search": {
      title: "Vektorsuche",
      description:
        "Fallback-Methode zum Finden relevanter Dokumente, wenn die Memo-Generierung fehlschlägt oder deaktiviert ist",
    },
    "memo-generation": {
      title: "Memo-Generierung",
      description:
        "Primäre Methode zur Einbindung von Wissen aus verknüpften Workspaces",
    },
    "linked-workspace-impact": {
      title: "Token-Auswirkung verknüpfter Workspaces",
      description:
        "Tokens für jeden verknüpften Workspace proportional zu ihrer Anzahl reservieren",
    },
    "vector-token-limit": {
      title: "Token-Limit für Vektorsuche",
      description:
        "Maximale Tokens pro verknüpftem Workspace für die Vektorsuche",
    },
    "memo-token-limit": {
      title: "Token-Limit für Memo-Erstellung",
      description: "Maximale Tokens für die Erstellung rechtlicher Memos",
    },
    "base-token-limit": {
      title: "Grundlegendes Token-Limit",
      description: "Maximale Tokens für den Abruf von Basisinhalten",
    },
    "toast-success": "Einstellungen erfolgreich aktualisiert",
    "toast-fail": "Einstellungen konnten nicht aktualisiert werden",
  },

  // =========================
  // MODALE (DOCUMENT & CONNECTORS)
  // =========================
  modale: {
    document: {
      title: "Meine Dokumente",
      document: "Dokumente",
      search: "Nach Dokument suchen",
      folder: "Neuer Ordner",
      name: "Name",
      empty: "Keine Dokumente",
      "move-workspace": "In Arbeitsbereich verschieben",
      "doc-processor": "Dokumentprozessor",
      "processor-offline":
        "Der Dokumentprozessor ist derzeit offline. Bitte versuchen Sie es später erneut.",
      "drag-drop": "Zum Hochladen klicken oder Dateien hierher ziehen",
      "supported-files": "Unterstützte Dateien: PDF",
      "submit-link": "Oder senden Sie einen Link zu einem Dokument",
      fetch: "Abrufen",
      fetching: "Abrufen...",
      "file-desc":
        "Hinweis: Das Dokument wird verarbeitet und Ihrem Arbeitsbereich hinzugefügt. Dies kann einen Moment dauern.",
      cost: "*Einmalige Kosten für Embedding",
      "save-embed": "Speichern und einbetten",
      "failed-uploads": "Fehlgeschlagene Uploads",
      "loading-message": "Dies kann bei großen Dokumenten eine Weile dauern",
      "exceeds-prompt-limit":
        "Hinweis: Der hochgeladene Inhalt übersteigt das, was in einer Anfrage Platz findet. Das System wird Anfragen durch mehrere Prompts verarbeiten, was die Zeit für die Generierung der Antwort verlängert und die Präzision beeinträchtigen kann.",
    },
    connectors: {
      title: "Daten-Connectoren",
      search: "Nach Daten-Connectoren suchen",
      empty: "Keine Daten-Connectoren gefunden.",
    },
    "justify-betweening": "Verarbeitung...",
  },

  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "GitHub-Repo",
      description:
        "Importiere ein gesamtes öffentliches oder privates GitHub-Repository mit einem Klick.",
      url: "GitHub-Repo URL",
      "collect-url": "URL des GitHub-Repos, das du abrufen möchtest.",
      "access-token": "GitHub Access Token",
      optional: "optional",
      "rate-limiting": "Access Token, um Ratenbegrenzungen zu vermeiden.",
      "desc-picker":
        "Nach Abschluss stehen alle Dateien im Dokumenten-Picker zur Einbettung in Arbeitsbereiche zur Verfügung.",
      branch: "Branch",
      "branch-desc": "Der Branch, aus dem du Dateien abrufen möchtest.",
      "branch-loading": "-- lade verfügbare Branches --",
      "desc-start": "Ohne Angabe des",
      "desc-token": "GitHub Access Tokens",
      "desc-connector":
        "kann dieser Connector aufgrund der Ratenbegrenzung der GitHub-API nur die",
      "desc-level": "obersten",
      "desc-end": "Dateien des Repos abrufen.",
      "personal-token":
        "Hole dir ein kostenloses Personal Access Token mit einem GitHub-Konto hier.",
      without: "Ohne ein",
      "personal-token-access": "Personal Access Token",
      "desc-api":
        ", kann die GitHub API die Anzahl der abrufbaren Dateien aufgrund von Ratenbegrenzungen einschränken. Du kannst",
      "temp-token": "ein temporäres Access Token erstellen",
      "avoid-issue": "um dieses Problem zu vermeiden.",
      submit: "Absenden",
      "collecting-files": "Dateien werden abgerufen...",
    },
    "youtube-transcript": {
      name: "YouTube Transkript",
      description:
        "Importiere das Transkript eines gesamten YouTube-Videos über einen Link.",
      url: "YouTube Video URL",
      "url-video": "URL des YouTube-Videos, das du transkribieren möchtest.",
      collect: "Transkript abrufen",
      collecting: "Transkript wird abgerufen...",
      "desc-end":
        "Nach Abschluss steht das Transkript im Dokumenten-Picker zur Einbettung in Arbeitsbereiche bereit.",
    },
    "website-depth": {
      name: "Bulk-Link-Scraper",
      description:
        "Durchsuche eine Website und ihre Unterseiten bis zu einer bestimmten Tiefe.",
      url: "Website URL",
      "url-scrape": "URL der zu durchsuchenden Website.",
      depth: "Tiefe",
      "child-links":
        "Anzahl der Unterlinks, denen der Worker von der Ausgangs-URL folgen soll.",
      "max-links": "Maximale Links",
      "links-scrape":
        "Maximale Anzahl von Links, die durchsucht werden sollen.",
      scraping: "Website wird durchsucht...",
      submit: "Absenden",
      "desc-scrap":
        "Nach Abschluss stehen alle durchsuchten Seiten im Dokumenten-Picker zur Einbettung in Arbeitsbereiche bereit.",
    },
    confluence: {
      name: "Confluence",
      description: "Importiere eine gesamte Confluence-Seite mit einem Klick.",
      url: "Confluence Seiten URL",
      "url-page": "URL einer Seite im Confluence-Space.",
      username: "Confluence-Benutzername",
      "own-username": "Dein Confluence-Benutzername.",
      token: "Confluence Access Token",
      "desc-start":
        "Du musst einen Access Token für die Authentifizierung angeben. Du kannst einen Access Token",
      here: "hier",
      access: "Access Token für die Authentifizierung.",
      collecting: "Seiten werden abgerufen...",
      submit: "Absenden",
      "desc-end":
        "Nach Abschluss stehen alle Seiten im Arbeitsbereich zur Einbettung bereit.",
    },
  },

  // =========================
  // MODULE DEFINITIONS
  // =========================
  module: {
    "legal-qa": "Legal Q&A",
    "document-drafting": "Dokumenten-Entwurf",
    "active-case": "Aktiver Fall",
  },

  // =========================
  // FINE-TUNE NOTIFICATION
  // =========================
  "fine-tune": {
    title: "Du hast genug Daten für ein Fine-Tuning!",
    link: "klicke hier für mehr Infos",
    dismiss: "ausblenden",
  },

  // =========================
  // MOBILE DISCLAIMER
  // =========================
  mobile: {
    disclaimer:
      "HAFTUNGSAUSSCHLUSS: Für das beste Erlebnis und den vollständigen Zugriff auf alle Funktionen, nutze bitte einen Computer, um auf die App zuzugreifen.",
  },
  // =========================
  // SHARE MODAL
  // =========================
  shareModal: {
    title: "Teilen {type}",
    titleWorkspace: "Arbeitsbereich teilen",
    titleThread: "Thread teilen",
    shareWithUsers: "Mit Benutzern teilen",
    shareWithOrg: "Mit der gesamten Organisation teilen",
    searchUsers: "Benutzer suchen...",
    noUsersFound: "Keine Benutzer gefunden",
    loadingUsers: "Benutzer werden geladen...",
    errorLoadingUsers: "Fehler beim Laden der Benutzer",
    errorLoadingStatus: "Fehler beim Laden des Freigabestatus",
    userAccessGranted: "Benutzerzugriff erfolgreich gewährt",
    userAccessRevoked: "Benutzerzugriff erfolgreich widerrufen",
    orgAccessGranted: "Organisationszugriff erfolgreich gewährt",
    orgAccessRevoked: "Organisationszugriff erfolgreich widerrufen",
    errorUpdateUser: "Fehler beim Aktualisieren des Benutzerzugriffs",
    errorNoOrg:
      "Teilen nicht möglich: Konto ist mit keiner Organisation verknüpft",
    errorUpdateOrg: "Fehler beim Aktualisieren des Organisationszugriffs",
    close: "Schließen",
    grantAccess: "Zugriff gewähren",
    revokeAccess: "Zugriff widerrufen",
  },

  // =========================
  // ONBOARDING
  // =========================
  onboarding: {
    welcome: "Willkommen bei",
    "get-started": "Loslegen",
    "llm-preference": {
      title: "LLM-Voreinstellung",
      description:
        "ISTLLM kann mit vielen LLM-Anbietern arbeiten. Dies wird der Service sein, der den Chat betreibt.",
      "LLM-search": "LLM-Anbieter durchsuchen",
    },
    "user-setup": {
      title: "Benutzereinrichtung",
      description: "Richte deine Benutzereinstellungen ein.",
      "sub-title": "Wie viele Personen werden deine Instanz nutzen?",
      "single-user": "Nur ich",
      "multiple-user": "Mein Team",
      "setup-password": "Möchtest du ein Passwort einrichten?",
      "password-requirment":
        "Passwörter müssen mindestens 8 Zeichen lang sein.",
      "save-password":
        "Es ist wichtig, dieses Passwort zu speichern, da es keine Wiederherstellungsmöglichkeit gibt.",
      "password-label": "Instanz-Passwort",
      username: "Admin-Konto E-Mail",
      password: "Admin-Konto Passwort",
      "account-requirment":
        "Die E-Mail muss gültig sein und nur Kleinbuchstaben, Zahlen, Unterstriche und Bindestriche ohne Leerzeichen enthalten. Das Passwort muss mindestens 8 Zeichen lang sein.",
      "password-note":
        "Standardmäßig bist du der einzige Administrator. Nach Abschluss der Einrichtung kannst du weitere Benutzer oder Administratoren erstellen und einladen. Verliere dein Passwort nicht, da nur Administratoren Passwörter zurücksetzen können.",
    },
    "data-handling": {
      title: "Datenverarbeitung & Datenschutz",
      description:
        "Wir setzen uns für Transparenz und Kontrolle bezüglich deiner persönlichen Daten ein.",
      "llm-label": "LLM-Auswahl",
      "embedding-label": "Embedding-Einstellung",
      "database-lablel": "Vektor-Datenbank",
      "reconfigure-option":
        "Diese Einstellungen können jederzeit in den Einstellungen neu konfiguriert werden.",
    },
    survey: {
      title: "Willkommen bei IST Legal LLM",
      description:
        "Hilf uns, IST Legal LLM auf deine Bedürfnisse zuzuschneiden. Optional.",
      email: "Wie lautet deine E-Mail?",
      usage: "Wofür wirst du die Plattform nutzen?",
      work: "Für die Arbeit",
      "personal-use": "Für den privaten Gebrauch",
      other: "Andere",
      comment: "Irgendwelche Anmerkungen an das Team?",
      optional: "(Optional)",
      feedback: "Vielen Dank für dein Feedback!",
    },
    button: {
      yes: "Ja",
      no: "Nein",
      "skip-survey": "Umfrage überspringen",
    },
    placeholder: {
      "admin-password": "Dein Admin-Passwort",
      "admin-username": "Deine Admin-E-Mail",
      "email-example": "<EMAIL>",
      comment:
        "Wenn du gerade Fragen oder Anmerkungen hast, kannst du sie hier hinterlassen – wir melden uns bei dir. Alternativ schreib <NAME_EMAIL>",
    },
  },

  // =========================
  // DEFAULT SETTINGS FOR LEGAL Q&A
  // =========================
  "default-settings": {
    "canvas-prompt": "Canvas-System-Prompt",
    "canvas-prompt-desc":
      "Prompt für das Canvas-Chat-System. Wird als System-Prompt für Canvas-Chat-Interaktionen verwendet.",
    title: "Standardeinstellungen für Legal Q&A",
    "default-desc":
      "Steuern Sie das Standardverhalten der Arbeitsbereiche für Legal Q&A",
    prompt: "Legal Q&A System-Prompt",
    "prompt-desc":
      "Der Standard-Prompt, der für neue Legal Q&A Arbeitsbereiche verwendet wird. Definieren Sie Kontext und Anweisungen für die KI, um eine Antwort zu generieren. Sie sollten einen sorgfältig formulierten Prompt bereitstellen, damit die KI eine relevante und genaue Antwort generieren kann. Um diese Einstellung auf alle bestehenden Arbeitsbereiche anzuwenden und deren benutzerdefinierte Einstellungen zu überschreiben, verwenden Sie die Schaltfläche unten.",
    "prompt-placeholder": "Geben Sie hier Ihren Prompt ein",
    "toast-success": "System-Standardprompt aktualisiert",
    "toast-fail": "System-Standardprompt konnte nicht aktualisiert werden",
    "apply-all-confirm":
      "Sind Sie sicher, dass Sie diesen Prompt auf alle bestehenden Legal Q&A Arbeitsbereiche anwenden möchten? Diese Aktion kann nicht rückgängig gemacht werden und wird alle benutzerdefinierten Einstellungen überschreiben.",
    "apply-to-all": "Auf alle bestehenden Legal Q&A Arbeitsbereiche anwenden",
    applying: "Wird angewendet...",
    "toast-apply-success":
      "Standardprompt auf {{count}} Arbeitsbereiche angewendet",
    "toast-apply-fail":
      "Standardprompt konnte nicht auf Arbeitsbereiche angewendet werden",
    snippets: {
      title: "Standardmäßige maximale Kontext-Snippets",
      description:
        "Diese Einstellung legt fest, wie viele Kontext-Snippets standardmäßig an das LLM für neue Arbeitsbereiche gesendet werden. Um diese Einstellung auf alle bestehenden Arbeitsbereiche anzuwenden und deren benutzerdefinierte Einstellungen zu überschreiben, verwenden Sie die Schaltfläche unten.",
      recommend:
        "Empfohlener Wert ist mindestens 30. Die Einstellung deutlich höherer Werte erhöht die Verarbeitungszeit, ohne zwangsläufig die Präzision abhängig von der Kapazität des verwendeten LLM zu verbessern.",
    },
    "rerank-limit": {
      title: "Maximales Reranking-Limit",
      description:
        "Diese Einstellung legt die maximale Anzahl von Dokumenten fest, die für das Reranking in Betracht gezogen werden. Ein höherer Wert kann bessere Ergebnisse liefern, erhöht jedoch die Verarbeitungszeit.",
      recommend: "Empfohlen: 50",
    },
    "validation-prompt": {
      title: "Validierungsprompt",
      description:
        "Diese Einstellung legt den Standard-Prompt fest, der an das LLM gesendet wird, um die gegebene Antwort zu validieren.",
      placeholder:
        "Bitte validiere die folgende Antwort, indem du alle rechtlichen Referenzen und Zitate auf Genauigkeit im Vergleich zum bereitgestellten Kontext überprüfst. Liste alle Ungenauigkeiten oder Fehler auf.",
    },
    "apply-vector-search-to-all":
      "Auf alle bestehenden Legal Q&A Arbeitsbereiche anwenden",
    "apply-vector-search-all-confirm":
      "Sind Sie sicher, dass Sie diese Vektorsuche-Einstellung auf alle bestehenden Legal Q&A Arbeitsbereiche anwenden möchten? Diese Aktion kann nicht rückgängig gemacht werden.",
    "toast-vector-search-apply-success":
      "Vektorsuche-Einstellung auf {{count}} Arbeitsbereiche angewendet",
    "toast-vector-search-apply-fail":
      "Anwendung der Vektorsuche-Einstellung auf Arbeitsbereiche fehlgeschlagen",
    "canvas-upload-prompt": "Canvas-Upload-System-Prompt",
    "canvas-upload-prompt-desc":
      "Das System-Prompt, das beim Verarbeiten von Uploads über das Canvas verwendet wird. Dieses Prompt steuert das Verhalten des LLM für hochgeladene Inhalte.",
  },

  // =========================
  // CONFIRM MESSAGE
  // =========================
  "confirm-message": {
    "delete-doc-title": "Dateien und Ordner löschen",
    "delete-doc":
      "Bist du sicher, dass du diese Dateien und Ordner löschen möchtest?\nDies entfernt die Dateien aus dem System und automatisch aus allen Arbeitsbereichen.\nDiese Aktion ist unwiderruflich.",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Rechtliche Aufgabe ausführen",
    noTaskfund: "Keine rechtlichen Aufgaben verfügbar",
    noSubtskfund: "Keine Unterkategorien verfügbar.",
    "loading-subcategory": "Lade Unterkategorien...",
    "select-category": "Kategorie auswählen",
    "choose-task": "Rechtliche Aufgabe zum Ausführen wählen",
    "duration-info":
      "Die Zeit für die Ausführung einer rechtlichen Aufgabe hängt von der Anzahl der Dokumente im Arbeitsbereich ab. Bei vielen Dokumenten und einer komplexen Aufgabe kann dies sehr lange dauern.",
    successMessage: "Die rechtliche Aufgabe wurde {{status}}",
    failureUpdateMessage:
      "Fehler beim Aktualisieren der Einstellung für rechtliche Aufgaben.",
    subStep: "Laufende oder in der Warteschlange befindliche Teilaufgabe",
    errorSubmitting:
      "Fehler beim Übermitteln der Einstellungen für rechtliche Aufgaben.",
    "additional-instructions-label": "Zusätzliche Anweisungen:",
    "custom-instructions-placeholder":
      "Geben Sie zusätzliche Anweisungen für die rechtliche Aufgabe ein (optional)...",
    "warning-title": "Warnung",
    "no-files-title": "Keine Dateien verfügbar",
    "no-files-description":
      "Es gibt keine Dateien in diesem Arbeitsbereich. Bitte laden Sie mindestens eine Datei vor der Ausführung einer rechtlichen Aufgabe hoch.",
    "settings-button":
      "Verfügbare rechtliche Aufgaben hinzufügen oder bearbeiten",
    settings: "Rechtliche Aufgaben-Einstellungen",
  },

  // =========================
  // CANVAS CHAT
  // =========================
  canvasChat: {
    title: "Canvas",
    "input-placeholder": "Rechtliche Informationen anfragen",
    chatboxinstruction: "Passe die Antwort nach Bedarf an",
    explanation:
      "Dieses Tool ist für die AI-Bearbeitung der Antwort in verschiedenen Weisen. Die Quellen für die zugrunde liegende Antwort werden angewendet, was bedeutet, dass du zusätzliche Klärungen anfragen kannst, indem du die gleichen Quellenmaterialien verwendest, die für die zugrunde liegende Antwort verwendet wurden.",
    editAnswer: "Antwort bearbeiten",
  },

  // =========================
  // STATUSES
  // =========================
  statuses: {
    enabled: "Aktiviert",
    disabled: "Deaktiviert",
  },

  // =========================
  // ANSWER UPGRADE
  // =========================

  // Moved to answerUpgrade.js

  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "PDR-Einstellungen",
    description:
      "Konfiguriere die Einstellungen für die Parent Document Retrieval (PDR) in deinen Arbeitsbereichen.",
    "desc-end":
      "Diese Einstellungen beeinflussen, wie PDR-Dokumente verarbeitet und in Chat-Antworten genutzt werden.",
    "global-override": {
      title: "Globales dynamisches PDR-Override",
      description:
        "Wenn aktiviert, werden alle Arbeitsbereich-Dokumente als PDR-aktiviert für den Kontext in Antworten behandelt. Wenn deaktiviert, werden nur Dokumente verwendet, die explizit als PDR markiert sind, was den verfügbaren Kontext reduzieren kann und zu deutlich schlechteren Antworten führen kann, da nur Vektorchunks aus der Suche als Quellen in diesen Fällen verwendet werden.",
    },
    "toast-success": "PDR-Einstellungen aktualisiert",
    "toast-fail": "Fehler beim Aktualisieren der PDR-Einstellungen",
    "adjacent-vector-limit": "Limit für benachbarte Vektoren",
    "adjacent-vector-limit-desc": "Begrenzung für benachbarte Vektoren.",
    "keep-pdr-vectors": "PDR-Vektoren behalten",
    "keep-pdr-vectors-desc": "Einstellung zum Behalten der PDR-Vektoren.",
    "adjacent-vector-limit-placeholder":
      "Limit für benachbarte Vektoren eingeben",
  },

  // =========================
  // VALIDATE RESPONSE
  // =========================
  "validate-response": {
    title: "Validierungsergebnis",
    "toast-fail": "Antwort konnte nicht validiert werden",
    validating: "Antwort wird validiert",
    button: "Antwort validieren",
    "adjust-prefix":
      "Übernehme alle angegebenen Änderungen an der Antwort basierend auf diesem Feedback: ",
    "adjust-button": "Vorgeschlagene Änderungen anwenden",
  },

  // =========================
  // WORKSPACE NAMES (LEGAL AREAS)
  // =========================
  "workspace-names": {
    "Administrative Law": "Verwaltungsrecht",
    "Business Law": "Wirtschaftsrecht",
    "Civil Law": "Zivilrecht",
    "Criminal Law": "Strafrecht",
    "Diplomatic Law": "Diplomatisches Recht",
    "Fundamental Law": "Grundgesetz",
    "Human Rights Law": "Menschenrechte",
    "Judicial Laws": "Gerichtsrecht",
    "Security Laws": "Sicherheitsrecht",
    "Taxation Laws": "Steuerrecht",
  },

  // =========================
  // VALIDATE ANSWER
  // =========================
  "validate-answer": {
    setting: "Validierungs-LLM",
    title: "Validierungs-LLM-Voreinstellung",
    description:
      "Dies sind die Zugangsdaten und Einstellungen für deinen bevorzugten Validierungs-LLM-Chat- und Embedding-Anbieter. Es ist wichtig, dass diese Schlüssel aktuell und korrekt sind, sonst funktioniert das System nicht ordnungsgemäß.",
    "toast-success": "Validierungs-LLM-Einstellungen aktualisiert",
    "toast-fail":
      "Fehler beim Aktualisieren der Validierungs-LLM-Einstellungen",
    saving: "Wird gespeichert...",
    "save-changes": "Änderungen speichern",
  },

  // =========================
  // ACTIVE CASE
  // =========================
  "active-case": {
    title: "Aktiver Fall",
    placeholder: "Referenznummer eingeben",
    "select-reference": "Referenz auswählen",
    "warning-title": "Fehlende Referenznummer",
    "warning-message":
      "Es wurde keine Referenznummer festgelegt. Möchten Sie ohne Referenznummer fortfahren?",
  },

  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Der Multi-User-Modus ist aus Sicherheitsgründen dauerhaft aktiviert",
    "password-validation": {
      "restricted-chars":
        "Ihr Passwort enthält unzulässige Zeichen. Erlaubte Symbole sind _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "Wenn aktiviert, kann jeder Benutzer ohne Anmeldung auf die öffentlichen Arbeitsbereiche zugreifen.",
    },
    button: {
      saving: "Speichern...",
      "save-changes": "Änderungen speichern",
    },
  },

  // =========================
  // ERRORS
  // =========================
  errors: {
    "fetch-models": "Modelle konnten nicht abgerufen werden",
    "fetch-models-error": "Fehler beim Abrufen der Modelle",
    "upgrade-error": "Fehler beim Upgrade",
    common: {
      error: "Fehler",
    },
    streaming: {
      failed:
        "Ein Fehler ist beim Streaming der Antwort aufgetreten, z.B. weil die KI-Engine offline oder überlastet ist.",
      code: "Code",
      unknown: "Unbekannter Fehler.",
    },
    workspace: {
      "already-exists": "Ein Arbeitsbereich mit diesem Namen existiert bereits",
    },
    auth: {
      "invalid-credentials": "Ungültige Anmeldedaten.",
      "account-suspended": "Konto wurde vom Administrator gesperrt.",
      "invalid-password": "Ungültiges Passwort angegeben",
    },
    env: {
      "anthropic-key-format":
        "Anthropic API-Schlüssel muss mit 'sk-ant-' beginnen",
      "openai-key-format": "OpenAI API-Schlüssel muss mit 'sk-' beginnen",
      "jina-key-format": "Jina API-Schlüssel muss mit 'jina_' beginnen",
    },
    "invalid-token-count": "Ungültige Token-Anzahl",
  },

  // =========================
  // LOADING STATES
  // =========================
  loading: {
    models: "-- lade verfügbare Modelle --",
    "waiting-url": "-- warte auf URL --",
    "waiting-api-key": "-- warte auf API-Schlüssel --",
    "waiting-models": "-- warte auf Modelle --",
  },

  // =========================
  // MODEL SELECTION
  // =========================

  // =========================
  // CHARTS
  // =========================
  charts: {
    downloading: "Bild wird heruntergeladen...",
    download: "Diagrammbild herunterladen",
  },

  // =========================
  // LITELLM
  // =========================

  // =========================
  // GENERIC LLM SETTINGS
  // =========================

  // =========================
  // MODALS
  // =========================
  modals: {
    warning: {
      title: "Warnung",
      proceed: "Bist du sicher, dass du fortfahren möchtest?",
      cancel: "Abbrechen",
      confirm: "Bestätigen",
      "got-it": "Okay, verstanden",
    },
  },

  // =========================
  // DOCUMENTS & PINNING
  // =========================
  documents: {
    "pin-info-button": "Über Dokument-Pinning",
    "pin-title": "Was ist Dokument-Pinning?",
    "pin-desc-1":
      "Beim Pinning eines Dokuments wird der gesamte Inhalt des Dokuments in dein Prompt-Fenster eingebunden, damit dein LLM den Inhalt vollständig erfassen kann.",
    "pin-desc-2":
      "Dies funktioniert am besten bei Modellen mit großem Kontext oder bei kleinen Dateien, die essenziell für das Wissensfundament sind.",
    "pin-desc-3":
      "Falls du nicht die gewünschten Antworten erhältst, ist Pinning eine einfache Möglichkeit, qualitativ hochwertigere Antworten zu erzielen.",
    "pin-add": "An Arbeitsbereich anheften",
    "pin-unpin": "Von Arbeitsbereich lösen",
    "watch-title": "Was bewirkt das 'Watching' eines Dokuments?",
    "watch-desc-1":
      "Wenn du ein Dokument beobachtest, wird dessen Inhalt in regelmäßigen Abständen automatisch mit der Originalquelle synchronisiert. So wird der Inhalt in allen Arbeitsbereichen aktualisiert, in denen diese Datei verwaltet wird.",
    "watch-desc-2":
      "Diese Funktion unterstützt derzeit nur online-basierte Inhalte und ist für manuell hochgeladene Dokumente nicht verfügbar.",
    "watch-desc-3": "Die Verwaltung beobachteter Dokumente findest du im",
    "file-manager": "Dateimanager",
    "admin-view": "Admin-Ansicht",
    "pdr-add": "Alle Dokumente zur PDR hinzugefügt",
    "pdr-remove": "Alle Dokumente aus der PDR entfernt",
    empty: "Keine Dokumente gefunden",
    tooltip: {
      date: "Datum: ",
      type: "Typ: ",
      cached: "Zwischengespeichert",
    },
    actions: {
      removing: "Entferne Datei aus Arbeitsbereich",
    },
    costs: {
      estimate: "Geschätzte Kosten: $",
      minimum: "< $0.01",
    },
    "new-folder": {
      title: "Neuen Ordner erstellen",
      "name-label": "Ordnername",
      "name-placeholder": "Ordnername eingeben",
      create: "Ordner erstellen",
    },
    error: {
      "create-folder": "Fehler beim Erstellen des Ordners",
    },
    "pin-chat": "Dokument anpinnen",
  },

  // =========================
  // LEGAL QUESTION
  // =========================
  "legal-question": {
    "category-one": "Kategorie Eins",
    "category-two": "Kategorie Zwei",
    "category-three": "Kategorie Drei",
    "category-four": "Kategorie Vier",
    "category-five": "Kategorie Fünf",
    "item-one": "Punkt Eins",
    "item-two": "Punkt Zwei",
    "item-three": "Punkt Drei",
    "item-four": "Punkt Vier",
    "item-five": "Punkt Fünf",
    "item-six": "Punkt Sechs",
    "item-seven": "Punkt Sieben",
    "example-title": "Iss Gut: Ein Leitfaden zum Genießen von Speisen",
    example: {
      breakfast: {
        title: "1. Gesunde Frühstücksoptionen",
        items: [
          "Haferbrei mit frischen Früchten und Honig",
          "Griechischer Joghurt mit Granola",
          "Avocadotoast mit pochierten Eiern",
          "Grüner Smoothie mit Spinat, Banane und Mandelmilch",
          "Vollkornpfannkuchen mit Ahornsirup",
        ],
      },
      lunch: {
        title: "2. Schnelle und einfache Mittagsideen",
        items: [
          "Gegrillter Hähnchen-Wrap mit gemischtem Salat",
          "Quinoasalat mit geröstetem Gemüse",
          "Truthahnsandwich auf Vollkornbrot",
          "Gemüsepfanne mit Naturreis",
          "Suppe und Beilagensalat",
        ],
      },
      dinner: {
        title: "3. Köstliche Abendessen-Rezepte",
        items: [
          "Gebackener Lachs mit Zitrone und Spargel",
          "Spaghetti mit Tomatensauce und Fleischbällchen",
          "Gemüsecurry mit Basmati-Reis",
          "Gegrilltes Steak mit Ofenkartoffeln",
          "Gefüllte Paprika mit Quinoa und Käse",
        ],
      },
    },
  },

  // =========================
  // AZURE SETTINGS
  // =========================

  // =========================
  // KOBOLDCPP SETTINGS
  // =========================

  // =========================
  // PRESETS
  // =========================
  presets: {
    "edit-title": "Standard-Prompt bearbeiten",
    description: "Beschreibung des Prompts",
    "description-placeholder":
      "Macht einen Zusammenfassung der angehängten Dateien.",
    deleting: "Wird gelöscht...",
    "delete-preset": "Standard-Prompt löschen",
    cancel: "Abbrechen",
    save: "Speichern",
    "add-title": "Standard-Prompt hinzufügen",
    "command-label": "Name des Prompts, ein einzelner Wort",
    "command-placeholder": "Zusammenfassung",
    "command-desc":
      "Der Name ist auch der Chatbox-Shortcut, der mit / beginnt, um dieses Prompt ohne Buttons zu verwenden.",
    "prompt-label": "Prompt",
    "prompt-placeholder":
      "Produziere eine Zusammenfassung der angehängten Dateien.",
    "prompt-desc":
      "Das Prompt, das gesendet wird, wenn dieses Standard-Prompt verwendet wird.",
    "tooltip-add": "Neues Standard-Prompt hinzufügen",
    "tooltip-hover": "Deine eigenen Standard-Prompts ansehen.",
    "confirm-delete": "Bestätige das Löschen der Standard-Prompt-Vorlage.",
  },

  // =========================
  // LEGAL CATEGORIES
  // =========================
  "legal-categories": {
    process: "Prozess",
    "process-stamning": "Klageerhebung",
    "process-svaromal": "Klagerwiderlegung",
    "process-yrkanden": "Forderungen und Darstellung",
    avtal: "Verträge",
    "avtal-anstallning": "Arbeitsverträge",
    "avtal-finansiering": "Finanzierungs- und Sicherungsverträge",
    "avtal-licens": "Lizenzverträge",
    "due-diligence": "Due Diligence",
    "due-diligence-avtal": "Vertragsprüfung",
    "due-diligence-checklista": "Due-Diligence-Checkliste",
    "due-diligence-compliance": "Compliance-Prüfung",
  },

  // =========================
  // VALIDATION
  // =========================
  validation: {
    responseHeader: "Hier ist die generierte Antwort",
    contextHeader: "Ursprünglicher Kontext und Quellen",
  },

  // =========================
  // DOCX EDITOR
  // =========================
  "docx-edit": {
    "edit-instructions":
      "Gib Anweisungen ein, wie du das Dokument bearbeiten möchtest. Sei spezifisch, welche Änderungen du vornehmen möchtest.",
    "instructions-placeholder":
      "z.B. Grammatikfehler korrigieren, den Ton formeller gestalten, einen Abschlussparagraphen hinzufügen...",
    "process-button": "Dokument verarbeiten",
    "upload-docx": "DOCX hochladen",
    "processing-upload": "Verarbeitung...",
    "content-extracted": "Inhalt aus DOCX-Datei extrahiert",
    "file-type-note": "Nur .docx-Dateien werden unterstützt",
    "upload-error": "Fehler beim Hochladen der Datei: ",
    "no-instructions": "Bitte gib Bearbeitungsanweisungen ein",
    "process-error": "Fehler bei der Verarbeitung des Dokuments: ",
    "changes-highlighted": "Dokument mit hervorgehobenen Änderungen",
    "download-button": "Dokument herunterladen",
    "start-over-button": "Neu beginnen",
    "no-document": "Kein Dokument zum Herunterladen verfügbar",
    "download-error": "Fehler beim Herunterladen des Dokuments: ",
    "download-success": "Dokument erfolgreich heruntergeladen",
    processing: "Dokument wird verarbeitet...",
    "instructions-used": "Verwendete Anweisungen",
    "import-success": "DOCX-Inhalt erfolgreich importiert",
    "edit-success": "DOCX-Inhalt erfolgreich aktualisiert",
    "canvas-document-title": "Canvas-Dokument",
    "upload-button": "DOCX hochladen",
    "download-as-docx": "Als DOCX herunterladen",
    "output-example": "Ausgabebeispiel",
    "output-example-desc":
      "Laden Sie eine DOCX-Datei hoch, um Beispielinhalte zu Ihrem Prompt hinzuzufügen",
    "content-examples-tag-open": "<INHALTS_BEISPIEL>",
    "content-examples-tag-close": "</INHALTS_BEISPIEL>",
    "content-examples-info":
      "<INFO>Dies ist ein Beispiel für den zu erstellenden Inhalt aus einer ähnlichen juristischen Aufgabe. Beachten Sie, dass dieser Beispielinhalt viel kürzer oder länger sein kann als der Inhalt, der jetzt erstellt werden soll.</INFO>",
    "contains-example-content": "[Enthält Beispielinhalt]",
  },

  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================

  // Document Builder Page
  "document-builder": {
    title: "Dokumentenersteller-Einstellungen",
    description: "Steuern Sie die Einstellungen für den Dokumentenersteller.",
    "toast-success": "Einstellungen erfolgreich aktualisiert",
    "toast-fail": "Aktualisierung der Einstellungen fehlgeschlagen",
    save: "Speichern",
    saving: "Speichern...",

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================

    "view-categories": "Alle Kategorien anzeigen",
    "hide-categories": "Liste ausblenden",
    "add-task": "Juristische Aufgabe hinzufügen",
    loading: "Laden...",
    table: {
      title: "Juristische Aufgaben",
      name: "Name",
      "sub-category": "Unterkategorie",
      description: "Beschreibung",
      prompt: "Juristischer Aufgabenprompt",
      actions: "Aktionen",
      "no-tasks": "Keine juristischen Aufgaben verfügbar.",
      delete: "Löschen",
      edit: "Bearbeiten",
      "delete-confirm":
        "Sind Sie sicher, dass Sie diese Kategorie löschen möchten?",
      "delete-success": "Kategorie wurde gelöscht",
      "delete-error": "Kategorie konnte nicht gelöscht werden",
    },
    "create-task": {
      title: "Juristische Aufgabe erstellen",
      category: {
        name: "Kategoriename",
        desc: "Geben Sie den Namen der Hauptkategorie ein.",
        placeholder: "Kategoriename eingeben",
        type: "Kategorietyp",
        new: "Neue Kategorie erstellen",
        existing: "Bestehende Kategorie verwenden",
        select: "Kategorie auswählen",
        "select-placeholder": "Wählen Sie eine bestehende Kategorie",
      },
      subcategory: {
        name: "Name der juristischen Aufgabe",
        desc: "Geben Sie den Namen der juristischen Aufgabe ein.",
        placeholder: "Name der juristischen Aufgabe eingeben",
      },
      description: {
        name: "Beschreibung und Benutzeranweisungen",
        desc: "Die Informationen und Anweisungen, die der Benutzer sehen wird.",
        placeholder:
          "Beschreiben Sie z.B. welche Arten von Dokumenten in den Arbeitsbereich hochgeladen werden müssen, um das bestmögliche Ergebnis zu erzielen",
      },
      prompt: {
        name: "Juristischer Aufgaben-Prompt",
        desc: "Geben Sie den Prompt ein, der für diese rechtliche Aufgabe verwendet werden soll. Sie können auch Beispieldokumente hochladen, um Inhaltsbeispiele zu Ihrem Prompt hinzuzufügen.",
        placeholder:
          "Geben Sie den rechtlichen Aufgabenprompt ein oder laden Sie Beispieldokumente hoch, um Ihren Prompt zu verbessern...",
      },
      submitting: "Wird gesendet...",
      submit: "Absenden",
      validation: {
        "category-required": "Kategoriename ist erforderlich.",
        "subcategory-required":
          "Name der juristischen Aufgabe ist erforderlich.",
        "description-required": "Beschreibung ist erforderlich.",
        "prompt-required": "Rechtlicher Prompt ist erforderlich.",
      },
    },
    "edit-task": {
      title: "Rechtliche Aufgabe bearbeiten",
      submitting: "Wird aktualisiert...",
      submit: "Aufgabe aktualisieren",
      subcategory: {
        name: "Name der rechtlichen Aufgabe",
        desc: "Geben Sie einen neuen Namen für diese rechtliche Aufgabe ein",
        placeholder: "Rechtliche Aufgabe eingeben...",
      },
      description: {
        name: "Beschreibung und Benutzeranweisungen",
        desc: "Geben Sie Beschreibung und Benutzeranweisungen für diese rechtliche Aufgabe ein",
        placeholder: "Beschreibung und Benutzeranweisungen eingeben...",
      },
      prompt: {
        name: "Rechtlicher Aufgabenprompt",
        desc: "Geben Sie den Prompt ein, der für diese rechtliche Aufgabe verwendet werden soll. Sie können auch Beispieldokumente hochladen, um Inhaltsbeispiele zu Ihrem Prompt hinzuzufügen.",
        placeholder:
          "Geben Sie den rechtlichen Aufgabenprompt ein oder laden Sie Beispieldokumente hoch, um Ihren Prompt zu verbessern...",
      },
      validation: {
        "subcategory-required": "Name der rechtlichen Aufgabe ist erforderlich",
        "description-required": "Beschreibung ist erforderlich",
        "prompt-required": "Rechtlicher Aufgabenprompt ist erforderlich",
      },
      "task-form": {
        "requires-main-doc-label": "Auswahl des Hauptdokuments erforderlich",
        "requires-main-doc-description":
          "Wenn diese Option aktiviert ist, muss der Benutzer bei der Ausführung dieser Aufgabe das Hauptdokument aus den hochgeladenen Dateien auswählen. Dies wird dringend empfohlen für juristische Aufgaben, die eine Antwort auf ein Schreiben oder eine Gerichtsvorlage oder ähnliches beinhalten, da es die Ausgabe basierend auf dem Dokument strukturiert, auf das geantwortet wird.",
        "requires-main-doc-placeholder": "Ja oder Nein",
        "requires-main-doc-explanation-default":
          "Eine Auswahl ist erforderlich, da dies bestimmt, wie das Dokument erstellt wird.",
        "requires-main-doc-explanation-yes":
          "Bei 'Ja' muss der Benutzer beim Starten dieser juristischen Aufgabe ein Hauptdokument auswählen. Dieses Dokument wird zentral für den Arbeitsablauf der Aufgabe sein.",
        "requires-main-doc-explanation-no":
          "Bei 'Nein' wird die juristische Aufgabe ohne ein vorausgewähltes Hauptdokument fortgesetzt. Die Aufgabe wird dynamischer eine Ausgabe basierend auf allen hochgeladenen Dokumenten und der juristischen Aufgabe erstellen.",
      },
    },
    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Generator Prompt überprüfen",
    reviewGeneratorPromptButtonTooltip:
      "Zeige den genauen Prompt-Vorlage, die verwendet wurde, um das juristische Aufgabenvorschlag zu generieren. (Nur Administratoren)",
    reviewGeneratorPromptTitle: "Generator Prompt Überprüfen",
    reviewPromptLabel: "Der folgende Prompt wurde verwendet:",
    reviewPromptTextareaLabel: "Generator Prompt Inhalt",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Dokumentzusammenfassung-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Dokumentzusammenfassung.",
        },
        document_relevance: {
          title: "Dokumentrelevanz-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Dokumentrelevanz.",
        },
        section_drafting: {
          title: "Abschnittserstellung-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Abschnittserstellung.",
        },
        section_legal_issues: {
          title: "Rechtliche Probleme für Abschnitt-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für rechtliche Probleme in Abschnitten.",
        },
        memo_creation: {
          title: "Memo-Erstellung-Prompts",
          description: "Konfigurieren Sie Prompts für die Memo-Erstellung.",
        },
        section_index: {
          title: "Abschnittsindex-Prompts",
          description: "Konfigurieren Sie Prompts für den Abschnittsindex.",
        },
        select_main_document: {
          title: "Hauptdokument-Auswahl-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Hauptdokument-Auswahl.",
        },
        section_list_from_main: {
          title: "Abschnittsliste aus Hauptdokument-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Abschnittsliste aus dem Hauptdokument.",
        },
        section_list_from_summaries: {
          title: "Abschnittsliste aus Zusammenfassungen-Prompts",
          description:
            "Konfigurieren Sie System- und Benutzer-Prompts für die Abschnittsliste aus Zusammenfassungen.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Dokumentzusammenfassung (System)",
      "document-summary-system-description":
        "System-Prompt zur Anweisung der KI, wie der Inhalt eines Dokuments und seine Relevanz für eine juristische Aufgabe zusammengefasst werden soll.",
      "document-summary-user-label": "Dokumentzusammenfassung (Benutzer)",
      "document-summary-user-description":
        "Benutzer-Prompt-Vorlage zur Erstellung einer detaillierten Zusammenfassung des Dokumentinhalts in Bezug auf eine bestimmte juristische Aufgabe.",

      // Document Relevance
      "document-relevance-system-label": "Dokumentrelevanz (System)",
      "document-relevance-system-description":
        "System-Prompt zur Bewertung, ob ein Dokument für eine juristische Aufgabe relevant ist, wobei eine Wahr/Falsch-Antwort erwartet wird.",
      "document-relevance-user-label": "Dokumentrelevanz (Benutzer)",
      "document-relevance-user-description":
        "Benutzer-Prompt-Vorlage zur Überprüfung, ob der Dokumentinhalt für eine bestimmte juristische Aufgabe relevant ist.",

      // Section Drafting
      "section-drafting-system-label": "Abschnittserstellung (System)",
      "section-drafting-system-description":
        "System-Prompt zur Erstellung eines einzelnen Dokumentabschnitts in professionellem juristischen Stil unter Verwendung spezifizierter Dokumente und Kontext.",
      "section-drafting-user-label": "Abschnittserstellung (Benutzer)",
      "section-drafting-user-description":
        "Benutzer-Prompt-Vorlage zur Erstellung eines bestimmten Abschnitts eines juristischen Dokuments unter Berücksichtigung von Titel, Aufgabe, Quelldokumenten und benachbarten Abschnitten.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Identifizierung rechtlicher Probleme für Abschnitt (System)",
      "section-legal-issues-system-description":
        "System-Prompt zur Identifizierung spezifischer rechtlicher Themen, für die Sachinformationen zur Unterstützung bei der Erstellung eines Dokumentabschnitts abgerufen werden sollten.",
      "section-legal-issues-user-label":
        "Identifizierung rechtlicher Probleme für Abschnitt (Benutzer)",
      "section-legal-issues-user-description":
        "Benutzer-Prompt-Vorlage zum Auflisten rechtlicher Themen oder Datenpunkte zum Abrufen von Hintergrundinformationen, die für einen bestimmten Dokumentabschnitt und eine juristische Aufgabe relevant sind.",

      // Memo Creation
      "memo-creation-template-label": "Standard-Memo-Erstellungsvorlage",
      "memo-creation-template-description":
        "Prompt-Vorlage zur Erstellung eines juristischen Memorandums zu einem bestimmten rechtlichen Problem unter Berücksichtigung der bereitgestellten Dokumente und des Aufgabenkontexts.",

      // Section Index
      "section-index-system-label": "Abschnittsindex (System)",
      "section-index-system-description":
        "System-Prompt zur Erstellung eines strukturierten Index von Abschnitten für ein juristisches Dokument.",

      // Select Main Document
      "select-main-document-system-label": "Hauptdokument auswählen (System)",
      "select-main-document-system-description":
        "System-Prompt zur Identifizierung des relevantesten Hauptdokuments für eine juristische Aufgabe aus mehreren Dokumentzusammenfassungen.",
      "select-main-document-user-label": "Hauptdokument auswählen (Benutzer)",
      "select-main-document-user-description":
        "Benutzer-Prompt-Vorlage zur Identifizierung des Hauptdokuments für eine juristische Aufgabe anhand von Zusammenfassungen mehrerer Dokumente.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Abschnittsliste aus Hauptdokument (System)",
      "section-list-from-main-system-description":
        "System-Prompt zur Erstellung einer JSON-strukturierten Liste von Abschnitten für ein juristisches Dokument basierend auf dem Inhalt des Hauptdokuments und der juristischen Aufgabe.",
      "section-list-from-main-user-label":
        "Abschnittsliste aus Hauptdokument (Benutzer)",
      "section-list-from-main-user-description":
        "Benutzer-Prompt-Vorlage zur Bereitstellung der juristischen Aufgabe und des Hauptdokumentinhalts zur Generierung einer Abschnittsliste.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Abschnittsliste aus Zusammenfassungen (System)",
      "section-list-from-summaries-system-description":
        "System-Prompt zur Erstellung einer JSON-strukturierten Liste von Abschnitten basierend auf Dokumentzusammenfassungen und der juristischen Aufgabe, wenn kein Hauptdokument existiert.",
      "section-list-from-summaries-user-label":
        "Abschnittsliste aus Zusammenfassungen (Benutzer)",
      "section-list-from-summaries-user-description":
        "Benutzer-Prompt-Vorlage zur Bereitstellung der juristischen Aufgabe und Dokumentzusammenfassungen zur Generierung einer Abschnittsliste, wenn kein Hauptdokument existiert.",
    },
  },

  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Rexor-Projekt registrieren",
    "project-id": "Projekt-ID",
    "resource-id": "Ressourcen-ID",
    "activity-id": "Aktivitäts-ID",
    register: "Projekt registrieren",
    registering: "Registrierung läuft...",
    "not-active": "Dieser Fall ist nicht aktiv für eine Registrierung",
    account: {
      title: "Bei Rexor anmelden",
      username: "Benutzername",
      password: "Passwort",
      "no-token": "Kein Token in handleLoginSuccess erhalten",
      logout: "Abmelden",
      "no-user": "Bitte melde dich zuerst an",
      connected: "Mit Rexor verbunden",
      "not-connected": "Nicht verbunden",
      "change-account": "Konto wechseln",
      "session-expired": "Sitzung abgelaufen. Bitte melde dich erneut an.",
    },
    "hide-article-transaction": "Formular für Zeittransaktionen ausblenden",
    "show-article-transaction": "Formular für Zeittransaktionen anzeigen",
    "article-transaction-title": "Zeittransaktion hinzufügen",
    "registration-date": "Registrierungsdatum",
    description: "Beschreibung",
    "description-internal": "Interne Beschreibung",
    "hours-worked": "Gearbeitete Stunden",
    "invoiced-hours": "Berechnete Stunden",
    invoiceable: "Abrechenbar",
    "sending-article-transaction": "Zeittransaktion wird gesendet...",
    "save-article-transaction": "Zeittransaktion speichern",
    "project-not-register": "Projekt muss zuerst registriert werden.",
    "article-transaction-error": "Fehler beim Absenden der Zeittransaktion",
    "not-exist": "Dieser Fall konnte nicht gefunden werden",
    "invoice-text": "Foynet Anzahl der Abfragen",
  },

  // =========================
  // OPTIONS
  // =========================
  options: {
    yes: "Ja",
    no: "Nein",
  },

  // =========================
  // AZURE AI
  // =========================

  // =========================
  // COHERE
  // =========================

  // =========================
  // JINA
  // =========================
  jina: {
    "api-key": "Jina API-Schlüssel",
    "api-key-placeholder": "Gib deinen Jina API-Schlüssel ein",
    "api-key-error": "Der API-Schlüssel muss mit 'jina_' beginnen",
    "model-preference": "Modell-Voreinstellung",
  },

  // =========================
  // OLLAMA
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Maximale Embedding-Chuck-Länge",
  },

  // =========================
  // OPENAI
  // =========================

  // =========================
  // VOYAGEAI EMBEDDING
  // =========================
  voyageai: {
    "api-key": "VoyageAI API-Schlüssel",
    "api-key-placeholder": "Gib deinen VoyageAI API-Schlüssel ein",
    "model-preference": "Modell-Voreinstellung",
  },

  // =========================
  // ANTHROPIC
  // =========================

  // =========================
  // FIREWORKSAI
  // =========================

  // =========================
  // PERPLEXITY
  // =========================

  // =========================
  // METRICS VISIBILITY
  // =========================
  "metrics.visibility.hover": "Metriken sind sichtbar.",
  "metrics.visibility.available": "Metriken sind verfügbar.",

  // =========================
  // PROMPT ERRORS
  // =========================
  prompt: {
    error: {
      empty: "Prompt darf nicht leer sein",
      upgrade: "Fehler beim Aktualisieren des Prompts",
    },
    decline: "Ablehnen",
  },

  // =========================
  // AGENT MENU
  // =========================
  "agent-menu": {
    "default-agent": "Standard-Agent",
    "ability.rag-search": "RAG-Suche",
    "ability.web-scraping": "Web-Scraping",
    "ability.web-browsing": "Web-Browsing",
    "ability.save-file-to-browser": "Datei im Browser speichern",
    "ability.list-documents": "Dokumente auflisten",
    "ability.summarize-document": "Dokument zusammenfassen",
    "ability.chart-generation": "Diagrammerstellung",
  },

  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "{{provider}}-Logo",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "LM Studio Einbettungsmodell",
      "max-chunk-length": "Maximale Chunk-Länge",
      "max-chunk-length-help":
        "Maximale Länge der Text-Chunks für die Einbettung.",
      "hide-endpoint": "Manuelle Endpunkt-Eingabe ausblenden",
      "show-endpoint": "Manuelle Endpunkt-Eingabe anzeigen",
      "base-url": "LM Studio Basis-URL",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help":
        "Geben Sie die URL ein, unter der LM Studio ausgeführt wird.",
      "auto-detect": "Automatisch erkennen",
      "loading-models": "--lade verfügbare Modelle--",
      "enter-url-first": "Geben Sie zuerst die LM Studio URL ein",
      "model-help":
        "Wählen Sie das LM Studio Modell für Einbettungen. Die Modelle werden geladen, nachdem eine gültige LM Studio URL eingegeben wurde.",
      "loaded-models": "Ihre geladenen Modelle",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Ollama Einbettungsmodell",
      "max-chunk-length": "Maximale Chunk-Länge",
      "max-chunk-length-help":
        "Maximale Länge der Text-Chunks für die Einbettung.",
      "hide-endpoint": "Manuelle Endpunkt-Eingabe ausblenden",
      "show-endpoint": "Manuelle Endpunkt-Eingabe anzeigen",
      "base-url": "Ollama Basis-URL",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help":
        "Geben Sie die URL ein, unter der Ollama ausgeführt wird.",
      "auto-detect": "Automatisch erkennen",
      "loading-models": "--lade verfügbare Modelle--",
      "enter-url-first": "Geben Sie zuerst die Ollama URL ein",
      "model-help":
        "Wählen Sie das Ollama Modell für Einbettungen. Die Modelle werden geladen, nachdem eine gültige Ollama URL eingegeben wurde.",
      "loaded-models": "Ihre geladenen Modelle",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Auswahl des Einbettungsmodells",
      "max-chunk-length": "Maximale Chunk-Länge",
      "max-chunk-length-help":
        "Maximale Länge der Text-Chunks für die Einbettung.",
      "api-key": "API-Schlüssel",
      optional: "optional",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- lade verfügbare Modelle --",
      "waiting-url": "-- warte auf URL --",
      "loaded-models": "Ihre geladenen Modelle",
      "model-tooltip": "Unterstützte Einbettungsmodelle anzeigen unter",
      "model-tooltip-link": "LiteLLM-Dokumentation",
      "model-tooltip-more":
        "für weitere Informationen zu den verfügbaren Modellen.",
    },
    // Cohere Embedding Options
    cohere: {
      "api-key": "Cohere API-Schlüssel",
      "api-key-placeholder": "Geben Sie Ihren Cohere API-Schlüssel ein",
      "model-label": "Modellauswahl",
      "available-models": "Verfügbare Einbettungsmodelle",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Jina API-Schlüssel",
      "api-key-format": "Jina API-Schlüssel muss mit 'jina_' beginnen",
      "api-key-placeholder": "Geben Sie Ihren Jina API-Schlüssel ein",
      "model-label": "Modellauswahl",
      "available-models": "Verfügbare Einbettungsmodelle",
      "embedding-type": "Einbettungstyp",
      "available-types": "Verfügbare Einbettungstypen",
      dimensions: "Dimensionen",
      "available-dimensions": "Verfügbare Dimensionen",
      task: "Aufgabe",
      "available-tasks": "Verfügbare Aufgaben",
      "late-chunking": "Spätes Chunking",
      "late-chunking-help":
        "Aktivieren Sie das späte Chunking für die Dokumentenverarbeitung",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Name des Einbettungsmodells",
      "hide-endpoint": "Erweiterte Einstellungen ausblenden",
      "show-endpoint": "Erweiterte Einstellungen anzeigen",
      "base-url": "LocalAI Basis-URL",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help":
        "Geben Sie die URL ein, unter der LocalAI ausgeführt wird.",
      "auto-detect": "Automatisch erkennen",
      "loading-models": "-- lade verfügbare Modelle --",
      "waiting-url": "-- warte auf URL --",
      "loaded-models": "Ihre geladenen Modelle",
    },
    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "Basis-URL",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Geben Sie die Basis-URL für Ihren OpenAI-kompatiblen API-Endpunkt ein.",
      "model-label": "Einbettungsmodell",
      "model-placeholder":
        "Geben Sie den Modellnamen ein (z.B. text-embedding-ada-002)",
      "model-help":
        "Geben Sie den Modellbezeichner zur Generierung von Einbettungen an.",
      "api-key": "API-Schlüssel",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help":
        "Geben Sie Ihren API-Schlüssel zur Authentifizierung ein.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "OpenAI API-Schlüssel",
      "api-key-placeholder": "Geben Sie Ihren OpenAI API-Schlüssel ein",
      "model-label": "Modellauswahl",
      "available-models": "Verfügbare Einbettungsmodelle",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "VoyageAI API-Schlüssel",
      "api-key-placeholder": "Geben Sie Ihren VoyageAI API-Schlüssel ein",
      "model-label": "Modellauswahl",
      "available-models": "Verfügbare Einbettungsmodelle",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Azure OpenAI Service-Endpunkt",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help":
        "Geben Sie die URL Ihres Azure OpenAI Service-Endpunkts ein",
      "api-key": "Azure OpenAI API-Schlüssel",
      "api-key-placeholder": "Geben Sie Ihren Azure OpenAI API-Schlüssel ein",
      "api-key-help":
        "Geben Sie Ihren Azure OpenAI API-Schlüssel zur Authentifizierung ein",
      "deployment-name": "Bereitstellungsname des Einbettungsmodells",
      "deployment-name-placeholder":
        "Geben Sie den Bereitstellungsnamen Ihres Azure OpenAI Einbettungsmodells ein",
      "deployment-name-help":
        "Der Bereitstellungsname für Ihr Azure OpenAI Einbettungsmodell",
    },
    // Native Embedding Options
    native: {
      description:
        "Verwendung eines nativen Einbettungsanbieters für die Textverarbeitung",
    },
  },

  // =========================
  // AGENTS
  // =========================
  agents: {
    title: "Agent-Fähigkeiten",
    "agent-skills": "Agent-Fähigkeiten konfigurieren und verwalten",
    "custom-skills": "Benutzerdefinierte Fähigkeiten",
    back: "Zurück",
    "select-skill": "Wählen Sie eine Fähigkeit zum Konfigurieren",
    "preferences-saved": "Agent-Einstellungen erfolgreich gespeichert",
    "preferences-failed": "Fehler beim Speichern der Agent-Einstellungen",
    "skill-status": {
      on: "Ein",
      off: "Aus",
    },
  },

  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "API-Schlüssel",
    description:
      "Verwalten Sie API-Schlüssel für die Verbindung mit dieser Instanz.",
    "generate-key": "Neuen API-Schlüssel generieren",
    "table-headers": {
      "connection-string": "Verbindungszeichenfolge",
      "created-by": "Erstellt von",
      "created-at": "Erstellt am",
      actions: "Aktionen",
    },
    "no-keys": "Keine API-Schlüssel gefunden",
    modal: {
      title: "Neuer Browser-Erweiterungs-API-Schlüssel",
      "multi-user-warning":
        "Warnung: Sie befinden sich im Mehrbenutzer-Modus. Dieser API-Schlüssel ermöglicht den Zugriff auf alle mit Ihrem Konto verknüpften Arbeitsbereiche. Bitte gehen Sie vorsichtig damit um.",
      "create-description":
        'Nach dem Klicken auf "API-Schlüssel erstellen" wird diese Instanz versuchen, einen neuen API-Schlüssel für die Browser-Erweiterung zu erstellen.',
      "connection-help":
        'Wenn Sie "Verbunden mit IST Legal" in der Erweiterung sehen, war die Verbindung erfolgreich. Wenn nicht, kopieren Sie bitte die Verbindungszeichenfolge und fügen Sie sie manuell in die Erweiterung ein.',
      cancel: "Abbrechen",
      "create-key": "API-Schlüssel erstellen",
      "copy-key": "API-Schlüssel kopieren",
      "key-copied": "API-Schlüssel kopiert!",
    },
    tooltips: {
      "copy-connection": "Verbindungszeichenfolge kopieren",
      "auto-connect": "Automatisch mit Erweiterung verbinden",
    },
    confirm: {
      revoke:
        "Sind Sie sicher, dass Sie diesen Browser-Erweiterungs-API-Schlüssel widerrufen möchten?\nDanach wird er nicht mehr verwendbar sein.\n\nDiese Aktion ist unwiderruflich.",
    },
    toasts: {
      "key-revoked":
        "Browser-Erweiterungs-API-Schlüssel wurde permanent widerrufen",
      "revoke-failed": "Fehler beim Widerrufen des API-Schlüssels",
      copied: "Verbindungszeichenfolge in die Zwischenablage kopiert",
      connecting: "Verbindung zur Browser-Erweiterung wird hergestellt...",
    },
    "revoke-title": "Browser-Erweiterungs-API-Schlüssel widerrufen",
    "revoke-message":
      "Sind Sie sicher, dass Sie diesen Browser-Erweiterungs-API-Schlüssel widerrufen möchten?\nDanach wird er nicht mehr verwendbar sein.\n\nDiese Aktion ist unwiderruflich.",
  },

  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Experimentelle Funktionen",
    description: "Funktionen, die sich derzeit in der Beta-Testphase befinden",
    "live-sync": {
      title: "Live-Dokumentensynchronisation",
      description:
        "Aktivieren Sie die automatische Inhaltssynchronisation aus externen Quellen",
      "manage-title": "Überwachte Dokumente",
      "manage-description":
        "Dies sind alle Dokumente, die derzeit in Ihrer Instanz überwacht werden. Der Inhalt dieser Dokumente wird regelmäßig synchronisiert.",
      "document-name": "Dokumentname",
      "last-synced": "Zuletzt synchronisiert",
      "next-refresh": "Zeit bis zur nächsten Aktualisierung",
      "created-on": "Erstellt am",
      "auto-sync": "Automatische Inhaltssynchronisation",
      "sync-description":
        'Aktivieren Sie die Möglichkeit, eine Inhaltsquelle zur "Überwachung" festzulegen. Überwachte Inhalte werden regelmäßig abgerufen und in dieser Instanz aktualisiert.',
      "sync-workspace-note":
        "Überwachte Inhalte werden automatisch in allen Arbeitsbereichen aktualisiert, in denen sie referenziert werden.",
      "sync-limitation":
        "Diese Funktion gilt nur für webbasierte Inhalte wie Websites, Confluence, YouTube und GitHub-Dateien.",
      documentation: "Funktionsdokumentation und Warnhinweise",
      "manage-content": "Überwachte Inhalte verwalten",
    },
    tos: {
      title: "Nutzungsbedingungen für experimentelle Funktionen",
      description:
        "Experimentelle Funktionen dieser Plattform sind Funktionen, die wir testen und die optional sind. Wir weisen Sie proaktiv auf mögliche Bedenken hin, sollten diese vor der Genehmigung einer Funktion bestehen.",
      "possibilities-title":
        "Die Nutzung einer Funktion auf dieser Seite kann unter anderem zu folgenden Möglichkeiten führen:",
      possibilities: {
        "data-loss": "Datenverlust.",
        "quality-change": "Änderung der Ergebnisqualität.",
        "storage-increase": "Erhöhter Speicherverbrauch.",
        "resource-consumption": "Erhöhter Ressourcenverbrauch.",
        "cost-increase":
          "Erhöhte Kosten oder Nutzung verbundener LLM- oder Embedding-Provider.",
        "potential-bugs":
          "Mögliche Fehler oder Probleme bei der Nutzung dieser Anwendung.",
      },
      "conditions-title":
        "Die Nutzung einer experimentellen Funktion ist mit folgenden nicht erschöpfenden Bedingungen verbunden:",
      conditions: {
        "future-updates":
          "Die Funktion existiert möglicherweise in zukünftigen Updates nicht mehr.",
        stability: "Die verwendete Funktion ist derzeit nicht stabil.",
        availability:
          "Die Funktion ist möglicherweise in zukünftigen Versionen, Konfigurationen oder Abonnements dieser Instanz nicht verfügbar.",
        privacy:
          "Ihre Datenschutzeinstellungen werden bei der Nutzung einer Beta-Funktion berücksichtigt.",
        changes: "Diese Bedingungen können sich in zukünftigen Updates ändern.",
      },
      "read-more":
        "Wenn Sie mehr erfahren möchten, können Sie sich beziehen auf",
      contact: "oder kontaktieren Sie",
      reject: "Ablehnen & Schließen",
      accept: "Ich verstehe",
    },
  },

  promptLogging: {
    title: "Prompt-Ausgabe-Protokollierung",
    description:
      "Aktivieren oder deaktivieren Sie die Protokollierung von Prompt-Ausgaben für die Systemüberwachung.",
    label: "Prompt-Ausgabe-Protokollierung: ",
    state: {
      enabled: "Aktiviert",
      disabled: "Deaktiviert",
    },
  },

  userAccess: {
    title: "Benutzerzugriff erlauben",
    description:
      "Aktivieren Sie diese Option, um normalen Benutzern den Zugriff auf rechtliche Aufgaben zu ermöglichen. Standardmäßig haben nur Superuser, Manager und Administratoren Zugriff.",
    label: "Benutzerzugriff: ",
    state: {
      enabled: "Aktiviert",
      disabled: "Deaktiviert",
    },
  },
  "cdb-llm-preference": {
    title: "CDB LLM-preferenz",
    settings: "CDB LLM",
    description: "Konfigurieren Sie den LLM-Anbieter für CDB",
  },
  "template-llm-preference": {
    title: "Vorlagen-LLM-Präferenz",
    settings: "Vorlagen-LLM",
    description:
      "Wählen Sie den LLM-Anbieter für die Dokumentvorlagenerstellung. Standard ist der Systemanbieter.",
    "toast-success": "Vorlagen-LLM-Einstellungen aktualisiert",
    "toast-fail": "Fehler beim Aktualisieren der Vorlagen-LLM-Einstellungen",
    saving: "Speichern...",
    "save-changes": "Änderungen speichern",
  },
  "custom-user-ai": {
    title: "Benutzerdefinierte KI",
    settings: "Benutzerdefinierte KI",
    description: "Benutzerdefinierten KI-Anbieter konfigurieren",
    "custom-model-reference": "Benutzerdefinierter Modellname & Beschreibung",
    "custom-model-reference-description":
      "Fügen Sie eine benutzerdefinierte Referenz für dieses Modell hinzu. Diese wird sichtbar, wenn Sie den benutzerdefinierten KI-Engine-Selektor im Prompt-Panel verwenden.",
    "custom-model-reference-name": "Benutzerdefinierter Modellname",
    "custom-model-reference-description-label": "Modellbeschreibung (Optional)",
    "custom-model-reference-description-placeholder":
      "Geben Sie eine optionale Beschreibung für dieses Modell ein",
    "custom-model-reference-name-placeholder":
      "Geben Sie einen benutzerdefinierten Namen für dieses Modell ein",
    "model-ref-placeholder":
      "Geben Sie einen benutzerdefinierten Namen oder eine Beschreibung für dieses Modell-Setup ein",
    "enter-custom-model-reference":
      "Geben Sie einen benutzerdefinierten Namen für dieses Modell ein",
    "standard-engine": "Standard KI-Engine",
    "standard-engine-description":
      "Unsere Standard-Engine, nützlich für die meisten Aufgaben",
    "dynamic-context-window-percentage":
      "Dynamischer Kontextfenster-Prozentsatz",
    "dynamic-context-window-percentage-desc":
      "Steuert, wie viel des LLM-Kontextfensters für zusätzliche Quellen verwendet werden kann (10-100%)",
    "no-alternative-title": "Kein alternatives Modell ausgewählt",
    "no-alternative-desc":
      "Wenn diese Option ausgewählt ist, haben Benutzer nicht die Möglichkeit, ein alternatives Modell auszuwählen.",
    "select-option": "Benutzerdefiniertes KI-Profil auswählen",
    tab: {
      "custom-1": "Benutzerdefinierte Engine 1",
      "custom-2": "Benutzerdefinierte Engine 2",
      "custom-3": "Benutzerdefinierte Engine 3",
      "custom-4": "Benutzerdefinierte Engine 4",
      "custom-5": "Benutzerdefinierte Engine 5",
      "custom-6": "Benutzerdefinierte Engine 6",
    },
    engine: {
      "custom-1": "Benutzerdefinierte Engine 1",
      "custom-2": "Benutzerdefinierte Engine 2",
      "custom-3": "Benutzerdefinierte Engine 3",
      "custom-4": "Benutzerdefinierte Engine 4",
      "custom-5": "Benutzerdefinierte Engine 5",
      "custom-6": "Benutzerdefinierte Engine 6",
      "custom-1-title": "Benutzerdefinierte Engine 1",
      "custom-2-title": "Benutzerdefinierte Engine 2",
      "custom-3-title": "Benutzerdefinierte Engine 3",
      "custom-4-title": "Benutzerdefinierte Engine 4",
      "custom-5-title": "Benutzerdefinierte Engine 5",
      "custom-6-title": "Benutzerdefinierte Engine 6",
      "custom-1-description":
        "Einstellungen für Benutzerdefinierte Engine 1 konfigurieren",
      "custom-2-description":
        "Einstellungen für Benutzerdefinierte Engine 2 konfigurieren",
      "custom-3-description":
        "Einstellungen für Benutzerdefinierte Engine 3 konfigurieren",
      "custom-4-description":
        "Einstellungen für Benutzerdefinierte Engine 4 konfigurieren",
      "custom-5-description":
        "Einstellungen für Benutzerdefinierte Engine 5 konfigurieren",
      "custom-6-description":
        "Einstellungen für Benutzerdefinierte Engine 6 konfigurieren",
    },
    "option-number": "Option {{number}}",
    "llm-provider-selection": "LLM-Anbieter-Auswahl",
    "llm-provider-selection-desc":
      "Wählen Sie den LLM-Anbieter für diese benutzerdefinierte KI-Konfiguration",
    "custom-option": "Benutzerdefinierte Option",
    saving: "Speichern...",
    "save-changes": "Änderungen speichern",
    "model-ref-saved":
      "Benutzerdefinierte Modelleinstellungen erfolgreich gespeichert",
    "model-ref-save-failed":
      "Fehler beim Speichern der benutzerdefinierten Modelleinstellungen: {{error}}",
    "llm-settings-save-failed":
      "Fehler beim Speichern der LLM-Einstellungen: {{error}}",
    "settings-fetch-failed": "Fehler beim Abrufen der Einstellungen",
    "llm-saved": "LLM-Einstellungen erfolgreich gespeichert",
    "select-provider-first":
      "Bitte wählen Sie einen LLM-Anbieter, um die Modelleinstellungen zu konfigurieren. Nach der Konfiguration kann diese Option als benutzerdefinierte KI-Engine in der Benutzeroberfläche ausgewählt werden.",
  },
  toast: {
    settings: {
      "welcome-messages-failed":
        "Willkommensnachrichten konnten nicht gespeichert werden: {{error}}",
      "welcome-messages-fetch-failed":
        "Willkommensnachrichten konnten nicht abgerufen werden",
      "welcome-messages-empty":
        "Bitte geben Sie entweder eine Überschrift oder einen Text ein",
      "welcome-messages-success":
        "Willkommensnachrichten erfolgreich gespeichert",
      "prompt-examples-failed":
        "Konnte Beispiel-Prompts nicht speichern: {{error}}",
      "prompt-examples-success": "Beispiel-Prompts gespeichert",
      "prompt-examples-validation":
        "Beispiel {{number}} fehlt erforderliche Felder: {{fields}}",
    },
    document: {
      "move-success": "Erfolgreich verschoben {{count}} Dokumente",
      "pdr-failed": "Fehler beim PDR-Dokument: {{message}}",
      "watch-failed": "Fehler beim Beobachten des Dokuments: {{message}}",
      "pdr-added": "Dokument hinzugefügt zu Parent Document Retrieval",
      "pdr-removed": "Dokument aus Parent Document Retrieval entfernt",
      "pin-success": "Dokument {{action}} workspace",
    },
    experimental: {
      "feature-enabled": "Experimentelle Funktionen aktiviert",
      "feature-disabled": "Experimentelle Funktionen deaktiviert",
      "update-failed":
        "Fehler beim Aktualisieren der experimentellen Funktionen",
      "live-sync": {
        enabled: "Live-Dokumentensynchronisation aktiviert",
        disabled: "Live-Dokumentensynchronisation deaktiviert",
      },
      "features-enabled":
        "Experimentelle Funktionen aktiviert. Seite wird neu geladen.",
    },
  },
  // =========================
  // CHAT LOGS & PREVIEW
  // =========================
  chat_logs: {
    display_description:
      "Anzeigen der Rohdatenprotokollierung, Öffnen und Herunterladen der Datei",
    display_prompt_output: "Rohdaten anzeigen",
    loading_prompt_output: "Rohdaten werden geladen...",
    not_available: "*** Rohdaten sind für diesen Chat nicht verfügbar.",
    token_count: "Tokens (in allen Rohdaten): {{count}}",
    token_count_detailed:
      "Tokens an LLM: {{promptTokens}} | Tokens in LLM-Antwort: {{completionTokens}} | Gesamttokens: {{totalTokens}}",
  },

  // LLM Provider specific translations
  "llm-provider.textgenwebui": "Connect to a Text Generation WebUI instance.",
  "llm-provider.litellm": "Connect to any LLM via LiteLLM.",
  "llm-provider.openai-generic":
    "Connect to any OpenAI-compatible API endpoint.",
  "llm-provider.system-default": "Use the built-in Native model.",

  // =========================
  // INVITATION RELATED KEYS
  // =========================
  invite: {
    "accept-button": "Einladung akzeptieren",
    newUser: {
      title: "Neues Konto erstellen",
      usernameLabel: "Benutzername",
      passwordLabel: "Passwort",
      description:
        "Nachdem Sie Ihr Konto erstellt haben, können Sie sich mit diesen Anmeldeinformationen anmelden und mit Arbeitsbereichen beginnen.",
    },
  },

  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Kontextfenster",
    "max-output-tokens": "Max Ausgabe-Tokens",
    "output-limit": "Ausgabelimit",
    tokens: "Tokens",
    "fallback-value": "Fallback-Wert verwendet",
  },

  // =========================
  // LEGAL TEMPLATES MODAL
  // =========================
  //moved to legalTemplates

  // =========================
  // CUSTOM LEGAL TEMPLATES MODAL
  // =========================

  // moved to customLegalTemplates.js

  // =========================
  // VARIOUS MODALS AND NEW FEATURES
  // =========================

  // Organization & Organizations translations for user management
  organization: {
    label: "Organisation",
    select: "-- Organisation auswählen --",
    none: "Keine",
    "create-new": "+ Neue Organisation erstellen",
    "new-name": "Name der neuen Organisation",
    "new-name-ph": "Geben Sie den Namen der neuen Organisation ein",
  },
  organizations: {
    "fetch-error": "Fehler beim Abrufen der Organisationen",
  },
  // =========================
  // REQUEST LEGAL ASSISTANCE
  // =========================
  "request-legal-assistance": {
    title: "Rechtshilfe anfordern",
    description:
      "Konfigurieren Sie die Sichtbarkeit der Schaltfläche zum Anfordern von Rechtshilfe.",
    enable: "Rechtshilfeanfrage aktivieren",
    "law-firm-name": "Name der Anwaltskanzlei",
    "law-firm-placeholder": "Geben Sie den Namen der Anwaltskanzlei ein",
    "law-firm-help":
      "Name der Anwaltskanzlei, die Rechtshilfeanfragen bearbeiten wird",
    email: "E-Mail für Rechtshilfe",
    "email-placeholder": "Geben Sie die E-Mail-Adresse für Rechtshilfe ein",
    "email-help": "E-Mail-Adresse, an die Rechtshilfeanfragen gesendet werden",
    "settings-saved": "Rechtshilfe-Einstellungen erfolgreich gespeichert",
    "save-error": "Fehler beim Speichern der Rechtshilfe-Einstellungen",
    status: "Schaltfläche für Rechtshilfe: ",
    "load-error": "Fehler beim Laden der Rechtshilfe-Einstellungen",
    "save-button": "Änderungen speichern",
    request: {
      title: "Rechtshilfe anfordern",
      description:
        "Senden Sie eine Anfrage an {{lawFirmName}} für Rechtshilfe, Abschluss von Recherchen oder andere Beratungen. Sie werden per E-Mail benachrichtigt, wenn die Anfrage bearbeitet wurde.",
      button: "Rechtshilfe anfordern",
      message: "Nachricht",
      "message-placeholder":
        "Geben Sie spezifische Anweisungen oder Informationen für das Rechtshilfeteam ein",
      send: "Anfrage senden",
      cancel: "Abbrechen",
      error: "Fehler beim Senden der Rechtshilfeanfrage",
      success: "Rechtshilfeanfrage erfolgreich gesendet",
      submitting: "Anfrage wird gesendet...",
      submit: "Anfrage einreichen",
      partyName: "Parteienname",
      partyOrgId: "Organisationsnummer für Partei",
      partyNamePlaceholder: "Geben Sie den Namen Ihrer Organisation ein",
      partyOrgIdPlaceholder: "Geben Sie Ihre Organisationnummer ein",
      partyNameRequired: "Parteienname ist erforderlich",
      partyOrgIdRequired: "Organisationsnummer für Partei ist erforderlich",
      opposingPartyName: "Name der Gegenseite (falls relevant)",
      opposingPartyOrgId: "Organisationsnummer für Gegenseite (falls bekannt)",
      opposingPartyNamePlaceholder: "Geben Sie den Namen der Gegenseite ein",
      opposingPartyOrgIdPlaceholder:
        "Geben Sie die Organisationnummer der Gegenseite ein",
    },
  },

  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Fortschritt der Antwortgenerierung",
    description:
      "Zeigt den realen Fortschritt der Aufgaben zum Abschluss der Erstellung von Dokumenten an, abhängig von der Verknüpfung mit anderen Arbeitsbereichen und der Größe der Dateien. Der Modal schließt automatisch, wenn alle Schritte abgeschlossen sind.",
    step_fetching_memos: "Abrufen von juristischen Daten zu aktuellen Themen",
    step_processing_chunks: "Verarbeitung hochgeladener Dokumente",
    step_combining_responses: "Antwort finalisieren",
    sub_step_chunk_label: "Verarbeitung der Dokumentengruppe {{index}}",
    sub_step_memo_label: "Juristische Daten abgerufen von {{workspaceSlug}}",
    placeholder_sub_task: "Wartender Schritt",
    desc_fetching_memos:
      "Abrufen relevanter juristischer Informationen aus verknüpften Arbeitsbereichen",
    desc_processing_chunks:
      "Analyse und Extraktion von Informationen aus Dokumentengruppen",
    desc_combining_responses:
      "Zusammenführung von Informationen zu einer umfassenden Antwort",
  },

  // =========================
  // MCP SERVER PAGE
  // =========================
  mcp: {
    title: "MCP-Server-Verwaltung",
    description:
      "Verwalten Sie Konfigurationen für Multi-Component Processing (MCP) Server.",
    currentServers: "Aktuelle Server",
    noServers: "Keine MCP-Server konfiguriert.",
    fetchError: "Fehler beim Abrufen der Server: {{error}}",
    addServerButton: "Neuen Server hinzufügen",
    addServerModalTitle: "Neuen MCP-Server hinzufügen",
    addServerModalDesc:
      "Definieren Sie die Konfiguration für den neuen MCP-Serverprozess.",
    serverName: "Servername (Eindeutige ID)",
    configJson: "Konfiguration (JSON)",
    addButton: "Server hinzufügen",
    addSuccess: "Server erfolgreich hinzugefügt.",
    addError: "Fehler beim Hinzufügen des Servers: {{error}}",
  },

  // =========================
  // ADMIN SYSTEM SETTINGS (UNIVERSITY MODE)
  // =========================
  admin: {
    system: {
      universityMode: {
        title: "Universitätsmodus",
        description:
          "Wenn aktiviert, wird die Validierung, die Prompt-Upgrade-Funktion, die Vorlagen- und Web-Suchtools für alle Benutzer ausgeblendet.",
        enable: "Universitätsmodus aktivieren",
        saved: "Universitätsmodus-Einstellungen erfolgreich gespeichert.",
        error: "Fehler beim Speichern der Universitätsmodus-Einstellungen.",
        saveChanges: "Universitätsmodus-Einstellungen speichern",
      },
    },
  },

  // Months
  "month.1": "Jan",
  "month.2": "Feb",
  "month.3": "Mär",
  "month.4": "Apr",
  "month.5": "Mai",
  "month.6": "Jun",
  "month.7": "Jul",
  "month.8": "Aug",
  "month.9": "Sep",
  "month.10": "Okt",
  "month.11": "Nov",
  "month.12": "Dez",

  // =========================
  // FEATURE CARDS
  // =========================
  featureCards: {
    "draft-from-template-title": "Dokumententwurf aus Vorlage erstellen",
    "draft-from-template-description":
      "Nutzen Sie die Funktion, um beispielsweise eine AML-Richtlinie, ein Protokoll für eine Hauptversammlung oder eine standardisierte Schiedsvereinbarung zu erstellen.",
    "complex-document-builder-title": "Komplexe juristische Aufgabe ausführen",
    "complex-document-builder-description":
      "Perfekt, wenn Sie beispielsweise Hunderte von Dokumenten vor einer Unternehmensübernahme prüfen oder eine detaillierte Klageschrift formulieren müssen.",
  },

  // =========================
  // WORKSPACE SELECTOR MODAL
  // =========================
  workspaceSelector: {
    chooseWorkspace: "Starten Sie einen neuen Chat",
    selectAiType:
      "Auswahl von Modul und Arbeitsbereich für die Initiierung der Funktion",
    cloudAiDescription:
      "Verwendet ein Cloud-basiertes KI-Modell für Chat und Frage-Antwort. Ihre Dokumente werden sicher in der Cloud verarbeitet und gespeichert.",
    localAiDescription:
      "Verwendet ein lokales KI-Modell für Chat und Dokumentenerstellung. Ihre Dokumente werden auf Ihrem lokalen Computer verarbeitet und gespeichert.",
    cloudAiDescriptionTemplateFeature:
      "Erstellen Sie eine Vorlage in einem bestehenden Arbeitsbereich mit juristischen Daten, die Vorlage kann je nach Anfrage juristische Daten abrufen.",
    localAiDescriptionTemplateFeature:
      "Erstellen Sie eine Vorlage in Ihrem eigenen Arbeitsbereich und nutzen Sie vollständig lokale KI, wenn diese auf dem Server aktiviert ist.",
    cloudAiDescriptionComplexFeature:
      "Die Erstellung komplexer Dokumente ist für diese Arbeitsbereiche nicht verfügbar, da der Benutzer vor dem Start Dokumente in den Arbeitsbereich hochladen muss",
    localAiDescriptionComplexFeature:
      "Wählen Sie einen Ihrer Arbeitsbereiche für die Initiierung einer juristischen Aufgabe und stellen Sie sicher, dass die erforderlichen Dokumente vor dem Start in den Arbeitsbereich hochgeladen werden.",
    newWorkspaceComplexTaskInfo:
      "Wenn Sie einen neuen Arbeitsbereich erstellen, gelangen Sie zur Upload-Ansicht, um alle erforderlichen Dokumente hochzuladen, was für die Durchführung einer juristischen Aufgabendokumentgenerierung notwendig ist.",
    selectExistingWorkspace: "Wählen Sie einen vorhandenen Arbeitsbereich",
    selectExistingDocumentDraftingWorkspace:
      "Wählen Sie einen vorhandenen Dokumenterstellungs-Arbeitsbereich",
    orCreateNewBelow:
      "Oder erstellen Sie unten einen neuen Dokumenterstellungs-Arbeitsbereich.",
    newWorkspaceName:
      "Geben Sie einen Namen für Ihren neuen Arbeitsbereich ein",
    newWorkspaceNameOptional:
      "Geben Sie einen Namen für Ihren neuen Arbeitsbereich ein (wenn Sie keinen vorhandenen Arbeitsbereich verwenden)",
    workspaceNamePlaceholder: "z.B.: Mein neuer Arbeitsbereich",
    next: "Weiter",
    pleaseSelectWorkspace: "Bitte wählen Sie einen Arbeitsbereich aus.",
    workspaceNameRequired: "Name des Arbeitsbereichs ist erforderlich.",
    workspaceNameOrExistingWorkspaceRequired:
      "Bitte geben Sie einen Arbeitsbereichsnamen ein oder wählen Sie einen vorhandenen Arbeitsbereich aus.",
    workspaceNameMustBeMoreThanOneCharacter:
      "Der Name des Arbeitsbereichs muss mehr als ein Zeichen umfassen.",
    noWorkspacesAvailable: "Keine Arbeitsbereiche verfügbar",
    selectWorkspacePlaceholder: "Bitte wählen",
    featureUnavailable: {
      title: "Funktion ist nicht verfügbar",
      description:
        "Diese Funktion ist für Ihr Konto nicht aktiviert oder in diesem System deaktiviert. Bitte kontaktieren Sie einen Administrator, um diese Funktion bei Bedarf zu aktivieren.",
      close: "Schließen",
    },
    createNewWorkspace: {
      title: "Neuen Dokumenterstellungs-Arbeitsbereich erstellen",
      description:
        "Dies erstellt einen neuen Arbeitsbereich speziell für die komplexe Dokumenterstellung mit der ausgewählten Vorlage.",
      workspaceName: "Name des Arbeitsbereichs",
      create: "Arbeitsbereich erstellen",
    },
    selectExisting: {
      title: "Arbeitsbereich für Rechtsfragen auswählen",
      description:
        "Wählen Sie einen vorhandenen Arbeitsbereich aus, um eine Rechtsfragen-Chat-Sitzung zu starten.",
      selectWorkspace: "Arbeitsbereich auswählen",
    },
  },
};

export default TRANSLATIONS;
