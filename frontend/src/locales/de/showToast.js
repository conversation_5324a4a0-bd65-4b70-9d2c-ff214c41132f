export default {
  "show-toast": {
    "recovery-codes": "Wiederherstellungscodes in die Zwischenablage kopiert",
    "token-minimum-error": "Dateiinhalt unter Mindest-Token-Anforderung",
    "file-process-error": "Fehler bei der Verarbeitung der Datei",
    "scraping-website":
      "Website wird gescrapt - dies kann einen Moment dauern.",
    "fetching-transcript": "Transkript für YouTube-Video wird abgerufen.",
    "request-legal-assistance-sent": "Rechtsbeistand anfordert wurde gesendet.",
    "request-legal-assistance-error":
      "Fehler beim Senden der Rechtsbeistand anfordert.",
    "updating-workspace": "Arbeitsbereich wird aktualisiert...",
    "flashing-started": "Flash-Vorgang gestartet...",
    "flashing-success": "Flash-Vorgang erfolgreich abgeschlossen",
    "flashing-error": "Fehler beim Flashen: {{error}}",
    "pin-success-added": "Dokument erfolgreich zum Arbeitsbereich hinzugefügt",
    "pin-success-removed": "Dokument erfolgreich vom Arbeitsbereich entfernt",
    "workspace-updated": "Arbeitsbereich erfolgreich aktualisiert.",
    "link-uploaded": "Link erfolgreich hochgeladen",
    "password-reset": "Passwort erfolgreich zurückgesetzt",
    "invalid-reset": "Ungültiger Rücksetz-Token",
    "delete-option": "Thread konnte nicht gelöscht werden!",
    "thread-deleted": "Thread erfolgreich gelöscht!",
    "threads-deleted": "Threads erfolgreich gelöscht!",
    "chat-deleted": "Chat erfolgreich gelöscht!",
    "failed-delete-chat":
      "Chat konnte nicht gelöscht werden. Bitte versuche es erneut.",
    "error-deleting-chat": "Beim Löschen des Chats ist ein Fehler aufgetreten.",
    "chat-memory-reset":
      "Chat-Speicher des Arbeitsbereichs wurde zurückgesetzt!",
    "picture-uploaded": "Profilbild hochgeladen.",
    "profile-updated": "Profil aktualisiert.",
    "logs-cleared": "Ereignisprotokolle erfolgreich gelöscht.",
    "preferences-updated": "Systemeinstellungen erfolgreich aktualisiert.",
    "user-created": "Benutzer erfolgreich erstellt.",
    "user-creation-error": "Benutzer konnte nicht erstellt werden: {{error}}",
    "user-exists-error":
      "Ein Benutzer mit dieser E-Mail-Adresse existiert bereits",
    "user-deleted": "Benutzer wurde aus dem System gelöscht.",
    "workspaces-saved": "Arbeitsbereiche erfolgreich gespeichert!",
    "failed-workspaces":
      "Arbeitsbereiche konnten nicht gespeichert werden. Bitte versuche es erneut.",
    "api-deleted": "API-Schlüssel dauerhaft gelöscht",
    "api-copied": "API-Schlüssel in die Zwischenablage kopiert",
    "appname-updated": "Benutzerdefinierter App-Name erfolgreich aktualisiert.",
    "appname-update-error":
      "Fehler beim Aktualisieren des benutzerdefinierten App-Namens: ",
    "language-updated": "Sprache erfolgreich aktualisiert.",
    "palette-updated": "Farbpalette erfolgreich aktualisiert.",
    "image-uploaded": "Bild erfolgreich hochgeladen.",
    "logo-remove-error": "Fehler beim Entfernen des Logos: ",
    "logo-removed": "Logo erfolgreich entfernt.",
    "logo-uploaded": "Logo erfolgreich hochgeladen.",
    "logo-upload-error": "Fehler beim Hochladen des Logos: ",
    "updated-welcome": "Willkommensnachrichten erfolgreich aktualisiert.",
    "update-welcome-error":
      "Fehler beim Aktualisieren der Willkommensnachrichten:",
    "updated-footer": "Footer-Icons erfolgreich aktualisiert.",
    "update-footer-error": "Fehler beim Aktualisieren der Footer-Icons: ",
    "updated-paragraph":
      "Benutzerdefinierter Absatztext erfolgreich aktualisiert.",
    "update-paragraph-error":
      "Fehler beim Aktualisieren des benutzerdefinierten Absatztexts: ",
    "updated-supportemail": "Support-E-Mail erfolgreich aktualisiert.",
    "update-supportemail-error":
      "Fehler beim Aktualisieren der Support-E-Mail: ",
    "stt-success": "Speech-to-Text-Einstellungen erfolgreich gespeichert.",
    "tts-success": "Text-to-Speech-Einstellungen erfolgreich gespeichert.",
    "failed-chats-export": "Chats konnten nicht exportiert werden.",
    "chats-exported": "Chats wurden erfolgreich als {{name}} exportiert.",
    "cleared-chats": "Alle Chats gelöscht.",
    "embed-deleted": "Embed aus dem System entfernt.",
    "snippet-copied": "Snippet in die Zwischenablage kopiert!",
    "embed-updated": "Embed erfolgreich aktualisiert.",
    "embedding-saved": "Embedding-Einstellungen erfolgreich gespeichert.",
    "chunking-settings":
      "Einstellungen zur Textaufteilung erfolgreich gespeichert.",
    "llm-saved": "LLM-Einstellungen erfolgreich gespeichert.",
    "llm-saving-error": "Fehler beim Speichern der LLM-Einstellungen: ",
    "multiuser-enabled": "Multi-User-Modus erfolgreich aktiviert.",
    "publicuser-enabled": "Öffentlicher Benutzer-Modus erfolgreich aktiviert.",
    "publicuser-disabled":
      "Öffentlicher Benutzer-Modus erfolgreich deaktiviert.",
    "page-refresh": "Deine Seite wird in wenigen Sekunden aktualisiert.",
    "transcription-saved":
      "Transkriptionseinstellungen erfolgreich gespeichert.",
    "vector-saved": "Vektor-Datenbank-Einstellungen erfolgreich gespeichert.",
    "workspace-not-deleted": "Arbeitsbereich konnte nicht gelöscht werden!",
    "maximum-messages": "Maximal 4 Nachrichten erlaubt.",
    "users-updated": "Benutzer erfolgreich aktualisiert.",
    "vectordb-not-reset":
      "Vektor-Datenbank des Arbeitsbereichs konnte nicht zurückgesetzt werden!",
    "vectordb-reset":
      "Vektor-Datenbank des Arbeitsbereichs wurde zurückgesetzt!",
    "meta-data-update": "Seiten-Einstellungen aktualisiert!",
    "linked-workspaces-updated":
      "Verknüpfte Arbeitsbereiche erfolgreich aktualisiert.",
    "upgrade-answer-error": "Fehler beim Upgrade der Antwort: ",
    "upgrade-text-error": "Fehler beim Upgrade des Texts: ",
    "reset-tab-name-error":
      "Fehler beim Zurücksetzen auf den Standard-Tab-Namen.",
    "update-tab-name-error": "Fehler beim Aktualisieren der Tab-Namen: ",
    "updated-website": "Website-Einstellungen erfolgreich aktualisiert.",
    "update-website-error": "Fehler beim Aktualisieren des Website-Links: ",
    "reset-website-error":
      "Fehler beim Zurücksetzen des Standard-Website-Links.",
    "palette-update-error": "Fehler beim Aktualisieren der Farbpalette: ",
    "citation-state-updated":
      "Zitationsstatus erfolgreich aktualisiert. {{citationState}}",
    "citation-state-update-error":
      "Fehler beim Aktualisieren der Zitations-Einstellung",
    "citation-update-error":
      "Fehler bei der Übermittlung der Zitations-Einstellung",
    "message-limit-updated": "Nachrichtenlimit erfolgreich aktualisiert.",
    "validate-response-error": "Validierung fehlgeschlagen mit ",
    "invoice-logging-state-updated":
      "Rechnungsprotokollierungseinstellungen erfolgreich aktualisiert.",
    "invoice-logging-state-update-error":
      "Fehler beim Aktualisieren der Rechnungsprotokollierung: ",
    "error-fetching-tab-names": "Fehler beim Abrufen der Tab-Namen",
    "active-case": {
      "reference-updated": "Aktive Fallreferenz erfolgreich aktualisiert",
      "reference-cleared": "Aktive Fallreferenz erfolgreich gelöscht",
    },
    "export-word": "Als Word exportieren",
    "export-error": "Fehler beim Exportieren des Word-Dokuments",
    "export-success": "Dokument erfolgreich exportiert",
    "file-upload-success": "Datei erfolgreich angehängt",
    "files-upload-success": "{{count}} Dateien erfolgreich angehängt",
    "file-upload-error": "Fehler beim Hochladen der Datei(en)",
    "file-removed": "Datei erfolgreich entfernt",
    "file-remove-error":
      "Datei konnte nicht vollständig entfernt werden. Bitte versuche es erneut.",
    "link-upload-success": "Link erfolgreich hochgeladen",
    "link-upload-error": "Fehler beim Hochladen des Links: {{error}}",
    "qura-login-success": "Qura Anmeldung erfolgreich",
    "error-linking-rexor": "Fehler beim Abrufen des Rexor-Verknüpfungsstatus",
    "article-transaction-registered":
      "Artikel-Transaktion erfolgreich registriert",
    "changes-saved": "Änderungen erfolgreich gespeichert",
    "missing-data": "Erforderliche Daten fehlen",
    "save-error": "Fehler beim Speichern der Änderungen",
    "invalid-response": "Ungültige Antwort erhalten",
    "streaming-error": "Fehler beim Streamen",
    "qura-auth.error.fill-fields": "Bitte alle Felder ausfüllen.",
    "show-toast.qura-login-success": "Login erfolgreich.",
    "qura-auth.error.invalid-credentials":
      "Ungültiger Benutzername oder Passwort.",
    "qura-auth.connect": "Mit Qura verbinden",
    "speech-to-text.microphone-access-error":
      "Mikrofonzugriff ist erforderlich.",
    "show-toast.meta-data-update": "Metadaten erfolgreich aktualisiert.",
    "appearance.siteSettings.tabIcon": "Tab-Icon",
    "appearance.siteSettings.fabIconUrl": "Favicon-URL",
    "appearance.siteSettings.placeholder": "Favicon-URL eingeben",
    "appearance.siteSettings.title-placeholder": "Website-Titel eingeben",
    "show-toast.workspace-updated": "Arbeitsbereich erfolgreich aktualisiert.",
    "pdr-settings.toast-success": "PDR-Einstellungen erfolgreich aktualisiert.",
    "pdr-settings.toast-fail":
      "Fehler beim Aktualisieren der PDR-Einstellungen.",
    "pdr-settings.title": "PDR-Einstellungen",
    "pdr-settings.description": "Verwalte hier deine PDR-Einstellungen.",
    "pdr-settings.desc-end": "Stelle sicher, dass alle Werte korrekt sind.",
    "pdr-settings.pdr-token-limit": "PDR Token-Limit",
    "pdr-settings.pdr-token-limit-desc": "Maximale Anzahl an Tokens für PDR.",
    "pdr-settings.pdr-token-limit-placeholder": "PDR Token-Limit eingeben",
    "pdr-settings.input-prompt-token-limit": "Token-Limit für Eingabe-Prompt",
    "pdr-settings.input-prompt-token-limit-desc":
      "Maximale Anzahl an Tokens für Eingabe-Prompts.",
    "pdr-settings.input-prompt-token-limit-placeholder":
      "Token-Limit für Eingabe-Prompt eingeben",
    "pdr-settings.response-token-limit": "Token-Limit für Antworten",
    "pdr-settings.response-token-limit-desc":
      "Maximale Anzahl an Tokens für Antworten.",
    "pdr-settings.response-token-limit-placeholder":
      "Token-Limit für Antworten eingeben",
    "pdr-settings.adjacent-vector-limit": "Limit für benachbarte Vektoren",
    "pdr-settings.adjacent-vector-limit-desc":
      "Limit für benachbarte Vektoren.",
    "pdr-settings.adjacent-vector-limit-placeholder":
      "Limit für benachbarte Vektoren eingeben",
    "pdr-settings.keep-pdr-vectors": "PDR-Vektoren beibehalten",
    "pdr-settings.keep-pdr-vectors-desc":
      "Option, um PDR-Vektoren zu behalten.",
    "workspace-update-error": "Fehler: {{error}}",
    "workspace-update-failed":
      "Arbeitsbereich-Update fehlgeschlagen: {{error}}",
    "token-window-exceeded":
      "Datei wurde nicht hinzugefügt, da sie das verfügbare Token-Limit der aktuellen KI-Engine überschreitet",
    "token-limit-exceeded":
      "Datei zu groß für verfügbares Kontextfenster. Verfügbare Tokens: {{available}}, Benötigte Tokens: {{required}}",
    "rexor-activity-id-update-success":
      "Rexor Aktivitäts-ID erfolgreich aktualisiert",
    "rexor-activity-id-update-error":
      "Fehler beim Aktualisieren der Rexor Aktivitäts-ID",
    "pin-error": "Dokument konnte nicht {{action}} werden.",
    "pin-error-detail": "Dokument konnte nicht angeheftet werden. {{message}}",
    "error-fetching-settings": "Fehler beim Abrufen der Einstellungen.",
    "experimental-features-unlocked":
      "Experimentelle Feature-Vorschauen freigeschaltet!",
    "workspace-updated-success": "Arbeitsbereich erfolgreich aktualisiert.",
    "legal-task-created": "Rechtliche Aufgabe erstellt!",
    "form-submission-failed": "Formular konnte nicht übermittelt werden!",
    "provider-endpoint-discovered": "Anbieter-Endpunkt automatisch erkannt.",
    "failed-save-embedding":
      "Fehler beim Speichern der Embedding-Einstellungen: {{error}}",
    "provider-endpoint-discovery-failed":
      "Der Anbieter-Endpunkt konnte nicht automatisch erkannt werden. Bitte geben Sie ihn manuell ein.",
    "qura-setting-update-failed":
      "Qura-Einstellung konnte nicht aktualisiert werden.",
    "qura-settings-submission-error":
      "Fehler beim Übermitteln der Qura-Einstellungen.",
    "preferences-save-error": "Fehler beim Speichern der Einstellungen.",
    "skill-config-updated": "Skill-Konfiguration erfolgreich aktualisiert.",
    "category-deleted-success": "Kategorie erfolgreich gelöscht!",
    "category-deletion-failed": "Kategorie konnte nicht gelöscht werden!",
    "embed-chats-export-failed": "Embed-Chats konnten nicht exportiert werden.",
    "api-key-revoked": "Browser-Extension API-Schlüssel dauerhaft widerrufen",
    "api-key-revoke-failed": "API-Schlüssel konnte nicht widerrufen werden",
    "connection-string-copied": "Verbindungsstring in Zwischenablage kopiert",
    "connecting-to-extension":
      "Versuche, mit Browser-Extension zu verbinden...",
    "error-loading-settings":
      "Fehler beim Laden der Einstellungen. Bitte Seite aktualisieren.",
    "error-fetching-prompt-logging":
      "Fehler beim Abrufen der Prompt-Output-Protokollierung.",
    "error-updating-prompt-logging":
      "Prompt-Output-Protokollierung konnte nicht aktualisiert werden.",
    "error-updating-feature":
      "Feature-Status konnte nicht aktualisiert werden.",
    "rexor-linkage-state-updated":
      "Rexor-Verknüpfungsstatus auf {{state}} aktualisiert",
    "rexor-linkage-state-update-error":
      "Fehler beim Aktualisieren des Rexor-Verknüpfungsstatus: {{error}}",
    "language-update-failed":
      "Sprache konnte nicht aktualisiert werden: {{error}}",
    "llm-settings-save-failed":
      "LLM-Einstellungen konnten nicht gespeichert werden: {{error}}",
    "setup-error": "Fehler: {{error}}",
    "template-saved": "Prompt-Vorlage erfolgreich gespeichert",
    "template-saving-error":
      "Vorlage konnte nicht gespeichert werden: {{error}}",
    "template-reset": "Vorlage auf Standard zurückgesetzt",
    "legal-task-updated": "Rechtliche Aufgabe erfolgreich aktualisiert",
    "legal-task-update-failed":
      "Rechtliche Aufgabe konnte nicht aktualisiert werden",
    "model-ref-saved":
      "Änderungen am benutzerdefinierten Modellnamen und/oder benutzerdefinierten dynamischen PDR gespeichert",
    "model-ref-save-failed":
      "Benutzerdefinierte Modelleinstellungen konnten nicht gespeichert werden: {{error}}",
    "settings-fetch-failed": "Einstellungen konnten nicht abgerufen werden",
    "api-key-saved": "API-Schlüssel erfolgreich gespeichert",
    "auto-environment-update":
      "Automatisches Umgebungsupdate gestartet. Dies dauert einige Sekunden.",
    "example-prompt-error":
      "Beispiel-Prompt konnte nicht übermittelt werden: {{error}}",
    "files-processed": "{{count}} Datei(en) erfolgreich verarbeitet",
    "token-validation-failed":
      "Dateivalidierung fehlgeschlagen - Token-Anzahl oder Größenlimit überschritten",
    "deleted-old-chats": "{{count}} alte Prompt(s) gelöscht",
    "pdr-failed": "PDR-Dokument fehlgeschlagen: {{message}}",
    "watch-failed": "Dokument konnte nicht überwacht werden: {{message}}",
    "pdr-added": "Dokument zu Parent Document Retrieval hinzugefügt",
    "pdr-removed": "Dokument von Parent Document Retrieval entfernt",
    "unsaved-changes":
      "Du hast ungespeicherte Änderungen. Bist du sicher, dass du sie abbrechen willst?",
  },
};
