export default {
  // =========================
  // GENERIC PROVIDER SELECTION SETTINGS
  // =========================
  generic: {
    "base-url": "Basis-URL",
    "api-key": "API-Schlüssel",
    "api-key-placeholder": "Gib deinen API-Schlüssel ein",
    "chat-model": "Chat-Modell",
    "chat-model-placeholder": "Gib das Chat-Modell ein",
    "token-window": "Token-Kontextfenster",
    "token-window-placeholder": "Kontextfensterlimit (z.B: 4096)",
    "max-tokens": "<PERSON> Tokens",
    "max-tokens-placeholder": "Max Tokens pro Anfrage (z.B: 1024)",
    "embedding-deployment": "Embedding-Deployment-Name",

    "embedding-deployment-placeholder":
      "Azure OpenAI Embedding-Modell-Deployment-Name",

    "embedding-model": "Embedding-Modell",
    "embedding-model-placeholder": "Gib das Embedding-Modell ein",
    "max-embedding-chunk-length": "Maximale Embedding-Chuck-Länge",
    saving: "Speichern...",
    "save-changes": "Änderungen speichern",
    "workspace-update-error": "Fehler: {{error}}",
    "base-url-placeholder": "z.B: https://proxy.openai.com",
    "password-mask-length": "12",
  },

  // =========================
  // GENERIC MODEL SELECTION SETTINGS
  // =========================
  model: {
    selection: "Chat-Modell-Auswahl",
    "embedding-selection": "Embedding-Modell-Auswahl",
    "enter-api-key":
      "Gib einen gültigen API-Schlüssel ein, um alle verfügbaren Modelle für dein Konto zu sehen.",
    "enter-url": "Gib zuerst die URL ein",
    "your-models": "Deine geladenen Modelle",
    "available-models": "Verfügbare Modelle",
  },

  // =========================
  // ANTHROPIC
  // =========================
  anthropic: {
    "api-key": "Anthropic API-Schlüssel",
    "api-key-placeholder": "Gib deinen Anthropic API-Schlüssel ein",
    "model-selection": "Chat-Modell-Auswahl",
  },

  // =========================
  // AZURE SETTINGS
  // =========================
  azure: {
    "service-endpoint": "Azure Service Endpoint",
    "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
    "api-key": "API-Schlüssel",
    "api-key-placeholder": "Azure OpenAI API-Schlüssel",
    "chat-deployment": "Chat Deployment Name",
    "chat-deployment-placeholder": "Azure OpenAI Chat-Modell-Deployment-Name",
    "token-limit": "Chat-Modell Token-Limit",
  },
  azureai: {
    "service-endpoint": "Azure Service Endpoint",
    "api-key": "API-Schlüssel",
    "api-key-placeholder": "Azure OpenAI API-Schlüssel",
    "embedding-deployment-name": "Embedding-Deployment-Name",
    "embedding-deployment-name-placeholder":
      "Gib den Embedding-Deployment-Namen ein",
  },

  // =========================
  // AWS BEDROCK SETTINGS
  // =========================
  bedrock: {
    "iam-warning":
      "Du solltest einen ordnungsgemäß definierten IAM-Benutzer für Inferenzen verwenden.",
    "read-more":
      "Lies mehr darüber, wie du AWS Bedrock in dieser Instanz verwendest",
    "access-id": "AWS Bedrock IAM Access ID",
    "access-id-placeholder": "AWS Bedrock IAM Benutzer Access ID",
    "access-key": "AWS Bedrock IAM Access Key",
    "access-key-placeholder": "AWS Bedrock IAM Benutzer Access Key",
    region: "AWS Region",
    "model-id": "Modell-ID",
    "model-id-placeholder": "Modell-ID von AWS z.B: meta.llama3.1-v0.1",
    "context-window": "Modell-Kontextfenster",
    "context-window-placeholder": "Kontextfensterlimit (z.B: 4096)",
  },

  // =========================
  // COHERE
  // =========================
  cohere: {
    "api-key": "Cohere API-Schlüssel",
    "api-key-placeholder": "Gib deinen Cohere API-Schlüssel ein",
    "model-preference": "Modell-Voreinstellung",
    "model-selection": "Modellauswahl",
  },

  // =========================
  // DEEPSEEK LLM SETTINGS
  // =========================
  deepseek: {
    "api-key": "DeepSeek API-Schlüssel",
    "api-key-placeholder": "Gib deinen DeepSeek API-Schlüssel ein",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
  },

  // =========================
  // FIREWORKSAI LLM SELECTION
  // =========================
  fireworksai: {
    "api-key": "FireworksAI API-Schlüssel",
    "api-key-placeholder": "Gib deinen FireworksAI API-Schlüssel ein",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
  },

  // =========================
  // GEMINI LLM OPTIONS
  // =========================
  gemini: {
    "api-key": "Google AI API-Schlüssel",
    "api-key-placeholder": "Google Gemini API-Schlüssel",
    "model-selection": "Modellauswahl",
    "loading-models": "Lade Modelle...",
    "manual-options": "Manuelle Optionen",
    "safety-setting": "Sicherheitseinstellung",
    experimental: "Experimentell",
    stable: "Stabil",
    "safety-options": {
      none: "Keine (Standard)",
      "block-few": "Nur Hochrisiko blockieren",
      "block-some": "Mittel- & Hochrisiko blockieren",
      "block-most": "Die meisten Inhalte blockieren",
    },
  },

  // =========================
  // GROQ LLM SELECTION
  // =========================
  groq: {
    "api-key": "Groq API-Schlüssel",
    "api-key-placeholder": "Gib deinen Groq API-Schlüssel ein",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
    "enter-api-key":
      "Gib einen gültigen API-Schlüssel ein, um alle verfügbaren Modelle für dein Konto zu sehen.",
    "available-models": "Verfügbare Modelle",
    "model-description":
      "Wähle das GroqAI-Modell aus, das du für deine Konversationen verwenden möchtest.",
  },

  // =========================
  // HUGGINGFACE LLM SETTINGS
  // =========================
  huggingface: {
    "inference-endpoint": "HuggingFace Inference Endpoint",
    "endpoint-placeholder": "https://example.endpoints.huggingface.cloud",
    "access-token": "HuggingFace Access Token",
    "token-placeholder": "Gib deinen HuggingFace Access Token ein",
    "token-limit": "Modell Token-Limit",
    "token-limit-placeholder": "4096",
  },

  // =========================
  // KOBOLDCPP SETTINGS
  // =========================
  koboldcpp: {
    "show-advanced": "Manuelle Endpunkt-Eingabe anzeigen",
    "hide-advanced": "Manuelle Endpunkt-Eingabe ausblenden",
    "base-url": "KoboldCPP Basis-URL",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "base-url-desc": "Gib die URL ein, unter der KoboldCPP läuft.",
    "auto-detect": "Automatisch erkennen",
    "token-context-window": "Token-Kontextfenster",
    "token-window-placeholder": "4096",
    "token-window-desc": "Maximale Anzahl an Tokens für Kontext und Antwort.",
    model: "KoboldCPP Modell",
    "loading-models": "-- lade verfügbare Modelle --",
    "enter-url": "Gib zuerst die KoboldCPP URL ein",
    "model-desc":
      "Wähle das KoboldCPP-Modell aus, das du verwenden möchtest. Modelle werden geladen, nachdem eine gültige KoboldCPP URL eingegeben wurde.",
    "model-choose":
      "Wähle das KoboldCPP-Modell, das du für deine Konversationen verwenden möchtest.",
  },

  // =========================
  // LITELLM
  // =========================
  litellm: {
    "model-tooltip":
      "Stelle sicher, dass du ein gültiges Embedding-Modell auswählst. Chat-Modelle sind keine Embedding-Modelle. Siehe",
    "model-tooltip-link": "diese Seite",
    "model-tooltip-more": "für weitere Informationen.",
    "base-url": "Basis-URL",
    "base-url-placeholder": "z.B: https://proxy.openai.com",
    "max-embedding-chunk-length": "Maximale Embedding-Chuck-Länge",
    "token-window": "Token-Kontextfenster",
    "token-window-placeholder": "Kontextfensterlimit (z.B: 4096)",
    "api-key": "API-Schlüssel",
    optional: "optional",
    "api-key-placeholder": "sk-meingeheimerschlüssel",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
    "waiting-url": "-- warte auf URL --",
    "loaded-models": "Deine geladenen Modelle",
    "manage-embedding": "Embedding verwalten",
    "embedding-required":
      "Litellm erfordert einen Embedding-Dienst zur Verwendung.",
  },

  // =========================
  // LMSTUDIO LLM SELECTION
  // =========================
  lmstudio: {
    "max-tokens": "Maximale Tokens",
    "max-tokens-desc": "Maximale Anzahl an Tokens für Kontext und Antwort.",
    "show-advanced": "Manuelle Endpunkt-Eingabe anzeigen",
    "hide-advanced": "Manuelle Endpunkt-Eingabe ausblenden",
    "base-url": "LM Studio Basis-URL",
    "base-url-placeholder": "http://localhost:1234/v1",
    "base-url-desc": "Gib die URL ein, unter der LM Studio läuft.",
    "auto-detect": "Automatisch erkennen",
    model: "LM Studio Modell",
    "model-loading": "-- lade verfügbare Modelle --",
    "model-url-first": "Bitte zuerst die LM Studio URL eingeben",
    "model-desc":
      "Wähle das LM Studio Modell aus, das du verwenden möchtest. Die Modelle werden geladen, nachdem eine gültige LM Studio URL eingegeben wurde.",
    "model-choose":
      "Wähle das LM Studio Modell, das du für deine Konversationen verwenden möchtest.",
    "model-loaded": "Geladene Modelle",
    "embedding-required":
      "LMStudio als dein LLM erfordert, dass du einen Embedding-Dienst einrichtest.",
    "manage-embedding": "Embedding verwalten",
    "max-embedding-chunk-length": "Maximale Embedding-Chuck-Länge",
  },

  // =========================
  // LOCALAI LLM SELECTION
  // =========================
  localai: {
    "token-window": "Token-Kontextfenster",
    "token-window-placeholder": "4096",
    "api-key": "Local AI API-Schlüssel",
    "api-key-optional": "optional",
    "api-key-placeholder": "sk-meingeheimerSchlüssel",
    "show-advanced": "Erweiterte Einstellungen anzeigen",
    "hide-advanced": "Erweiterte Einstellungen ausblenden",
    "base-url": "Local AI Basis-URL",
    "base-url-placeholder": "http://localhost:8080/v1",
    "base-url-help": "Gib die URL ein, unter der LocalAI läuft.",
    "auto-detect": "Automatisch erkennen",
    "max-embedding-chunk-length": "Maximale Embedding-Chuck-Länge",
    "embedding-required": "Embedding ist für LocalAI erforderlich.",
    "manage-embedding": "Embedding verwalten",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
    "waiting-url": "-- warte auf URL --",
    "loaded-models": "Deine geladenen Modelle",
  },

  // =========================
  // MISTRAL LLM OPTIONS
  // =========================
  mistral: {
    "api-key": "Mistral API-Schlüssel",
    "api-key-placeholder": "Mistral API-Schlüssel",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
    "waiting-key": "-- warte auf API-Schlüssel --",
    "available-models": "Verfügbare Mistral-Modelle",
  },

  // =========================
  // NATIVE LLM SETTINGS
  // =========================
  native: {
    "experimental-warning":
      "Die Verwendung eines lokal gehosteten LLM ist experimentell und kann unerwartetes Verhalten zeigen.",
    "model-desc": "Wähle ein Modell aus deinen lokal gehosteten Modellen.",
    "token-desc": "Maximale Anzahl an Tokens für Kontext und Antwort.",
  },

  // =========================
  // OLLAMA LLM SELECTION
  // =========================
  ollamallmselection: {
    "max-tokens": "Maximale Tokens",
    "max-tokens-desc": "Maximale Anzahl an Tokens für Kontext und Antwort.",
    "show-advanced": "Manuelle Endpunkt-Eingabe anzeigen",
    "hide-advanced": "Manuelle Endpunkt-Eingabe ausblenden",
    "base-url": "Ollama Basis-URL",
    "base-url-placeholder": "http://127.0.0.1:11434",
    "base-url-desc": "Gib die URL ein, unter der Ollama läuft.",
    "auto-detect": "Automatisch erkennen",
    "keep-alive": "Ollama aktiv halten",
    "no-cache": "Kein Cache",
    "five-minutes": "5 Minuten",
    "one-hour": "1 Stunde",
    forever: "Für immer",
    "keep-alive-desc": "Halte das Modell im Speicher geladen.",
    "learn-more": "Mehr erfahren",
    "performance-mode": "Leistungsmodus",
    "base-default": "Basis (Standard)",
    maximum: "Maximum",
    "performance-mode-desc": "Wähle den Leistungsmodus aus.",
    note: "Hinweis:",
    "maximum-warning": "Der Maximum-Modus verbraucht mehr Ressourcen.",
    base: "Basis",
    "base-desc": "Basis-Modus ist die Standardeinstellung.",
    "maximum-desc":
      "Maximum-Modus bietet höhere Leistung, indem das volle Kontextfenster bis zur maximalen Tokenanzahl genutzt wird.",
    model: "Ollama-Modell",
    "loading-models": "-- lade verfügbare Modelle --",
    "enter-url": "Gib zuerst die Ollama-URL ein",
    "model-desc":
      "Wähle das Ollama-Modell aus, das du verwenden möchtest. Modelle werden geladen, wenn eine gültige URL eingegeben wurde.",
    "model-choose":
      "Wähle das Ollama-Modell, das du für deine Konversationen verwenden möchtest.",
  },

  // =========================
  // OPENAI LLM SELECTION
  // =========================
  openai: {
    "api-key": "OpenAI API-Schlüssel",
    "api-key-placeholder": "Gib deinen OpenAI API-Schlüssel ein",
    "model-preference": "Modell-Voreinstellung",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
    "model-divider": "──────────",
  },

  // =========================
  // OPENROUTER LLM SELECTION
  // =========================
  openrouter: {
    "api-key": "OpenRouter API-Schlüssel",
    "api-key-placeholder": "Gib deinen OpenRouter API-Schlüssel ein",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
  },

  // =========================
  // PERPLEXITY LLM SELECTION
  // =========================
  perplexity: {
    "api-key": "Perplexity API-Schlüssel",
    "api-key-placeholder": "Gib deinen Perplexity API-Schlüssel ein",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
    "available-models": "Verfügbare Perplexity-Modelle",
  },

  // =========================
  // TOGETHERAI
  // =========================
  togetherai: {
    "api-key": "TogetherAI API-Schlüssel",
    "api-key-placeholder": "Gib deinen TogetherAI API-Schlüssel ein",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
  },

  // =========================
  // XAI
  // =========================
  xai: {
    "api-key": "xAI API-Schlüssel",
    "api-key-placeholder": "xAI API-Schlüssel",
    "model-selection": "Chat-Modell-Auswahl",
    "loading-models": "-- lade verfügbare Modelle --",
    "enter-api-key":
      "Gib einen gültigen API-Schlüssel ein, um verfügbare Modelle für dein Konto zu sehen.",
    "available-models": "Verfügbare Modelle",
    "model-description":
      "Wähle das xAI-Modell aus, das du für deine Konversationen verwenden möchtest.",
  },
};
