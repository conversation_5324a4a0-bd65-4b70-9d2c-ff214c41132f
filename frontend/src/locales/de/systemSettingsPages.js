export default {
  appearance: {
    title: "Erscheinungsbild",
    description: "Passe das Erscheinungsbild deiner Plattform an.",
    logo: {
      title: "Logo anpassen",
      description: "Lade dein eigenes Logo für den Hellmodus hoch.",
      add: "Benutzerdefiniertes Logo hinzufügen",
      recommended: "<PERSON>pfoh<PERSON>ße: 800 x 200",
      remove: "Entfernen",
    },
    logoDark: {
      title: "Logo für den Dunkelmodus anpassen",
      description: "Lade dein eigenes Logo für den Dunkelmodus hoch.",
    },
    "welcome-message": {
      title: "Willkommensnachrichten anpassen",
      description:
        "Passe die Überschrift und den Text an, die auf der Startseite angezeigt werden.",
      heading: "Überschrift",
      text: "Text",
      save: "Nachrichten speichern",
    },
    icons: {
      title: "Benutzerdefinierte Footer-Icons",
      description:
        "Passe die Footer-Icons an, die unten in der Seitenleiste angezeigt werden.",
      icon: "Icon",
      link: "<PERSON>",
    },
    display: {
      title: "Anzeigesprache",
      description: "Wähle die bevorzugte Sprache aus.",
    },
    color: {
      title: "Benutzerdefinierte Farben",
      reset: "Zurücksetzen",
      "desc-start": "Passe die",
      "desc-mid": "*Hintergrundfarbe, *Primärfarbe",
      "desc-and": "und",
      "desc-end": "*Textfarbe an",
      red: "Rot",
      gray: "Grau",
      foynet: "Foynet",
      brown: "Braun",
      green: "Grün",
      yellow: "Gelb",
      cyan: "Cyan",
      magenta: "Magenta",
      orange: "TenderFlow",
      purple: "Lila",
      navy: "Marineblau",
      black: "Schwarz",
    },
    login: {
      title: "Benutzerdefinierter Login-Text",
      description:
        "Passe den Absatztext an, der auf der Login-Seite angezeigt wird.",
      placeholder: "Bitte kontaktiere den Administrator des Systems",
      website: {
        title: "Anwendungs-Website anpassen",
        description: "Passe die URL der Website deiner Anwendung an.",
        toggle: "Website-Link anzeigen",
      },
      validation: { invalidURL: "Bitte gib eine gültige URL ein." },
    },
    siteSettings: {
      title: "Benutzerdefinierte Seiteneinstellungen",
      description:
        "Passe den Inhalt des Browser-Tabs für Anpassung und Branding an.",
      tabTitle: "Tab-Titel",
      tabDescription:
        "Lege einen benutzerdefinierten Tab-Titel fest, der angezeigt wird, wenn die App im Browser geöffnet ist.",
      tabIcon: "Tab-Favicon",
      fabIconUrl: "Definiere eine URL zu einem Bild für dein Favicon",
      placeholder: "URL zu deinem Bild",
      "invalid-file-type":
        "Ungültiger Dateityp. Bitte verwende PNG, JPEG, GIF, WebP, SVG oder ICO-Dateien.",
      "file-too-large": "Datei zu groß. Maximale Größe ist 5MB.",
      "default-title": "IST Legal",
    },
    appName: {
      title: "Benutzerdefinierter App-Name",
      description:
        "Lege einen benutzerdefinierten App-Namen fest, der auf der Login-Seite angezeigt wird.",
    },
    customTab: {
      title: "Tab-Namen anpassen",
      tab1: "Tab 1",
      tab2: "Tab 2",
      tab3: "Tab 3",
    },
    template: {
      title: "System DOCX-Vorlage",
      description:
        "Lade eine Standard-Word-Vorlage (.docx) hoch, die für Canvas-Exporte verwendet wird.",
      tags: "Erforderliche Zusammenführungs-Tags im Dokument: {{title}} für den Dokumenttitel und {{body}} für den Hauptinhalt. Optionale Tags: {{author}}, {{date}}, {{createdAt}}, {{updatedAt}}, {{header}}, {{footer}}, {{pageNumber}}, {{totalPages}}, {{company}}, {{reference}}.",
      remove: "Entfernen",
      upload: "Vorlage hochladen",
      invalid: "Nur .docx-Dateien sind erlaubt",
    },
    "prompt-examples": {
      title: "Beispiel-Prompts",
      description:
        "Verwalte die Beispiel-Prompts, die auf der Hauptseite angezeigt werden.",
      example: "Beispiel {{number}}",
      remove: "Entfernen",
      "title-field": "Titel",
      area: "Rechtsgebiet",
      prompt: "Prompt",
      icon: "Icon auswählen",
      workspace: "Arbeitsbereich",
      add: "Beispiel hinzufügen",
      save: "UI speichern",
    },
  },

  llm: {
    title: "LLM-Voreinstellung",
    description:
      "Dies sind die Zugangsdaten und Einstellungen für deinen bevorzugten LLM-Chat- und Embedding-Anbieter. Es ist wichtig, dass diese Schlüssel aktuell und korrekt sind, sonst funktioniert das System nicht ordnungsgemäß.",
    provider: "LLM-Anbieter",
    "secondary-provider": "Sekundärer LLM-Anbieter",
    "none-selected": "Keiner ausgewählt",
    "select-llm":
      "Agenten funktionieren nicht, bis eine gültige Auswahl getroffen wurde.",
    "search-llm": "Durchsuche alle LLM-Anbieter",
    "context-window-warning":
      "Warnung: Kontextfenster für das ausgewählte Modell konnte nicht abgerufen werden.",
    "context-window-waiting": " -- warte auf Kontextfensterinformationen -- ",
    "validation-prompt": {
      disable: {
        label: "Validierungs-Prompt deaktivieren",
        description:
          "Wenn aktiviert, wird die Validierungsschaltfläche in der Benutzeroberfläche nicht angezeigt.",
      },
    },
    "prompt-upgrade": {
      title: "LLM-Anbieter für Prompt-Verbesserung",
      description:
        "Der spezifische LLM-Anbieter und das Modell, das für die Verbesserung von Benutzer-Prompts verwendet wird. Standardmäßig werden der System-LLM-Anbieter und dessen Einstellungen verwendet.",
      search: "Verfügbare LLM-Anbieter für diese Funktion durchsuchen",
      template: "Prompt-Verbesserungsvorlage",
      "template-description":
        "Diese Vorlage wird beim Verbessern von Prompts verwendet. Verwenden Sie {{prompt}}, um sich auf den zu verbessernden Text zu beziehen.",
      "template-placeholder":
        "Geben Sie die Vorlage ein, die für die Verbesserung von Prompts verwendet werden soll...",
      "template-hint":
        "Beispiel: Bitte verbessern Sie den folgenden Text unter Beibehaltung seiner Bedeutung: {{prompt}}",
    },
    "logo-alt": "{{name}} logo",
    "context-window": "Kontextfenster",
    "default-context-window": "(Standardgröße für diesen Anbieter)",
    tokens: "Tokens",
    "save-error": "LLM-Einstellungen konnten nicht gespeichert werden",
  },
};
