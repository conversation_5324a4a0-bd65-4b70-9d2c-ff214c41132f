export default {
  // =========================
  // HEADER
  // =========================
  header: {
    account: "Konto",
    login: "Anmelden",
    "sign-out": "Abmelden",
  },

  // =========================
  // FEATURE CARDS
  // =========================
  featureCards: {
    "draft-from-template-title": "Dokumententwurf aus Vorlage erstellen",
    "draft-from-template-description":
      "Nutzen Sie die Funktion, um beispielsweise eine AML-Richtlinie, ein Protokoll für eine Hauptversammlung oder eine standardisierte Schiedsvereinbarung zu erstellen.",
    "complex-document-builder-title": "Komplexe juristische Aufgabe ausführen",
    "complex-document-builder-description":
      "Perfekt, wenn Sie beispielsweise Hunderte von Dokumenten vor einer Unternehmensübernahme prüfen oder eine detaillierte Klageschrift formulieren müssen.",
  },
  // =========================
  // DEFAULT CHAT
  // =========================
  "default-chat": {
    welcome: "Willkommen bei IST Legal.",
    "choose-legal": "Wähle links ein Rechtsgebiet aus.",
  },
  // =========================
  // MODULE DEFINITIONS
  // =========================
  module: {
    "legal-qa": "Legal Q&A",
    "document-drafting": "Dokumenten-Entwurf",
    "active-case": "Aktiver Fall",
  },
  // =========================
  // MOBILE DISCLAIMER
  // =========================
  mobile: {
    disclaimer:
      "HAFTUNGSAUSSCHLUSS: Für das beste Erlebnis und den vollständigen Zugriff auf alle Funktionen, nutze bitte einen Computer, um auf die App zuzugreifen.",
  },
  // =========================
  // ACTIVE CASE
  // =========================
  "active-case": {
    title: "Aktiver Fall",
    placeholder: "Referenznummer eingeben",
    "select-reference": "Referenz auswählen",
    "warning-title": "Fehlende Referenznummer",
    "warning-message":
      "Es wurde keine Referenznummer festgelegt. Möchten Sie ohne Referenznummer fortfahren?",
  },
  // =========================
  // PROMPT ERRORS
  // =========================
  prompt: {
    error: {
      empty: "Prompt darf nicht leer sein",
      upgrade: "Fehler beim Aktualisieren des Prompts",
    },
    decline: "Ablehnen",
  },
};
