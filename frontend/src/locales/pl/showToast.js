export default {
  "show-toast": {
    "recovery-codes": "Kody odzyskiwania skopiowane do schowka",
    "token-minimum-error": "Zawartość pliku poniżej minimalnej liczby tokenów",
    "file-process-error": "Nie udało się przetworzyć pliku",
    "scraping-website":
      "Pobieranie zawartości strony - to może chwilę potrwać.",
    "fetching-transcript": "Pobieranie transkrypcji wideo z YouTube.",
    "request-legal-assistance-sent": "Żądanie pomocy prawnej zostało wysłane.",
    "request-legal-assistance-error":
      "Nie udało się wysłać żądania pomocy prawnej.",
    "updating-workspace": "Aktualizowanie przestrzeni roboczej...",
    "flashing-started": "Proces aktualizacji rozpoczęty...",
    "flashing-success": "Aktualizacja zakończona sukcesem",
    "flashing-error": "<PERSON>łąd podczas aktualizacji: {{error}}",
    "pin-success-added": "Dokument przypięty do przestrzeni roboczej",
    "pin-success-removed": "Dokument odpięty od przestrzeni roboczej",
    "workspace-updated": "Przestrzeń robocza zaktualizowana pomyślnie.",
    "link-uploaded": "Link przesłany pomyślnie",
    "password-reset": "Resetowanie hasła zakończone sukcesem",
    "invalid-reset": "Nieprawidłowy token resetowania",
    "delete-option": "Nie udało się usunąć wątku!",
    "thread-deleted": "Wątek usunięty pomyślnie!",
    "threads-deleted": "Wątki usunięte pomyślnie!",
    "chat-deleted": "Czat usunięty pomyślnie!",
    "failed-delete-chat": "Nie udało się usunąć czatu. Spróbuj ponownie.",
    "error-deleting-chat": "Wystąpił błąd podczas usuwania czatu.",
    "chat-memory-reset":
      "Pamięć czatu przestrzeni roboczej została zresetowana!",
    "picture-uploaded": "Zdjęcie profilowe przesłane.",
    "profile-updated": "Profil zaktualizowany.",
    "logs-cleared": "Logi zdarzeń wyczyszczone pomyślnie.",
    "preferences-updated": "Preferencje systemowe zaktualizowane pomyślnie.",
    "user-created": "Użytkownik utworzony pomyślnie.",
    "user-creation-error": "Nie udało się utworzyć użytkownika: {{error}}",
    "user-exists-error": "Użytkownik o tym adresie e-mail już istnieje",
    "user-deleted": "Użytkownik został usunięty z systemu.",
    "workspaces-saved": "Przestrzenie robocze zapisane pomyślnie!",
    "failed-workspaces":
      "Nie udało się zapisać przestrzeni roboczych. Spróbuj ponownie.",
    "api-deleted": "Klucz API został trwale usunięty",
    "api-copied": "Klucz API skopiowany do schowka",
    "appname-updated":
      "Niestandardowa nazwa aplikacji zaktualizowana pomyślnie.",
    "appname-update-error":
      "Nie udało się zaktualizować niestandardowej nazwy aplikacji: ",
    "language-updated": "Język zaktualizowany pomyślnie.",
    "palette-updated": "Paleta kolorów zaktualizowana pomyślnie.",
    "image-uploaded": "Obraz przesłany pomyślnie.",
    "logo-remove-error": "Błąd przy usuwaniu logo: ",
    "logo-removed": "Logo zostało pomyślnie usunięte.",
    "logo-uploaded": "Logo przesłane pomyślnie.",
    "logo-upload-error": "Nie udało się przesłać logo: ",
    "updated-welcome": "Wiadomości powitalne zaktualizowane pomyślnie.",
    "update-welcome-error":
      "Nie udało się zaktualizować wiadomości powitalnych:",
    "updated-footer": "Ikony stopki zaktualizowane pomyślnie.",
    "update-footer-error": "Nie udało się zaktualizować ikon stopki: ",
    "updated-paragraph":
      "Niestandardowy tekst akapitu zaktualizowany pomyślnie.",
    "update-paragraph-error":
      "Nie udało się zaktualizować niestandardowego tekstu akapitu: ",
    "updated-supportemail": "Adres e-mail wsparcia zaktualizowany pomyślnie.",
    "update-supportemail-error":
      "Nie udało się zaktualizować adresu e-mail wsparcia: ",
    "stt-success": "Preferencje mowy na tekst zapisane pomyślnie.",
    "tts-success": "Preferencje tekstu na mowę zapisane pomyślnie.",
    "failed-chats-export": "Nie udało się wyeksportować czatów.",
    "chats-exported": "Czaty wyeksportowane pomyślnie jako {{name}}.",
    "cleared-chats": "Wyczyszczono wszystkie czaty.",
    "embed-deleted": "Osadzenie usunięte z systemu.",
    "snippet-copied": "Fragment skopiowany do schowka!",
    "embed-updated": "Osadzenie zaktualizowane pomyślnie.",
    "embedding-saved": "Preferencje osadzania zapisane pomyślnie.",
    "chunking-settings": "Ustawienia strategii fragmentacji tekstu zapisane.",
    "llm-saved": "Preferencje LLM zapisane pomyślnie.",
    "llm-saving-error": "Nie udało się zapisać ustawień LLM: ",
    "multiuser-enabled": "Tryb wieloużytkownikowy włączony pomyślnie.",
    "publicuser-enabled": "Tryb użytkownika publicznego włączony pomyślnie.",
    "publicuser-disabled": "Tryb użytkownika publicznego wyłączony pomyślnie.",
    "page-refresh": "Twoja strona odświeży się za kilka sekund.",
    "transcription-saved": "Preferencje transkrypcji zapisane pomyślnie.",
    "vector-saved": "Preferencje bazy danych wektorowych zapisane pomyślnie.",
    "workspace-not-deleted": "Nie udało się usunąć przestrzeni roboczej!",
    "maximum-messages": "Dozwolonych jest maksymalnie 4 wiadomości.",
    "users-updated": "Użytkownicy zaktualizowani pomyślnie.",
    "vectordb-not-reset":
      "Nie udało się zresetować bazy danych wektorowych przestrzeni roboczej!",
    "vectordb-reset":
      "Baza danych wektorowych przestrzeni roboczej została zresetowana!",
    "meta-data-update": "Preferencje witryny zaktualizowane!",
    "linked-workspaces-updated":
      "Powiązane przestrzenie robocze zaktualizowane pomyślnie.",
    "upgrade-answer-error": "Nie udało się ulepszyć odpowiedzi: ",
    "upgrade-text-error": "Nie udało się ulepszyć tekstu: ",
    "reset-tab-name-error":
      "Nie udało się zresetować nazwy zakładki do domyślnej.",
    "update-tab-name-error": "Nie udało się zaktualizować nazw zakładek: ",
    "updated-website":
      "Ustawienia strony internetowej zaktualizowane pomyślnie.",
    "update-website-error": "Nie udało się zaktualizować linku do strony: ",
    "reset-website-error":
      "Nie udało się zresetować linku do strony do domyślnego.",
    "palette-update-error": "Nie udało się zaktualizować palety kolorów: ",
    "citation-state-updated":
      "Stan cytowania zaktualizowany pomyślnie. {{citationState}}",
    "citation-state-update-error":
      "Nie udało się zaktualizować ustawienia cytowania",
    "citation-update-error": "Błąd podczas przesyłania ustawienia cytowania",
    "message-limit-updated":
      "Preferencje limitu wiadomości zaktualizowane pomyślnie.",
    "validate-response-error": "Walidacja nie powiodła się z ",
    "invoice-logging-state-updated":
      "Preferencje logowania faktur zaktualizowane pomyślnie.",
    "invoice-logging-state-update-error":
      "Błąd podczas aktualizacji stanu logowania faktur: ",
    "error-fetching-tab-names": "Błąd pobierania nazw zakładek",
    "active-case": {
      "reference-updated":
        "Referencja aktywnej sprawy zaktualizowana pomyślnie",
      "reference-cleared": "Referencja aktywnej sprawy usunięta pomyślnie",
    },
    "export-word": "Eksportuj do Worda",
    "export-error": "Błąd eksportu do dokumentu Word",
    "export-success": "Dokument wyeksportowany pomyślnie",
    "file-upload-success": "Plik pomyślnie dołączony",
    "files-upload-success": "{{count}} plików pomyślnie dołączonych",
    "file-upload-error": "Błąd przesyłania pliku(-ów)",
    "file-removed": "Plik usunięty pomyślnie",
    "file-remove-error":
      "Nie udało się całkowicie usunąć pliku. Spróbuj ponownie.",
    "link-upload-success": "Link przesłany pomyślnie",
    "link-upload-error": "Błąd przesyłania linku: {{error}}",
    "qura-login-success": "Zalogowano pomyślnie do Qura",
    "error-linking-rexor": "Błąd pobierania statusu powiązania z Rexorem",
    "article-transaction-registered":
      "Transakcja czasowa zarejestrowana pomyślnie",
    "changes-saved": "Zmiany zapisane pomyślnie",
    "missing-data": "Brak wymaganych danych",
    "save-error": "Błąd zapisywania zmian",
    "invalid-response": "Otrzymano nieprawidłową odpowiedź",
    "streaming-error": "Błąd podczas transmisji",
    "qura-auth.error.fill-fields": "Proszę wypełnić wszystkie pola.",
    "show-toast.qura-login-success": "Logowanie udane.",
    "qura-auth.error.invalid-credentials":
      "Nieprawidłowa nazwa użytkownika lub hasło.",
    "qura-auth.connect": "Połącz z Qura",
    "speech-to-text.microphone-access-error":
      "Dostęp do mikrofonu jest wymagany.",
    "show-toast.meta-data-update": "Metadane zaktualizowane pomyślnie.",
    "appearance.siteSettings.tabIcon": "Ikona zakładki",
    "appearance.siteSettings.fabIconUrl": "URL favicon",
    "appearance.siteSettings.placeholder": "Wprowadź URL favicon",
    "appearance.siteSettings.title-placeholder": "Wprowadź tytuł strony",
    "show-toast.workspace-updated":
      "Przestrzeń robocza zaktualizowana pomyślnie.",
    "pdr-settings.toast-success": "Ustawienia PDR zaktualizowane pomyślnie.",
    "pdr-settings.toast-fail": "Nie udało się zaktualizować ustawień PDR.",
    "pdr-settings.title": "Ustawienia PDR",
    "pdr-settings.description": "Zarządzaj tutaj ustawieniami PDR.",
    "pdr-settings.desc-end": "Upewnij się, że wszystkie wartości są poprawne.",
    "pdr-settings.pdr-token-limit": "Limit tokenów PDR",
    "pdr-settings.pdr-token-limit-desc":
      "Maksymalna liczba tokenów używanych przez algorytm PDR.",
    "pdr-settings.pdr-token-limit-placeholder": "Wprowadź limit tokenów PDR",
    "pdr-settings.input-prompt-token-limit":
      "Limit tokenów dla zapytania wejściowego",
    "pdr-settings.input-prompt-token-limit-desc":
      "Maksymalna liczba tokenów używanych dla zapytań wejściowych.",
    "pdr-settings.input-prompt-token-limit-placeholder":
      "Wprowadź limit tokenów dla zapytania wejściowego",
    "pdr-settings.response-token-limit": "Limit tokenów odpowiedzi",
    "pdr-settings.response-token-limit-desc":
      "Maksymalna liczba tokenów używanych dla odpowiedzi.",
    "pdr-settings.response-token-limit-placeholder":
      "Wprowadź limit tokenów dla odpowiedzi",
    "pdr-settings.adjacent-vector-limit": "Limit sąsiednich wektorów",
    "pdr-settings.adjacent-vector-limit-desc": "Limit dla sąsiednich wektorów.",
    "pdr-settings.adjacent-vector-limit-placeholder":
      "Wprowadź limit dla sąsiednich wektorów",
    "pdr-settings.keep-pdr-vectors": "Zachowaj wektory PDR",
    "pdr-settings.keep-pdr-vectors-desc": "Opcja zachowania wektorów PDR.",
    "workspace-update-error": "Błąd: {{error}}",
    "workspace-update-failed":
      "Aktualizacja przestrzeni roboczej nie powiodła się: {{error}}",
    "token-window-exceeded":
      "Plik nie został dodany, ponieważ przekracza dostępny limit tokenów dla obecnego silnika AI",
    "token-limit-exceeded":
      "Plik jest za duży dla dostępnego okna kontekstowego. Dostępne tokeny: {{available}}, Wymagane tokeny: {{required}}",
    "rexor-activity-id-update-success":
      "ID aktywności Rexor zaktualizowane pomyślnie",
    "rexor-activity-id-update-error":
      "Nie udało się zaktualizować ID aktywności Rexor",
    "pin-error": "Nie udało się {{action}} dokumentu.",
    "pin-error-detail": "Nie udało się przypiąć dokumentu. {{message}}",
    "error-fetching-settings": "Wystąpił błąd podczas pobierania ustawień.",
    "experimental-features-unlocked":
      "Podgląd funkcji eksperymentalnych odblokowany!",
    "workspace-updated-success": "Przestrzeń robocza zaktualizowana pomyślnie.",
    "legal-task-created": "Utworzono zadanie prawne!",
    "form-submission-failed": "Nie udało się przesłać formularza!",
    "provider-endpoint-discovered":
      "Punkt końcowy dostawcy wykryty automatycznie.",
    "failed-save-embedding": "Nie można zapisać ustawień osadzania: {{error}}",
    "provider-endpoint-discovery-failed":
      "Nie można automatycznie wykryć punktu końcowego dostawcy. Proszę wprowadzić go ręcznie.",
    "qura-setting-update-failed":
      "Nie udało się zaktualizować ustawienia Qura.",
    "qura-settings-submission-error": "Błąd podczas przesyłania ustawień Qura.",
    "preferences-save-error": "Wystąpił błąd podczas zapisywania preferencji.",
    "skill-config-updated":
      "Konfiguracja umiejętności zaktualizowana pomyślnie.",
    "category-deleted-success": "Kategoria usunięta pomyślnie!",
    "category-deletion-failed": "Nie udało się usunąć kategorii!",
    "embed-chats-export-failed":
      "Nie udało się wyeksportować osadzonych czatów.",
    "api-key-revoked":
      "Klucz API rozszerzenia przeglądarki trwale unieważniony",
    "api-key-revoke-failed": "Nie udało się unieważnić klucza API",
    "connection-string-copied": "Ciąg połączenia skopiowany do schowka",
    "connecting-to-extension":
      "Próba połączenia z rozszerzeniem przeglądarki...",
    "error-loading-settings":
      "Błąd ładowania ustawień. Proszę odświeżyć stronę.",
    "error-fetching-prompt-logging":
      "Błąd pobierania ustawienia logowania wyników promptów.",
    "error-updating-prompt-logging":
      "Nie udało się zaktualizować ustawienia logowania wyników promptów.",
    "error-updating-feature": "Nie udało się zaktualizować statusu funkcji.",
    "rexor-linkage-state-updated":
      "Stan połączenia Rexor zaktualizowany do {{state}}",
    "rexor-linkage-state-update-error":
      "Nie udało się zaktualizować stanu połączenia Rexor: {{error}}",
    "language-update-failed": "Aktualizacja języka nie powiodła się: {{error}}",
    "llm-settings-save-failed": "Nie udało się zapisać ustawień LLM: {{error}}",
    "setup-error": "Błąd: {{error}}",
    "template-saved": "Szablon monitu zapisany pomyślnie",
    "template-saving-error": "Nie udało się zapisać szablonu: {{error}}",
    "template-reset": "Szablon zresetowany do domyślnego",
    "legal-task-updated": "Zadanie prawne zaktualizowane pomyślnie",
    "legal-task-update-failed": "Nie udało się zaktualizować zadania prawnego",
    "model-ref-saved":
      "Zapisano zmiany w nazwie modelu niestandardowego i/lub niestandardowym PDR",
    "model-ref-save-failed":
      "Nie udało się zapisać ustawień modelu niestandardowego: {{error}}",
    "settings-fetch-failed": "Nie udało się pobrać ustawień",
    "api-key-saved": "Pomyślnie zapisano klucz API",
    "auto-environment-update":
      "Rozpoczęto automatyczną aktualizację środowiska. Potrwa to kilka sekund.",
    "example-prompt-error":
      "Nie udało się przesłać przykładowego promptu: {{error}}",
    "files-processed": "Pomyślnie przetworzono {{count}} plik(ów)",
    "token-validation-failed":
      "Weryfikacja pliku nie powiodła się - przekroczono limit tokenów lub rozmiaru",
    "deleted-old-chats": "Usunięto {{count}} starych promptów",
    "pdr-failed": "Nie udało się PDR dokumentu: {{message}}",
    "watch-failed": "Nie udało się obserwować dokumentu: {{message}}",
    "pdr-added": "Dokument dodany do pobierania dokumentu nadrzędnego",
    "pdr-removed": "Dokument usunięty z pobierania dokumentu nadrzędnego",
    "unsaved-changes":
      "Masz niezapisane zmiany. Czy jesteś pewny, że chcesz anulować?",
  },
  toast: {
    export: {
      success: "{{name}} wyeksportowany pomyślnie",
      failed: "Nie udało się wyeksportować",
      "embed-chats-success":
        "Osadzone czaty wyeksportowane pomyślnie jako {{name}}",
      "embed-chats-failed": "Nie udało się wyeksportować osadzonych czatów",
    },
    profile: {
      "upload-failed": "Nie udało się przesłać zdjęcia profilowego: {{error}}",
      "remove-failed": "Nie udało się usunąć zdjęcia profilowego: {{error}}",
    },
    workspace: {
      "create-failed": "Nie udało się utworzyć przestrzeni roboczej: {{error}}",
      "update-failed":
        "Nie udało się zaktualizować przestrzeni roboczej: {{error}}",
      "thread-create-failed": "Nie udało się utworzyć wątku: {{error}}",
      "thread-update-failed": "Nie udało się zaktualizować wątku: {{message}}",
    },
    errors: {
      "file-move": "Błąd przenoszenia plików: {{message}}",
      "thread-create": "Nie udało się utworzyć wątku - {{error}}",
      "connector-error": "{{error}}",
      "generic-error": "{{error}}",
      "failed-update-user":
        "Nie udało się zaktualizować użytkownika: {{error}}",
      "failed-save-llm": "Nie udało się zapisać ustawień LLM: {{error}}",
      "failed-save-preferences": "Nie udało się zapisać preferencji: {{error}}",
      "failed-clear-logs": "Nie udało się wyczyścić logów: {{error}}",
      "failed-create-thread": "Nie udało się utworzyć wątku - {{error}}",
      "failed-move-files": "Błąd przenoszenia plików: {{message}}",
      streaming: {
        failed:
          "Wystąpił błąd podczas przesyłania odpowiedzi, np. silnik AI jest offline lub przeciążony.",
        code: "Kod",
        unknown: "Nieznany błąd.",
      },
    },
    success: {
      "workspace-created":
        "Przestrzeń robocza utworzona pomyślnie! Przekierowywanie do strony głównej...",
      "generic-success": "{{message}}",
    },
    "prompt-output-logging": {
      enabled: "Logowanie wyników promptów zostało włączone",
      disabled: "Logowanie wyników promptów zostało wyłączone",
      error: "Błąd podczas przesyłania ustawień logowania wyników promptów.",
    },
    "welcome-messages": {
      "update-failed":
        "Nie udało się zaktualizować wiadomości powitalnych: {{error}}",
      "drafting-update-failed":
        "Nie udało się zaktualizować wiadomości dotyczących tworzenia dokumentów: {{error}}",
    },
    info: { "file-moved": "{{message}}" },
    document: {
      "move-success": "Pomyślnie przeniesiono {{count}} dokumentów",
      "pdr-failed": "Nie udało się PDR dokumentu: {{message}}",
      "watch-failed": "Nie udało się obserwować dokumentu: {{message}}",
      "pdr-added": "Dokument dodany do Parent Document Retrieval",
      "pdr-removed": "Dokument usunięty z Parent Document Retrieval",
      "pin-success":
        "PRZESTARZAŁE - Użyj document.pin-success-added lub document.pin-success-removed zamiast tego",
      "pin-success-added": "Dokument przypięty do przestrzeni roboczej",
      "pin-success-removed": "Dokument odpięty od przestrzeni roboczej",
    },
    settings: {
      "preferences-failed": "Nie udało się zapisać preferencji: {{error}}",
      "multi-user-failed":
        "Nie udało się włączyć trybu wieloużytkownikowego: {{error}}",
      "public-user-failed":
        "Nie udało się włączyć trybu użytkownika publicznego: {{error}}",
      "password-failed": "Nie udało się zaktualizować hasła: {{error}}",
      "vector-db-failed":
        "Nie udało się zapisać ustawień bazy danych wektorowych: {{error}}",
      "llm-failed": "Nie udało się zapisać ustawień LLM: {{error}}",
      "llm-save-failed": "Nie udało się zapisać ustawień {{name}}: {{error}}",
      "preferences-save-failed": "Nie udało się zapisać preferencji: {{error}}",
      "transcription-save-failed":
        "Nie udało się zapisać ustawień transkrypcji: {{error}}",
      "embedding-save-failed":
        "Nie udało się zapisać ustawień osadzania: {{error}}",
      "welcome-messages-failed":
        "Nie udało się zapisać wiadomości powitalnych: {{error}}",
      "welcome-messages-fetch-failed":
        "Nie udało się pobrać wiadomości powitalnych",
      "welcome-messages-empty":
        "Proszę wprowadzić nagłówek lub tekst wiadomości",
      "welcome-messages-success": "Wiadomości powitalne zapisane pomyślnie",
      "user-update-failed":
        "Nie udało się zaktualizować użytkownika: {{error}}",
      "logs-clear-failed": "Nie udało się wyczyścić logów: {{error}}",
      "generic-error": "Błąd: {{message}}",
      "prompt-examples-failed":
        "Nie udało się zapisać przykładów promptów: {{error}}",
      "prompt-examples-success": "Przykłady promptów zapisane pomyślnie",
      "prompt-examples-validation":
        "Przykład {{number}} brakuje wymaganych pól: {{fields}}",
      "import-success": "Pomyślnie zaimportowano wątek: {{threadName}}",
    },
    experimental: {
      "feature-enabled": "Funkcje eksperymentalne włączone",
      "feature-disabled": "Funkcje eksperymentalne wyłączone",
      "update-failed":
        "Nie udało się zaktualizować statusu funkcji eksperymentalnej",
      "features-enabled":
        "Funkcje eksperymentalne włączone. Strona zostanie przeładowana.",
      "live-sync": {
        enabled: "Synchronizacja dokumentów na żywo włączona",
        disabled: "Synchronizacja dokumentów na żywo wyłączona",
      },
    },
  },
};
