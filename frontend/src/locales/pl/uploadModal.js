export default {
  // =========================
  // DOCUMENTS & PINNING
  // =========================
  documents: {
    "pin-info-button": "O przypinaniu dokumentów",
    "pin-title": "Co to jest przypinanie dokumentów?",
    "pin-desc-1":
      "Kiedy przypinasz dokument, platforma wstrzykuje całą zawartość dokumentu do okna zapytania, aby Twój LLM mógł go w pełni zrozumieć.",
    "pin-desc-2":
      "Naj<PERSON><PERSON>j działa to z modelami o dużym kontekście lub z małymi plikami, które są kluczowe dla bazy wiedzy.",
    "pin-desc-3":
      "Jeśli nie uzyskujesz oczekiwanych odpowiedzi domyślnie, przypinanie jest doskonałym sposobem na uzyskanie wyższ<PERSON> jakości odpowiedzi jednym kliknięciem.",
    "pin-add": "Przypnij do przestrzeni roboczej",
    "pin-unpin": "Odepnij od przestrzeni roboczej",
    "watch-title": "Co oznacza obserwowanie dokumentu?",
    "watch-desc-1":
      "Kiedy obserwujesz dokument, będziemy automatycznie synchronizować jego zawartość z oryginalnego źródła w regularnych odstępach czasu. Automatycznie zaktualizuje to zawartość we wszystkich przestrzeniach roboczych, w których plik jest zarządzany.",
    "watch-desc-2":
      "Funkcja ta obsługuje obecnie treści online i nie będzie dostępna dla dokumentów przesłanych ręcznie.",
    "watch-desc-3": "Możesz zarządzać obserwowanymi dokumentami w",
    "file-manager": "Menedżerze plików",
    "admin-view": "widoku administratora",
    "pdr-add":
      "Dodano wszystkie dokumenty do wyszukiwania dokumentów nadrzędnych (PDR)",
    "pdr-remove":
      "Usunięto wszystkie dokumenty z wyszukiwania dokumentów nadrzędnych (PDR)",
    empty: "Nie znaleziono dokumentów",
    tooltip: {
      date: "Data: ",
      type: "Typ: ",
      cached: "Zbuforowany",
    },
    actions: {
      removing: "Usuwanie pliku z przestrzeni roboczej",
    },
    costs: {
      estimate: "Szacunkowy koszt: $",
      minimum: "< $0.01",
    },
    "new-folder": {
      title: "Utwórz nowy folder",
      "name-label": "Nazwa folderu",
      "name-placeholder": "Wprowadź nazwę folderu",
      create: "Utwórz folder",
    },
    error: {
      "create-folder": "Nie udało się utworzyć folderu",
    },
  },
};
