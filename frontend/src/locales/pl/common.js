// /Users/<USER>/GitHub/I_produktion/ISTLegal/frontend/src/locales/pl/common.js

const TRANSLATIONS = {
  // =========================
  // COMMON STRINGS & PLACEHOLDERS
  // =========================
  common: {
    examples: "Przykłady",
    "workspaces-name": "Nazwa obszaru roboczego",
    ok: "OK",
    error: "błąd",
    confirm: "Potwierdź",
    confirmstart: "Potwierdź i rozpocznij",
    savesuccess: "Pomyślnie zapisano ustawienia",
    saveerror: "Nie udało się zapisać ustawień",
    success: "sukces",
    user: "Użytkownik",
    selection: "Wybór modelu",
    saving: "Zapisywanie...",
    save: "Zap<PERSON>z zmiany",
    previous: "Poprzednia strona",
    next: "Następna strona",
    cancel: "Anuluj",
    "search-placeholder": "Szukaj...",
    "more-actions": "Więcej działań",
    "delete-message": "Usuń wiadomość",
    copy: "Kopiuj",
    edit: "Edytuj",
    regenerate: "Wygeneruj ponownie",
    "export-word": "Eksportuj do Worda",
    "stop-generating": "Zatrzymaj generowanie",
    "attach-file": "Dołącz plik do tego czatu",
    home: "Strona główna",
    settings: "Ustawienia",
    support: "Wsparcie",
    "clear-reference": "Wyczyść odniesienie",
    "send-message": "Wyślij wiadomość",
    "ask-legal": "Zapytaj o informacje prawne",
    "stop-response": "Zatrzymaj generowanie odpowiedzi",
    "contact-support": "Skontaktuj się ze wsparciem",
    "copy-connection": "Kopiuj ciąg połączenia",
    "auto-connect": "Automatycznie połącz z rozszerzeniem",
    back: "Wstecz",
    off: "Wyłączone",
    on: "Włączone",
    "back-to-workspaces": "Powrót do obszarów roboczych",
    continue: "Kontynuuj",
    rename: "Zmień nazwę",
    delete: "Usuń",
    "default-skill":
      "Ta umiejętność jest domyślnie włączona i nie można jej wyłączyć.",
    placeholder: {
      username: "Moja nazwa użytkownika",
      password: "Twoje hasło",
      email: "Wprowadź swój e-mail",
      "support-email": "<EMAIL>",
      website: "https://www.przyklad.pl",
      "site-name": "IST Legal",
      "search-llm": "Szukaj konkretnego dostawcy LLM",
      "search-providers": "Szukaj dostępnych dostawców",
      "message-heading": "Nagłówek wiadomości",
      "message-content": "Wiadomość",
      "token-limit": "4096",
      "max-tokens": "Maksymalna liczba tokenów na żądanie (np.: 1024)",
      "api-key": "Klucz API",
      "base-url": "Adres URL podstawowy",
      endpoint: "Punkt końcowy API",
    },
    tooltip: {
      copy: "Kopiuj do schowka",
      delete: "Usuń ten element",
      edit: "Edytuj ten element",
      save: "Zapisz zmiany",
      cancel: "Anuluj zmiany",
      search: "Szukaj elementów",
      add: "Dodaj nowy element",
      remove: "Usuń element",
      upload: "Prześlij plik",
      download: "Pobierz plik",
      refresh: "Odśwież dane",
      settings: "Otwórz ustawienia",
      more: "Więcej opcji",
    },
    "default.message": "Wprowadź swoją wiadomość tutaj",
    preview: "Podgląd",
    prompt: "Prompt",
    loading: "Ładowanie...",
    download: "Pobierz",
    open_in_new_tab: "Otwórz w nowej karcie",
    timeframes: "Okresy czasu",
    other: "Inne opcje",
    close: "Zamknij",
    note: "Uwaga",
  },

  // =========================
  // RECENT UPLOADS COMPONENT
  // =========================

  // moved to recentUploads.js

  // =========================
  // CHAT BOX DRAG & DROP COMPONENT
  // =========================
  chatboxdnd: {
    title: "Dodaj plik do tego zapytania",
    description:
      "Upuść tutaj swój plik, aby dodać go do tego zapytania. Nie jest on przechowywany w przestrzeni roboczej jako trwałe źródło.",
    "file-prefix": "Plik:",
    "attachment-tooltip":
      "Ten plik zostanie dołączony do Twojej wiadomości. Nie zostanie zapisany w przestrzeni roboczej jako trwałe źródło.",
    "uploaded-file-tag": "PRZESŁANY PLIK UŻYTKOWNIKA",
  },

  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Klucz przestrzeni Confluence",
    "space-key-desc":
      "Jest to klucz przestrzeni Twojej instancji Confluence, który będzie używany. Zwykle zaczyna się od ~",
    "space-key-placeholder": "np.: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "np.: https://example.atlassian.net, http://localhost:8211, itd...",
    "token-tooltip": "Możesz utworzyć token API",
    "token-tooltip-here": "tutaj",
  },

  // =========================
  // CONFIRMATION MESSAGES
  // =========================
  deleteWorkspaceConfirmation:
    "Czy na pewno chcesz usunąć {{name}}?\nPo wykonaniu tej operacji będzie ona niedostępna w tej instancji.\n\nTa operacja jest nieodwracalna.",
  deleteConfirmation:
    "Czy na pewno chcesz usunąć ${user.username}?\nPo wykonaniu tej operacji użytkownik zostanie wylogowany i nie będzie mógł korzystać z tej instancji.\n\nTa operacja jest nieodwracalna.",
  suspendConfirmation:
    "Czy na pewno chcesz zawiesić {{username}}?\nPo wykonaniu tej operacji użytkownik zostanie wylogowany i nie będzie mógł się ponownie zalogować do tej instancji, dopóki administrator nie zdejmie zawieszenia.",
  flushVectorCachesWorkspaceConfirmation:
    "Czy na pewno chcesz wyczyścić pamięć podręczną wektorów dla tej przestrzeni roboczej?",
  apiKeys: {
    "deactivate-title": "Dezaktywuj klucz API",
    "deactivate-message":
      "Czy na pewno chcesz dezaktywować ten klucz API?\nPo wykonaniu tej operacji nie będzie już można go używać.\n\nTa operacja jest nieodwracalna.",
  },
  // =========================
  // SETTINGS SIDEBAR MENU ITEMS
  // =========================

  // Settings section moved to settings.js

  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Ustawienia interfejsu czatu",
    description: "Skonfiguruj ustawienia czatu.",
    auto_submit: {
      title: "Automatyczne wysyłanie wprowadzania głosowego",
      description:
        "Automatycznie wysyłaj wprowadzanie głosowe po okresie ciszy",
    },
    auto_speak: {
      title: "Automatyczne odczytywanie odpowiedzi",
      description: "Automatycznie odczytuj odpowiedzi AI",
    },
  },

  // =========================
  // QURA BUTTONS
  // =========================
  qura: {
    "copy-to-cora": "Sprawdź źródło Qura",
    "qura-status": "Przycisk Qura jest ",
    "copy-option": "Kopiuj opcję",
    "option-quest": "Pytanie",
    "option-resp": "Odpowiedź",
    "role-description":
      "Dodaj przycisk Qura, aby pobudzić odpowiedzi na Qura.law",
  },

  // =========================
  // LOGIN & SIGN-IN PAGES
  // =========================
  login: {
    "multi-user": {
      welcome: "Witamy w",
      "placeholder-username": "Adres e-mail",
      "placeholder-password": "Hasło",
      login: "Zaloguj się",
      validating: "Weryfikacja...",
      "forgot-pass": "Zapomniałeś hasła",
      "back-to-login": "Powrót do logowania",
      "reset-password": "Zresetuj hasło",
      reset: "Resetuj",
      "reset-password-info":
        "Podaj poniżej niezbędne informacje, aby zresetować hasło.",
    },
    "sign-in": {
      start: "Zaloguj się na swoje konto",
      end: "konto.",
    },
    button: "zaloguj",
    password: {
      forgot: "Zapomniałeś hasła?",
      contact: "Skontaktuj się z administratorem systemu.",
    },
    publicMode: "Spróbuj bez konta",
    logging: "Logowanie...",
  },

  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Wybór binarnego LLM",
    "secondary-llm-toggle-description":
      "Włącz tę opcję, aby administratorzy mogli wybierać pomiędzy dwoma modelami LLM w module tworzenia dokumentów.",
    "secondary-llm-toggle-status": "Status: ",
    "secondary-llm-user-level": "Poziom użytkownika dla drugorzędnego LLM",
    "secondary-llm-user-level-description":
      "Włącz tę opcję, aby WSZYSCY użytkownicy mogli wybierać między dwoma modelami LLM w przestrzeni roboczej tworzenia dokumentów.",
  },

  // =========================
  // NEW WORKSPACE
  // =========================
  "new-workspace": {
    title: "Nowa przestrzeń robocza",
    placeholder: "Moja przestrzeń robocza",
    "legal-areas": "Obszary prawa",
    create: {
      title: "Utwórz nową przestrzeń roboczą",
      description:
        "Po utworzeniu tej przestrzeni tylko administratorzy będą mogli ją zobaczyć. Użytkowników możesz dodać po jej utworzeniu.",
      error: "Błąd: ",
      cancel: "Anuluj",
      "create-workspace": "Utwórz przestrzeń roboczą",
    },
  },

  // =========================
  // WORKSPACE CHATS
  // =========================
  "workspace-chats": {
    welcome: "Witaj w swojej nowej przestrzeni roboczej.",
    "desc-start": "Aby rozpocząć, możesz albo",
    "desc-mid": "przesłać dokument",
    "desc-or": "lub",
    start: "Aby rozpocząć",
    "desc-end": "wysłać wiadomość.",
    "attached-file": "Załączony plik",
    "attached-files": "Załączone pliki",
    "token-count": "Liczba tokenów",
    "total-tokens": "Całkowita liczba tokenów",
    "context-window": "Dostępne okno kontekstu",
    "remaining-tokens": "Pozostało",
    "view-files": "Zobacz załączone pliki",
    prompt: {
      send: "Wyślij",
      "send-message": "Wyślij wiadomość",
      placeholder: "Zapytaj o informacje prawne",
      "change-size": "Zmień rozmiar tekstu",
      reset: "Zresetuj czat",
      clear: "Wyczyść historię czatu i rozpocznij nową rozmowę",
      command: "Polecenie",
      description: "Opis",
      save: "zapisz",
      small: "Mały",
      normal: "Normalny",
      large: "Duży",
      larger: "Większy",
      attach: "Dołącz plik do tego czatu",
      upgrade: "Ulepsz swoje zapytanie",
      upgrading: "Ulepszanie zapytania...",
      "original-prompt": "Oryginalne zapytanie:",
      "upgraded-prompt": "Ulepszone zapytanie:",
      "edit-prompt": "Możesz edytować nowe zapytanie przed wysłaniem",
      "shortcut-tip":
        "Wskazówka: Naciśnij Enter, aby zaakceptować zmiany. Użyj Shift+Enter dla nowych linii.",
      "speak-prompt": "Wypowiedz swoje zapytanie",
      "view-agents": "Pokaż wszystkich dostępnych agentów do czatowania",
      "deep-search": "Wyszukiwanie w sieci",
      "deep-search-tooltip":
        "Wyszukaj w sieci informacje, aby ulepszyć odpowiedzi",
      "ability-tag": "Umiejętność",
      "workspace-chats.prompt.view-agents": "Pokaż agentów",
      "workspace-chats.prompt.ability-tag": "Umiejętność",
      "workspace-chats.prompt.speak-prompt": "Wypowiedz swoje zapytanie",
    },
  },

  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Wyszukiwanie zaawansowane",
    description:
      "Skonfiguruj możliwości wyszukiwania w sieci dla odpowiedzi w czacie. Po włączeniu system może przeszukiwać sieć w poszukiwaniu informacji, aby ulepszyć odpowiedzi.",
    enable: "Włącz wyszukiwanie zaawansowane",
    enable_description:
      "Pozwól systemowi wyszukiwać informacje w sieci podczas odpowiadania na zapytania.",
    provider_settings: "Ustawienia dostawcy",
    provider: "Dostawca wyszukiwania",
    model: "Model",
    api_key: "Klucz API",
    api_key_placeholder: "Wprowadź swój klucz API",
    api_key_placeholder_set:
      "Klucz API jest ustawiony (wprowadź nowy klucz, aby zmienić)",
    api_key_help:
      "Twój klucz API jest przechowywany bezpiecznie i używany tylko do zapytań wyszukiwania w sieci.",
    context_percentage: "Procent kontekstu",
    context_percentage_help:
      "Procent okna kontekstu LLM przeznaczony na wyniki wyszukiwania w sieci (5-20%).",
    fetch_error: "Nie udało się pobrać ustawień wyszukiwania zaawansowanego",
    save_success: "Ustawienia wyszukiwania zaawansowanego zapisane pomyślnie",
    save_error:
      "Nie udało się zapisać ustawień wyszukiwania zaawansowanego: {{error}}",
    toast_success: "Ustawienia wyszukiwania zaawansowanego zapisane pomyślnie",
    toast_error:
      "Nie udało się zapisać ustawień wyszukiwania zaawansowanego: {{error}}",
    brave_recommended:
      "Brave Search jest obecnie zalecanym i najbardziej niezawodnym dostawcą wyszukiwania.",
  },

  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Osadzanie kontekstowe",
      hint: "Włącz osadzanie kontekstowe, aby ulepszyć proces osadzania dodatkowymi parametrami",
    },
    systemPrompt: {
      label: "Polecenie systemowe",
      placeholder: "Wprowadź wartość...",
      description:
        "Przykład: Podaj krótki, zwięzły kontekst, który umiejscowi ten fragment w całym dokumencie w celu poprawy wyszukiwania tego fragmentu. Odpowiedz tylko zwięźle, nic więcej.",
    },
    userPrompt: {
      label: "Polecenie użytkownika",
      placeholder: "Wprowadź wartość...",
      description:
        "Przykład: <document>\n{file}\n</document>\nOto fragment, który chcemy umiejscowić w całym dokumencie\n<chunk>\n{chunk}\n</chunk>",
    },
  },

  // =========================
  // HEADER
  // =========================
  header: {
    account: "Konto",
    login: "Logowanie",
    "sign-out": "Wyloguj się",
  },

  // =========================
  // WORKSPACE OVERVIEW
  // =========================
  workspace: {
    title: "Przestrzenie robocze instancji",
    description:
      "To są wszystkie przestrzenie robocze, które istnieją w tej instancji. Usunięcie przestrzeni spowoduje usunięcie wszystkich powiązanych czatów i ustawień.",
    "new-workspace": "Nowa przestrzeń robocza",
    name: "Nazwa",
    link: "Link",
    users: "Użytkownicy",
    type: "Typ",
    "created-on": "Utworzona",
    save: "Zapisz zmiany",
    cancel: "Anuluj",
    "sort-by-name": "Sortuj według nazwy",
    sort: "Sortuj alfabetycznie",
    unsort: "Przywróć pierwotną kolejność",
    deleted: {
      title: "Przestrzeń robocza nie znaleziona!",
      description:
        "Wygląda na to, że przestrzeń robocza o tej nazwie nie jest dostępna.",
      homepage: "Powrót do strony głównej",
    },
    "no-workspace": {
      title: "Brak dostępnej przestrzeni roboczej",
      description: "Nie masz jeszcze dostępu do żadnych przestrzeni roboczych.",
      "contact-admin":
        "Skontaktuj się z administratorem, aby poprosić o dostęp.",
      "learn-more": "Dowiedz się więcej o przestrzeniach roboczych",
    },
    "no-workspaces":
      "Nie masz jeszcze żadnych przestrzeni roboczych. Wybierz obszar prawny po lewej stronie, aby rozpocząć.",
    "my-workspaces": "Moje przestrzenie robocze",
    "show-my": "Pokaż moje przestrzenie robocze",
    "show-all": "Pokaż wszystkie przestrzenie robocze",
    "creator-id": "Utworzone przez użytkownika ID: {{id}}",
    "loading-username": "Ładowanie nazwy użytkownika...",
    "cloud-ai": "AI w chmurze",
    "local-ai": "Lokalne AI",
    "welcome-mobile":
      "Naciśnij przycisk w lewym górnym rogu, aby wybrać obszar prawny",
    "today-time": "Dziś, {{time}}",
    "date-time": "{{day}} {{month}}, {{time}}",
    "ai-type": "Moduł",
    "latest-activity": "Ostatnia aktywność",
  },

  // =========================
  // WORKSPACES SETTINGS MENU
  // =========================
  "workspaces-settings": {
    general: "Ustawienia ogólne",
    chat: "Ustawienia czatu",
    vector: "Baza danych wektorowych",
    members: "Członkowie",
    agent: "Konfiguracja agenta",
    "general-settings": {
      "workspace-name": "Nazwa przestrzeni roboczej",
      "desc-name":
        "To zmieni tylko nazwę wyświetlaną Twojej przestrzeni roboczej.",
      "assistant-profile": "Zdjęcie profilowe asystenta",
      "assistant-image":
        "Dostosuj zdjęcie profilowe asystenta dla tej przestrzeni roboczej.",
      "workspace-image": "Zdjęcie przestrzeni roboczej",
      "remove-image": "Usuń zdjęcie przestrzeni roboczej",
      delete: "Usuń przestrzeń roboczą",
      deleting: "Usuwanie przestrzeni roboczej...",
      update: "Aktualizuj przestrzeń roboczą",
      updating: "Aktualizowanie przestrzeni roboczej...",
    },
    "chat-settings": {
      type: "Typ czatu",
      private: "Prywatny",
      standard: "Standardowy",
      "private-desc-start": "umożliwi ręczne przydzielanie dostępu dla",
      "private-desc-mid": "tylko",
      "private-desc-end": "określonych użytkowników.",
      "standard-desc-start": "automatycznie przyzna dostęp",
      "standard-desc-mid": "wszystkim",
      "standard-desc-end": "nowym użytkownikom.",
    },
    users: {
      manage: "Zarządzaj użytkownikami",
      "workspace-member": "Brak członków przestrzeni roboczej",
      username: "Adres e-mail",
      role: "Rola",
      date: "Data dodania",
      users: "Użytkownicy",
      search: "Szukaj użytkownika",
      "no-user": "Nie znaleziono użytkowników",
      select: "Zaznacz wszystko",
      unselect: "Odznacz",
      save: "Zapisz",
    },
    "linked-workspaces": {
      title: "Powiązane przestrzenie robocze",
      description:
        "Jeśli przestrzenie robocze są powiązane, dane prawne związane z zapytaniem będą automatycznie pobierane z każdego powiązanego obszaru prawnego. Uwaga: powiązane przestrzenie robocze zwiększą czas przetwarzania",
      "linked-workspace": "Brak powiązanych przestrzeni roboczych",
      manage: "Zarządzaj przestrzeniami roboczymi",
      name: "Nazwa",
      slug: "Slug",
      date: "Data dodania",
      workspaces: "Przestrzenie robocze",
      search: "Szukaj przestrzeni roboczej",
      "no-workspace": "Nie znaleziono przestrzeni roboczych",
      select: "Zaznacz wszystko",
      unselect: "Odznacz",
      save: "Zapisz",
    },
    "delete-workspace": "Usuń przestrzeń roboczą",
    "delete-workspace-message": "Ushaka gusiba ibiganiro?",
    "vector-database": {
      reset: {
        title: "Réinitialiser la base de données vectorielle",
        message:
          "Vous êtes sur le point de réinitialiser la base de données vectorielle de votre {{workspace}}. Cela supprimera toutes les incorporations vectorielles actuellement incorporées.\n\nLes fichiers sources d'origine resteront intacts. Cette action est irréversible.",
      },
    },
  },

  // =========================
  // GENERAL APPEARANCE & CUSTOMIZATION
  // =========================
  general: {
    vector: {
      title: "Liczba wektorów",
      description:
        "Całkowita liczba wektorów w Twojej bazie danych wektorowych.",
      vectors: "Liczba wektorów",
    },
    names: {
      description:
        "To zmieni tylko nazwę wyświetlaną Twojej przestrzeni roboczej.",
    },
    message: {
      title: "Sugerowane wiadomości czatu",
      description:
        "Dostosuj wiadomości, które będą sugerowane użytkownikom Twojej przestrzeni roboczej.",
      add: "Dodaj nową wiadomość",
      save: "Zapisz wiadomości",
      heading: "Wytłumacz mi",
      body: "korzyści platformy",
      message: "Wiadomość",
      "new-heading": "Nagłówek",
    },
    pfp: {
      title: "Zdjęcie profilowe asystenta",
      description:
        "Dostosuj zdjęcie profilowe asystenta dla tej przestrzeni roboczej.",
      image: "Zdjęcie przestrzeni roboczej",
      remove: "Usuń zdjęcie przestrzeni roboczej",
    },
    delete: {
      delete: "Usuń przestrzeń roboczą",
      deleting: "Usuwanie przestrzeni roboczej...",
      "confirm-start": "Zamierzasz usunąć całą",
      "confirm-end":
        "przestrzeń roboczą. To spowoduje usunięcie wszystkich osadzonych wektorów w Twojej bazie danych wektorowych.\n\nOryginalne pliki źródłowe pozostaną nietknięte. Ta operacja jest nieodwracalna.",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chat: {
    llm: {
      title: "Dostawca LLM dla przestrzeni roboczej",
      description:
        "Określony dostawca i model LLM, który będzie używany dla tej przestrzeni roboczej. Domyślnie używany jest systemowy dostawca i ustawienia LLM.",
      search: "Szukaj wszystkich dostawców LLM",
      "save-error": "Nie udało się zapisać ustawień {{provider}}: {{error}}",
      setup: "Konfiguracja",
      use: "Aby użyć",
      "need-setup":
        "jako LLM dla tej przestrzeni, musisz go najpierw skonfigurować.",
      cancel: "Anuluj",
      save: "Zapisz",
      settings: "ustawienia",
      "multi-model": "Ten dostawca nie obsługuje wielu modeli.",
      "workspace-use": "Ta przestrzeń robocza będzie używać",
      "model-set": "zestawu modeli dla systemu.",
      "system-default": "Domyślny systemowo",
      "system-default-desc":
        "Użyj domyślnych ustawień LLM systemu dla tej przestrzeni roboczej.",
      "no-selection": "Nie wybrano LLM",
      "select-provider": "Wybierz dostawcę LLM",
      "system-standard-name": "LLM standard",
      "system-standard-desc":
        "Użyj domyślnych ustawień LLM systemu dla tej przestrzeni roboczej.",
    },
    "speak-prompt": "Wypowiedz swój monit",
    "view-agents": "Pokaż wszystkich dostępnych agentów do czatowania",
    "ability-tag": "Umiejętność",
    "change-text-size": "Zmień rozmiar tekstu",
    "aria-text-size": "Zmień rozmiar tekstu",
    model: {
      title: "Model czatu przestrzeni roboczej",
      description:
        "Określony model czatu, który będzie używany dla tej przestrzeni roboczej. Jeśli pozostanie pusty, użyte zostaną ustawienia systemowe.",
      wait: "-- oczekiwanie na modele --",
      general: "Modele ogólne",
      custom: "Modele niestandardowe",
    },
    mode: {
      title: "Tryb czatu",
      chat: {
        title: "Czat",
        "desc-start":
          "będzie udzielał odpowiedzi korzystając z ogólnej wiedzy LLM",
        and: "oraz",
        "desc-end": "kontekst dokumentu, który zostanie znaleziony.",
      },
      query: {
        title: "Zapytanie",
        "desc-start": "będzie udzielać odpowiedzi",
        only: "tylko",
        "desc-end": "jeśli zostanie znaleziony kontekst dokumentu.",
      },
    },
    history: {
      title: "Historia czatu",
      "desc-start":
        "Liczba poprzednich czatów, które zostaną uwzględnione w krótkoterminowej pamięci odpowiedzi.",
      recommend: "Zalecane 20. ",
      "desc-end":
        "Wszystko powyżej 45 może prowadzić do ciągłych błędów czatu w zależności od rozmiaru wiadomości.",
    },
    prompt: {
      title: "Zapytanie",
      description:
        "Zapytanie, które będzie używane w tej przestrzeni roboczej. Zdefiniuj kontekst i instrukcje dla AI, aby wygenerowało odpowiedź. Powinieneś dostarczyć starannie przygotowane zapytanie, aby AI mogło wygenerować odpowiednią i dokładną odpowiedź.",
    },
    refusal: {
      title: "Odpowiedź odmowna w trybie zapytania",
      "desc-start": "Gdy w",
      query: "zapytaniu",
      "desc-end":
        "trybie możesz chcieć zwrócić niestandardową odpowiedź odmowną, gdy nie zostanie znaleziony kontekst.",
    },
    temperature: {
      title: "Temperatura LLM",
      "desc-start":
        'To ustawienie kontroluje, jak "kreatywne" będą odpowiedzi Twojego LLM.',
      "desc-end":
        "Im wyższa liczba, tym bardziej kreatywne odpowiedzi. Dla niektórych modeli może to prowadzić do niespójnych odpowiedzi, gdy wartość jest ustawiona zbyt wysoko.",
      hint: "Większość LLM ma różne akceptowalne zakresy wartości. Skonsultuj się z dostawcą LLM, aby uzyskać te informacje.",
    },
    "dynamic-pdr": {
      title: "Dynamiczny PDR dla przestrzeni roboczej",
      description:
        "Włącz lub wyłącz dynamiczny PDR dla tej przestrzeni roboczej.",
      "global-enabled":
        "Dynamiczny PDR jest włączony globalnie i nie może być wyłączony dla poszczególnych przestrzeni roboczych.",
    },
  },

  // =========================
  // VECTOR DATABASE (WORKSPACE)
  // =========================
  "vector-workspace": {
    identifier: "Identyfikator bazy danych wektorowych",
    snippets: {
      title: "Maksymalna liczba fragmentów kontekstu",
      description:
        "To ustawienie kontroluje maksymalną liczbę fragmentów kontekstu, które zostaną wysłane do LLM dla każdego czatu lub zapytania.",
      recommend:
        "Zalecana wartość to co najmniej 30. Ustawienie znacznie wyższych liczb zwiększy czas przetwarzania bez konieczności poprawy precyzji w zależności od pojemności używanego LLM.",
    },
    doc: {
      title: "Próg podobieństwa dokumentu",
      description:
        "Minimalna wartość podobieństwa wymagana, aby źródło zostało uznane za związane z czatem. Im wyższa liczba, tym bardziej źródło musi być podobne do czatu.",
      zero: "Brak ograniczeń",
      low: "Niski (podobieństwo ≥ 0,25)",
      medium: "Średni (podobieństwo ≥ 0,50)",
      high: "Wysoki (podobieństwo ≥ 0,75)",
    },
    reset: {
      reset: "Zresetuj bazę danych wektorowych",
      resetting: "Czyszczenie wektorów...",
      confirm:
        "Zamierzasz zresetować bazę danych wektorowych tej przestrzeni roboczej. Spowoduje to usunięcie wszystkich aktualnie osadzonych wektorów.\n\nOryginalne pliki źródłowe pozostaną nietknięte. Ta operacja jest nieodwracalna.",
      error:
        "Nie udało się zresetować bazy danych wektorowych przestrzeni roboczej!",
      success:
        "Baza danych wektorowych przestrzeni roboczej została zresetowana!",
    },
  },

  // =========================
  // AGENT CONFIGURATION
  // =========================
  agent: {
    "performance-warning":
      "Wydajność LLM, które nie obsługują wywoływania narzędzi, zależy w dużej mierze od możliwości i dokładności modelu. Niektóre funkcje mogą być ograniczone lub nie działać.",
    provider: {
      title: "Dostawca LLM dla agenta przestrzeni roboczej",
      description:
        "Określony dostawca i model LLM, który będzie używany dla agenta @agent w tej przestrzeni roboczej.",
      "need-setup":
        "To use {{name}} as this workspace's agent LLM you need to set it up first.",
    },
    mode: {
      chat: {
        title: "Model czatu agenta przestrzeni roboczej",
        description:
          "Określony model czatu, który będzie używany dla agenta @agent w tej przestrzeni roboczej.",
      },
      title: "Model agenta przestrzeni roboczej",
      description:
        "Określony model LLM, który będzie używany dla agenta @agent w tej przestrzeni roboczej.",
      wait: "-- oczekiwanie na modele --",
    },
    skill: {
      title: "Domyślne umiejętności agenta",
      description:
        "Ulepsz naturalne zdolności domyślnego agenta dzięki tym wstępnie zbudowanym umiejętnościom. Ta konfiguracja dotyczy wszystkich przestrzeni roboczych.",
      rag: {
        title: "RAG i pamięć długoterminowa",
        description:
          'Pozwól agentowi wykorzystać Twoje lokalne dokumenty do udzielania odpowiedzi na zapytanie lub poproś agenta o "zapamiętanie" fragmentów treści do późniejszego wykorzystania w pamięci długoterminowej.',
      },
      configure: {
        title: "Skonfiguruj umiejętności agenta",
        description:
          "Dostosuj i zwiększ zdolności domyślnego agenta, włączając lub wyłączając konkretne umiejętności. Te ustawienia zostaną zastosowane we wszystkich przestrzeniach roboczych.",
      },
      view: {
        title: "Przeglądaj i podsumowuj dokumenty",
        description:
          "Pozwól agentowi wyświetlać i podsumowywać zawartość plików przestrzeni roboczej, które są obecnie osadzone.",
      },
      scrape: {
        title: "Pobieraj zawartość stron",
        description:
          "Pozwól agentowi odwiedzać i pobierać zawartość stron internetowych.",
      },
      generate: {
        title: "Generuj wykresy",
        description:
          "Włącz domyślnego agenta, aby generował różne typy wykresów na podstawie dostarczonych danych lub danych z czatu.",
      },
      save: {
        title: "Generuj i zapisuj pliki w przeglądarce",
        description:
          "Włącz domyślnego agenta, aby generował i zapisywał pliki, które można pobrać w przeglądarce.",
      },
      web: {
        title: "Wyszukiwanie w sieci na żywo i przeglądanie stron",
        "desc-start":
          "Włącz możliwość wyszukiwania w sieci przez agenta, aby odpowiadał na Twoje pytania, łącząc się z dostawcą wyszukiwania internetowego (SERP).",
        "desc-end":
          "Wyszukiwanie w sieci podczas sesji agenta nie będzie działać, dopóki nie zostanie skonfigurowane.",
      },
    },
  },

  cdbProgress: {
    "close-msg": "Czy na pewno chcesz anulować proces?",
    general: {
      placeholderSubTask: "Przetwarzanie elementu {{index}}...",
    },
    main: {
      step1: {
        label: "Generuj listę sekcji",
        desc: "Wykorzystanie głównego dokumentu do utworzenia początkowej struktury.",
      },
      step2: {
        label: "Przetwarzanie dokumentów",
        desc: "Generowanie opisów i sprawdzanie istotności.",
      },
      step3: {
        label: "Przypisanie dokumentów do sekcji",
        desc: "Przydzielanie odpowiednich dokumentów do każdej sekcji.",
      },
      step4: {
        label: "Identyfikacja kwestii prawnych",
        desc: "Wyodrębnianie kluczowych kwestii prawnych dla każdej sekcji.",
      },
      step5: {
        label: "Generowanie notatek prawnych",
        desc: "Tworzenie notatek prawnych dla zidentyfikowanych kwestii.",
      },
      step6: {
        label: "Tworzenie sekcji",
        desc: "Komponowanie treści dla każdej pojedynczej sekcji.",
      },
      step7: {
        label: "Łączenie i finalizacja dokumentu",
        desc: "Składanie sekcji w ostateczny dokument.",
      },
    },
    noMain: {
      step1: {
        label: "Przetwarzanie dokumentów",
        desc: "Generowanie opisów dla wszystkich przesłanych plików.",
      },
      step2: {
        label: "Generowanie listy sekcji",
        desc: "Tworzenie ustrukturyzowanej listy sekcji na podstawie podsumowań dokumentów.",
      },
      step3: {
        label: "Finalizacja mapowania dokumentów",
        desc: "Potwierdzanie istotności dokumentów dla każdej planowanej sekcji.",
      },
      step4: {
        label: "Identyfikacja kwestii prawnych",
        desc: "Wyodrębnianie kluczowych kwestii prawnych dla każdej sekcji.",
      },
      step5: {
        label: "Generowanie notatek prawnych",
        desc: "Tworzenie notatek prawnych dla zidentyfikowanych kwestii.",
      },
      step6: {
        label: "Tworzenie sekcji",
        desc: "Komponowanie treści dla każdej pojedynczej sekcji.",
      },
      step7: {
        label: "Łączenie i finalizacja dokumentu",
        desc: "Składanie wszystkich sekcji w ostateczny dokument prawny.",
      },
    },
  },

  // =========================
  // RECORDED WORKSPACE CHATS
  // =========================
  recorded: {
    title: "Czaty przestrzeni roboczych",
    description:
      "To są wszystkie zapisane czaty i wiadomości wysłane przez użytkowników, uporządkowane według daty utworzenia.",
    export: "Eksportuj",
    table: {
      id: "Id",
      by: "Wysłane przez",
      workspace: "Przestrzeń robocza",
      prompt: "Zapytanie",
      response: "Odpowiedź",
      at: "Wysłane o",
      invoice: "Numer faktury",
      "completion-token": "Token ukończenia",
      "prompt-token": "Token zapytania",
    },
    "clear-chats": "Usuń wszystkie bieżące czaty",
    "confirm-clear-chats":
      "Czy na pewno chcesz wyczyścić wszystkie czaty?\n\nTa akcja jest nieodwracalna.",
    "fine-tune-modal": "Zamów model Fine-Tune",
    "confirm-delete.chat":
      "Czy na pewno chcesz usunąć ten czat?\n\nTa akcja jest nieodwracalna.",
    next: "Następna strona",
    previous: "Poprzednia strona",
    filters: {
      "by-name": "Filtruj według nazwy użytkownika",
      "by-reference": "Numer referencyjny",
    },
    bulk_delete_title: "Masowe usuwanie starych czatów",
    bulk_delete_description:
      "Usuń wszystkie logi czatów starsze niż wybrany okres czasu.",
    delete_old_chats: "Usuń stare czaty",
    total_logs: "Łączna liczba logów",
    filtered_logs: "Przefiltrowane logi",
    reset_filters: "Resetuj filtry",
    "no-chats-found": "Nie znaleziono logów czatów",
    "no-chats-description":
      "Nie znaleziono logów czatów pasujących do twoich filtrów. Spróbuj zmienić kryteria wyszukiwania lub usunąć starszy okres czasu.",
    "deleted-old-chats": "Usunięto {{count}} stary(ch) czat(ów)",
    two_days: "2 dni",
    one_week: "1 tydzień",
    two_weeks: "2 tygodnie",
    one_month: "1 miesiąc",
    two_months: "2 miesiące",
    three_months: "3 miesiące",
    total_deleted: "Łącznie usuniętych logów czatów",
  },

  // =========================
  // API KEYS
  // =========================
  api: {
    title: "Klucze API",
    description:
      "Klucze API umożliwiają posiadaczowi programowy dostęp do tej instancji oraz jej zarządzanie.",
    link: "Przeczytaj dokumentację API",
    generate: "Wygeneruj nowy klucz API",
    table: {
      key: "Klucz API",
      by: "Utworzony przez",
      created: "Utworzono",
    },
    new: {
      title: "Utwórz nowy klucz API",
      description:
        "Po utworzeniu klucz API może być używany do programowego dostępu i konfiguracji tej instancji.",
      doc: "Przeczytaj dokumentację API",
      cancel: "Anuluj",
      "create-api": "Utwórz klucz API",
    },
  },

  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "Standardowa opcja dla większości zastosowań niekomercyjnych.",
    azure: "Opcja korporacyjna OpenAI hostowana na usługach Azure.",
    anthropic: "Przyjazny asystent AI hostowany przez Anthropic.",
    gemini: "Największy i najbardziej zaawansowany model AI Google.",
    huggingface:
      "Dostęp do ponad 150 000 otwartoźródłowych LLM oraz globalnej społeczności AI.",
    ollama: "Uruchamiaj LLM lokalnie na własnym komputerze.",
    lmstudio:
      "Odkrywaj, pobieraj i uruchamiaj tysiące nowoczesnych LLM kilkoma kliknięciami.",
    localai: "Uruchamiaj LLM lokalnie na własnym komputerze.",
    togetherai: "Uruchamiaj modele open source od Together AI.",
    mistral: "Uruchamiaj modele open source od Mistral AI.",
    perplexityai:
      "Uruchamiaj potężne modele połączone z internetem, hostowane przez Perplexity AI.",
    openrouter: "Zunifikowany interfejs dla LLM.",
    groq: "Najszybsze inferencje LLM dostępne dla aplikacji AI w czasie rzeczywistym.",
    koboldcpp: "Uruchamiaj lokalne LLM przy użyciu koboldcpp.",
    oobabooga:
      "Uruchamiaj lokalne LLM przy użyciu interfejsu Text Generation Web UI Oobabooga.",
    cohere: "Uruchamiaj potężne modele osadzania od Cohere.",
    lite: "Uruchamiaj kompatybilny z OpenAI proxy LiteLLM dla różnych LLM.",
    "generic-openai":
      "Połącz się z dowolną usługą kompatybilną z OpenAI za pomocą niestandardowej konfiguracji",
    native:
      "Użyj pobranego niestandardowego modelu Llama do czatowania w tej instancji.",
    xai: "Uruchamiaj potężne LLM xAI, takie jak Grok-2 i inne.",
    "aws-bedrock":
      "Uruchamiaj potężne modele podstawowe prywatnie z AWS Bedrock.",
    deepseek: "Uruchamiaj potężne modele LLM od DeepSeek.",
    fireworksai:
      "Najszybszy i najbardziej wydajny silnik inferencji do tworzenia gotowych do produkcji, złożonych systemów AI.",
    bedrock: "Uruchamiaj potężne modele podstawowe prywatnie z AWS Bedrock.",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Preferencje mowy na tekst",
    provider: "Dostawca",
    "system-native": "System natywny",
    "desc-speech":
      "Tutaj możesz określić, jakich dostawców tekstu na mowę i mowy na tekst chcesz używać w swojej platformie. Domyślnie używamy wbudowanego wsparcia przeglądarki dla tych usług, ale możesz chcieć użyć innych.",
    "title-text": "Preferencje tekstu na mowę",
    "desc-text":
      "Tutaj możesz określić, jakich dostawców tekstu na mowę chcesz używać w swojej platformie. Domyślnie używamy wbudowanego wsparcia przeglądarki dla tych usług, ale możesz chcieć użyć innych.",
    "desc-config":
      "Nie jest wymagana żadna konfiguracja dla natywnego tekstu na mowę w przeglądarce.",
    "placeholder-stt": "Szukaj dostawców mowy na tekst",
    "placeholder-tts": "Szukaj dostawców tekstu na mowę",
    "native-stt":
      "Używa wbudowanej usługi STT przeglądarki, jeśli jest obsługiwana.",
    "native-tts":
      "Używa wbudowanej usługi TTS przeglądarki, jeśli jest obsługiwana.",
    "piper-tts":
      "Uruchamiaj modele TTS lokalnie w swojej przeglądarce prywatnie.",
    "openai-description": "Używaj głosów i technologii tekstu na mowę OpenAI.",
    openai: {
      "api-key": "Klucz API",
      "api-key-placeholder": "Klucz API OpenAI",
      "voice-model": "Model głosowy",
    },
    elevenlabs: "Użyj głosów i technologii ElevenLabs dla tekstu na mowę.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Preferencje modelu transkrypcji",
    description:
      "Są to dane uwierzytelniające i ustawienia dla preferowanego dostawcy modelu transkrypcji. Ważne, aby te klucze były aktualne i poprawne, w przeciwnym razie pliki multimedialne i audio nie zostaną przetranskrybowane.",
    provider: "Dostawca transkrypcji",
    "warn-start":
      "Użycie lokalnego modelu whisper na maszynach z ograniczoną pamięcią RAM lub CPU może zatrzymać platformę podczas przetwarzania plików multimedialnych.",
    "warn-recommend":
      "Zalecamy co najmniej 2GB pamięci RAM i przesyłanie plików <10Mb.",
    "warn-end":
      "Wbudowany model zostanie automatycznie pobrany przy pierwszym użyciu.",
    "search-audio": "Szukaj dostawców transkrypcji audio",
    "api-key": "Klucz API",
    "api-key-placeholder": "Klucz API OpenAI",
    "whisper-model": "Model Whisper",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Domyślny Wbudowany",
    "default-built-in-desc":
      "Uruchom wbudowany model whisper prywatnie na tej instancji.",
    "openai-name": "OpenAI",
    "openai-desc":
      "Wykorzystaj model OpenAI Whisper-large używając swojego klucza API.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Nowa nazwa modelu
    "model-size-turbo": "(~810mb)", // Nowy rozmiar modelu
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Preferencje osadzania",
    "desc-start":
      "Kiedy używasz LLM, który nie obsługuje natywnie silnika osadzania – możesz dodatkowo musieć podać dane uwierzytelniające do osadzania tekstu.",
    "desc-end":
      "Osadzanie to proces przekształcania tekstu w wektory. Te dane uwierzytelniające są wymagane, aby przekształcić Twoje pliki i zapytania w format, który platforma może przetwarzać.",
    provider: {
      title: "Dostawca osadzania",
      description:
        "Nie jest wymagana żadna konfiguracja podczas korzystania z natywnego silnika osadzania platformy.",
      "search-embed": "Szukaj wszystkich dostawców osadzania",
      select: "Wybierz dostawcę osadzania",
      search: "Szukaj wszystkich dostawców osadzania",
    },
    workspace: {
      title: "Preferencje osadzania dla przestrzeni roboczej",
      description:
        "Określony dostawca i model osadzania, który będzie używany dla tej przestrzeni roboczej. Domyślnie używane są ustawienia i dostawca systemowy.",
      "multi-model":
        "Wsparcie dla wielu modeli nie jest jeszcze obsługiwane dla tego dostawcy.",
      "workspace-use": "Ta przestrzeń robocza będzie używać",
      "model-set": "zestawu modeli dla systemu.",
      embedding: "Model osadzania dla przestrzeni roboczej",
      model:
        "Określony model osadzania, który będzie używany dla tej przestrzeni roboczej. Jeśli pozostanie pusty, użyte zostaną systemowe preferencje osadzania.",
      wait: "-- oczekiwanie na modele --",
      setup: "Konfiguracja",
      use: "Aby użyć",
      "need-setup":
        "jako osadzacz dla tej przestrzeni, musisz go najpierw skonfigurować.",
      cancel: "Anuluj",
      save: "zapisz",
      settings: "ustawienia",
      search: "Szukaj wszystkich dostawców osadzania",
      "need-llm":
        "jako LLM dla tej przestrzeni, musisz go najpierw skonfigurować.",
      "save-error": "Nie udało się zapisać ustawień {{provider}}: {{error}}",
      "system-default": "Ustawienia systemowe",
      "system-default-desc":
        "Użyj systemowych preferencji osadzania dla tej przestrzeni roboczej.",
    },
    warning: {
      "switch-model":
        "Zmiana modelu osadzania spowoduje, że poprzednio osadzone dokumenty przestaną działać podczas czatu. Musisz je usunąć z każdej przestrzeni roboczej i całkowicie usunąć i ponownie przesłać, aby mogły być osadzone przy użyciu nowego modelu osadzania.",
    },
  },

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Preferencje dzielenia tekstu i fragmentacji",
    "desc-start":
      "Czasami możesz chcieć zmienić domyślny sposób, w jaki nowe dokumenty są dzielone i fragmentowane przed dodaniem do bazy danych wektorowych.",
    method: {
      title: "Metoda dzielenia tekstu",
      "native-explain":
        "Użyj lokalnego rozmiaru i nakładania się fragmentów do podziału.",
      "jina-explain":
        "Deleguj fragmentację/segmentację do wbudowanej metody Jina.",
      size: {
        title: "Rozmiar fragmentu",
        description: "Maksymalna liczba tokenów na fragment.",
      },
      jina: {
        api_key: "Klucz API Jina",
        api_key_desc:
          "Wymagany do korzystania z usługi segmentacji Jina. Klucz zostanie zapisany w twoim środowisku.",
        max_tokens: "Jina: Maksymalna liczba tokenów na fragment",
        max_tokens_desc:
          "Określa maksymalną liczbę tokenów w każdym fragmencie dla segmentatora Jina (maksymalnie 2000 tokenów).",
        return_tokens: "Zwróć informacje o tokenach",
        return_tokens_desc:
          "Dołącz liczbę tokenów i informacje o tokenizatorze w odpowiedzi.",
        return_chunks: "Zwróć informacje o fragmentach",
        return_chunks_desc:
          "Dołącz szczegółowe informacje o wygenerowanych fragmentach w odpowiedzi.",
      },
      "jina-info": "Fragmentacja Jina aktywna.",
    },
    "desc-end":
      "Modyfikuj to ustawienie tylko, jeśli rozumiesz, jak działa dzielenie tekstu i jakie ma skutki uboczne.",
    "warn-start": "Zmiany tutaj będą miały zastosowanie tylko do",
    "warn-center": "nowo osadzonych dokumentów",
    "warn-end": ", a nie do już istniejących dokumentów.",
    size: {
      title: "Rozmiar fragmentu tekstu",
      description:
        "To maksymalna liczba znaków, która może być zawarta w pojedynczym wektorze.",
      recommend: "Maksymalna długość modelu osadzania wynosi",
    },
    overlap: {
      title: "Nakładanie się fragmentów tekstu",
      description:
        "To maksymalne nakładanie się znaków, które występuje podczas fragmentacji pomiędzy dwoma przylegającymi fragmentami tekstu.",
      error:
        "Nakładanie się fragmentów nie może być większe lub równe rozmiarowi fragmentu.",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Baza danych wektorowych",
    description:
      "To są poświadczenia i ustawienia określające sposób działania Twojej instancji platformy. Ważne jest, aby te klucze były aktualne i poprawne.",
    provider: {
      title: "Dostawca bazy danych wektorowych",
      description: "Dla LanceDB nie jest wymagana konfiguracja.",
      "search-db": "Przeszukaj wszystkich dostawców baz danych wektorowych",
    },
    search: {
      title: "Tryb wyszukiwania wektorowego",
      mode: {
        "globally-enabled":
          "To ustawienie jest kontrolowane globalnie w ustawieniach systemowych. Odwiedź ustawienia systemowe, aby zmienić zachowanie ponownego rankingu.",
        default: "Wyszukiwanie standardowe",
        "default-desc":
          "Standardowe wyszukiwanie podobieństwa wektorowego bez ponownego rankingu.",
        "accuracy-optimized": "Zoptymalizowane pod kątem dokładności",
        "accuracy-desc":
          "Ponownie szereguje wyniki, aby poprawić dokładność przy użyciu uwagi krzyżowej.",
      },
    },
  },

  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Osadzalne widżety czatu",
    description:
      "Osadzalne widżety czatu to interfejsy czatu dostępne publicznie, powiązane z pojedynczą przestrzenią roboczą. Pozwalają one tworzyć przestrzenie, które następnie możesz opublikować na świecie.",
    create: "Utwórz osadzenie",
    table: {
      workspace: "Przestrzeń robocza",
      chats: "Wysłane czaty",
      Active: "Aktywne domeny",
    },
  },

  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Osadzone czaty",
    export: "Eksportuj",
    description:
      "To są wszystkie zapisane czaty i wiadomości z każdego osadzenia, które opublikowałeś.",
    table: {
      embed: "Osadzenie",
      sender: "Nadawca",
      message: "Wiadomość",
      response: "Odpowiedź",
      at: "Wysłane o",
    },
    delete: {
      title: "Usuń czat",
      message: "Czy na pewno chcesz usunąć ten czat?",
    },
    config: {
      "delete-title": "Usuń osadzenie",
      "delete-message":
        "Czy na pewno chcesz usunąć to osadzenie?\n\nTa akcja jest nieodwracalna.",
      "disable-title": "Dezaktywuj osadzenie",
      "disable-message":
        "Czy na pewno chcesz dezaktywować to osadzenie?\n\nTa akcja jest nieodwracalna.",
      "enable-title": "Aktywuj osadzenie",
      "enable-message":
        "Czy na pewno chcesz aktywować to osadzenie?\n\nTa akcja jest nieodwracalna.",
    },
  },

  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Tryb wieloużytkownikowy",
    description:
      "Skonfiguruj swoją instancję, aby wspierała Twój zespół, aktywując Multi-User Mode.",
    enable: {
      "is-enable": "Tryb wieloużytkownikowy jest włączony",
      enable: "Włącz tryb wieloużytkownikowy",
      description:
        "Domyślnie będziesz jedynym administratorem. Jako administrator będziesz musiał utworzyć konta dla wszystkich nowych użytkowników lub administratorów. Nie zgub swojego hasła, ponieważ tylko administrator może resetować hasła.",
      username: "E-mail konta administratora",
      password: "Hasło konta administratora",
      "username-placeholder": "Twój e-mail administratora",
      "password-placeholder": "Twoje hasło administratora",
    },
    password: {
      title: "Ochrona hasłem",
      description:
        "Chroń swoją instancję hasłem. Jeśli je zapomnisz, nie ma możliwości odzyskania, więc upewnij się, że je zapiszesz.",
    },
    instance: {
      title: "Chroń instancję hasłem",
      description:
        "Domyślnie będziesz jedynym administratorem. Jako administrator będziesz musiał tworzyć konta dla wszystkich nowych użytkowników lub administratorów. Nie zgub swojego hasła, ponieważ tylko administrator może resetować hasła.",
      password: "Hasło instancji",
    },
  },

  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Logi zdarzeń",
    description:
      "Przeglądaj wszystkie działania i zdarzenia występujące w tej instancji w celach monitorowania.",
    clear: "Wyczyść logi zdarzeń",
    table: {
      type: "Typ zdarzenia",
      user: "Użytkownik",
      occurred: "Wystąpiło",
    },
  },

  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Prywatność i zarządzanie danymi",
    description:
      "To jest Twoja konfiguracja dotycząca sposobu, w jaki podłączone zewnętrzne usługi i nasza platforma zarządzają Twoimi danymi.",
    llm: "Wybór LLM",
    embedding: "Preferencje osadzania",
    vector: "Baza danych wektorowych",
    anonymous: "Włączona anonimowa telemetria",
    "desc-event": "Wszystkie zdarzenia nie rejestrują adresu IP i zawierają",
    "desc-id": "bez danych identyfikacyjnych",
    "desc-cont":
      "treści, ustawień, czatów lub innych informacji nieopartych na użyciu. Aby zobaczyć listę zbieranych tagów zdarzeń, możesz zajrzeć na",
    "desc-git": "Github tutaj",
    "desc-end":
      "Jako projekt open-source szanujemy Twoje prawo do prywatności. Jesteśmy zobowiązani do przejrzystości i kontroli w zakresie Twoich danych osobowych. Jeśli zdecydujesz się wyłączyć telemetrię, prosimy jedynie o przesłanie nam opinii i sugestii, abyśmy mogli dalej ulepszać platformę.",
  },

  // =========================
  // DEFAULT CHAT
  // =========================
  "default-chat": {
    welcome: "Witamy w IST Legal.",
    "choose-legal": "Wybierz obszar prawa po lewej stronie.",
  },

  // =========================
  // INVITES
  // =========================
  invites: {
    title: "Zaproszenia",
    description:
      "Twórz linki zaproszeń dla osób w Twojej organizacji, aby mogły zaakceptować i zarejestrować się. Zaproszenia mogą być użyte tylko przez jednego użytkownika.",
    link: "Utwórz link zaproszenia",
    accept: "Zaakceptowane przez",
    usage: "Użycie",
    "created-by": "Utworzone przez",
    created: "Utworzono",
    new: {
      title: "Utwórz nowe zaproszenie",
      "desc-start":
        "Po utworzeniu będziesz mógł skopiować zaproszenie i wysłać je nowemu użytkownikowi, który będzie mógł założyć konto jako",
      "desc-mid": "domyślny",
      "desc-end":
        "rola i automatycznie zostanie dodany do wybranych przestrzeni roboczych.",
      "auto-add": "Automatycznie dodaj zaproszonego do przestrzeni roboczych",
      "desc-add":
        "Opcjonalnie możesz automatycznie przypisać użytkownika do poniższych przestrzeni roboczych, wybierając je. Domyślnie użytkownik nie będzie miał widocznych żadnych przestrzeni roboczych. Możesz przypisać przestrzenie po zaakceptowaniu zaproszenia.",
      cancel: "Anuluj",
      "create-invite": "Utwórz zaproszenie",
      error: "Błąd: ",
    },
    "link-copied": "Link zaproszenia skopiowany",
    "copy-link": "Skopiuj link zaproszenia",
    "delete-invite-title": "Dezaktywuj zaproszenie",
    "delete-invite-confirmation":
      "Czy na pewno chcesz dezaktywować to zaproszenie?\nPo tej operacji nie będzie już możliwe jego użycie.\n\nTa operacja jest nieodwracalna.",
    status: {
      label: "Status",
      pending: "Oczekujące",
      disabled: "Wyłączone",
      claimed: "Zaakceptowane",
    },
  },

  // =========================
  // USER MENU
  // =========================
  "user-menu": {
    edit: "Edytuj konto",
    profile: "Zdjęcie profilowe",
    size: "800 x 800",
    "remove-profile": "Usuń zdjęcie profilowe",
    username: "Adres e-mail",
    "username-placeholder": "Wprowadź adres e-mail",
    "new-password": "Nowe hasło",
    "new-password-placeholder": "Nowe hasło",
    cancel: "Anuluj",
    update: "Aktualizuj konto",
    language: "Preferowany język",
    email: "Adres e-mail",
    "email-placeholder": "Wprowadź adres e-mail",
  },

  // =========================
  // SIDEBAR (THREADS)
  // =========================
  sidebar: {
    thread: {
      "load-thread": "Ładowanie wątków...",
      "starting-thread": "Rozpoczynanie wątku...",
      delete: "Usuń wybrane wątki",
      deleted: "usunięty",
      "empty-thread": "Nowy wątek",
      default: "Standardowy",
      thread: "Nowy wątek",
      "rename-message": "Wpisz nową nazwę wątku:",
      "delete-message":
        "Czy na pewno chcesz usunąć ten wątek? Tej akcji nie można cofnąć.",
      rename: "Zmień nazwę",
      "delete-thread": "Usuń wątek",
      "rename-thread-title": "Zmień nazwę wątku",
      "new-name-placeholder": "Wpisz nową nazwę wątku",
      "delete-thread-title": "Usunąć wątek?",
      "delete-confirmation-message":
        'Czy na pewno chcesz usunąć wątek "{{name}}"? Tej akcji nie można cofnąć.',
    },
  },

  // =========================
  // THREAD NAME ERROR
  // =========================
  thread_name_error:
    "Nazwa wątku musi mieć od 3 do 255 znaków i może zawierać tylko litery, cyfry, spacje lub myślniki.",

  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Użyj wbudowanego dostawcy osadzania. Bez konfiguracji!",
    openai: "Standardowa opcja dla większości zastosowań niekomercyjnych.",
    azure: "Opcja korporacyjna OpenAI hostowana na usługach Azure.",
    localai: "Uruchamiaj modele osadzania lokalnie na swoim komputerze.",
    ollama: "Uruchamiaj modele osadzania lokalnie na swoim komputerze.",
    lmstudio:
      "Odkrywaj, pobieraj i uruchamiaj tysiące nowoczesnych LLM kilkoma kliknięciami.",
    cohere: "Uruchamiaj potężne modele osadzania od Cohere.",
    voyageai: "Uruchamiaj potężne modele osadzania od Voyage AI.",
    "generic-openai": "Użyj ogólnego modelu osadzania OpenAI.",
    "default.embedder": "Domyślny dostawca osadzania",
    jina: "Uruchamiaj potężne modele osadzania od Jina.",
    litellm: "Uruchamiaj potężne modele osadzania od LiteLLM.",
  },

  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb:
      "W 100% lokalna baza danych wektorowych, działająca na tej samej instancji co platforma.",
    chroma:
      "Otwarta baza danych wektorowych, którą możesz hostować samodzielnie lub w chmurze.",
    pinecone:
      "W pełni oparta na chmurze baza danych wektorowych dla zastosowań korporacyjnych.",
    zilliz:
      "Baza danych wektorowych hostowana w chmurze, stworzona dla przedsiębiorstw z zgodnością SOC 2.",
    qdrant: "Otwarta, lokalna i rozproszona chmurowa baza danych wektorowych.",
    weaviate:
      "Otwarta, lokalna i chmurowa baza danych wektorowych multimodalnych.",
    milvus: "Otwarty kod, wysoce skalowalny i niesamowicie szybki.",
    astra: "Wyszukiwanie wektorowe dla rzeczywistego GenAI.",
  },

  // =========================
  // SYSTEM PREFERENCES
  // =========================
  system: {
    title: "Preferencje systemowe",
    "desc-start": "To są ogólne ustawienia i konfiguracje Twojej instancji.",
    context_window: {
      title: "Dynamiczne okno kontekstu",
      desc: "Kontroluj, ile okna kontekstu LLM jest używane dla dodatkowych źródeł.",
      label: "Procent okna kontekstu",
      help: "Procent okna kontekstu, który może być użyty do wzbogacenia (10-100%).",
      "toast-success":
        "Procent okna kontekstu został pomyślnie zaktualizowany.",
      "toast-error": "Nie udało się zaktualizować procentu okna kontekstu.",
    },
    "change-login-ui": {
      title: "Wybierz domyślny interfejs logowania",
      status: "Bieżące",
      subtitle:
        "Interfejs zostanie zastosowany jako domyślny interfejs logowania w aplikacji",
    },
    attachment_context: {
      title: "Okno Kontekstu Załączników",
      desc: "Kontroluj, ile okna kontekstu LLM może być użyte do załączników.",
      label: "Procent Kontekstu Załączników",
      help: "Procent okna kontekstu, który może być użyty do załączników (10-80%).",
      "toast-success": "Procent kontekstu załączników został zaktualizowany.",
      "toast-error":
        "Nie udało się zaktualizować procentu kontekstu załączników.",
      "validation-error":
        "Procent kontekstu załączników musi być między 10 a 80.",
    },
    user: "Użytkownicy mogą usuwać przestrzenie robocze",
    "desc-delete":
      "Pozwól użytkownikom nie będącym administratorami usuwać przestrzeni, do których są przypisani. Spowoduje to usunięcie przestrzeni dla wszystkich.",
    limit: {
      title: "Limit wiadomości",
      "desc-limit":
        "Ogranicz liczbę wiadomości, które użytkownik może wysłać dziennie.",
      "per-day": "Wiadomości na dzień",
      label: "Limit wiadomości wynosi obecnie ",
    },
    max_tokens: {
      title: "Maksymalna liczba tokenów logowania na użytkownika",
      desc: "Ustaw maksymalną liczbę aktywnych tokenów uwierzytelniających, które każdy użytkownik może mieć jednocześnie. Po przekroczeniu starsze tokeny zostaną automatycznie usunięte.",
      label: "Maksymalna liczba tokenów",
      help: "Wartość musi być większa niż 0",
    },
    state: {
      enabled: "włączony",
      disabled: "wyłączony",
    },
    "source-highlighting": {
      title: "Włącz / Wyłącz podświetlanie źródeł",
      description: "Ukryj lub wyświetl podświetlanie źródeł dla użytkowników.",
      label: "Cytat: ",
      "toast-success": "Ustawienie podświetlania źródeł zostało zaktualizowane",
      "toast-error":
        "Nie udało się zaktualizować ustawienia podświetlania źródeł",
    },
    "usage-registration": {
      title: "Rejestracja użycia do fakturowania",
      description:
        "Włącz lub wyłącz rejestrowanie faktur dla monitorowania systemu.",
      label: "Logowanie faktur jest ",
    },
    "forced-invoice-logging": {
      title: "Wymuszone logowanie faktur",
      description:
        "Włącz, aby wymusić podanie odniesienia do faktury przed użyciem platformy.",
      label: "Wymuszone logowanie faktur jest ",
    },
    "rexor-linkage": {
      title: "Powiązanie z Rexorem",
      description:
        "Włącz powiązanie z Rexorem, aby uzyskać referencje aktywnych spraw z usługi Rexor.",
      label: "Powiązanie z Rexorem wynosi ",
      "activity-id": "ID aktywności",
      "activity-id-description": "Wprowadź ID aktywności dla integracji Rexor",
    },
    rerank: {
      title: "Ustawienia ponownego rankingu",
      description:
        "Skonfiguruj ustawienia ponownego rankingu, aby poprawić trafność wyników wyszukiwania z LanceDB.",
      "enable-title": "Włącz ponowny ranking",
      "enable-description":
        "Włącz ponowny ranking, aby poprawić trafność wyników wyszukiwania poprzez uwzględnienie większego kontekstu.",
      status: "Status ponownego rankingu",
      "vector-count-title": "Dodatkowe wektory do ponownego rankingu",
      "vector-count-description":
        "Liczba dodatkowych wektorów do pobrania ponad ustawienie liczby wektorów przestrzeni roboczej. Na przykład, jeśli przestrzeń robocza jest ustawiona na pobieranie 30 wektorów, a to jest ustawione na 50, łącznie 80 wektory zostaną uwzględnione do ponownego rankingu. Wyższa liczba może poprawić dokładność, ale wydłuży czas przetwarzania.",
      "lancedb-only": "Tylko LanceDB",
      "lancedb-notice":
        "Ta funkcja jest dostępna tylko przy użyciu LanceDB jako bazy danych wektorowych.",
    },
    save: "Zapisz zmiany",
  },

  feedback: {
    thankYou: "Dziękujemy! Twoja opinia została pomyślnie przesłana.",
    emailSendError: "Nie udało się wysłać e-maila: ",
    submitFeedbackError: "Nie udało się przesłać opinii: ",
    attachFile: "Dołącz plik",
    improvePlatform: "Pomóż nam ulepszyć platformę!",
    suggestionOrQuestion: "Jakieś sugestie? Lub pytania?",
    clickToWrite: "Proszę kliknąć, aby do nas napisać",
    noFeedback: "Nie znaleziono opinii",
    previewImage: "Podgląd obrazu",
    filePreview: "Podgląd pliku",
    noFile: "Brak załączonego pliku",
    fullName: "Pełne imię i nazwisko",
    fullNamePlaceholder: "Wprowadź swoje pełne imię i nazwisko",
    message: "Wiadomość",
    messagePlaceholder: "Wprowadź swoją opinię",
    attachment: "Załącznik",
    submit: "Wyślij opinię",
    submitting: "Wysyłanie...",
    submitSuccess: "Opinia została wysłana pomyślnie",
    submitError: "Nie udało się wysłać opinii",
    imageLoadError: "Nie udało się załadować obrazu",
    unsupportedFile: "Nieobsługiwany typ pliku",
    validation: {
      fullNameMinLength: "Pełne imię i nazwisko musi mieć co najmniej 2 znaki",
      fullNameMaxLength:
        "Pełne imię i nazwisko nie może mieć więcej niż 100 znaków",
      fullNameFormat:
        "Pełne imię i nazwisko może zawierać tylko litery, cyfry, spacje lub myślniki",
      messageMinLength: "Wiadomość musi mieć co najmniej 12 znaków",
      messageMaxLength: "Wiadomość nie może mieć więcej niż 1000 znaków",
      messageMinWords: "Wiadomość musi zawierać co najmniej 4 słowa",
      fileType: "Plik musi być obrazem JPEG, PNG lub PDF",
      fileSize: "Plik musi mieć mniej niż 5MB",
    },
  },

  "feedback-settings": {
    "delete-feedback": "Opinia została pomyślnie usunięta!",
    "delete-error": "Opinia nie została usunięta",
    "header-title": "Lista opinii",
    "header-description":
      "To pełna lista opinii dla tej instancji. Proszę pamiętać, że usunięcie opinii jest trwałe i nie można go cofnąć.",
    title: "Przycisk opinii użytkowników",
    description:
      "Włącz lub wyłącz przycisk przesyłania opinii dla użytkowników.",
    successMessage: "Przycisk opinii użytkowników został zaktualizowany",
    failureUpdateMessage:
      "Nie udało się zaktualizować statusu przycisku opinii użytkowników.",
    errorSubmitting: "Błąd podczas wysyłania ustawień opinii.",
    errorFetching: "Błąd podczas pobierania ustawień opinii.",
  },

  // =========================
  // USER SETTINGS (INSTANCE USERS)
  // =========================
  "user-setting": {
    description:
      "To są wszystkie konta, które posiadają konto w tej instancji. Usunięcie konta natychmiast pozbawi dostępu do tej instancji.",
    "add-user": "Dodaj użytkownika",
    username: "Adres e-mail",
    role: "Rola",
    "economy-id": "ID Ekonomiczne",
    "economy-id-ph": "Wprowadź identyfikator systemu ekonomicznego",
    "economy-id-hint":
      "ID używane do integracji z zewnętrznymi systemami ekonomicznymi (np. Rexor)",
    default: "Domyślny",
    manager: "Manager",
    admin: "Administrator",
    superuser: "Superuser",
    "date-added": "Data dodania",
    "all-domains": "Wszystkie domeny",
    "other-users": "Inni użytkownicy (bez domeny)",
    // Opcje sortowania dla listy użytkowników
    "sort-username": "Sortuj według nazwy użytkownika",
    "sort-organization": "Sortuj według organizacji",
    edit: "Edytuj: ",
    "new-password": "Nowe hasło",
    "password-rule": "Hasło musi mieć co najmniej 8 znaków.",
    "update-user": "Aktualizuj użytkownika",
    placeholder: "Wprowadź adres e-mail",
    cancel: "Anuluj",
    "remove-user": "Usuń użytkownika",
    "remove-user-title": "Usuń użytkownika",
    "remove-user-confirmation": "Czy na pewno chcesz usunąć tego użytkownika?",
    error: "Błąd: ",
  },

  "login-ui": {
    "show-toast": {
      "update-failed": "Nie udało się zaktualizować interfejsu logowania",
      "updated-login-ui": "Interfejs logowania został zaktualizowany",
    },
    "visit-website": "Odwiedź stronę",
    loading: "Ładowanie ...",
    "rw-login-description":
      "Zmaksymalizuj produktywność prawną dzięki naszej platformie opartej na sztucznej inteligencji!",
  },

  // =========================
  // SUPPORT EMAIL
  // =========================
  support: {
    title: "Adres e-mail wsparcia",
    description:
      "Ustaw adres e-mail wsparcia, który będzie wyświetlany w menu użytkownika podczas logowania do tej instancji.",
    clear: "Wyczyść",
    save: "Zapisz",
  },

  // =========================
  // PUBLIC MODE
  // =========================
  "public-mode": {
    enable: "Włącz tryb użytkownika publicznego",
    enabled: "Tryb użytkownika publicznego jest włączony",
  },

  // =========================
  // BUTTON LABELS
  // =========================
  button: {
    delete: "Usuń",
    edit: "Edytuj",
    suspend: "Zawiesić",
    unsuspend: "Wznowić",
    save: "Zapisz",
    accept: "Zaakceptuj",
    decline: "Odrzuć",
    ok: "OK",
    "flush-vector-caches": "Wyczyść pamięć podręczną wektorów",
    cancel: "Anuluj",
    saving: "Zapisywanie",
    save_llm: "Zapisz wybór LLM",
    save_template: "Zapisz szablon",
    "reset-to-default": "Przywróć domyślne",
    create: "Utwórz",
    enable: "Włącz",
    disable: "Wyłącz",
    reset: "Przywróć",
    revoke: "Odwołaj",
  },

  // =========================
  // NEW USER (INSTANCE)
  // =========================
  "new-user": {
    title: "Dodaj użytkownika do instancji",
    username: "Adres e-mail",
    "username-ph": "Wprowadź adres e-mail",
    password: "Hasło",
    "password-ph": "Początkowe hasło użytkownika",
    role: "Rola",
    default: "Domyślny",
    manager: "Manager",
    admin: "Administrator",
    superuser: "Superuser",
    description:
      "Po utworzeniu użytkownika będzie musiał się zalogować za pomocą początkowego hasła, aby uzyskać dostęp.",
    cancel: "Anuluj",
    "add-User": "Dodaj użytkownika",
    error: "Błąd: ",
    "invalid-email": "Wprowadź poprawny adres e-mail.",
    permissions: {
      title: "Uprawnienia",
      default: [
        "Może wysyłać czaty tylko w przestrzeniach, do których został dodany przez administratora lub managerów.",
        "Nie może modyfikować żadnych ustawień.",
      ],
      manager: [
        "Może przeglądać, tworzyć i usuwać dowolne przestrzenie robocze oraz modyfikować ustawienia specyficzne dla przestrzeni.",
        "Może tworzyć, aktualizować i zapraszać nowych użytkowników do instancji.",
        "Nie może modyfikować LLM, vectorDB, osadzania ani innych połączeń.",
      ],
      admin: [
        "Najwyższy poziom uprawnień.",
        "Może wszystko widzieć i robić w systemie.",
      ],
      superuser: [
        "Może uzyskać dostęp do określonych stron ustawień, takich jak Konstruktor dokumentów i Ulepszanie promptów.",
        "Nie może modyfikować ustawień systemowych, takich jak konfiguracje LLM, vectorDB.",
        "Może wysyłać czaty w przestrzeniach, do których został dodany przez administratora lub managerów.",
      ],
    },
  },

  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Utwórz nowe osadzenie dla przestrzeni roboczej",
    error: "Błąd: ",
    "desc-start":
      "Po utworzeniu osadzenia otrzymasz link, który możesz opublikować na swojej stronie internetowej za pomocą prostego",
    script: "skryptu",
    tag: "tagu.",
    cancel: "Anuluj",
    "create-embed": "Utwórz osadzenie",
    workspace: "Przestrzeń robocza",
    "desc-workspace":
      "To jest przestrzeń robocza, na której opiera się Twoje okno czatu. Wszystkie ustawienia domyślne zostaną odziedziczone z przestrzeni, chyba że zostaną zmienione w tej konfiguracji.",
    "allowed-chat": "Dozwolona metoda czatu",
    "desc-query":
      "Ustaw, jak powinien działać Twój chatbot. Zapytanie oznacza, że odpowie tylko, jeśli dokument pomoże odpowiedzieć na zapytanie.",
    "desc-chat":
      "Czat otwiera czat nawet dla ogólnych pytań i może odpowiadać na zupełnie niezwiązane zapytania dotyczące Twojej przestrzeni roboczej.",
    "desc-response":
      "Czat: Odpowiadaj na wszystkie pytania niezależnie od kontekstu",
    "query-response":
      "Zapytanie: Odpowiadaj tylko na czaty związane z dokumentami w przestrzeni roboczej",
    restrict: "Ogranicz żądania z domen",
    filter:
      "Ten filtr zablokuje wszystkie żądania pochodzące z domen innych niż te wymienione poniżej.",
    "use-embed":
      "Pozostawienie tego pustego oznacza, że każdy może użyć Twojego osadzenia na dowolnej stronie.",
    "max-chats": "Maksymalna liczba czatów na dzień",
    "limit-chats":
      "Ogranicz liczbę czatów, które to osadzone czat może obsłużyć w ciągu 24 godzin. Zero oznacza brak ograniczeń.",
    "chats-session": "Maksymalna liczba czatów na sesję",
    "limit-chats-session":
      "Ogranicz liczbę czatów, które użytkownik sesji może wysłać przy użyciu tego osadzenia w ciągu 24 godzin. Zero oznacza brak ograniczeń.",
    "enable-dynamic": "Włącz dynamiczne użycie modelu",
    "llm-override":
      "Pozwól na ustawienie preferowanego modelu LLM, aby zastąpić domyślny model przestrzeni roboczej.",
    "llm-temp": "Włącz dynamiczną temperaturę LLM",
    "desc-temp":
      "Pozwól na ustawienie temperatury LLM, aby zastąpić domyślną wartość przestrzeni roboczej.",
    "prompt-override": "Włącz nadpisanie zapytania",
    "desc-override":
      "Pozwól na ustawienie zapytania systemowego, aby zastąpić domyślne ustawienia przestrzeni roboczej.",
  },

  // =========================
  // SHOW TOAST MESSAGES
  // =========================

  // Moved to showToast.js

  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    openai: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla OpenAI",
      ],
    },
    azure: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twój tekst i tekst osadzania nie są widoczne dla OpenAI ani Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla Anthropic",
      ],
    },
    gemini: {
      description: [
        "Twoje czaty są anonimizowane i wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla Google",
      ],
    },
    lmstudio: {
      description: [
        "Twój model i czaty są dostępne tylko na serwerze, na którym działa LMStudio",
      ],
    },
    localai: {
      description: [
        "Twój model i czaty są dostępne tylko na serwerze, na którym działa LocalAI",
      ],
    },
    ollama: {
      description: [
        "Twój model i czaty są dostępne tylko na maszynie, na której działają modele Ollama",
      ],
    },
    native: {
      description: ["Twój model i czaty są dostępne tylko w tej instancji"],
    },
    togetherai: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla TogetherAI",
      ],
    },
    mistral: {
      description: [
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla Mistral",
      ],
    },
    huggingface: {
      description: [
        "Twoje zapytania i tekst dokumentów używane przy odpowiedziach są wysyłane do zarządzanego punktu końcowego HuggingFace",
      ],
    },
    perplexity: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla OpenRouter",
      ],
    },
    groq: {
      description: [
        "Twoje czaty nie będą wykorzystywane do treningu",
        "Twoje zapytania i tekst dokumentów używane przy tworzeniu odpowiedzi są widoczne dla Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Twój model i czaty są dostępne tylko na serwerze, na którym działa KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Twój model i czaty są dostępne tylko na serwerze, na którym działa interfejs Text Generation Web UI Oobabooga",
      ],
    },
    "generic-openai": {
      description: [
        "Dane są udostępniane zgodnie z warunkami usługi obowiązującymi u Twojego dostawcy ogólnego punktu końcowego.",
      ],
    },
    cohere: {
      description: [
        "Dane są udostępniane zgodnie z warunkami usługi cohere.com oraz lokalnymi przepisami o ochronie prywatności.",
      ],
    },
    litellm: {
      description: [
        "Twój model i czaty są dostępne tylko na serwerze, na którym działa LiteLLM",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane na Twojej instancji Chroma",
        "Dostęp do Twojej instancji jest zarządzany przez Ciebie",
      ],
    },
    pinecone: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane na serwerach Pinecone",
        "Dostęp do Twoich danych jest zarządzany przez Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane na Twojej instancji Qdrant (w chmurze lub samodzielnie hostowanej)",
      ],
    },
    weaviate: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane na Twojej instancji Weaviate (w chmurze lub samodzielnie hostowanej)",
      ],
    },
    milvus: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane na Twojej instancji Milvus (w chmurze lub samodzielnie hostowanej)",
      ],
    },
    zilliz: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane w Twoim klastrze chmurowym Zilliz.",
      ],
    },
    astra: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane w Twojej bazie danych AstraDB w chmurze.",
      ],
    },
    lancedb: {
      description: [
        "Twoje wektory i tekst dokumentów są przechowywane prywatnie w tej instancji platformy",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: [
        "Tekst Twoich dokumentów jest osadzany prywatnie w tej instancji platformy",
      ],
    },
    openai: {
      description: [
        "Tekst Twoich dokumentów jest wysyłany do serwerów OpenAI",
        "Twoje dokumenty nie są używane do treningu",
      ],
    },
    azure: {
      description: [
        "Tekst Twoich dokumentów jest wysyłany do Twojej usługi Microsoft Azure",
        "Twoje dokumenty nie są używane do treningu",
      ],
    },
    localai: {
      description: [
        "Tekst Twoich dokumentów jest osadzany prywatnie na serwerze działającym LocalAI",
      ],
    },
    ollama: {
      description: [
        "Tekst Twoich dokumentów jest osadzany prywatnie na serwerze działającym Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Tekst Twoich dokumentów jest osadzany prywatnie na serwerze działającym LMStudio",
      ],
    },
    cohere: {
      description: [
        "Dane są udostępniane zgodnie z warunkami usługi cohere.com oraz lokalnymi przepisami o ochronie prywatności.",
      ],
    },
    voyageai: {
      description: [
        "Dane wysłane do serwerów Voyage AI są udostępniane zgodnie z warunkami usługi voyageai.com.",
      ],
    },
  },

  // =========================
  // PROMPT VALIDATION
  // =========================
  "prompt-validate": {
    edit: "Edytuj",
    response: "Odpowiedź",
    prompt: "Zapytanie",
    regenerate: "Generuj ponownie odpowiedź",
    good: "Dobra odpowiedź",
    bad: "Zła odpowiedź",
    copy: "Kopiuj",
    more: "Więcej akcji",
    fork: "Rozgałęź",
    delete: "Usuń",
    cancel: "Anuluj",
    save: "Zapisz i wyślij",
    "export-word": "Eksportuj do Worda",
    exporting: "Eksportowanie...",
  },

  // =========================
  // CITATIONS
  // =========================
  citations: {
    show: "Pokaż cytowania",
    hide: "Ukryj cytowania",
    chunk: "Fragmenty cytowań",
    pdr: "Dokument źródłowy",
    "pdr-h": "Podświetlenie dokumentu",
    referenced: "Cytowane",
    times: "razy.",
    citation: "Cytat",
    match: "dopasowanie",
    download:
      "Ta przeglądarka nie obsługuje plików PDF. Proszę pobierz plik PDF, aby go wyświetlić:",
    "download-btn": "Pobierz PDF",
    view: "Zobacz cytowania",
    sources: "Źródła cytowań",
    "pdf-collapse-tip":
      "Wskazówka: Możesz zwinąć tę kartę PDF za pomocą przycisku w lewym górnym rogu",
    "open-in-browser": "Otwórz w przeglądarce",
    "loading-pdf": "-- ładowanie PDF --",
    "error-loading": "Błąd podczas ładowania PDF",
    "no-valid-path": "Nie znaleziono prawidłowej ścieżki PDF",
    "web-search": "Wyszukiwanie w sieci",
    "web-search-summary": "Podsumowanie wyszukiwania w sieci",
    "web-search-results": "Wyniki wyszukiwania w sieci",
    "no-web-search-results": "Nie znaleziono wyników wyszukiwania w sieci",
    "previous-highlight": "Poprzednie podświetlenie",
    "next-highlight": "Następne podświetlenie",
    "try-alternative-view": "Wypróbuj alternatywny widok",
  },

  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Tworzenie dokumentów",
    description: "Kontroluj swoje ustawienia tworzenia dokumentów.",
    configuration: "Konfiguracja",
    "drafting-model": "LLM do tworzenia dokumentów",
    enabled: "Tworzenie dokumentów jest włączone",
    disabled: "Tworzenie dokumentów jest wyłączone",
    "enabled-toast": "Tworzenie dokumentów włączone",
    "disabled-toast": "Tworzenie dokumentów wyłączone",
    "desc-settings":
      "Administrator może zmienić ustawienia tworzenia dokumentów dla wszystkich użytkowników.",
    "drafting-llm": "Preferencje LLM do tworzenia dokumentów",
    saving: "Zapisywanie...",
    save: "Zapisz zmiany",
    "chat-settings": "Ustawienia czatu",
    "drafting-chat-settings": "Ustawienia czatu dla tworzenia dokumentów",
    "chat-settings-desc":
      "Kontroluj zachowanie funkcji czatu dla tworzenia dokumentów.",
    "drafting-prompt": "Systemowe zapytanie do tworzenia dokumentów",
    "drafting-prompt-desc":
      "Systemowe zapytanie używane w tworzeniu dokumentów różni się od systemowego zapytania do prawnego Q&A. Definiuje ono kontekst i instrukcje dla AI, aby wygenerowała odpowiedź. Powinieneś dostarczyć starannie przygotowane zapytanie, aby AI mogło wygenerować odpowiednią i dokładną odpowiedź",
    linking: "Łączenie dokumentów",
    "legal-issues-prompt": "Zapytanie o kwestie prawne",
    "legal-issues-prompt-desc":
      "Wprowadź zapytanie dotyczące kwestii prawnych.",
    "memo-prompt": "Zapytanie o notatkę",
    "memo-prompt-desc": "Wprowadź zapytanie dotyczące notatki.",
    "desc-linkage":
      "Włącz dodawanie dalszego kontekstu prawnego poprzez wyszukiwania wektorowe/PDR w oparciu o pobieranie notatek",
    message: {
      title: "Sugerowane wiadomości przy tworzeniu dokumentów",
      description:
        "Dodaj sugerowane wiadomości, które użytkownicy mogą szybko wybrać przy tworzeniu dokumentów.",
      heading: "Domyślny nagłówek wiadomości",
      body: "Domyślna treść wiadomości",
      "new-heading": "Nagłówek wiadomości",
      message: "Wiadomość",
      add: "Dodaj wiadomość",
      save: "Zapisz wiadomości",
    },
    "combine-prompt": "Polecenie Łączenia",
    "combine-prompt-desc":
      "Podaj polecenie systemowe do łączenia wielu odpowiedzi w jedną odpowiedź. To polecenie jest używane zarówno do łączenia odpowiedzi i notatek DD Linkage, jak i do łączenia różnych odpowiedzi z przetwarzania Infinity Context.",
    "page-description":
      "Ta strona służy do dostosowywania różnych zapytań używanych w różnych funkcjach modułu tworzenia dokumentów. W każdym polu wprowadzania pokazane jest domyślne zapytanie, które będzie używane, chyba że na tej stronie zostanie zastosowane niestandardowe zapytanie.",
    "dd-linkage-steps": "Zapytania stosowane w krokach łączenia dokumentów",
    "general-combination-prompt": "Ogólne polecenie łączenia",
    "import-memo": {
      title: "Importuj z Legal QA",
      "button-text": "Importuj notatkę",
      "search-placeholder": "Szukaj wątków...",
      import: "Importuj",
      importing: "Importowanie...",
      "no-threads": "Nie znaleziono wątków Legal QA",
      "no-matching-threads": "Żadne wątki nie pasują do twojego wyszukiwania",
      "thread-not-found": "Nie znaleziono wybranego wątku",
      "empty-thread": "Wybrany wątek nie ma żadnej treści do zaimportowania",
      "import-success": "Treść wątku została pomyślnie zaimportowana",
      "import-error": "Nie udało się zaimportować treści wątku",
      "import-error-details": "Błąd podczas importowania: {{details}}",
      "fetch-error": "Nie udało się pobrać wątków. Spróbuj ponownie później.",
      "imported-from": "Zaimportowano z wątku Legal QA",
      "unnamed-thread": "Nienazwany wątek",
      "unknown-workspace": "Nieznana przestrzeń robocza",
      "no-threads-available": "Brak wątków do zaimportowania",
      "create-conversations-first":
        "Najpierw utwórz konwersacje w przestrzeni roboczej Legal QA, a następnie będziesz mógł je tutaj zaimportować.",
      "no-legal-qa-workspaces":
        "Nie znaleziono przestrzeni roboczych Legal QA z aktywnymi wątkami. Utwórz najpierw konwersacje w przestrzeni roboczej Legal QA, aby je zaimportować.",
      "empty-workspaces-with-names":
        "Znaleziono przestrzenie robocze Legal QA ({{workspaceNames}}), ale nie zawierają one jeszcze aktywnych wątków. Utwórz najpierw konwersacje w tych przestrzeniach roboczych, aby je zaimportować.",
      "import-success-with-name":
        "Pomyślnie zaimportowano wątek: {{threadName}}",
    },
    "create-task": {
      title: "Utwórz zadanie prawne",
      category: {
        name: "Nazwa kategorii",
        desc: "Podaj nazwę głównej kategorii.",
        placeholder: "Wprowadź nazwę kategorii",
        type: "Typ kategorii",
        new: "Utwórz nową kategorię",
        existing: "Użyj istniejącej kategorii",
        select: "Wybierz kategorię",
        "select-placeholder": "Wybierz istniejącą kategorię",
      },
      subcategory: {
        name: "Nazwa podkategorii",
        desc: "Podaj nazwę podkategorii.",
        placeholder: "Wprowadź nazwę podkategorii",
      },
      description: {
        name: "Opis",
        desc: "Podaj opis kategorii i podkategorii.",
        placeholder: "Wprowadź krótki opis",
      },
      prompt: {
        name: "Prompt zadania prawnego",
        desc: "Ten prompt kieruje LLM w tworzeniu konkretnego planu działania na podstawie wybranego zadania.",
        placeholder: "Wprowadź nazwę promptu prawnego",
      },
      submitting: "Wysyłanie...",
      submit: "Wyślij",
      validation: {
        "category-required": "Nazwa kategorii jest wymagana.",
        "subcategory-required": "Nazwa podkategorii jest wymagana.",
        "description-required": "Opis jest wymagany.",
        "prompt-required": "Nazwa promptu prawnego jest wymagana.",
      },
    },
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Generator promptu użytkownika dla zadań prawnych",
    description:
      "Automatyczna sugestia niestandardowego promptu dla zadania prawnego",
    "task-description": "Opis zadania prawnego",
    "task-description-placeholder":
      "Opisz zadanie prawno-administracyjne, które chcesz wykonać...",
    "suggested-prompt": "Sugerowany prompt użytkownika",
    "specific-instructions": "Specjalne instrukcje lub wiedza",
    "specific-instructions-description":
      "Uwzględnij wszystkie specjalne instrukcje lub wiedzę specyficzną dla tego zadania prawnego",
    "specific-instructions-placeholder":
      "Dodaj specjalne instrukcje, wiedzę lub doświadczenie do obsługi tego zadania prawnego...",
    "generation-prompt": "Prompt do generowania",
    "create-task":
      "Utwórz zadanie prawno-administracyjne na podstawie tej sugestii",
    generating: "Generowanie...",
    generate: "Generuj sugestię",
    "toast-success": "Sukces! Prompt został wygenerowany",
    "toast-fail": "Nie udało się wygenerować promptu",
    button: "Generuj prompt",
    success: "Prompt wygenerowany pomyślnie",
    error: "Proszę najpierw podać nazwę lub podkategorię",
    failed: "Nie udało się wygenerować promptu",
  },

  // =========================
  // DD SETTINGS (WORKSPACE LINKING SETTINGS)
  // =========================
  "dd-settings": {
    title: "Ustawienia łączenia obszarów roboczych",
    description:
      "Kontroluj limity tokenów i zachowanie powiązanych obszarów roboczych",
    "vector-search": {
      title: "Wyszukiwanie wektorowe",
      description:
        "Gdy ta funkcja jest włączona, semantyczne wyszukiwania wektorowe są wykonywane we wszystkich powiązanych obszarach roboczych w celu znalezienia odpowiednich dokumentów prawnych. System konwertuje zapytania użytkownika na osadzenia wektorowe i dopasowuje je do wektorów dokumentów w bazie danych każdego powiązanego obszaru roboczego. Ta funkcja działa jako rozwiązanie awaryjne, gdy generowanie notatek jest włączone, ale nie przynosi rezultatów. Gdy generowanie notatek jest wyłączone, wyszukiwanie wektorowe staje się podstawową metodą pobierania informacji z powiązanych obszarów roboczych. Głębokość wyszukiwania jest kontrolowana przez ustawienie limitu tokenów wektorowych.",
    },
    "memo-generation": {
      title: "Generowanie notatek",
      description:
        "Ta funkcja automatycznie generuje zwięzłe notatki prawne z dokumentów znalezionych w powiązanych obszarach roboczych. Po włączeniu system analizuje pobrane dokumenty, aby utworzyć ustrukturyzowane podsumowania kluczowych punktów prawnych, precedensów i odpowiedniego kontekstu. Te notatki służą jako podstawowa metoda włączania wiedzy z powiązanych obszarów roboczych. Jeśli generowanie notatek nie powiedzie się lub nie przyniesie rezultatów, system automatycznie przejdzie do wyszukiwania wektorowego (jeśli jest włączone), aby zapewnić, że odpowiednie informacje zostaną mimo to pobrane. Długość i poziom szczegółowości tych notatek są regulowane przez ustawienie limitu tokenów notatek.",
    },
    "linked-workspace-impact": {
      title: "Wpływ tokenów na powiązane obszary robocze",
      description:
        "Kontroluje, w jaki sposób system zarządza swoim budżetem tokenów w wielu powiązanych obszarach roboczych. Gdy ta funkcja jest włączona, system dynamicznie dostosowuje dostępne tokeny dla każdego obszaru roboczego w oparciu o całkowitą liczbę powiązanych obszarów roboczych, zapewniając sprawiedliwy podział zasobów danych. Zapobiega to dominacji jednego obszaru roboczego w oknie kontekstu, jednocześnie utrzymując kompleksowe pokrycie wszystkich odpowiednich obszarów prawnych. To ustawienie rezerwuje pojemność tokenów specjalnie dla wyników generowania notatek i/lub wyszukiwania wektorowego z każdego powiązanego obszaru roboczego, co może zmniejszyć całkowitą liczbę tokenów dostępnych dla głównego obszaru roboczego, gdy wiele obszarów roboczych jest powiązanych.",
    },
    "vector-token-limit": {
      title: "Limit tokenów wektorowych",
      description:
        "Określa maksymalną liczbę tokenów przydzielonych dla wyników wyszukiwania wektorowego z każdego powiązanego obszaru roboczego. Limit ten ma zastosowanie, gdy używane jest wyszukiwanie wektorowe, albo jako metoda podstawowa (gdy generowanie notatek jest wyłączone), albo jako rozwiązanie awaryjne (gdy generowanie notatek nie powiedzie się). Wyższe limity umożliwiają bardziej kompleksowe pobieranie dokumentów, ale zmniejszają liczbę tokenów dostępnych dla innych operacji.",
    },
    "memo-token-limit": {
      title: "Limit tokenów notatek",
      description:
        "Kontroluje maksymalną długość generowanych notatek prawnych z każdego powiązanego obszaru roboczego. Jako podstawowa metoda integracji wiedzy, notatki te podsumowują kluczowe punkty prawne z dokumentów powiązanego obszaru roboczego. Jeśli notatka przekroczy ten limit tokenów, zostanie odrzucona, a system przejdzie do wyszukiwania wektorowego (jeśli jest włączone). Wyższe limity umożliwiają bardziej szczegółową analizę prawną, ale mogą zmniejszyć liczbę powiązanych obszarów roboczych, które można włączyć.",
    },
    "toast-success": "Ustawienia zaktualizowane pomyślnie",
    "toast-fail": "Nie udało się zaktualizować ustawień",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Ustawienia łączenia obszarów roboczych",
    description:
      "Kontroluj limity tokenów i zachowanie powiązanych obszarów roboczych",
    "vector-search": {
      title: "Wyszukiwanie wektorowe",
      description:
        "Metoda awaryjnego znajdowania odpowiednich dokumentów, gdy generowanie notatek nie powiedzie się lub jest wyłączone",
    },
    "memo-generation": {
      title: "Generowanie notatek",
      description:
        "Podstawowa metoda włączania wiedzy z powiązanych obszarów roboczych",
    },
    "linked-workspace-impact": {
      title: "Wpływ tokenów na powiązane obszary robocze",
      description:
        "Rezerwuj tokeny dla każdego powiązanego obszaru roboczego proporcjonalnie do ich liczby",
    },
    "vector-token-limit": {
      title: "Limit tokenów wektorowych",
      description:
        "Maksymalna liczba tokenów na powiązaną przestrzeń roboczą dla wyszukiwania wektorowego",
    },
    "memo-token-limit": {
      title: "Limit tokenów notatki",
      description:
        "Maksymalna liczba tokenów dla generowania notatek dotyczących kwestii prawnych",
    },
    "base-token-limit": {
      title: "Limit tokenów bazowych",
      description: "Maksymalna liczba tokenów dla pobierania treści bazowych",
    },
    "toast-success": "Ustawienia zaktualizowane pomyślnie",
    "toast-fail": "Nie udało się zaktualizować ustawień",
  },

  // =========================
  // MODALE (DOCUMENT & CONNECTORS)
  // =========================
  modale: {
    document: {
      title: "Moje dokumenty",
      document: "Dokumenty",
      search: "Szukaj dokumentu",
      folder: "Nowy folder",
      name: "Nazwa",
      empty: "Brak dokumentów",
      "move-workspace": "Przenieś do przestrzeni roboczej",
      "doc-processor": "Procesor dokumentów",
      "processor-offline":
        "Procesor dokumentów jest obecnie offline. Spróbuj ponownie później.",
      "drag-drop": "Kliknij, aby przesłać lub przeciągnij i upuść",
      "supported-files": "Obsługiwane pliki: PDF",
      "submit-link": "Lub prześlij link do dokumentu",
      fetch: "Pobierz",
      fetching: "Pobieranie...",
      "file-desc":
        "Uwaga: Dokument zostanie przetworzony i dodany do twojego obszaru roboczego. Może to zająć chwilę.",
      cost: "*Jednorazowy koszt za osadzenia",
      "save-embed": "Zapisz i osadź",
      "failed-uploads": "Nieudane przesyłania",
      "loading-message": "To może zająć chwilę w przypadku dużych dokumentów",
      "uploading-file": "Przesyłanie pliku...",
      "scraping-link": "Przetwarzanie linku...",
      "moving-documents": "Przenoszenie {{count}} dokumentów. Proszę czekać.",
      "exceeds-prompt-limit":
        "Uwaga: Przesłana zawartość przekracza to, co może zmieścić się w jednym zapytaniu. System będzie przetwarzać zapytania za pomocą wielu promptów, co wydłuży czas generowania odpowiedzi, a precyzja może być obniżona.",
    },
    connectors: {
      title: "Łączniki danych",
      search: "Szukaj łączników danych",
      empty: "Nie znaleziono łączników danych.",
    },
    "justify-betweening": "Przetwarzanie...",
  },

  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "Repozytorium GitHub",
      description:
        "Zaimportuj całe publiczne lub prywatne repozytorium GitHub jednym kliknięciem.",
      url: "URL repozytorium GitHub",
      "collect-url": "URL repozytorium GitHub, które chcesz zebrać.",
      "access-token": "Token dostępu GitHub",
      optional: "opcjonalnie",
      "rate-limiting": "Token dostępu, aby zapobiec ograniczeniom szybkości.",
      "desc-picker":
        "Po zakończeniu wszystkie pliki będą dostępne do osadzania w przestrzeniach roboczych w przeglądarce dokumentów.",
      branch: "Gałąź",
      "branch-desc": "Gałąź, z której chcesz zebrać pliki.",
      "branch-loading": "-- ładowanie dostępnych gałęzi --",
      "desc-start": "Bez wypełnienia",
      "desc-token": "Tokena dostępu GitHub",
      "desc-connector": "ten łącznik danych będzie mógł zebrać tylko",
      "desc-level": "pliki z najwyższego poziomu",
      "desc-end": "repozytorium, ze względów publicznego API GitHub.",
      "personal-token":
        "Uzyskaj darmowy token dostępu osobistego za pomocą konta GitHub tutaj.",
      without: "Bez",
      "personal-token-access": "dostępu do osobistego tokena",
      "desc-api":
        ", publiczne API GitHub może ograniczyć liczbę plików, które można zebrać ze względu na ograniczenia szybkości. Możesz",
      "temp-token": "utworzyć tymczasowy token dostępu",
      "avoid-issue": "aby uniknąć tego problemu.",
      submit: "Wyślij",
      "collecting-files": "Zbieranie plików...",
    },
    "youtube-transcript": {
      name: "Transkrypcja YouTube",
      description:
        "Zaimportuj transkrypcję całego filmu z YouTube za pomocą linku.",
      url: "URL filmu z YouTube",
      "url-video": "URL filmu z YouTube, którego transkrypcję chcesz uzyskać.",
      collect: "Pobierz transkrypcję",
      collecting: "Pobieranie transkrypcji...",
      "desc-end":
        "po zakończeniu transkrypcja będzie dostępna do osadzania w przestrzeniach roboczych w przeglądarce dokumentów.",
    },
    "website-depth": {
      name: "Bulk Link Scraper",
      description:
        "Zeskanuj stronę internetową i jej podlinki do określonej głębokości.",
      url: "URL strony internetowej",
      "url-scrape": "URL strony, którą chcesz zeskanować.",
      depth: "Głębokość",
      "child-links":
        "To liczba podlinków, które robot powinien śledzić od URL źródłowego.",
      "max-links": "Maksymalna liczba linków",
      "links-scrape": "Maksymalna liczba linków do zeskanowania.",
      scraping: "Skanowanie strony...",
      submit: "Wyślij",
      "desc-scrap":
        "Po zakończeniu wszystkie zeskanowane strony będą dostępne do osadzania w przestrzeniach roboczych w przeglądarce dokumentów.",
    },
    confluence: {
      name: "Confluence",
      description: "Zaimportuj całą stronę Confluence jednym kliknięciem.",
      url: "URL strony Confluence",
      "url-page": "URL strony w przestrzeni Confluence.",
      username: "Nazwa użytkownika Confluence",
      "own-username": "Twoja nazwa użytkownika Confluence.",
      token: "Token dostępu Confluence",
      "desc-start":
        "Musisz podać token dostępu do uwierzytelnienia. Możesz wygenerować token dostępu",
      here: "tutaj",
      access: "Token dostępu do uwierzytelnienia.",
      collecting: "Zbieranie stron...",
      submit: "Wyślij",
      "desc-end":
        "Po zakończeniu wszystkie strony będą dostępne do osadzania w przestrzeniach roboczych.",
    },
  },

  // =========================
  // MODULE DEFINITIONS
  // =========================
  module: {
    "legal-qa": "Prawne Q&A",
    "document-drafting": "Tworzenie dokumentów",
    "active-case": "Aktywna sprawa",
  },

  // =========================
  // FINE-TUNE NOTIFICATION
  // =========================
  "fine-tune": {
    title: "Masz wystarczająco danych do fine-tune!",
    link: "kliknij, aby dowiedzieć się więcej",
    dismiss: "odrzuć",
  },

  // =========================
  // MOBILE DISCLAIMER
  // =========================
  mobile: {
    disclaimer:
      "UWAGA: Aby uzyskać najlepsze wrażenia i pełen dostęp do wszystkich funkcji, użyj komputera do korzystania z aplikacji.",
  },
  // =========================
  // SHARE MODAL
  // =========================
  shareModal: {
    title: "Udostępnij {type}",
    titleWorkspace: "Udostępnij przestrzeń roboczą",
    titleThread: "Udostępnij wątek",
    shareWithUsers: "Udostępnij użytkownikom",
    shareWithOrg: "Udostępnij całej organizacji",
    searchUsers: "Szukaj użytkowników...",
    noUsersFound: "Nie znaleziono użytkowników",
    loadingUsers: "Ładowanie użytkowników...",
    errorLoadingUsers: "Błąd podczas ładowania użytkowników",
    errorLoadingStatus: "Błąd podczas ładowania statusu udostępniania",
    userAccessGranted: "Dostęp użytkownika przyznany pomyślnie",
    userAccessRevoked: "Dostęp użytkownika cofnięty pomyślnie",
    orgAccessGranted: "Dostęp organizacji przyznany pomyślnie",
    orgAccessRevoked: "Dostęp organizacji cofnięty pomyślnie",
    errorUpdateUser: "Błąd podczas aktualizacji dostępu użytkownika",
    errorNoOrg: "Nie można udostępnić: konto nie jest powiązane z organizacją",
    errorUpdateOrg: "Błąd podczas aktualizacji dostępu organizacji",
    close: "Zamknij",
    grantAccess: "Przyznaj dostęp",
    revokeAccess: "Cofnij dostęp",
  },

  // =========================
  // ONBOARDING
  // =========================
  onboarding: {
    welcome: "Witamy w",
    "get-started": "Rozpocznij",
    "llm-preference": {
      title: "Preferencje LLM",
      description:
        "ISTLLM może współpracować z wieloma dostawcami LLM. To będzie usługa obsługująca czat.",
      "LLM-search": "Szukaj dostawców LLM",
    },
    "user-setup": {
      title: "Konfiguracja użytkownika",
      description: "Skonfiguruj ustawienia użytkownika.",
      "sub-title": "Ile osób będzie korzystać z Twojej instancji?",
      "single-user": "Tylko ja",
      "multiple-user": "Mój zespół",
      "setup-password": "Czy chcesz ustawić hasło?",
      "password-requirment": "Hasła muszą mieć co najmniej 8 znaków.",
      "save-password":
        "Ważne, aby zapisać to hasło, ponieważ nie ma możliwości jego odzyskania.",
      "password-label": "Hasło instancji",
      username: "E-mail administratora",
      password: "Hasło administratora",
      "account-requirment":
        "E-mail musi być poprawny i może zawierać tylko małe litery, cyfry, podkreślenia i myślniki, bez spacji. Hasło musi mieć co najmniej 8 znaków.",
      "password-note":
        "Domyślnie będziesz jedynym administratorem. Po zakończeniu konfiguracji możesz tworzyć i zapraszać innych użytkowników lub administratorów. Nie zgub swojego hasła, ponieważ tylko administratorzy mogą resetować hasła.",
    },
    "data-handling": {
      title: "Zarządzanie danymi i prywatność",
      description:
        "Jesteśmy zobowiązani do przejrzystości i kontroli, jeśli chodzi o Twoje dane osobowe.",
      "llm-label": "Wybór LLM",
      "embedding-label": "Preferencje osadzania",
      "database-lablel": "Baza danych wektorowych",
      "reconfigure-option":
        "Te ustawienia można zmienić w dowolnym momencie w ustawieniach.",
    },
    survey: {
      title: "Witamy w IST Legal LLM",
      description:
        "Pomóż nam stworzyć IST Legal LLM dopasowany do Twoich potrzeb. Opcjonalne.",
      email: "Jaki jest Twój adres e-mail?",
      usage: "Do czego będziesz używać platformy?",
      work: "Do pracy",
      "personal-use": "Do użytku osobistego",
      other: "Inne",
      comment: "Masz jakieś uwagi dla zespołu?",
      optional: "(Opcjonalnie)",
      feedback: "Dziękujemy za Twoją opinię!",
    },
    button: {
      yes: "Tak",
      no: "Nie",
      "skip-survey": "Pomiń ankietę",
    },
    placeholder: {
      "admin-password": "Twoje hasło administratora",
      "admin-username": "Twój e-mail administratora",
      "email-example": "<EMAIL>",
      comment:
        "Jeśli masz pytania lub uwagi, możesz je tu wpisać, a my się z Tobą skontaktujemy. Możesz również napisać <NAME_EMAIL>",
    },
  },

  // =========================
  // DEFAULT SETTINGS FOR LEGAL Q&A
  // =========================
  "default-settings": {
    "canvas-prompt": "Canvas System Prompt",
    "canvas-prompt-desc":
      "Prompt for the canvas chat system. Used as the system prompt for canvas chat interactions.",
    "canvas-prompt-placeholder":
      "You are an assistant helping the user refine a previous AI-generated response. Use the provided sources and context from the *original* response to accurately implement the user's requested adjustments. Always respond in the same language as the user's message.",
    title: "Ustawienia domyślne dla Legal Q&A",
    "default-desc":
      "Kontroluj domyślne zachowanie przestrzeni roboczych dla Legal Q&A",
    prompt: "Prompt systemowy Legal Q&A",
    "prompt-desc":
      "Domyślny prompt, który będzie używany dla nowych przestrzeni roboczych Legal Q&A. Zdefiniuj kontekst i instrukcje dla AI do generowania odpowiedzi. Powinieneś dostarczyć starannie przygotowany prompt, aby AI mogło wygenerować odpowiednią i dokładną odpowiedź. Aby zastosować to ustawienie do wszystkich istniejących przestrzeni roboczych, zastępując ich niestandardowe ustawienia, użyj przycisku poniżej.",
    "prompt-placeholder": "Wprowadź swój prompt tutaj",
    "toast-success": "Zaktualizowano domyślny prompt systemowy",
    "toast-fail": "Nie udało się zaktualizować domyślnego promptu systemowego",
    "apply-all-confirm":
      "Czy na pewno chcesz zastosować ten prompt do wszystkich istniejących przestrzeni roboczych Legal Q&A? Tej akcji nie można cofnąć i zastąpi ona wszystkie niestandardowe ustawienia.",
    "apply-to-all":
      "Zastosuj do wszystkich istniejących przestrzeni roboczych Legal Q&A",
    applying: "Trwa stosowanie...",
    "toast-apply-success":
      "Zastosowano domyślny prompt do {{count}} przestrzeni roboczych",
    "toast-apply-fail":
      "Nie udało się zastosować domyślnego promptu do przestrzeni roboczych",
    snippets: {
      title: "Domyślna maksymalna liczba fragmentów kontekstu",
      description:
        "Liczba fragmentów odpowiednich dokumentów do uwzględnienia w kontekście dla nowych przestrzeni roboczych. Aby zastosować to ustawienie do wszystkich istniejących przestrzeni roboczych, zastępując ich niestandardowe ustawienia, użyj przycisku poniżej.",
      recommend:
        "Zalecana wartość to co najmniej 30. Ustawienie znacznie wyższych liczb zwiększy czas przetwarzania bez konieczności poprawy precyzji w zależności od pojemności używanego LLM.",
    },
    "rerank-limit": {
      title: "Maksymalny limit ponownego rankingu",
      description: "Maksymalna liczba fragmentów do ponownego rankingu",
      recommend: "Zalecana wartość to od 20 do 50",
    },
    "validation-prompt": {
      title: "Zapytanie walidacyjne",
      description:
        "To ustawienie kontroluje domyślne zapytanie walidacyjne, które zostanie wysłane do LLM w celu weryfikacji podanej odpowiedzi.",
      placeholder:
        "Proszę zweryfikuj następującą odpowiedź, sprawdzając wszystkie odniesienia prawne i cytowania pod kątem dokładności względem dostarczonego kontekstu. Wypisz wszelkie nieścisłości lub błędy.",
    },
    "apply-vector-search-to-all":
      "Zastosuj do wszystkich istniejących przestrzeni roboczych Legal Q&A",
    "apply-vector-search-all-confirm":
      "Czy na pewno chcesz zastosować to ustawienie wyszukiwania wektorowego do wszystkich istniejących przestrzeni roboczych Legal Q&A? Tej akcji nie można cofnąć.",
    "toast-vector-search-apply-success":
      "Zastosowano ustawienie wyszukiwania wektorowego do {{count}} przestrzeni roboczych",
    "toast-vector-search-apply-fail":
      "Nie udało się zastosować ustawienia wyszukiwania wektorowego do przestrzeni roboczych",
  },

  // =========================
  // CONFIRM MESSAGE
  // =========================
  "confirm-message": {
    "delete-doc-title": "Usuń pliki i foldery",
    "delete-doc":
      "Czy na pewno chcesz usunąć te pliki i foldery?\nSpowoduje to usunięcie plików z systemu oraz automatyczne usunięcie ich z dowolnych istniejących przestrzeni roboczych.\nTa operacja jest nieodwracalna.",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Wykonaj zadanie prawne",
    noTaskfund: "Brak dostępnych zadań prawnych",
    noSubtskfund: "Brak dostępnych podkategorii.",
    "duration-info":
      "Czas wykonania zadania prawnego zależy od liczby dokumentów w przestrzeni roboczej. Przy wielu dokumentach i złożonym zadaniu może to zająć bardzo dużo czasu.",
    "loading-subcategory": "Ładowanie podkategorii...",
    "action-btn": "Utwórz zadanie prawne",
    description:
      "Włącz lub wyłącz przycisk wykonywania zadania prawnego w module tworzenia dokumentów.",
    successMessage: "Wykonanie zadania prawnego zostało {{status}}",
    "select-category": "Wybierz kategorię",
    "choose-task": "Wybierz zadanie prawne do wykonania",
    subStep: "Trwające lub oczekujące podzadanie",
    errorUpdateMessage:
      "Nie udało się zaktualizować ustawień zadania prawnego.",
    errorSubmitting: "Błąd podczas przesyłania ustawień zadania prawnego.",
    "additional-instructions-label": "Dodatkowe instrukcje:",
    "custom-instructions-placeholder":
      "Wprowadź dodatkowe instrukcje dla zadania prawnego (opcjonalnie)...",
    failureUpdateMessage:
      "Nie udało się zaktualizować ustawień zadania prawnego.",
    "warning-title": "Ostrzeżenie",
    "no-files-title": "Brak dostępnych plików",
    "no-files-description":
      "W przestrzeni roboczej nie ma dostępnych plików. Wprowadź co najmniej jeden plik przed wykonaniem zadania prawnego.",
    "settings-button": "Dodaj lub edytuj dostępne zadania prawne",
    settings: "Ustawienia zadań prawnych",
  },

  // =========================
  // CANVAS CHAT
  // =========================
  canvasChat: {
    title: "Płótno",
    "input-placeholder": "Zapytaj o informacje prawne",
    chatboxinstruction: "Wydaj polecenia modyfikujące odpowiedź",
    explanation:
      "To narzędzie jest do edycji odpowiedzi przez AI w różnych sposobach. Źródła dla podstawowej odpowiedzi są stosowane, co oznacza, że możesz zadawać dodatkowe pytania dotyczące dodatkowych szczegółów, korzystając z tego samego materiału źródłowego, który został użyty do podstawowej odpowiedzi.",
    editAnswer: "Edytuj odpowiedź",
  },

  // =========================
  // STATUSES
  // =========================
  statuses: {
    enabled: "włączony",
    disabled: "wyłączony",
  },

  // =========================
  // ANSWER UPGRADE
  // =========================

  // Moved to answerUpgrade.js

  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "Ustawienia PDR",
    description:
      "Skonfiguruj ustawienia wyszukiwania dokumentów nadrzędnych (PDR) dla Twoich przestrzeni roboczych.",
    "desc-end":
      "Te ustawienia wpływają na sposób przetwarzania dokumentów PDR i ich wykorzystanie w odpowiedziach czatu.",
    "global-override": {
      title: "Globalne nadpisanie dynamicznego PDR",
      description:
        "Po włączeniu ta opcja wymusi włączenie dynamicznego PDR dla wszystkich przestrzeni roboczych, niezależnie od ich indywidualnych ustawień.",
    },
    "toast-success": "Ustawienia PDR zaktualizowane",
    "toast-fail": "Nie udało się zaktualizować ustawień PDR",
    "adjacent-vector-limit": "Limit sąsiednich wektorów",
    "adjacent-vector-limit-desc": "Limit dla sąsiednich wektorów.",
    "adjacent-vector-limit-placeholder":
      "Wprowadź limit dla sąsiednich wektorów",
    "keep-pdr-vectors": "Zachowaj wektory PDR",
    "keep-pdr-vectors-desc": "Opcja zachowania wektorów PDR.",
    "pdr-token-limit-placeholder": "Wprowadź limit tokenów PDR",
    "input-prompt-token-limit-placeholder":
      "Wprowadź limit tokenów dla promptu wejściowego",
    "response-token-limit-placeholder": "Wprowadź limit tokenów dla odpowiedzi",
  },

  // =========================
  // VALIDATE RESPONSE
  // =========================
  "validate-response": {
    title: "Wynik walidacji",
    "toast-fail": "Nie udało się zweryfikować odpowiedzi",
    validating: "Weryfikowanie odpowiedzi",
    button: "Zweryfikuj odpowiedź",
    "adjust-prefix":
      "Wprowadź wszystkie wskazane zmiany do odpowiedzi na podstawie tej opinii: ",
    "adjust-button": "Zastosuj sugerowane zmiany",
  },

  // =========================
  // WORKSPACE NAMES (LEGAL AREAS)
  // =========================
  "workspace-names": {
    "Administrative Law": "Prawo administracyjne",
    "Business Law": "Prawo gospodarcze",
    "Civil Law": "Prawo cywilne",
    "Criminal Law": "Prawo karne",
    "Diplomatic Law": "Prawo dyplomatyczne",
    "Fundamental Law": "Prawo fundamentalne",
    "Human Rights Law": "Prawo praw człowieka",
    "Judicial Laws": "Prawo sądowe",
    "Security Laws": "Prawo bezpieczeństwa",
    "Taxation Laws": "Prawo podatkowe",
  },

  // =========================
  // VALIDATE ANSWER
  // =========================
  "validate-answer": {
    setting: "Walidacyjny LLM",
    title: "Preferencje walidacyjnego LLM",
    description:
      "Są to dane uwierzytelniające i ustawienia dla preferowanego dostawcy czatu walidacyjnego LLM i osadzania. Ważne, aby te klucze były aktualne i poprawne, w przeciwnym razie system nie będzie działał poprawnie.",
    "toast-success": "Ustawienia walidacyjnego LLM zaktualizowane",
    "toast-fail": "Nie udało się zaktualizować ustawień walidacyjnego LLM",
    saving: "Zapisywanie...",
    "save-changes": "Zapisz zmiany",
  },

  // =========================
  // ACTIVE CASE
  // =========================
  "active-case": {
    title: "Aktywna sprawa",
    placeholder: "Wprowadź numer referencyjny",
    "select-reference": "Wybierz referencję",
    "warning-title": "Brak numeru referencyjnego",
    "warning-message":
      "Nie ustawiono numeru referencyjnego. Czy chcesz kontynuować bez numeru referencyjnego?",
    "reference-updated": "Referencja aktywnej sprawy zaktualizowana pomyślnie",
    "reference-cleared": "Referencja aktywnej sprawy usunięta pomyślnie",
  },

  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Tryb wieloużytkownikowy jest trwale włączony ze względów bezpieczeństwa",
    "password-validation": {
      "restricted-chars":
        "Twoje hasło zawiera niedozwolone znaki. Dozwolone symbole to _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "Po włączeniu każdy użytkownik może uzyskać dostęp do publicznych przestrzeni roboczych bez logowania.",
    },
    button: {
      saving: "Zapisywanie...",
      "save-changes": "Zapisz zmiany",
    },
  },

  // =========================
  // ERRORS
  // =========================
  errors: {
    "fetch-models": "Nie udało się pobrać niestandardowych modeli",
    "fetch-models-error": "Błąd podczas pobierania modeli",
    "upgrade-error": "Błąd podczas ulepszania",
    common: {
      error: "Błąd",
    },
    streaming: {
      failed:
        "Wystąpił błąd podczas przesyłania strumieniowego odpowiedzi, na przykład gdy silnik AI jest offline lub przeciążony.",
      code: "Kod",
      unknown: "Nieznany błąd.",
    },
    workspace: {
      "already-exists": "Obszar roboczy o tej nazwie już istnieje",
    },
    env: {
      "anthropic-key-format":
        "Klucz API Anthropic musi zaczynać się od 'sk-ant-'",
      "openai-key-format": "Klucz API OpenAI musi zaczynać się od 'sk-'",
      "jina-key-format": "Klucz API Jina musi zaczynać się od 'jina_'",
    },
    auth: {
      "invalid-credentials": "Nieprawidłowe dane logowania.",
      "account-suspended": "Konto zawieszone przez administratora.",
      "invalid-password": "Podano nieprawidłowe hasło",
    },
    "invalid-token-count": "Nieprawidłowa liczba tokenów",
  },

  // =========================
  // LOADING STATES
  // =========================
  loading: {
    models: "-- ładowanie dostępnych modeli --",
    "waiting-url": "-- oczekiwanie na URL --",
    "waiting-api-key": "-- oczekiwanie na klucz API --",
    "waiting-models": "-- oczekiwanie na modele --",
  },

  // =========================
  // CHARTS
  // =========================
  charts: {
    downloading: "Pobieranie obrazu...",
    download: "Pobierz obraz wykresu",
  },

  // =========================
  // MODALS
  // =========================
  modals: {
    warning: {
      title: "Ostrzeżenie",
      proceed: "Czy na pewno chcesz kontynuować?",
      cancel: "Anuluj",
      confirm: "Potwierdź",
      "got-it": "OK, rozumiem",
    },
  },

  // =========================
  // DOCUMENTS & PINNING
  // =========================
  documents: {
    "pin-info-button": "O przypinaniu dokumentów",
    "pin-title": "Co to jest przypinanie dokumentów?",
    "pin-desc-1":
      "Kiedy przypinasz dokument, platforma wstrzykuje całą zawartość dokumentu do okna zapytania, aby Twój LLM mógł go w pełni zrozumieć.",
    "pin-desc-2":
      "Najlepiej działa to z modelami o dużym kontekście lub z małymi plikami, które są kluczowe dla bazy wiedzy.",
    "pin-desc-3":
      "Jeśli nie uzyskujesz oczekiwanych odpowiedzi domyślnie, przypinanie jest doskonałym sposobem na uzyskanie wyższej jakości odpowiedzi jednym kliknięciem.",
    "pin-add": "Przypnij do przestrzeni roboczej",
    "pin-unpin": "Odepnij od przestrzeni roboczej",
    "watch-title": "Co oznacza obserwowanie dokumentu?",
    "watch-desc-1":
      "Kiedy obserwujesz dokument, będziemy automatycznie synchronizować jego zawartość z oryginalnego źródła w regularnych odstępach czasu. Automatycznie zaktualizuje to zawartość we wszystkich przestrzeniach roboczych, w których plik jest zarządzany.",
    "watch-desc-2":
      "Funkcja ta obsługuje obecnie treści online i nie będzie dostępna dla dokumentów przesłanych ręcznie.",
    "watch-desc-3": "Możesz zarządzać obserwowanymi dokumentami w",
    "file-manager": "Menedżerze plików",
    "admin-view": "widoku administratora",
    "pdr-add":
      "Dodano wszystkie dokumenty do wyszukiwania dokumentów nadrzędnych (PDR)",
    "pdr-remove":
      "Usunięto wszystkie dokumenty z wyszukiwania dokumentów nadrzędnych (PDR)",
    empty: "Nie znaleziono dokumentów",
    tooltip: {
      date: "Data: ",
      type: "Typ: ",
      cached: "Zbuforowany",
    },
    actions: {
      removing: "Usuwanie pliku z przestrzeni roboczej",
    },
    costs: {
      estimate: "Szacunkowy koszt: $",
      minimum: "< $0.01",
    },
    "new-folder": {
      title: "Utwórz nowy folder",
      "name-label": "Nazwa folderu",
      "name-placeholder": "Wprowadź nazwę folderu",
      create: "Utwórz folder",
    },
    error: {
      "create-folder": "Nie udało się utworzyć folderu",
    },
  },

  // =========================
  // LEGAL QUESTION
  // =========================
  "legal-question": {
    "category-one": "Kategoria pierwsza",
    "category-two": "Kategoria druga",
    "category-three": "Kategoria trzecia",
    "category-four": "Kategoria czwarta",
    "category-five": "Kategoria piąta",
    "item-one": "Element pierwszy",
    "item-two": "Element drugi",
    "item-three": "Element trzeci",
    "item-four": "Element czwarty",
    "item-five": "Element piąty",
    "item-six": "Element szósty",
    "item-seven": "Element siódmy",
    "example-title":
      "Jedz zdrowo: Przewodnik po czerpaniu przyjemności z jedzenia",
    example: {
      breakfast: {
        title: "1. Zdrowe opcje śniadaniowe",
        items: [
          "Owsianka ze świeżymi owocami i miodem",
          "Parfait z greckiego jogurtu z granolą",
          "Tost z awokado i jajkiem w koszulce",
          "Zielony koktajl z jarmużem, bananem i mlekiem migdałowym",
          "Naleśniki pełnoziarniste z syropem klonowym",
        ],
      },
      lunch: {
        title: "2. Szybkie i proste pomysły na lunch",
        items: [
          "Wrap z grillowanym kurczakiem i mieszanką sałat",
          "Sałatka z quinoa z pieczonymi warzywami",
          "Kanapka z indykiem na pełnoziarnistym chlebie",
          "Warzywne stir-fry z brązowym ryżem",
          "Zupa z sałatką na boku",
        ],
      },
      dinner: {
        title: "3. Pyszne przepisy na obiad",
        items: [
          "Pieczeń z łososia z cytryną i szparagami",
          "Spaghetti z sosem marinara i pulpecikami",
          "Curry warzywne podawane z ryżem basmati",
          "Grillowana wołowina z pieczonymi ziemniakami",
          "Nadziewane papryki z quinoa i serem",
        ],
      },
    },
  },

  // =========================
  // PRESETS
  // =========================
  presets: {
    "edit-title": "Edytuj Standardowy Prompt",
    description: "Opis promptu",
    "description-placeholder": "Tworzy podsumowanie dołączonych plików.",
    deleting: "Usuwanie...",
    "delete-preset": "Usuń Standardowy Prompt",
    cancel: "Anuluj",
    save: "Zapisz",
    "add-title": "Dodaj Standardowy Prompt",
    "command-label": "Nazwa promptu, jedno słowo",
    "command-placeholder": "Podsumowanie",
    "command-desc":
      "Nazwa jest również skrótem chatbox, zaczynającym się od /, aby użyć tego promptu bez naciśnięcia przycisków.",
    "prompt-label": "Prompt",
    "prompt-placeholder": "Tworzy podsumowanie dołączonych plików.",
    "prompt-desc":
      "Prompt, który zostanie wysłany po użyciu tego predefiniowanego zestawu.",
    "tooltip-add": "Dodaj nowy Standardowy Prompt",
    "tooltip-hover": "Zobacz swoje własne Standardowe Prompty.",
    "confirm-delete": "Potwierdź usunięcie standardowego szablonu monitu.",
  },

  // =========================
  // LEGAL CATEGORIES
  // =========================
  "legal-categories": {
    process: "Postępowanie",
    "process-stamning": "Pozew",
    "process-svaromal": "Odpowiedź procesowa",
    "process-yrkanden": "Roszczenia i prezentacja",
    avtal: "Umowy",
    "avtal-anstallning": "Umowy o pracę",
    "avtal-finansiering": "Umowy finansowania i zabezpieczenia",
    "avtal-licens": "Umowy licencyjne",
    "due-diligence": "Due diligence",
    "due-diligence-avtal": "Przegląd umów",
    "due-diligence-checklista": "Lista kontrolna due diligence",
    "due-diligence-compliance": "Kontrola zgodności",
  },

  // =========================
  // VALIDATION
  // =========================
  validation: {
    responseHeader: "Oto wygenerowana odpowiedź",
    contextHeader: "Oryginalny kontekst i źródła",
  },

  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================

  // Document Builder Page
  "document-builder": {
    title: "Ustawienia Konstruktora Dokumentów",
    description: "Kontroluj ustawienia konstruktora dokumentów.",
    "toast-success": "Ustawienia zaktualizowane pomyślnie",
    "toast-fail": "Nie udało się zaktualizować ustawień",
    save: "Zapisz",
    saving: "Zapisywanie...",

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================

    "view-categories": "Zobacz wszystkie kategorie",
    "hide-categories": "Ukryj listę",
    "add-task": "Dodaj zadanie prawne",
    loading: "Ładowanie...",
    table: {
      title: "Zadania prawne",
      name: "Nazwa",
      "sub-category": "Podkategoria",
      description: "Opis",
      prompt: "Prompt zadania prawnego",
      actions: "Akcje",
      "no-tasks": "Brak dostępnych zadań prawnych.",
      delete: "Usuń",
      edit: "Edytuj",
      "delete-confirm": "Czy na pewno chcesz usunąć tę kategorię?",
      "delete-success": "Kategoria została usunięta",
      "delete-error": "Nie można usunąć kategorii",
      "delete-title": "Usuń zadanie prawne",
    },
    "create-task": {
      title: "Utwórz zadanie prawne",
      category: {
        name: "Nazwa kategorii",
        desc: "Podaj nazwę głównej kategorii.",
        placeholder: "Wprowadź nazwę kategorii",
        type: "Typ kategorii",
        new: "Utwórz nową kategorię",
        existing: "Użyj istniejącej kategorii",
        select: "Wybierz kategorię",
        "select-placeholder": "Wybierz istniejącą kategorię",
      },
      subcategory: {
        name: "Nazwa podkategorii",
        desc: "Podaj nazwę podkategorii.",
        placeholder: "Wprowadź nazwę podkategorii",
      },
      description: {
        name: "Opis",
        desc: "Podaj opis kategorii i podkategorii.",
        placeholder: "Wprowadź krótki opis",
      },
      prompt: {
        name: "Prompt zadania prawnego",
        desc: "Wprowadź prompt, który zostanie użyty dla tego zadania prawnego. Możesz również przesłać przykładowe dokumenty, aby ulepszyć prompt.",
        placeholder:
          "Wprowadź prompt zadania prawnego lub prześlij przykładowe dokumenty, aby poprawić Twój prompt...",
      },
      submitting: "Wysyłanie...",
      submit: "Wyślij",
      validation: {
        "category-required": "Nazwa kategorii jest wymagana.",
        "subcategory-required": "Nazwa podkategorii jest wymagana.",
        "description-required": "Opis jest wymagany.",
        "prompt-required": "Nazwa promptu prawnego jest wymagana.",
      },
    },
    "edit-task": {
      title: "Edytuj zadanie prawnego",
      submitting: "Aktualizacja...",
      submit: "Aktualizuj zadanie",
      subcategory: {
        name: "Nazwa zadania prawnego",
        desc: "Wprowadź nową nazwę dla tego zadania prawnego",
        placeholder: "Wprowadź zadanie prawnego...",
      },
      description: {
        name: "Opis i instrukcje użytkownika",
        desc: "Podaj opis i instrukcje użytkownika dla tego zadania prawnego",
        placeholder: "Wprowadź opis i instrukcje użytkownika...",
      },
      prompt: {
        name: "Prompt zadania prawnego",
        desc: "Wprowadź prompt, który zostanie użyty dla tego zadania prawnego",
        placeholder: "Wprowadź prompt zadania prawnego...",
      },
      validation: {
        "subcategory-required": "Nazwa zadania prawnego jest wymagana",
        "description-required": "Opis jest wymagany",
        "prompt-required": "Prompt zadania prawnego jest wymagany",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Wymagany wybór głównego dokumentu",
      "requires-main-doc-description":
        "Jeśli zaznaczone, użytkownik musi wybrać główny dokument spośród przesłanych plików podczas wykonywania tego zadania. Jest to szczególnie zalecane w przypadku zadań prawnych związanych z odpowiedzią na pismo lub wniosek sądowy lub podobne, ponieważ strukturyzuje wynik w oparciu o dokument, na który udzielana jest odpowiedź.",
      "requires-main-doc-placeholder": "Tak lub Nie",
      "requires-main-doc-explanation-default":
        "Wybór jest konieczny, ponieważ określa sposób budowania dokumentu.",
      "requires-main-doc-explanation-yes":
        "Jeśli 'Tak', użytkownik będzie musiał wybrać główny dokument podczas rozpoczynania tego zadania prawnego. Ten dokument będzie centralnym elementem przepływu pracy zadania.",
      "requires-main-doc-explanation-no":
        "Jeśli 'Nie', zadanie prawne będzie kontynuowane bez wymagania wcześniej wybranego głównego dokumentu. Zadanie będzie bardziej dynamicznie tworzyć wynik w oparciu o wszystkie przesłane dokumenty i zadanie prawne.",
    },
    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Przejrzyj prompt generatora",
    reviewGeneratorPromptButtonTooltip:
      "Zobacz dokładny szablon promptu użyty do wygenerowania sugestii zadania prawnego. (Tylko dla administratorów)",
    reviewGeneratorPromptTitle: "Przegląd promptu generatora",
    reviewPromptLabel: "Do generowania użyto następującego promptu:",
    reviewPromptTextareaLabel: "Zawartość promptu generatora",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Prompter podsumowania dokumentu",
          description:
            "Konfiguruj prompter systemowe i użytkownika dla podsumowania dokumentu.",
        },
        document_relevance: {
          title: "Prompter istotności dokumentu",
          description:
            "Konfiguruj prompter systemowe i użytkownika dla istotności dokumentu.",
        },
        section_drafting: {
          title: "Prompter tworzenia sekcji",
          description:
            "Konfiguruj prompter systemowe i użytkownika dla tworzenia sekcji.",
        },
        section_legal_issues: {
          title: "Prompter kwestii prawnych sekcji",
          description:
            "Konfiguruj prompter systemowe i użytkownika dla kwestii prawnych sekcji.",
        },
        memo_creation: {
          title: "Prompter tworzenia notatki",
          description: "Konfiguruj prompter dla tworzenia notatki.",
        },
        section_index: {
          title: "Prompter indeksu sekcji",
          description: "Konfiguruj prompter dla indeksu sekcji.",
        },
        select_main_document: {
          title: "Prompter wyboru głównego dokumentu",
          description:
            "Konfiguruj prompter systemowe i użytkownika dla wyboru głównego dokumentu.",
        },
        section_list_from_main: {
          title: "Prompter listy sekcji z głównego dokumentu",
          description:
            "Konfiguruj prompter systemowe i użytkownika dla listy sekcji z głównego dokumentu.",
        },
        section_list_from_summaries: {
          title: "Prompter listy sekcji z podsumowań",
          description:
            "Konfiguruj prompter systemowe i użytkownika dla listy sekcji z podsumowań.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Podsumowanie dokumentu (System)",
      "document-summary-system-description":
        "Prompt systemowy instruujący AI, jak podsumować zawartość dokumentu i jego znaczenie dla zadania prawnego.",
      "document-summary-user-label": "Podsumowanie dokumentu (Użytkownik)",
      "document-summary-user-description":
        "Szablon promptu użytkownika do generowania szczegółowego podsumowania zawartości dokumentu w odniesieniu do konkretnego zadania prawnego.",

      // Document Relevance
      "document-relevance-system-label": "Istotność dokumentu (System)",
      "document-relevance-system-description":
        "Prompt systemowy do oceny, czy dokument jest istotny dla zadania prawnego, oczekujący odpowiedzi prawda/fałsz.",
      "document-relevance-user-label": "Istotność dokumentu (Użytkownik)",
      "document-relevance-user-description":
        "Szablon promptu użytkownika do sprawdzenia, czy zawartość dokumentu jest istotna dla danego zadania prawnego.",

      // Section Drafting
      "section-drafting-system-label": "Tworzenie sekcji (System)",
      "section-drafting-system-description":
        "Prompt systemowy do generowania pojedynczej sekcji dokumentu w profesjonalnym stylu prawniczym, wykorzystujący określone dokumenty i kontekst.",
      "section-drafting-user-label": "Tworzenie sekcji (Użytkownik)",
      "section-drafting-user-description":
        "Szablon promptu użytkownika do generowania konkretnej sekcji dokumentu prawnego, uwzględniający tytuł, zadanie, dokumenty źródłowe i sąsiednie sekcje.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Identyfikacja kwestii prawnych sekcji (System)",
      "section-legal-issues-system-description":
        "Prompt systemowy do identyfikacji konkretnych tematów prawnych, dla których należy pozyskać informacje faktyczne wspierające tworzenie sekcji dokumentu.",
      "section-legal-issues-user-label":
        "Identyfikacja kwestii prawnych sekcji (Użytkownik)",
      "section-legal-issues-user-description":
        "Szablon promptu użytkownika do wylistowania tematów prawnych lub punktów danych do pozyskania informacji kontekstowych istotnych dla konkretnej sekcji dokumentu i zadania prawnego.",

      // Memo Creation
      "memo-creation-template-label": "Domyślny szablon tworzenia notatki",
      "memo-creation-template-description":
        "Szablon promptu do tworzenia notatki prawnej odnoszącej się do konkretnej kwestii prawnej, uwzględniający dostarczone dokumenty i kontekst zadania.",

      // Section Index
      "section-index-system-label": "Indeks sekcji (System)",
      "section-index-system-description":
        "Prompt systemowy do generowania ustrukturyzowanego indeksu sekcji dla dokumentu prawnego.",

      // Select Main Document
      "select-main-document-system-label": "Wybór głównego dokumentu (System)",
      "select-main-document-system-description":
        "Prompt systemowy do identyfikacji najbardziej istotnego głównego dokumentu dla zadania prawnego spośród wielu podsumowań dokumentów.",
      "select-main-document-user-label":
        "Wybór głównego dokumentu (Użytkownik)",
      "select-main-document-user-description":
        "Szablon promptu użytkownika do identyfikacji głównego dokumentu dla zadania prawnego na podstawie podsumowań wielu dokumentów.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Lista sekcji z głównego dokumentu (System)",
      "section-list-from-main-system-description":
        "Prompt systemowy do tworzenia listy sekcji w strukturze JSON dla dokumentu prawnego na podstawie zawartości głównego dokumentu i zadania prawnego.",
      "section-list-from-main-user-label":
        "Lista sekcji z głównego dokumentu (Użytkownik)",
      "section-list-from-main-user-description":
        "Szablon promptu użytkownika do dostarczenia zadania prawnego i zawartości głównego dokumentu w celu wygenerowania listy sekcji.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Lista sekcji z podsumowań (System)",
      "section-list-from-summaries-system-description":
        "Prompt systemowy do tworzenia listy sekcji w strukturze JSON na podstawie podsumowań dokumentów i zadania prawnego, gdy nie istnieje główny dokument.",
      "section-list-from-summaries-user-label":
        "Lista sekcji z podsumowań (Użytkownik)",
      "section-list-from-summaries-user-description":
        "Szablon promptu użytkownika do dostarczenia zadania prawnego i podsumowań dokumentów w celu wygenerowania listy sekcji, gdy nie istnieje główny dokument.",
    },
  },

  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Zarejestruj projekt Rexor",
    "project-id": "ID projektu",
    "resource-id": "ID zasobu",
    "activity-id": "ID aktywności",
    register: "Zarejestruj projekt",
    registering: "rejestrowanie...",
    "not-active": "Ta sprawa nie jest aktywna do rejestracji",
    account: {
      title: "Zaloguj się do Rexor",
      username: "Nazwa użytkownika",
      password: "Hasło",
      "no-token": "Brak tokena otrzymanego w handleLoginSuccess",
      logout: "Wyloguj się",
      "no-user": "Proszę najpierw się zalogować",
      connected: "Połączono z Rexorem",
      "not-connected": "Nie połączono",
      "change-account": "Zmień konto",
      "session-expired": "Sesja wygasła. Proszę zaloguj się ponownie.",
    },
    "hide-article-transaction": "Ukryj formularz transakcji czasowej",
    "show-article-transaction": "Pokaż formularz transakcji czasowej",
    "article-transaction-title": "Dodaj transakcję czasową",
    "registration-date": "Data rejestracji",
    description: "Opis",
    "description-internal": "Opis wewnętrzny",
    "hours-worked": "Przepracowane godziny",
    "invoiced-hours": "Zafakturowane godziny",
    invoiceable: "Fakturowalne",
    "sending-article-transaction": "Wysyłanie transakcji czasowej...",
    "save-article-transaction": "Zapisz transakcję czasową",
    "project-not-register": "Projekt musi być najpierw zarejestrowany.",
    "article-transaction-error": "Nie udało się zapisać transakcji czasowej",
    "not-exist": "Nie można znaleźć tej sprawy",
    "invoice-text": "Liczba wyszukiwań Foynet",
  },

  // =========================
  // OPTIONS
  // =========================
  options: {
    yes: "Tak",
    no: "Nie",
  },

  // =========================
  // GENERIC PROVIDER SELECTION SETTINGS
  // =========================

  // =========================
  // DOCX EDITOR
  // =========================
  "docx-edit": {
    "edit-instructions":
      "Wprowadź instrukcje, jak chcesz edytować dokument. Bądź konkretny co do zmian, które chcesz wprowadzić.",
    "instructions-placeholder":
      "np. Popraw błędy gramatyczne, nadaj bardziej formalny ton, dodaj akapit podsumowujący...",
    "process-button": "Przetwórz dokument",
    "upload-docx": "Prześlij DOCX",
    "processing-upload": "Przetwarzanie...",
    "content-extracted": "Zawartość wyodrębniona z pliku DOCX",
    "file-type-note": "Obsługiwane są tylko pliki .docx",
    "upload-error": "Błąd podczas przesyłania pliku: ",
    "no-instructions": "Wprowadź instrukcje edycji",
    "process-error": "Błąd podczas przetwarzania dokumentu: ",
    "changes-highlighted": "Dokument z podświetlonymi zmianami",
    "download-button": "Pobierz dokument",
    "start-over-button": "Zacznij od nowa",
    "no-document": "Brak dokumentu dostępnego do pobrania",
    "download-error": "Błąd podczas pobierania dokumentu: ",
    "download-success": "Dokument pobrany pomyślnie",
    processing: "Przetwarzanie dokumentu...",
    "instructions-used": "Użyte instrukcje",
    "import-success": "Zawartość DOCX zaimportowana pomyślnie",
    "edit-success": "Zawartość DOCX zaktualizowana pomyślnie",
    "canvas-document-title": "Dokument Canvas",
    "upload-button": "Prześlij DOCX",
    "download-as-docx": "Pobierz jako DOCX",
    "output-example": "Przykład wyjścia",
    "output-example-desc":
      "Prześlij plik DOCX, aby dodać przykładową treść do swojego promptu",
    "content-examples-tag-open": "<PRZYKŁAD_TREŚCI>",
    "content-examples-tag-close": "</PRZYKŁAD_TREŚCI>",
    "content-examples-info":
      "<INFO>To jest przykład treści, która ma zostać wygenerowana, z podobnego zadania prawnego. Zauważ, że ta przykładowa treść może być znacznie krótsza lub dłuższa niż treść, która ma zostać teraz wygenerowana.</INFO>",
    "contains-example-content": "[Zawiera przykładową treść]",
  },

  // =========================
  // GENERIC MODEL SELECTION SETTINGS
  // =========================

  // =========================
  // ANTHROPIC
  // =========================

  // =========================
  // AZURE SETTINGS
  // =========================

  // =========================
  // AWS BEDROCK SETTINGS
  // =========================

  // =========================
  // COHERE
  // =========================

  // =========================
  // DEEPSEEK LLM SETTINGS
  // =========================

  // =========================
  // FIREWORKSAI LLM SELECTION
  // =========================

  // =========================
  // GEMINI LLM OPTIONS
  // =========================

  // =========================
  // GROQ LLM SELECTION
  // =========================

  // =========================
  // HUGGINGFACE LLM SETTINGS
  // =========================

  // =========================
  // KOBOLDCPP SETTINGS
  // =========================

  // =========================
  // LITELLM
  // =========================

  // =========================
  // LMSTUDIO LLM SELECTION
  // =========================

  // =========================
  // LOCALAI LLM SELECTION
  // =========================

  // =========================
  // MISTRAL LLM OPTIONS
  // =========================

  // =========================
  // NATIVE LLM SETTINGS
  // =========================

  // =========================
  // OLLAMA LLM SELECTION
  // =========================

  // =========================
  // OPENAI LLM SELECTION
  // =========================

  // =========================
  // OPENROUTER LLM SELECTION
  // =========================

  // =========================
  // PERPLEXITY LLM SELECTION
  // =========================

  // =========================
  // TEXTGENWEBUI COMPONENT
  // =========================
  textgenwebui: {
    "base-url": "Adres URL podstawowy",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "token-window": "Okno kontekstu tokenów",
    "token-window-placeholder": "Limit okna zawartości (np.: 4096)",
    "api-key": "Klucz API (opcjonalnie)",
    "api-key-placeholder": "Klucz API TextGen Web UI",
    "max-tokens": "Maksymalna liczba tokenów",
    "max-tokens-placeholder":
      "Maksymalna liczba tokenów na żądanie (np.: 1024)",
  },

  // =========================
  // TOGETHERAI LLM SELECTION
  // =========================

  // =========================
  // JINA EMBEDDING
  // =========================
  jina: {
    "api-key": "Klucz API Jina",
    "api-key-placeholder": "Wprowadź swój klucz API Jina",
    "model-preference": "Preferencje modelu",
    "api-key-format": "Klucz API Jina musi zaczynać się od 'jina_'",
  },

  // =========================
  // OLLAMA EMBEDDING
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Maksymalna długość fragmentu osadzania",
  },

  // =========================
  // VOYAGEAI EMBEDDING
  // =========================
  voyageai: {
    "api-key": "Klucz API VoyageAI",
    "api-key-placeholder": "Wprowadź swój klucz API VoyageAI",
    "model-preference": "Preferencje modelu",
  },

  // =========================
  // METRICS VISIBILITY
  // =========================
  "metrics.visibility.hover": "Metryki są widoczne.",
  "metrics.visibility.available": "Metryki są dostępne.",

  // =========================
  // PROMPT ERRORS
  // =========================
  prompt: {
    error: {
      empty: "Zapytanie nie może być puste",
      upgrade: "Błąd podczas ulepszania zapytania",
    },
    decline: "Odrzuć",
  },

  // =========================
  // AGENT MENU
  // =========================
  "agent-menu.default-agent": "Domyślny agent",
  "agent-menu.ability.rag-search": "RAG Search",
  "agent-menu.ability.web-scraping": "Pobieranie zawartości stron",
  "agent-menu.ability.web-browsing": "Przeglądanie stron",
  "agent-menu.ability.save-file-to-browser": "Zapisz plik w przeglądarce",
  "agent-menu.ability.list-documents": "Wyświetl dokumenty",
  "agent-menu.ability.summarize-document": "Podsumuj dokument",
  "agent-menu.ability.chart-generation": "Generowanie wykresów",

  // =========================
  // PIPER TTS OPTIONS
  // =========================
  piperTTS: {
    description:
      "Wszystkie modele PiperTTS będą uruchamiane lokalnie w Twojej przeglądarce. Może to być wymagające zasobów na słabszych urządzeniach.",
    "voice-model": "Wybór modelu głosu",
    "loading-models": "-- ładowanie dostępnych modeli --",
    "stored-indicator":
      'Symbol "✔" oznacza, że ten model jest już przechowywany lokalnie i nie trzeba go pobierać przed uruchomieniem.',
    "flush-cache": "Wyczyść pamięć głosów",
    "flush-success": "Wszystkie głosy wyczyszczone z pamięci przeglądarki",
    demo: {
      stop: "Zatrzymaj demo",
      loading: "Ładowanie głosu",
      play: "Odtwórz próbkę",
      text: "Cześć, witamy w IST Legal!",
    },
  },

  // =========================
  // ADMIN AGENTS
  // =========================
  agents: {
    title: "Umiejętności Agenta",
    "agent-skills": "Konfiguruj i zarządzaj możliwościami agenta",
    "custom-skills": "Niestandardowe Umiejętności",
    back: "Powrót",
    "select-skill": "Wybierz umiejętność do konfiguracji",
    "preferences-saved": "Pomyślnie zapisano preferencje agenta",
    "preferences-failed": "Nie udało się zapisać preferencji agenta",
    "skill-status": {
      on: "Włączone",
      off: "Wyłączone",
    },
  },

  // =========================
  // CHAT SETTINGS (MISC)
  // =========================
  chatSettings: {
    placeholder: {
      drafting:
        "Biorąc pod uwagę następującą rozmowę, odpowiedni kontekst oraz pytanie uzupełniające, odpowiedz na bieżące pytanie zadane przez użytkownika. Zwróć tylko swoją odpowiedź na pytanie, biorąc pod uwagę powyższe informacje i postępuj zgodnie z instrukcjami użytkownika w razie potrzeby.",
      "legal-questions":
        "Jakie pytania prawne wynikają z podanego kontekstu wraz z podpowiedzią",
      "legal-memo":
        "Przygotuj notatkę dotyczącą każdego z tych zagadnień prawnych",
    },
  },

  // =========================
  // LOGGING
  // =========================
  logging: {
    show: "pokaż",
    hide: "ukryj",
    "event-metadata": "Metadane zdarzenia",
  },

  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Pokaż kod",
    enable: "Włącz",
    disable: "Wyłącz",
    "all-domains": "wszystkie",
    "disable-confirm":
      "Czy na pewno chcesz wyłączyć to osadzenie?\nPo wyłączeniu osadzenie nie będzie już odpowiadać na żadne żądania czatu.",
    "delete-confirm":
      "Czy na pewno chcesz usunąć to osadzenie?\nPo usunięciu to osadzenie nie będzie już odpowiadać na czaty ani być aktywne.\n\nTa operacja jest nieodwracalna.",
    "disabled-toast": "Osadzenie zostało wyłączone",
    "enabled-toast": "Osadzenie jest aktywne",
  },

  badges: {
    default: {
      text: "Domyślny",
      tooltip:
        "Ta umiejętność jest włączona domyślnie i nie może zostać wyłączona.",
    },
  },

  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "{{provider}} logo",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "Model osadzania LM Studio",
      "max-chunk-length": "Maksymalna długość fragmentu",
      "max-chunk-length-help":
        "Maksymalna długość fragmentów tekstu przy osadzaniu.",
      "hide-endpoint": "Ukryj ręczne wprowadzanie punktu końcowego",
      "show-endpoint": "Pokaż ręczne wprowadzanie punktu końcowego",
      "base-url": "Podstawowy adres URL LM Studio",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Wprowadź adres URL, pod którym działa LM Studio.",
      "auto-detect": "Wykryj automatycznie",
      "loading-models": "--ładowanie dostępnych modeli--",
      "enter-url-first": "Najpierw wprowadź adres URL LM Studio",
      "model-help":
        "Wybierz model LM Studio do osadzania. Modele zostaną załadowane po wprowadzeniu poprawnego adresu URL LM Studio.",
      "loaded-models": "Twoje załadowane modele",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Model osadzania Ollama",
      "max-chunk-length": "Maksymalna długość fragmentu",
      "max-chunk-length-help":
        "Maksymalna długość fragmentów tekstu przy osadzaniu.",
      "hide-endpoint": "Ukryj ręczne wprowadzanie punktu końcowego",
      "show-endpoint": "Pokaż ręczne wprowadzanie punktu końcowego",
      "base-url": "Podstawowy adres URL Ollama",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Wprowadź adres URL, pod którym działa Ollama.",
      "auto-detect": "Wykryj automatycznie",
      "loading-models": "--ładowanie dostępnych modeli--",
      "enter-url-first": "Najpierw wprowadź adres URL Ollama",
      "model-help":
        "Wybierz model Ollama do osadzania. Modele zostaną załadowane po wprowadzeniu poprawnego adresu URL Ollama.",
      "loaded-models": "Twoje załadowane modele",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Wybór modelu osadzania",
      "max-chunk-length": "Maksymalna długość fragmentu",
      "max-chunk-length-help":
        "Maksymalna długość fragmentów tekstu przy osadzaniu.",
      "api-key": "Klucz API",
      optional: "opcjonalne",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- ładowanie dostępnych modeli --",
      "waiting-url": "-- oczekiwanie na adres URL --",
      "loaded-models": "Twoje załadowane modele",
      "model-tooltip": "Zobacz obsługiwane modele osadzania na",
      "model-tooltip-link": "Dokumentacja LiteLLM",
      "model-tooltip-more":
        "aby uzyskać więcej informacji o dostępnych modelach.",
    },
    // Cohere Embedding Options
    cohere: {
      "api-key": "Klucz API Cohere",
      "api-key-placeholder": "Wprowadź swój klucz API Cohere",
      "model-label": "Wybór modelu",
      "available-models": "Dostępne modele osadzania",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Klucz API Jina",
      "api-key-placeholder": "Wprowadź swój klucz API Jina",
      "api-key-error": "Klucz API musi zaczynać się od 'jina_'",
      "model-label": "Wybór modelu",
      "available-models": "Dostępne modele osadzania",
      "embedding-type": "Typ osadzania",
      "available-types": "Dostępne typy osadzania",
      dimensions: "Wymiary",
      "available-dimensions": "Dostępne wymiary",
      task: "Zadanie",
      "available-tasks": "Dostępne zadania",
      "late-chunking": "Późne dzielenie na fragmenty",
      "late-chunking-help":
        "Włącz późne dzielenie na fragmenty przy przetwarzaniu dokumentów",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Nazwa modelu osadzania",
      "hide-endpoint": "Ukryj zaawansowane ustawienia",
      "show-endpoint": "Pokaż zaawansowane ustawienia",
      "base-url": "Podstawowy adres URL LocalAI",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Wprowadź adres URL, pod którym działa LocalAI.",
      "auto-detect": "Wykryj automatycznie",
      "loading-models": "-- ładowanie dostępnych modeli --",
      "waiting-url": "-- oczekiwanie na adres URL --",
      "loaded-models": "Twoje załadowane modele",
    },
    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "Podstawowy adres URL",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Wprowadź bazowy adres URL dla punktu końcowego API kompatybilnego z OpenAI.",
      "model-label": "Model osadzania",
      "model-placeholder": "Wprowadź nazwę modelu (np. text-embedding-ada-002)",
      "model-help": "Określ identyfikator modelu do generowania osadzeń.",
      "api-key": "Klucz API",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Wprowadź swój klucz API do uwierzytelniania.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "Klucz API OpenAI",
      "api-key-placeholder": "Wprowadź swój klucz API OpenAI",
      "model-label": "Wybór modelu",
      "available-models": "Dostępne modele osadzania",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "Klucz API VoyageAI",
      "api-key-placeholder": "Wprowadź swój klucz API VoyageAI",
      "model-label": "Wybór modelu",
      "available-models": "Dostępne modele osadzania",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Punkt końcowy usługi Azure OpenAI",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help":
        "Wprowadź adres URL punktu końcowego usługi Azure OpenAI",
      "api-key": "Klucz API Azure OpenAI",
      "api-key-placeholder": "Wprowadź swój klucz API Azure OpenAI",
      "api-key-help":
        "Wprowadź swój klucz API Azure OpenAI do uwierzytelniania",
      "deployment-name": "Nazwa wdrożenia modelu osadzania",
      "deployment-name-placeholder":
        "Wprowadź nazwę wdrożenia modelu osadzania Azure OpenAI",
      "deployment-name-help":
        "Nazwa wdrożenia twojego modelu osadzania Azure OpenAI",
    },
    // Native Embedding Options
    native: {
      description:
        "Używanie natywnego dostawcy osadzania do przetwarzania tekstu",
    },
  },

  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "Klucze API",
    description: "Zarządzaj kluczami API do łączenia się z tą instancją.",
    "generate-key": "Wygeneruj nowy klucz API",
    "table-headers": {
      "connection-string": "Ciąg połączenia",
      "created-by": "Utworzony przez",
      "created-at": "Data utworzenia",
      actions: "Akcje",
      delete: "Usuń",
      edit: "Edytuj",
      "delete-confirm": "Czy na pewno chcesz usunąć to zadanie prawne?",
    },
    "no-keys": "Nie znaleziono kluczy API",
    modal: {
      title: "Nowy klucz API rozszerzenia przeglądarki",
      "multi-user-warning":
        "Uwaga: Jesteś w trybie wieloużytkownikowym. Ten klucz API umożliwi dostęp do wszystkich przestrzeni roboczych powiązanych z Twoim kontem. Prosimy o ostrożne udostępnianie.",
      "create-description":
        'Po kliknięciu "Utwórz klucz API", ta instancja spróbuje utworzyć nowy klucz API dla rozszerzenia przeglądarki.',
      "connection-help":
        'Jeśli widzisz "Połączono z IST Legal" w rozszerzeniu, połączenie powiodło się. Jeśli nie, skopiuj ciąg połączenia i wklej go ręcznie do rozszerzenia.',
      cancel: "Anuluj",
      "create-key": "Utwórz klucz API",
      "copy-key": "Kopiuj klucz API",
      "key-copied": "Klucz API skopiowany!",
    },
    tooltips: {
      "copy-connection": "Kopiuj ciąg połączenia",
      "auto-connect": "Automatycznie połącz z rozszerzeniem",
    },
    confirm: {
      revoke:
        "Czy na pewno chcesz odwołać ten klucz API rozszerzenia przeglądarki?\nPo tej operacji nie będzie już możliwe jego użycie.\n\nTa akcja jest nieodwracalna.",
    },
    toasts: {
      "key-revoked":
        "Klucz API rozszerzenia przeglądarki został trwale odwołany",
      "revoke-failed": "Nie udało się odwołać klucza API",
      copied: "Ciąg połączenia skopiowany do schowka",
      connecting: "Próba połączenia z rozszerzeniem przeglądarki...",
    },
    "revoke-title": "Odwołaj klucz API rozszerzenia przeglądarki",
    "revoke-message":
      "Czy na pewno chcesz odwołać ten klucz API rozszerzenia przeglądarki?\nPo tej operacji nie będzie już możliwe jego użycie.\n\nTa akcja jest nieodwracalna.",
  },

  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Funkcje Eksperymentalne",
    description: "Funkcje obecnie w fazie testów beta",
    "live-sync": {
      title: "Synchronizacja Dokumentów na Żywo",
      description:
        "Włącz automatyczną synchronizację treści ze źródeł zewnętrznych",
      "manage-title": "Monitorowane dokumenty",
      "manage-description":
        "To są wszystkie dokumenty obecnie monitorowane w Twojej instancji.",
      "document-name": "Nazwa dokumentu",
      "last-synced": "Ostatnia synchronizacja",
      "next-refresh": "Czas do następnej aktualizacji",
      "created-on": "Utworzono",
      "auto-sync": "Automatyczna Synchronizacja Treści",
      "sync-description":
        "Włącz możliwość określenia źródła treści do monitorowania.",
      "sync-workspace-note":
        "Monitorowana treść będzie automatycznie aktualizowana.",
      "sync-limitation": "Ta funkcja dotyczy tylko treści internetowych.",
      documentation: "Dokumentacja funkcji i ostrzeżenia",
      "manage-content": "Zarządzaj monitorowaną treścią",
    },
    tos: {
      title: "Warunki użytkowania",
      description: "Funkcje w fazie testów.",
      "possibilities-title": "Możliwe skutki:",
      possibilities: {
        "data-loss": "Utrata danych",
        "quality-change": "Zmiana jakości",
        "storage-increase": "Więcej pamięci",
        "resource-consumption": "Więcej zasobów",
        "cost-increase": "Wyższe koszty",
        "potential-bugs": "Możliwe błędy",
      },
      "conditions-title": "Warunki:",
      conditions: {
        "future-updates": "Może zniknąć",
        stability: "Niestabilne",
        availability: "Ograniczona dostępność",
        privacy: "Prywatność zachowana",
        changes: "Zmienne warunki",
      },
      "read-more": "Więcej",
      contact: "Kontakt",
      reject: "Odrzuć",
      accept: "OK",
    },
  },

  "legal-task-prompt":
    "Polecenie użytkownika planu działania (Na podstawie zadania prawnego)",
  "legal-task-prompt-description":
    "Polecenie użytkownika planu działania kieruje LLM w tworzeniu konkretnego planu działania na podstawie wybranego zadania prawnego.",
  "legal-task-prompt-placeholder":
    "Wprowadź polecenie użytkownika planu działania...",

  promptLogging: {
    title: "Logowanie wyjścia promptów",
    description:
      "Włącz lub wyłącz logowanie wyjść promptów do monitorowania systemu.",
    label: "Logowanie wyjścia promptów: ",
    state: {
      enabled: "Włączone",
      disabled: "Wyłączone",
    },
  },

  userAccess: {
    title: "Zezwól na dostęp użytkowników",
    description:
      "Włącz, aby zezwolić zwykłym użytkownikom na dostęp do zadań prawnych. Domyślnie dostęp mają tylko superużytkownicy, menedżerowie i administratorzy.",
    label: "Dostęp użytkowników: ",
    state: {
      enabled: "Włączony",
      disabled: "Wyłączony",
    },
  },
  "cdb-llm-preference": {
    title: "Preferencje LLM CDB",
    settings: "LLM CDB",
    description: "Konfiguruj dostawcę LLM dla CDB",
  },
  "template-llm-preference": {
    title: "Preferencje LLM szablonu",
    settings: "Szablon LLM",
    description:
      "Wybierz dostawcę LLM używanego do generowania szablonów dokumentów. Domyślnie używany jest dostawca systemowy.",
    "toast-success": "Zaktualizowano ustawienia LLM szablonu",
    "toast-fail": "Nie udało się zaktualizować ustawień LLM szablonu",
    saving: "Zapisywanie...",
    "save-changes": "Zapisz zmiany",
  },
  "custom-user-ai": {
    title: "Niestandardowe AI użytkownika",
    settings: "Niestandardowe AI użytkownika",
    description: "Skonfiguruj dostawcę niestandardowego AI",
    "custom-model-reference": "Niestandardowa nazwa i opis modelu",
    "custom-model-reference-description":
      "Dodaj niestandardową referencję dla tego modelu. Będzie ona widoczna podczas używania selektora niestandardowego silnika AI użytkownika w panelu podpowiedzi.",
    "custom-model-reference-name": "Niestandardowa nazwa modelu",
    "custom-model-reference-description-label": "Opis modelu (opcjonalnie)",
    "custom-model-reference-description-placeholder":
      "Wpisz opcjonalny opis dla tego modelu",
    "custom-model-reference-name-placeholder":
      "Wprowadź niestandardową nazwę dla tego modelu",
    "model-ref-placeholder":
      "Wprowadź niestandardową nazwę lub opis dla tej konfiguracji modelu",
    "enter-custom-model-reference":
      "Wprowadź niestandardową nazwę dla tego modelu",
    "standard-engine": "Standardowy silnik AI",
    "standard-engine-description":
      "Nasz domyślny silnik, przydatny do większości zadań",
    "dynamic-context-window-percentage":
      "Procent dynamicznego okna kontekstowego",
    "dynamic-context-window-percentage-desc":
      "Kontroluje, ile okna kontekstowego LLM może być używane dla dodatkowych źródeł (10-100%)",
    "no-alternative-title": "Nie wybrano modelu alternatywnego",
    "no-alternative-desc":
      "Gdy ta opcja jest wybrana, użytkownicy nie mają możliwości wyboru modelu alternatywnego.",
    "select-option": "Wybierz niestandardowy profil AI",
    tab: {
      "custom-1": "Niestandardowy silnik 1",
      "custom-2": "Niestandardowy silnik 2",
      "custom-3": "Niestandardowy silnik 3",
      "custom-4": "Niestandardowy silnik 4",
      "custom-5": "Niestandardowy silnik 5",
      "custom-6": "Niestandardowy silnik 6",
    },
    engine: {
      "custom-1": "Niestandardowy silnik 1",
      "custom-2": "Niestandardowy silnik 2",
      "custom-3": "Niestandardowy silnik 3",
      "custom-4": "Niestandardowy silnik 4",
      "custom-5": "Niestandardowy silnik 5",
      "custom-6": "Niestandardowy silnik 6",
      "custom-1-title": "Niestandardowy silnik 1",
      "custom-2-title": "Niestandardowy silnik 2",
      "custom-3-title": "Niestandardowy silnik 3",
      "custom-4-title": "Niestandardowy silnik 4",
      "custom-5-title": "Niestandardowy silnik 5",
      "custom-6-title": "Niestandardowy silnik 6",
      "custom-1-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 1",
      "custom-2-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 2",
      "custom-3-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 3",
      "custom-4-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 4",
      "custom-5-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 5",
      "custom-6-description":
        "Skonfiguruj ustawienia dla Niestandardowego silnika 6",
    },
    "option-number": "Opcja {{number}}",
    "llm-provider-selection": "Wybór dostawcy LLM",
    "llm-provider-selection-desc":
      "Wybierz dostawcę LLM dla tej niestandardowej konfiguracji AI",
    "custom-option": "Opcja niestandardowa",
    saving: "Zapisywanie...",
    "save-changes": "Zapisz zmiany",
    "model-ref-saved": "Pomyślnie zapisano niestandardowe ustawienia modelu",
    "model-ref-save-failed":
      "Nie udało się zapisać niestandardowych ustawień modelu: {{error}}",
    "llm-settings-save-failed": "Nie udało się zapisać ustawień LLM: {{error}}",
    "settings-fetch-failed": "Nie udało się pobrać ustawień",
    "llm-saved": "Pomyślnie zapisano ustawienia LLM",
    "select-provider-first":
      "Wybierz dostawcę LLM, aby skonfigurować ustawienia modelu. Po skonfigurowaniu ta opcja będzie dostępna jako niestandardowy silnik AI w interfejsie użytkownika.",
  },
  toast: {
    settings: {
      "welcome-messages-failed":
        "Nie udało się zapisać wiadomości powitalnych: {{error}}",
      "welcome-messages-fetch-failed":
        "Nie udało się pobrać wiadomości powitalnych",
      "welcome-messages-empty":
        "Proszę wprowadzić nagłówek lub tekst wiadomości",
      "welcome-messages-success": "Wiadomości powitalne zapisane pomyślnie",
      "prompt-examples-failed":
        "Nie udało się zapisać przykładowych pytań: {{error}}",
      "prompt-examples-success": "Przykładowe pytania zapisane",
      "prompt-examples-validation":
        "Przykład {{number}} brakuje wymaganych pól: {{fields}}",
    },
    document: {
      "move-success": "Pomyślnie przeniesiono {{count}} dokumentów",
      "pdr-failed": "Nie udało się przenieść dokumentu: {{message}}",
      "watch-failed": "Nie udało się obserwować dokumentu: {{message}}",
      "pdr-added": "Dokument dodany do Parent Document Retrieval",
      "pdr-removed": "Dokument usunięty z Parent Document Retrieval",
      "pin-success": "Dokument {{action}} workspace",
    },
    experimental: {
      "feature-enabled": "Funkcje eksperymentalne włączone",
      "feature-disabled": "Funkcje eksperymentalne wyłączone",
      "update-failed":
        "Nie udało się zaktualizować statusu funkcji eksperymentalnej",
      "features-enabled":
        "Funkcje eksperymentalne włączone. Strona zostanie przeładowana.",
      "live-sync": {
        enabled: "Synchronizacja dokumentów na żywo włączona",
        disabled: "Synchronizacja dokumentów na żywo wyłączona",
      },
    },
  },
  // =========================
  // CHAT LOGS & PREVIEW
  // =========================
  chat_logs: {
    display_description:
      "Wyświetl rejestrowanie danych surowych, otwórz i pobierz plik",
    display_prompt_output: "Wyświetl dane surowe",
    loading_prompt_output: "Ładowanie danych surowych...",
    not_available: "*** Dane surowe nie są dostępne dla tego czatu.",
    token_count: "Tokeny (we wszystkich danych surowych): {{count}}",
    token_count_detailed:
      "Tokeny do LLM: {{promptTokens}} | Tokeny w odpowiedzi LLM: {{completionTokens}} | Łączna liczba tokenów: {{totalTokens}}",
  },

  // LLM Provider specific translations
  "llm-provider.textgenwebui": "Połącz z instancją Text Generation WebUI.",
  "llm-provider.litellm": "Połącz z dowolnym LLM za pomocą LiteLLM.",
  "llm-provider.openai-generic":
    "Połącz z dowolnym punktem końcowym API OpenAI-kompatybilnym.",
  "llm-provider.system-default": "Użyj wbudowanego modelu wbudowanego.",
  // =========================
  // INVITATION STRINGS
  // =========================
  invite: {
    "accept-button": "Zaakceptuj zaproszenie",
    newUser: {
      title: "Utwórz nowe konto",
      usernameLabel: "Nazwa użytkownika",
      passwordLabel: "Hasło",
      description:
        "Po utworzeniu konta będziesz mógł się zalogować przy użyciu tych poświadczeń i rozpocząć korzystanie z obszarów roboczych.",
    },
  },

  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Okno kontekstu",
    "max-output-tokens": "Maksymalna liczba tokenów wyjściowych",
    "output-limit": "Limit wyjścia",
    tokens: "tokenów",
    "fallback-value": "Użyto wartości domyślnej",
  },

  // =========================
  // LEGAL TEMPLATES MODAL
  // =========================

  //moved to legalTemplates

  // =========================
  // CUSTOM LEGAL TEMPLATES MODAL
  // =========================

  // moved to customLegalTemplates.js

  // =========================
  // VARIOUS MODALS AND NEW FEATURES
  // =========================

  // Organization & Organizations translations for user management
  organization: {
    label: "Organizacja",
    select: "-- Wybierz organizację --",
    none: "Brak",
    "create-new": "+ Utwórz nową organizację",
    "new-name": "Nazwa nowej organizacji",
    "new-name-ph": "Wprowadź nazwę nowej organizacji",
  },
  organizations: {
    "fetch-error": "Nie udało się pobrać organizacji",
    "organization-label": "Organizacja",
    "user-label": "Użytkownik",
    "fetch-org-error": "Nie udało się pobrać organizacji",
    "fetch-user-error": "Nie udało się pobrać użytkowników",
  },

  // =========================
  // REQUEST LEGAL ASSISTANCE
  // =========================
  "request-legal-assistance": {
    title: "Prośba o pomoc prawną",
    description:
      "Skonfiguruj widoczność przycisku do proszenia o pomoc prawną.",
    enable: "Włącz prośbę o pomoc prawną",
    "law-firm-name": "Nazwa kancelarii prawnej",
    "law-firm-placeholder": "Wprowadź nazwę kancelarii prawnej",
    "law-firm-help":
      "Nazwa kancelarii prawnej, która będzie obsługiwać prośby o pomoc prawną",
    email: "E-mail do pomocy prawnej",
    "email-placeholder": "Wprowadź adres e-mail do pomocy prawnej",
    "email-help": "Adres e-mail, na który będą wysyłane prośby o pomoc prawną",
    "settings-saved": "Ustawienia pomocy prawnej zapisane pomyślnie",
    "save-error": "Nie udało się zapisać ustawień pomocy prawnej",
    status: "Przycisk pomocy prawnej: ",
    "load-error": "Nie udało się załadować ustawień pomocy prawnej",
    "save-button": "Zapisz zmiany",
    request: {
      title: "Prośba o pomoc prawną",
      description:
        "Wyślij prośbę do {{lawFirmName}} o pomoc prawną, zakończenie badań lub inne konsultacje. Zostaniesz powiadomiony e-mailem, gdy prośba zostanie przetworzona.",
      button: "Prośba o pomoc prawną",
      message: "Wiadomość",
      "message-placeholder":
        "Wprowadź wszelkie szczegółowe instrukcje lub informacje dla zespołu pomocy prawnej",
      send: "Wyślij prośbę",
      cancel: "Anuluj",
      error: "Nie udało się wysłać prośby o pomoc prawną",
      success: "Prośba o pomoc prawną wysłana pomyślnie",
      submitting: "Wysyłanie prośby...",
      submit: "Wyślij prośbę",
      partyName: "Nazwa partii",
      partyOrgId: "Numer organizacji partii",
      partyNamePlaceholder: "Wprowadź nazwę swojej organizacji",
      partyOrgIdPlaceholder: "Wprowadź numer organizacji",
      partyNameRequired: "Nazwa partii jest wymagana",
      partyOrgIdRequired: "Numer organizacji partii jest wymagany",
      opposingPartyName: "Nazwa przeciwnika (jeśli dotyczy)",
      opposingPartyOrgId: "Numer organizacji przeciwnika (jeśli znany)",
      opposingPartyNamePlaceholder: "Wprowadź nazwę przeciwnika",
      opposingPartyOrgIdPlaceholder: "Wprowadź numer organizacji przeciwnika",
    },
  },

  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Postęp generowania odpowiedzi",
    description:
      "Wyświetla postęp w czasie zadań do zakończenia promptu, w zależności od połączenia z innymi obszarami roboczymi i rozmiarem plików. Modal zostanie automatycznie zamknięty po zakończeniu wszystkich kroków.",
    step_fetching_memos: "Pobieranie danych prawnych na bieżące tematy",
    step_processing_chunks: "Przetwarzanie przesłanych dokumentów",
    step_combining_responses: "Finalizacja odpowiedzi",
    sub_step_chunk_label: "Przetwarzanie grupy dokumentów {{index}}",
    sub_step_memo_label: "Pobrano dane prawne z {{workspaceSlug}}",
    placeholder_sub_task: "Krok w kolejce",
    desc_fetching_memos:
      "Pobieranie istotnych informacji prawnych z powiązanych obszarów roboczych",
    desc_processing_chunks:
      "Analizowanie i wyodrębnianie informacji z grup dokumentów",
    desc_combining_responses: "Synteza informacji w kompleksową odpowiedź",
  },

  // =========================
  // MCP SERVER PAGE
  // =========================

  mcp: {
    title: "Zarządzanie serwerem MCP",
    description:
      "Zarządzaj konfiguracjami serwera Multi-Component Processing (MCP).",
    currentServers: "Aktualne serwery",
    noServers: "Brak skonfigurowanych serwerów MCP.",
    fetchError: "Nie udało się pobrać serwerów: {{error}}",
    addServerButton: "Dodaj nowy serwer",
    addServerModalTitle: "Dodaj nowy serwer MCP",
    addServerModalDesc:
      "Zdefiniuj konfigurację dla nowego procesu serwera MCP.",
    serverName: "Nazwa serwera (unikalny ID)",
    configJson: "Konfiguracja (JSON)",
    addButton: "Dodaj serwer",
    addSuccess: "Serwer dodany pomyślnie.",
    addError: "Nie udało się dodać serwera: {{error}}",
  },

  // =========================
  // ADMIN SYSTEM SETTINGS (UNIVERSITY MODE)
  // =========================
  admin: {
    system: {
      universityMode: {
        title: "University Mode",
        description:
          "Gdy jest włączony, ukrywa narzędzia walidacji, aktualizacji promptu, szablonów i wyszukiwania internetowego dla wszystkich użytkowników.",
        enable: "Włącz tryb uniwersytecki",
        saved: "Ustawienia trybu uniwersyteckiego zapisane.",
        error: "Nie udało się zapisać ustawień trybu uniwersyteckiego.",
        saveChanges: "Zapisz ustawienia trybu uniwersyteckiego",
      },
    },
  },

  // Months
  "month.1": "sty",
  "month.2": "lut",
  "month.3": "mar",
  "month.4": "kwi",
  "month.5": "maj",
  "month.6": "cze",
  "month.7": "lip",
  "month.8": "sie",
  "month.9": "wrz",
  "month.10": "paź",
  "month.11": "lis",
  "month.12": "gru",

  // =========================
  // FEATURE CARDS
  // =========================
  featureCards: {
    "draft-from-template-title": "Utwórz wersję roboczą dokumentu z szablonu",
    "draft-from-template-description":
      "Użyj funkcji, aby na przykład stworzyć politykę AML, protokół ze zgromadzenia wspólników lub standardową umowę arbitrażową.",
    "complex-document-builder-title": "Wykonaj złożone zadanie prawne",
    "complex-document-builder-description":
      "Idealne, gdy na przykład musisz przejrzeć setki dokumentów przed przejęciem firmy lub sporządzić szczegółowy pozew.",
  },

  // =========================
  // WORKSPACE SELECTOR MODAL
  // =========================
  workspaceSelector: {
    chooseWorkspace: "Rozpocznij nowy czat",
    selectAiType: "Wybór modułu i obszaru roboczego do inicjowania funkcji",
    cloudAiDescription:
      "Wykorzystuje model AI oparty na chmurze do czatu i odpowiadania na pytania. Twoje dokumenty będą przetwarzane i przechowywane bezpiecznie w chmurze.",
    localAiDescription:
      "Wykorzystuje lokalny model AI do czatu i tworzenia dokumentów. Twoje dokumenty będą przetwarzane i przechowywane na Twoim lokalnym komputerze.",
    cloudAiDescriptionTemplateFeature:
      "Utwórz szablon w istniejącym obszarze roboczym z danymi prawnymi, szablon może pobierać dane prawne w zależności od zapytania.",
    localAiDescriptionTemplateFeature:
      "Utwórz szablon we własnym obszarze roboczym, korzystając z całkowicie lokalnej AI, jeśli jest włączona na serwerze.",
    cloudAiDescriptionComplexFeature:
      "Tworzenie złożonych dokumentów nie jest dostępne dla tych obszarów roboczych, ponieważ użytkownik musi przesłać dokumenty do obszaru roboczego przed rozpoczęciem",
    localAiDescriptionComplexFeature:
      "Wybierz jeden z obszarów roboczych do zainicjowania zadania prawnego i upewnij się, że niezbędne dokumenty są przesłane do obszaru roboczego przed rozpoczęciem.",
    newWorkspaceComplexTaskInfo:
      "Jeśli tworzysz nowy obszar roboczy, przejdziesz do widoku przesyłania, aby przesłać wszystkie niezbędne dokumenty, co jest konieczne do wykonania generowania dokumentów zadań prawnych.",
    selectExistingWorkspace: "Wybierz istniejący obszar roboczy",
    selectExistingDocumentDraftingWorkspace:
      "Wybierz istniejący obszar roboczy do tworzenia dokumentów",
    orCreateNewBelow:
      "Lub utwórz nowy obszar roboczy do tworzenia dokumentów poniżej.",
    newWorkspaceName: "Wprowadź nazwę dla nowego obszaru roboczego",
    newWorkspaceNameOptional:
      "Wprowadź nazwę dla nowego obszaru roboczego (jeśli nie używasz istniejącego obszaru roboczego)",
    workspaceNamePlaceholder: "np. Mój nowy obszar roboczy",
    next: "Dalej",
    pleaseSelectWorkspace: "Proszę wybrać obszar roboczy.",
    workspaceNameRequired: "Nazwa obszaru roboczego jest wymagana.",
    workspaceNameOrExistingWorkspaceRequired:
      "Proszę wprowadzić nazwę obszaru roboczego lub wybrać istniejący obszar roboczy.",
    workspaceNameMustBeMoreThanOneCharacter:
      "Nazwa obszaru roboczego musi mieć więcej niż jeden znak.",
    noWorkspacesAvailable: "Brak dostępnych obszarów roboczych",
    selectWorkspacePlaceholder: "Proszę wybrać",
    featureUnavailable: {
      title: "Funkcja jest niedostępna",
      description:
        "Ta funkcja nie jest włączona dla Twojego konta lub jest wyłączona w tym systemie. Skontaktuj się z administratorem, aby włączyć tę funkcję, jeśli jest wymagana.",
      close: "Zamknij",
    },
    createNewWorkspace: {
      title: "Utwórz nowy obszar roboczy do tworzenia dokumentów",
      description:
        "Spowoduje to utworzenie nowego obszaru roboczego specjalnie do złożonego tworzenia dokumentów przy użyciu wybranego szablonu.",
      workspaceName: "Nazwa obszaru roboczego",
      create: "Utwórz obszar roboczy",
    },
    selectExisting: {
      title: "Wybierz obszar roboczy dla pytań prawnych",
      description:
        "Wybierz istniejący obszar roboczy, aby rozpocząć sesję pytań i odpowiedzi prawnych.",
      selectWorkspace: "Wybierz obszar roboczy",
    },
  },
};

export default TRANSLATIONS;
