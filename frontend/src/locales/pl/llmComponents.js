export default {
  // =========================
  // GENERIC PROVIDER SELECTION SETTINGS
  // =========================
  generic: {
    "base-url": "Bazowy URL",
    "api-key": "Klucz API",
    "api-key-placeholder": "Wprowadź swój klucz API",
    "chat-model": "Model czatu",
    "chat-model-placeholder": "Wprowadź model czatu",
    "token-window": "Okno kontekstu tokenów",
    "token-window-placeholder": "Limit okna kontekstu (np: 4096)",
    "max-tokens": "Maks tokeny",
    "max-tokens-placeholder": "Maks tokeny na żądanie (np: 1024)",
    "embedding-deployment": "Nazwa wdrożenia osadzania",
    "embedding-deployment-placeholder":
      "Nazwa wdrożenia modelu osadzania Azure OpenAI",
    "embedding-model": "Model osadzania",
    "embedding-model-placeholder": "Wprowadź model osadzania",
    "max-embedding-chunk-length": "Maks długość fragmentu osadzania",
    saving: "Zapisywanie...",
    "save-changes": "Zapisz zmiany",
    "workspace-update-error": "Błąd: {{error}}",
    "base-url-placeholder": "np: https://proxy.openai.com",
    "password-mask-length": "12",
  },

  // =========================
  // GENERIC MODEL SELECTION SETTINGS
  // =========================
  model: {
    selection: "Wybór modelu czatu",
    "embedding-selection": "Wybór modelu osadzania",
    "enter-api-key":
      "Wprowadź poprawny klucz API, aby zobaczyć wszystkie dostępne modele dla Twojego konta.",
    "enter-url": "Najpierw wprowadź URL",
    "your-models": "Twoje załadowane modele",
    "available-models": "Dostępne modele",
  },

  // =========================
  // ANTHROPIC
  // =========================
  anthropic: {
    "api-key": "Klucz API Anthropic",
    "api-key-placeholder": "Wprowadź swój klucz API Anthropic",
    "model-selection": "Wybór modelu czatu",
  },

  // =========================
  // AZURE SETTINGS
  // =========================
  azure: {
    "service-endpoint": "Punkt końcowy usługi Azure",
    "service-endpoint-placeholder": "https://moj-azure.openai.com",
    "api-key": "Klucz API",
    "api-key-placeholder": "Klucz API Azure OpenAI",
    "chat-deployment": "Nazwa wdrożenia czatu",
    "chat-deployment-placeholder": "Nazwa wdrożenia modelu czatu Azure OpenAI",
    "token-limit": "Limit tokenów modelu czatu",
  },
  azureai: {
    "service-endpoint": "Punkt końcowy usługi Azure AI",
    "api-key": "Klucz API",
    "api-key-placeholder": "Klucz API Azure OpenAI",
    "embedding-deployment-name": "Nazwa wdrożenia osadzania",
    "embedding-deployment-name-placeholder":
      "Wprowadź nazwę wdrożenia osadzania",
  },

  // =========================
  // AWS BEDROCK SETTINGS
  // =========================
  bedrock: {
    "iam-warning":
      "Powinieneś używać prawidłowo zdefiniowanego użytkownika IAM do inferencji.",
    "read-more":
      "Dowiedz się więcej o korzystaniu z AWS Bedrock w tej instancji",
    "access-id": "AWS Bedrock IAM Access ID",
    "access-id-placeholder": "AWS Bedrock IAM User Access ID",
    "access-key": "AWS Bedrock IAM Access Key",
    "access-key-placeholder": "AWS Bedrock IAM User Access Key",
    region: "Region AWS",
    "model-id": "ID modelu",
    "model-id-placeholder": "ID modelu z AWS np.: meta.llama3.1-v0.1",
    "context-window": "Okno kontekstu modelu",
    "context-window-placeholder": "Limit okna treści (np.: 4096)",
  },

  // =========================
  // COHERE
  // =========================
  cohere: {
    "api-key": "Klucz API Cohere",
    "api-key-placeholder": "Wprowadź swój klucz API Cohere",
    "model-preference": "Preferencje modelu",
    "model-selection": "Wybór modelu",
  },

  // =========================
  // DEEPSEEK LLM SETTINGS
  // =========================
  deepseek: {
    "api-key": "Klucz API DeepSeek",
    "api-key-placeholder": "Wprowadź swój klucz API DeepSeek",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
  },

  // =========================
  // FIREWORKSAI LLM SELECTION
  // =========================
  fireworksai: {
    "api-key": "Klucz API FireworksAI",
    "api-key-placeholder": "Wprowadź swój klucz API FireworksAI",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
  },

  // =========================
  // GEMINI LLM OPTIONS
  // =========================
  gemini: {
    "api-key": "Klucz API Google AI",
    "api-key-placeholder": "Klucz API Google Gemini",
    "model-selection": "Wybór modelu",
    "loading-models": "Ładowanie modeli...",
    "manual-options": "Opcje ręczne",
    "safety-setting": "Ustawienia bezpieczeństwa",
    experimental: "Eksperymentalny",
    stable: "Stabilny",
    "safety-options": {
      none: "Brak (domyślnie)",
      "block-few": "Blokuj tylko wysokie ryzyko",
      "block-some": "Blokuj średnie i wysokie ryzyko",
      "block-most": "Blokuj większość treści",
    },
  },

  // =========================
  // GROQ LLM SELECTION
  // =========================
  groq: {
    "api-key": "Klucz API Groq",
    "api-key-placeholder": "Wprowadź swój klucz API Groq",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
    "enter-api-key":
      "Wprowadź poprawny klucz API, aby zobaczyć wszystkie dostępne modele dla Twojego konta.",
    "available-models": "Dostępne modele",
    "model-description":
      "Wybierz model GroqAI, którego chcesz używać w rozmowach.",
  },

  // =========================
  // HUGGINGFACE LLM SETTINGS
  // =========================
  huggingface: {
    "inference-endpoint": "Punkt końcowy HuggingFace Inference",
    "endpoint-placeholder": "https://example.endpoints.huggingface.cloud",
    "access-token": "Token dostępu HuggingFace",
    "token-placeholder": "Wprowadź swój token dostępu HuggingFace",
    "token-limit": "Limit tokenów modelu",
    "token-limit-placeholder": "4096",
  },

  // =========================
  // KOBOLDCPP SETTINGS
  // =========================
  koboldcpp: {
    "show-advanced": "Pokaż ręczne wprowadzenie punktu końcowego",
    "hide-advanced": "Ukryj ręczne wprowadzenie punktu końcowego",
    "base-url": "Podstawowy URL KoboldCPP",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "base-url-desc": "Wprowadź URL, na którym działa KoboldCPP.",
    "auto-detect": "Automatyczne wykrywanie",
    "token-context-window": "Okno kontekstu tokenów",
    "token-window-placeholder": "4096",
    "token-window-desc":
      "Maksymalna liczba tokenów dla kontekstu i odpowiedzi.",
    model: "Model KoboldCPP",
    "loading-models": "--loading available models--",
    "enter-url": "Najpierw wprowadź URL KoboldCPP",
    "model-desc":
      "Wybierz model KoboldCPP, którego chcesz używać. Modele zostaną załadowane po wprowadzeniu poprawnego URL KoboldCPP.",
    "model-choose":
      "Wybierz model KoboldCPP, którego chcesz używać w rozmowach.",
  },

  // =========================
  // LITELLM
  // =========================
  litellm: {
    "model-tooltip":
      "Upewnij się, że wybrałeś poprawny model osadzania. Modele czatu nie są modelami osadzania. Zobacz",
    "model-tooltip-link": "tę stronę",
    "model-tooltip-more": "aby uzyskać więcej informacji.",
    "base-url": "Podstawowy URL",
    "base-url-placeholder": "np: https://proxy.openai.com",
    "max-embedding-chunk-length": "Maksymalna długość fragmentu osadzania",
    "token-window": "Okno kontekstu tokenów",
    "token-window-placeholder": "Limit okna kontekstu (np: 4096)",
    "api-key": "Klucz API",
    optional: "opcjonalnie",
    "api-key-placeholder": "sk-mysecretkey",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
    "waiting-url": "-- oczekiwanie na adres URL --",
    "loaded-models": "Twoje załadowane modele",
    "manage-embedding": "Zarządzaj osadzaniem",
    "embedding-required":
      "Litellm wymaga ustawienia usługi osadzania do użycia.",
  },

  // =========================
  // LMSTUDIO LLM SELECTION
  // =========================
  lmstudio: {
    "max-tokens": "Maksymalna liczba tokenów",
    "max-tokens-desc": "Maksymalna liczba tokenów dla kontekstu i odpowiedzi.",
    "show-advanced": "Pokaż ręczne wprowadzenie punktu końcowego",
    "hide-advanced": "Ukryj ręczne wprowadzenie punktu końcowego",
    "base-url": "Podstawowy URL LM Studio",
    "base-url-placeholder": "http://localhost:1234/v1",
    "base-url-desc": "Wprowadź URL, na którym działa LM Studio.",
    "auto-detect": "Automatyczne wykrywanie",
    model: "Model LM Studio",
    "model-loading": "--ładowanie dostępnych modeli--",
    "model-url-first": "Najpierw wprowadź URL LM Studio",
    "model-desc":
      "Wybierz model LM Studio, którego chcesz używać. Modele zostaną załadowane po wprowadzeniu poprawnego URL LM Studio.",
    "model-choose":
      "Wybierz model LM Studio, którego chcesz używać w rozmowach.",
    "model-loaded": "Twoje załadowane modele",
    "embedding-required":
      "LMStudio jako Twój LLM wymaga ustawienia usługi osadzania (embedding).",
    "manage-embedding": "Zarządzaj osadzaniem",
    "max-embedding-chunk-length": "Maksymalna długość fragmentu osadzania",
  },

  // =========================
  // LOCALAI LLM SELECTION
  // =========================
  localai: {
    "token-window": "Okno kontekstu tokenów",
    "token-window-placeholder": "4096",
    "api-key": "Klucz API Local AI",
    "api-key-optional": "opcjonalnie",
    "api-key-placeholder": "sk-mysecretkey",
    "show-advanced": "Pokaż zaawansowane ustawienia",
    "hide-advanced": "Ukryj zaawansowane ustawienia",
    "base-url": "Podstawowy URL Local AI",
    "base-url-placeholder": "http://localhost:8080/v1",
    "base-url-help": "Wprowadź URL, na którym działa LocalAI.",
    "auto-detect": "Automatyczne wykrywanie",
    "max-embedding-chunk-length": "Maksymalna długość fragmentu osadzania",
    "embedding-required": "Osadzanie (embedding) jest wymagane dla LocalAI.",
    "manage-embedding": "Zarządzaj osadzaniem",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
    "waiting-url": "-- oczekiwanie na URL --",
    "loaded-models": "Twoje załadowane modele",
  },

  // =========================
  // MISTRAL LLM OPTIONS
  // =========================
  mistral: {
    "api-key": "Klucz API Mistral",
    "api-key-placeholder": "Mistral API Key",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
    "waiting-key": "-- oczekiwanie na klucz API --",
    "available-models": "Dostępne modele Mistral",
  },

  // =========================
  // NATIVE LLM SETTINGS
  // =========================

  native: {
    "experimental-warning":
      "Korzystanie z lokalnie hostowanego LLM jest eksperymentalne i może nie działać zgodnie z oczekiwaniami.",
    "model-desc": "Wybierz model z Twoich lokalnie hostowanych modeli.",
    "token-desc": "Maksymalna liczba tokenów dla kontekstu i odpowiedzi.",
  },

  // =========================
  // OLLAMA LLM SELECTION
  // =========================

  ollamallmselection: {
    "max-tokens": "Maksymalna liczba tokenów",
    "max-tokens-desc": "Maksymalna liczba tokenów dla kontekstu i odpowiedzi.",
    "show-advanced": "Pokaż ręczne wprowadzenie punktu końcowego",
    "hide-advanced": "Ukryj ręczne wprowadzenie punktu końcowego",
    "base-url": "Podstawowy URL Ollama",
    "base-url-placeholder": "http://127.0.0.1:11434",
    "base-url-desc": "Wprowadź URL, na którym działa Ollama.",
    "auto-detect": "Automatyczne wykrywanie",
    "keep-alive": "Utrzymaj aktywność Ollama",
    "no-cache": "Brak buforowania",
    "five-minutes": "5 minut",
    "one-hour": "1 godzina",
    forever: "Na zawsze",
    "keep-alive-desc": "Utrzymuj model załadowany w pamięci.",
    "learn-more": "Dowiedz się więcej",
    "performance-mode": "Tryb wydajności",
    "base-default": "Podstawowy (Domyślny)",
    maximum: "Maksymalny",
    "performance-mode-desc": "Wybierz tryb wydajności.",
    note: "Uwaga:",
    "maximum-warning": "Tryb maksymalny zużywa więcej zasobów.",
    base: "Podstawowy",
    "base-desc": "Tryb podstawowy jest ustawieniem domyślnym.",
    "maximum-desc":
      "Tryb maksymalny zapewnia wyższą wydajność, wykorzystując pełne okno kontekstu do maksymalnej liczby tokenów.",
    model: "Model Ollama",
    "loading-models": "--loading available models--",
    "enter-url": "Najpierw wprowadź URL Ollama",
    "model-desc":
      "Wybierz model Ollama, którego chcesz używać. Modele zostaną załadowane po wprowadzeniu poprawnego URL Ollama.",
    "model-choose": "Wybierz model Ollama, którego chcesz używać w rozmowach.",
  },

  // =========================
  // OPENAI LLM SELECTION
  // =========================
  openai: {
    "api-key": "Klucz API OpenAI",
    "api-key-placeholder": "Wprowadź swój klucz API OpenAI",
    "model-preference": "Preferencje modelu",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
    "model-divider": "──────────",
  },

  // =========================
  // OPENROUTER LLM SELECTION
  // =========================
  openrouter: {
    "api-key": "Klucz API OpenRouter",
    "api-key-placeholder": "Wprowadź swój klucz API OpenRouter",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
  },

  // =========================
  // PERPLEXITY LLM SELECTION
  // =========================
  perplexity: {
    "api-key": "Klucz API Perplexity",
    "api-key-placeholder": "Wprowadź swój klucz API Perplexity",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
    "available-models": "Dostępne modele Perplexity",
  },

  // =========================
  // TOGETHERAI
  // =========================
  togetherai: {
    "api-key": "Klucz API TogetherAI",
    "api-key-placeholder": "Wprowadź swój klucz API TogetherAI",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
  },

  // =========================
  // XAI LLM OPTIONS
  // =========================
  xai: {
    "api-key": "Klucz API xAI",
    "api-key-placeholder": "xAI API Key",
    "model-selection": "Wybór modelu czatu",
    "loading-models": "-- ładowanie dostępnych modeli --",
    "enter-api-key":
      "Wprowadź poprawny klucz API, aby zobaczyć wszystkie dostępne modele dla Twojego konta.",
    "available-models": "Dostępne modele",
    "model-description":
      "Wybierz model xAI, którego chcesz używać w rozmowach.",
  },
};
