export default {
  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Zarejestruj projekt Rexor",
    "project-id": "ID projektu",
    "resource-id": "ID zasobu",
    "activity-id": "ID aktywności",
    register: "Zarejestruj projekt",
    "invoice-text": "Liczba wyszukiwań Foynet",
    registering: "rejestrowanie...",
    "not-active": "Ta sprawa nie jest aktywna do rejestracji",
    account: {
      title: "<PERSON><PERSON>uj się do Rexor",
      username: "<PERSON>zwa użytkownika",
      password: "<PERSON>ł<PERSON>",
      "no-token": "Brak tokena otrzymanego w handleLoginSuccess",
      logout: "Wyloguj się",
      "no-user": "Proszę najpierw się zalogować",
      connected: "Połączono z Rexorem",
      "not-connected": "Nie połączono",
      "change-account": "Zmień konto",
      "session-expired": "Sesja wygasła. Proszę zaloguj się ponownie.",
    },
    "hide-article-transaction": "Ukryj formularz transakcji czasowej",
    "show-article-transaction": "Pokaż formularz transakcji czasowej",
    "article-transaction-title": "Dodaj transakcję czasową",
    "registration-date": "Data rejestracji",
    description: "Opis",
    "description-internal": "Opis wewnętrzny",
    "hours-worked": "Przepracowane godziny",
    "invoiced-hours": "Zafakturowane godziny",
    invoiceable: "Fakturowalne",
    "sending-article-transaction": "Wysyłanie transakcji czasowej...",
    "save-article-transaction": "Zapisz transakcję czasową",
    "project-not-register": "Projekt musi być najpierw zarejestrowany.",
    "article-transaction-error": "Nie udało się zapisać transakcji czasowej",
    "not-exist": "Nie można znaleźć tej sprawy",
  },
};
