export default {
  "show-toast": {
    "recovery-codes": "Codes de récupération copiés dans le presse-papiers",
    "token-minimum-error":
      "Contenu du fichier inférieur au minimum de tokens requis",
    "file-process-error": "Échec du traitement du fichier",
    "scraping-website": "Extraction du site web - cela peut prendre un moment.",
    "fetching-transcript":
      "Récupération de la transcription de la vidéo YouTube.",
    "request-legal-assistance-sent":
      "La demande d'assistance juridique a été envoyée.",
    "request-legal-assistance-error":
      "Échec de l'envoi de la demande d'assistance juridique.",
    "updating-workspace": "Mise à jour de l'espace de travail...",
    "flashing-started": "Démarrage du flash...",
    "flashing-success": "Flash terminé avec succès",
    "flashing-error": "Erreur lors du flash : {{error}}",
    "pin-success-added": "Document épinglé à l'espace de travail",
    "pin-success-removed": "Document détaché de l'espace de travail",
    "workspace-updated": "Espace de travail mis à jour avec succès.",
    "link-uploaded": "Lien téléchargé avec succès",
    "password-reset": "Réinitialisation du mot de passe réussie",
    "invalid-reset": "Jeton de réinitialisation invalide",
    "delete-option": "Impossible de supprimer ce fil !",
    "thread-deleted": "Fil supprimé avec succès !",
    "threads-deleted": "Fils supprimés avec succès !",
    "chat-deleted": "Chat supprimé avec succès !",
    "failed-delete-chat":
      "Échec de la suppression du chat. Veuillez réessayer.",
    "error-deleting-chat": "Erreur lors de la suppression du chat.",
    "chat-memory-reset":
      "Mémoire du chat de l'espace de travail réinitialisée !",
    "picture-uploaded": "Photo de profil téléchargée.",
    "profile-updated": "Profil mis à jour.",
    "logs-cleared": "Journaux effacés avec succès.",
    "preferences-updated": "Préférences système mises à jour avec succès.",
    "user-created": "Utilisateur créé avec succès.",
    "user-creation-error": "Échec de la création de l'utilisateur : {{error}}",
    "user-exists-error": "Un utilisateur avec cet e-mail existe déjà",
    "user-deleted": "Utilisateur supprimé du système.",
    "workspaces-saved": "Espaces de travail enregistrés avec succès !",
    "failed-workspaces":
      "Échec de l'enregistrement des espaces de travail. Veuillez réessayer.",
    "api-deleted": "Clé API supprimée définitivement",
    "api-copied": "Clé API copiée dans le presse-papiers",
    "appname-updated":
      "Nom personnalisé de l'application mis à jour avec succès.",
    "appname-update-error": "Échec de la mise à jour du nom personnalisé : ",
    "language-updated": "Langue mise à jour avec succès.",
    "palette-updated": "Palette mise à jour avec succès.",
    "image-uploaded": "Image téléchargée avec succès.",
    "logo-remove-error": "Erreur lors de la suppression du logo : ",
    "logo-removed": "Logo supprimé avec succès.",
    "logo-uploaded": "Logo téléchargé avec succès.",
    "logo-upload-error": "Échec du téléchargement du logo : ",
    "updated-welcome": "Messages de bienvenue mis à jour avec succès.",
    "update-welcome-error":
      "Échec de la mise à jour des messages de bienvenue :",
    "updated-footer": "Icônes de pied de page mises à jour avec succès.",
    "update-footer-error":
      "Échec de la mise à jour des icônes de pied de page : ",
    "updated-paragraph": "Texte personnalisé mis à jour avec succès.",
    "update-paragraph-error":
      "Échec de la mise à jour du texte personnalisé : ",
    "updated-supportemail": "Email de support mis à jour avec succès.",
    "update-supportemail-error":
      "Échec de la mise à jour de l'email de support : ",
    "stt-success":
      "Préférences de reconnaissance vocale enregistrées avec succès.",
    "tts-success": "Préférences de synthèse vocale enregistrées avec succès.",
    "failed-chats-export": "Échec de l'exportation des chats.",
    "chats-exported": "Chats exportés avec succès sous le nom {{name}}.",
    "cleared-chats": "Tous les chats effacés.",
    "embed-deleted": "Intégration supprimée du système.",
    "snippet-copied": "Extrait copié dans le presse-papiers !",
    "embed-updated": "Intégration mise à jour avec succès.",
    "embedding-saved": "Préférences d'incorporation enregistrées avec succès.",
    "chunking-settings": "Paramètres de découpage de texte enregistrés.",
    "llm-saved": "Préférences LLM enregistrées avec succès.",
    "llm-saving-error": "Échec de l'enregistrement des paramètres LLM : ",
    "multiuser-enabled": "Mode multi-utilisateur activé avec succès.",
    "publicuser-enabled": "Mode utilisateur public activé avec succès.",
    "publicuser-disabled": "Mode utilisateur public désactivé avec succès.",
    "page-refresh": "La page sera actualisée dans quelques secondes.",
    "transcription-saved":
      "Préférences de transcription enregistrées avec succès.",
    "vector-saved":
      "Préférences de base de données vectorielle enregistrées avec succès.",
    "workspace-not-deleted": "L'espace de travail n'a pas pu être supprimé !",
    "maximum-messages": "Maximum de 4 messages autorisés.",
    "users-updated": "Utilisateurs mis à jour avec succès.",
    "vectordb-not-reset":
      "La base de données vectorielle n'a pas pu être réinitialisée !",
    "vectordb-reset": "La base de données vectorielle a été réinitialisée !",
    "meta-data-update": "Préférences du site mises à jour !",
    "linked-workspaces-updated": "Espaces liés mis à jour avec succès.",
    "upgrade-answer-error": "Échec de l'amélioration de la réponse : ",
    "upgrade-text-error": "Échec de l'amélioration du texte : ",
    "reset-tab-name-error":
      "Échec de la réinitialisation du nom d'onglet par défaut.",
    "update-tab-name-error": "Échec de la mise à jour des noms d'onglets : ",
    "updated-website": "Paramètres du site mis à jour avec succès.",
    "update-website-error": "Échec de la mise à jour du lien du site : ",
    "reset-website-error": "Échec de la réinitialisation du lien par défaut.",
    "palette-update-error": "Échec de la mise à jour de la palette : ",
    "citation-state-updated":
      "État de citation mis à jour avec succès. {{citationState}}",
    "citation-state-update-error": "Échec de la mise à jour de la citation",
    "citation-update-error": "Erreur lors de la mise à jour de la citation",
    "message-limit-updated":
      "Préférences de limite de messages mises à jour avec succès.",
    "validate-response-error": "La validation a échoué avec ",
    "invoice-logging-state-updated":
      "Préférences de journalisation des factures mises à jour avec succès.",
    "invoice-logging-state-update-error":
      "Erreur lors de la mise à jour de la journalisation des factures : ",
    "error-fetching-tab-names":
      "Erreur lors de la récupération des noms d'onglets",
    "active-case": {
      "reference-updated": "Référence du dossier actif mise à jour avec succès",
      "reference-cleared": "Référence du dossier actif effacée avec succès",
    },
    "export-word": "Exporter vers Word",
    "export-error": "Erreur lors de l'exportation vers Word",
    "export-success": "Document exporté avec succès",
    "file-upload-success": "Fichier téléchargé avec succès",
    "files-upload-success": "{{count}} fichiers téléchargés avec succès",
    "file-upload-error": "Erreur lors du téléchargement du/des fichier(s)",
    "file-removed": "Fichier supprimé avec succès",
    "file-remove-error":
      "Impossible de supprimer complètement le fichier. Veuillez réessayer.",
    "link-upload-success": "Lien téléchargé avec succès",
    "link-upload-error": "Erreur lors du téléchargement du lien : {{error}}",
    "qura-login-success": "Connexion à Qura réussie",
    "error-linking-rexor":
      "Erreur lors de la récupération du statut de liaison Rexor",
    "article-transaction-registered":
      "Transaction temporelle enregistrée avec succès",
    "changes-saved": "Modifications enregistrées",
    "missing-data": "Données requises manquantes",
    "save-error": "Erreur lors de l'enregistrement des modifications",
    "invalid-response": "Réponse invalide reçue",
    "streaming-error": "Erreur lors du streaming",
    "qura-auth.error.fill-fields": "Veuillez remplir tous les champs.",
    "show-toast.qura-login-success": "Login successful.",
    "qura-auth.error.invalid-credentials": "Invalid username or password.",
    "qura-auth.connect": "Connect to Qura",
    "speech-to-text.microphone-access-error": "Microphone access is required.",
    "show-toast.meta-data-update": "Metadata updated successfully.",
    "appearance.siteSettings.tabIcon": "Tab Icon",
    "appearance.siteSettings.fabIconUrl": "Favicon URL",
    "appearance.siteSettings.placeholder": "Enter favicon URL",
    "appearance.siteSettings.title-placeholder": "Enter site title",
    "show-toast.workspace-updated": "Workspace updated successfully.",
    "pdr-settings.toast-success": "PDR settings updated successfully.",
    "pdr-settings.toast-fail": "Failed to update PDR settings.",
    "pdr-settings.title": "PDR Settings",
    "pdr-settings.description": "Manage your PDR settings here.",
    "pdr-settings.desc-end": "Ensure all values are correct.",
    "pdr-settings.pdr-token-limit": "PDR Token Limit",
    "pdr-settings.pdr-token-limit-desc": "Maximum number of tokens for PDR.",
    "pdr-settings.pdr-token-limit-placeholder": "Enter PDR token limit",
    "pdr-settings.input-prompt-token-limit": "Input Prompt Token Limit",
    "pdr-settings.input-prompt-token-limit-desc":
      "Maximum number of tokens for input prompts.",
    "pdr-settings.input-prompt-token-limit-placeholder":
      "Enter input prompt token limit",
    "pdr-settings.response-token-limit": "Response Token Limit",
    "pdr-settings.response-token-limit-desc":
      "Maximum number of tokens for responses.",
    "pdr-settings.response-token-limit-placeholder":
      "Enter response token limit",
    "pdr-settings.adjacent-vector-limit": "Adjacent Vector Limit",
    "pdr-settings.adjacent-vector-limit-desc": "Limit for adjacent vectors.",
    "pdr-settings.adjacent-vector-limit-placeholder":
      "Enter adjacent vector limit",
    "pdr-settings.keep-pdr-vectors": "Keep PDR Vectors",
    "pdr-settings.keep-pdr-vectors-desc": "Option to keep PDR vectors.",
    "workspace-update-error": "Error: {{error}}",
    "workspace-update-failed":
      "Échec de la mise à jour de l'espace de travail : {{error}}",
    "token-window-exceeded":
      "Le fichier n'a pas été ajouté car il dépasse la limite de tokens disponible du moteur IA actuel",
    "token-limit-exceeded":
      "Fichier trop volumineux pour la fenêtre de contexte disponible. Tokens disponibles : {{available}}",
    "rexor-activity-id-update-success":
      "ID d'activité Rexor mis à jour avec succès",
    "rexor-activity-id-update-error":
      "Échec de la mise à jour de l'ID d'activité Rexor",
    "pin-error": "Impossible de {{action}} le document.",
    "pin-error-detail": "Impossible d'épingler le document. {{message}}",
    "error-fetching-settings": "An error occurred while fetching settings.",
    "experimental-features-unlocked":
      "Aperçus des fonctionnalités expérimentales débloqués !",
    "workspace-updated-success": "Espace de travail mis à jour avec succès.",
    "legal-task-created": "Tâche juridique créée !",
    "form-submission-failed": "Échec de la soumission du formulaire !",
    "provider-endpoint-discovered":
      "Provider endpoint discovered automatically.",
    "failed-save-embedding":
      "Échec de l'enregistrement des paramètres d'intégration : {{error}}",
    "provider-endpoint-discovery-failed":
      "Impossible de détecter automatiquement le point de terminaison du fournisseur. Veuillez l'entrer manuellement.",
    "qura-setting-update-failed": "Failed to update Qura setting.",
    "qura-settings-submission-error": "Error while submitting Qura settings.",
    "preferences-save-error": "An error occurred while saving preferences.",
    "skill-config-updated": "Skill config updated successfully.",
    "category-deleted-success": "Category deleted successfully!",
    "category-deletion-failed": "Failed to delete category!",
    "embed-chats-export-failed": "Failed to export embed chats.",
    "api-key-revoked": "Browser Extension API Key permanently revoked",
    "api-key-revoke-failed": "Failed to revoke API Key",
    "connection-string-copied": "Connection string copied to clipboard",
    "connecting-to-extension": "Attempting to connect to browser extension...",
    "error-loading-settings":
      "Error loading settings. Please refresh the page.",
    "error-fetching-prompt-logging":
      "Error fetching prompt output logging setting.",
    "error-updating-prompt-logging":
      "Failed to update prompt output logging setting.",
    "error-updating-feature": "Failed to update status of feature.",
    "rexor-linkage-state-updated":
      "État de liaison Rexor mis à jour vers {{state}}",
    "rexor-linkage-state-update-error":
      "Échec de la mise à jour de l'état de liaison Rexor : {{error}}",
    "language-update-failed":
      "La mise à jour de la langue a échoué : {{error}}",
    "llm-settings-save-failed":
      "Impossible d'enregistrer les paramètres LLM : {{error}}",
    "setup-error": "Error: {{error}}",
    "template-saved": "Prompt template saved successfully",
    "template-saving-error": "Failed to save template: {{error}}",
    "template-reset": "Template reset to default",
    "legal-task-updated": "Tâche juridique mise à jour avec succès",
    "legal-task-update-failed": "Échec de la mise à jour de la tâche juridique",
    "model-ref-saved":
      "Modifications du nom du modèle personnalisé et/ou du PDR dynamique personnalisé enregistrées",
    "model-ref-save-failed":
      "Échec de l'enregistrement des paramètres du modèle personnalisé : {{error}}",
    "settings-fetch-failed": "Failed to fetch settings",
    "api-key-saved": "Clé API enregistrée avec succès",
    "auto-environment-update":
      "Mise à jour automatique de l'environnement démarrée. Cela prendra quelques secondes.",
    "example-prompt-error":
      "Impossible de soumettre l'invite d'exemple: {{error}}",
    "files-processed": "{{count}} fichier(s) traité(s) avec succès",
    "token-validation-failed":
      "Échec de la validation du fichier - nombre de tokens ou limite de taille dépassé",
    "deleted-old-chats": "{{count}} ancien(s) prompt(s) supprimé(s)",
    "pdr-failed": "Failed to PDR document: {{message}}",
    "watch-failed": "Failed to watch document: {{message}}",
    "pdr-added": "Document added to Parent Document Retrieval",
    "pdr-removed": "Document removed from Parent Document Retrieval",
    "unsaved-changes":
      "Vous avez des modifications non enregistrées. Êtes-vous sûr de vouloir annuler?",
  },
};
