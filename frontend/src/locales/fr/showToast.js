export default {
  "show-toast": {
    "recovery-codes": "Codes de récupération copiés dans le presse-papiers",
    "token-minimum-error":
      "Contenu du fichier inférieur au minimum de tokens requis",
    "file-process-error": "Échec du traitement du fichier",
    "scraping-website": "Extraction du site web - cela peut prendre un moment.",
    "fetching-transcript":
      "Récupération de la transcription de la vidéo YouTube.",
    "request-legal-assistance-sent":
      "La demande d'assistance juridique a été envoyée.",
    "request-legal-assistance-error":
      "Échec de l'envoi de la demande d'assistance juridique.",
    "updating-workspace": "Mise à jour de l'espace de travail...",
    "flashing-started": "Démarrage du flash...",
    "flashing-success": "Flash terminé avec succès",
    "flashing-error": "Erreur lors du flash : {{error}}",
    "pin-success-added": "Document épinglé à l'espace de travail",
    "pin-success-removed": "Document détaché de l'espace de travail",
    "workspace-updated": "Espace de travail mis à jour avec succès.",
    "link-uploaded": "Lien téléchargé avec succès",
    "password-reset": "Réinitialisation du mot de passe réussie",
    "invalid-reset": "Jeton de réinitialisation invalide",
    "delete-option": "Impossible de supprimer ce fil !",
    "thread-deleted": "Fil supprimé avec succès !",
    "threads-deleted": "Fils supprimés avec succès !",
    "chat-deleted": "Chat supprimé avec succès !",
    "failed-delete-chat":
      "Échec de la suppression du chat. Veuillez réessayer.",
    "error-deleting-chat": "Erreur lors de la suppression du chat.",
    "chat-memory-reset":
      "Mémoire du chat de l'espace de travail réinitialisée !",
    "picture-uploaded": "Photo de profil téléchargée.",
    "profile-updated": "Profil mis à jour.",
    "logs-cleared": "Journaux effacés avec succès.",
    "preferences-updated": "Préférences système mises à jour avec succès.",
    "user-created": "Utilisateur créé avec succès.",
    "user-creation-error": "Échec de la création de l'utilisateur : {{error}}",
    "user-exists-error": "Un utilisateur avec cet e-mail existe déjà",
    "user-deleted": "Utilisateur supprimé du système.",
    "workspaces-saved": "Espaces de travail enregistrés avec succès !",
    "failed-workspaces":
      "Échec de l'enregistrement des espaces de travail. Veuillez réessayer.",
    "api-deleted": "Clé API supprimée définitivement",
    "api-copied": "Clé API copiée dans le presse-papiers",
    "appname-updated":
      "Nom personnalisé de l'application mis à jour avec succès.",
    "appname-update-error": "Échec de la mise à jour du nom personnalisé : ",
    "language-updated": "Langue mise à jour avec succès.",
    "palette-updated": "Palette mise à jour avec succès.",
    "image-uploaded": "Image téléchargée avec succès.",
    "logo-remove-error": "Erreur lors de la suppression du logo : ",
    "logo-removed": "Logo supprimé avec succès.",
    "logo-uploaded": "Logo téléchargé avec succès.",
    "logo-upload-error": "Échec du téléchargement du logo : ",
    "updated-welcome": "Messages de bienvenue mis à jour avec succès.",
    "update-welcome-error":
      "Échec de la mise à jour des messages de bienvenue :",
    "updated-footer": "Icônes de pied de page mises à jour avec succès.",
    "update-footer-error":
      "Échec de la mise à jour des icônes de pied de page : ",
    "updated-paragraph": "Texte personnalisé mis à jour avec succès.",
    "update-paragraph-error":
      "Échec de la mise à jour du texte personnalisé : ",
    "updated-supportemail": "Email de support mis à jour avec succès.",
    "update-supportemail-error":
      "Échec de la mise à jour de l'email de support : ",
    "stt-success":
      "Préférences de reconnaissance vocale enregistrées avec succès.",
    "tts-success": "Préférences de synthèse vocale enregistrées avec succès.",
    "failed-chats-export": "Échec de l'exportation des chats.",
    "chats-exported": "Chats exportés avec succès sous le nom {{name}}.",
    "cleared-chats": "Tous les chats effacés.",
    "embed-deleted": "Intégration supprimée du système.",
    "snippet-copied": "Extrait copié dans le presse-papiers !",
    "embed-updated": "Intégration mise à jour avec succès.",
    "embedding-saved": "Préférences d'incorporation enregistrées avec succès.",
    "chunking-settings": "Paramètres de découpage de texte enregistrés.",
    "llm-saved": "Préférences LLM enregistrées avec succès.",
    "llm-saving-error": "Échec de l'enregistrement des paramètres LLM : ",
    "multiuser-enabled": "Mode multi-utilisateur activé avec succès.",
    "publicuser-enabled": "Mode utilisateur public activé avec succès.",
    "publicuser-disabled": "Mode utilisateur public désactivé avec succès.",
    "page-refresh": "La page sera actualisée dans quelques secondes.",
    "transcription-saved":
      "Préférences de transcription enregistrées avec succès.",
    "vector-saved":
      "Préférences de base de données vectorielle enregistrées avec succès.",
    "workspace-not-deleted": "L'espace de travail n'a pas pu être supprimé !",
    "maximum-messages": "Maximum de 4 messages autorisés.",
    "users-updated": "Utilisateurs mis à jour avec succès.",
    "vectordb-not-reset":
      "La base de données vectorielle n'a pas pu être réinitialisée !",
    "vectordb-reset": "La base de données vectorielle a été réinitialisée !",
    "meta-data-update": "Préférences du site mises à jour !",
    "linked-workspaces-updated": "Espaces liés mis à jour avec succès.",
    "upgrade-answer-error": "Échec de l'amélioration de la réponse : ",
    "upgrade-text-error": "Échec de l'amélioration du texte : ",
    "reset-tab-name-error":
      "Échec de la réinitialisation du nom d'onglet par défaut.",
    "update-tab-name-error": "Échec de la mise à jour des noms d'onglets : ",
    "updated-website": "Paramètres du site mis à jour avec succès.",
    "update-website-error": "Échec de la mise à jour du lien du site : ",
    "reset-website-error": "Échec de la réinitialisation du lien par défaut.",
    "palette-update-error": "Échec de la mise à jour de la palette : ",
    "citation-state-updated":
      "État de citation mis à jour avec succès. {{citationState}}",
    "citation-state-update-error": "Échec de la mise à jour de la citation",
    "citation-update-error": "Erreur lors de la mise à jour de la citation",
    "message-limit-updated":
      "Préférences de limite de messages mises à jour avec succès.",
    "validate-response-error": "La validation a échoué avec ",
    "invoice-logging-state-updated":
      "Préférences de journalisation des factures mises à jour avec succès.",
    "invoice-logging-state-update-error":
      "Erreur lors de la mise à jour de la journalisation des factures : ",
    "error-fetching-tab-names":
      "Erreur lors de la récupération des noms d'onglets",
    "active-case": {
      "reference-updated": "Référence du dossier actif mise à jour avec succès",
      "reference-cleared": "Référence du dossier actif effacée avec succès",
    },
    "export-word": "Exporter vers Word",
    "export-error": "Erreur lors de l'exportation vers Word",
    "export-success": "Document exporté avec succès",
    "file-upload-success": "Fichier téléchargé avec succès",
    "files-upload-success": "{{count}} fichiers téléchargés avec succès",
    "file-upload-error": "Erreur lors du téléchargement du/des fichier(s)",
    "file-removed": "Fichier supprimé avec succès",
    "file-remove-error":
      "Impossible de supprimer complètement le fichier. Veuillez réessayer.",
    "link-upload-success": "Lien téléchargé avec succès",
    "link-upload-error": "Erreur lors du téléchargement du lien : {{error}}",
    "qura-login-success": "Connexion à Qura réussie",
    "error-linking-rexor":
      "Erreur lors de la récupération du statut de liaison Rexor",
    "article-transaction-registered":
      "Transaction temporelle enregistrée avec succès",
    "changes-saved": "Modifications enregistrées",
    "missing-data": "Données requises manquantes",
    "save-error": "Erreur lors de l'enregistrement des modifications",
    "invalid-response": "Réponse invalide reçue",
    "streaming-error": "Erreur lors du streaming",
    "qura-auth.error.fill-fields": "Veuillez remplir tous les champs.",
    "show-toast.qura-login-success": "Connexion réussie.",
    "qura-auth.error.invalid-credentials":
      "Nom d'utilisateur ou mot de passe invalide.",
    "qura-auth.connect": "Se connecter à Qura",
    "speech-to-text.microphone-access-error":
      "L'accès au microphone est requis.",
    "show-toast.meta-data-update": "Métadonnées mises à jour avec succès.",
    "appearance.siteSettings.tabIcon": "Icône d'onglet",
    "appearance.siteSettings.fabIconUrl": "URL du favicon",
    "appearance.siteSettings.placeholder": "Entrez l'URL du favicon",
    "appearance.siteSettings.title-placeholder": "Entrez le titre du site",
    "show-toast.workspace-updated": "Espace de travail mis à jour avec succès.",
    "pdr-settings.toast-success": "Paramètres PDR mis à jour avec succès.",
    "pdr-settings.toast-fail": "Échec de la mise à jour des paramètres PDR.",
    "pdr-settings.title": "Paramètres PDR",
    "pdr-settings.description": "Gérez vos paramètres PDR ici.",
    "pdr-settings.desc-end":
      "Assurez-vous que toutes les valeurs sont correctes.",
    "pdr-settings.pdr-token-limit": "Limite de tokens PDR",
    "pdr-settings.pdr-token-limit-desc": "Nombre maximum de tokens pour PDR.",
    "pdr-settings.pdr-token-limit-placeholder":
      "Entrez la limite de tokens PDR",
    "pdr-settings.input-prompt-token-limit":
      "Limite de tokens de prompt d'entrée",
    "pdr-settings.input-prompt-token-limit-desc":
      "Nombre maximum de tokens pour les prompts d'entrée.",
    "pdr-settings.input-prompt-token-limit-placeholder":
      "Entrez la limite de tokens de prompt d'entrée",
    "pdr-settings.response-token-limit": "Limite de tokens de réponse",
    "pdr-settings.response-token-limit-desc":
      "Nombre maximum de tokens pour les réponses.",
    "pdr-settings.response-token-limit-placeholder":
      "Entrez la limite de tokens de réponse",
    "pdr-settings.adjacent-vector-limit": "Limite de vecteurs adjacents",
    "pdr-settings.adjacent-vector-limit-desc":
      "Limite pour les vecteurs adjacents.",
    "pdr-settings.adjacent-vector-limit-placeholder":
      "Entrez la limite de vecteurs adjacents",
    "pdr-settings.keep-pdr-vectors": "Conserver les vecteurs PDR",
    "pdr-settings.keep-pdr-vectors-desc":
      "Option pour conserver les vecteurs PDR.",
    "workspace-update-error": "Erreur : {{error}}",
    "workspace-update-failed":
      "Échec de la mise à jour de l'espace de travail : {{error}}",
    "token-window-exceeded":
      "Le fichier n'a pas été ajouté car il dépasse la limite de tokens disponible du moteur IA actuel",
    "token-limit-exceeded":
      "Fichier trop volumineux pour la fenêtre de contexte disponible. Tokens disponibles : {{available}}",
    "rexor-activity-id-update-success":
      "ID d'activité Rexor mis à jour avec succès",
    "rexor-activity-id-update-error":
      "Échec de la mise à jour de l'ID d'activité Rexor",
    "pin-error": "Impossible de {{action}} le document.",
    "pin-error-detail": "Impossible d'épingler le document. {{message}}",
    "error-fetching-settings":
      "Une erreur s'est produite lors de la récupération des paramètres.",
    "experimental-features-unlocked":
      "Aperçus des fonctionnalités expérimentales débloqués !",
    "workspace-updated-success": "Espace de travail mis à jour avec succès.",
    "legal-task-created": "Tâche juridique créée !",
    "form-submission-failed": "Échec de la soumission du formulaire !",
    "provider-endpoint-discovered":
      "Point de terminaison du fournisseur découvert automatiquement.",
    "failed-save-embedding":
      "Échec de l'enregistrement des paramètres d'intégration : {{error}}",
    "provider-endpoint-discovery-failed":
      "Impossible de détecter automatiquement le point de terminaison du fournisseur. Veuillez l'entrer manuellement.",
    "qura-setting-update-failed": "Échec de la mise à jour du paramètre Qura.",
    "qura-settings-submission-error":
      "Erreur lors de la soumission des paramètres Qura.",
    "preferences-save-error":
      "Une erreur s'est produite lors de l'enregistrement des préférences.",
    "skill-config-updated":
      "Configuration des compétences mise à jour avec succès.",
    "category-deleted-success": "Catégorie supprimée avec succès !",
    "category-deletion-failed": "Échec de la suppression de la catégorie !",
    "embed-chats-export-failed": "Échec de l'exportation des chats intégrés.",
    "api-key-revoked":
      "Clé API de l'extension de navigateur révoquée définitivement",
    "api-key-revoke-failed": "Échec de la révocation de la clé API",
    "connection-string-copied":
      "Chaîne de connexion copiée dans le presse-papiers",
    "connecting-to-extension":
      "Tentative de connexion à l'extension de navigateur...",
    "error-loading-settings":
      "Erreur lors du chargement des paramètres. Veuillez actualiser la page.",
    "error-fetching-prompt-logging":
      "Erreur lors de la récupération du paramètre de journalisation de sortie des prompts.",
    "error-updating-prompt-logging":
      "Échec de la mise à jour du paramètre de journalisation de sortie des prompts.",
    "error-updating-feature":
      "Échec de la mise à jour du statut de la fonctionnalité.",
    "rexor-linkage-state-updated":
      "État de liaison Rexor mis à jour vers {{state}}",
    "rexor-linkage-state-update-error":
      "Échec de la mise à jour de l'état de liaison Rexor : {{error}}",
    "language-update-failed":
      "La mise à jour de la langue a échoué : {{error}}",
    "llm-settings-save-failed":
      "Impossible d'enregistrer les paramètres LLM : {{error}}",
    "setup-error": "Erreur : {{error}}",
    "template-saved": "Modèle de prompt enregistré avec succès",
    "template-saving-error": "Échec de l'enregistrement du modèle : {{error}}",
    "template-reset": "Modèle réinitialisé par défaut",
    "legal-task-updated": "Tâche juridique mise à jour avec succès",
    "legal-task-update-failed": "Échec de la mise à jour de la tâche juridique",
    "model-ref-saved":
      "Modifications du nom du modèle personnalisé et/ou du PDR dynamique personnalisé enregistrées",
    "model-ref-save-failed":
      "Échec de l'enregistrement des paramètres du modèle personnalisé : {{error}}",
    "settings-fetch-failed": "Échec de la récupération des paramètres",
    "api-key-saved": "Clé API enregistrée avec succès",
    "auto-environment-update":
      "Mise à jour automatique de l'environnement démarrée. Cela prendra quelques secondes.",
    "example-prompt-error":
      "Impossible de soumettre l'invite d'exemple: {{error}}",
    "files-processed": "{{count}} fichier(s) traité(s) avec succès",
    "token-validation-failed":
      "Échec de la validation du fichier - nombre de tokens ou limite de taille dépassé",
    "deleted-old-chats": "{{count}} ancien(s) prompt(s) supprimé(s)",
    "pdr-failed": "Échec du PDR du document : {{message}}",
    "watch-failed": "Échec de la surveillance du document : {{message}}",
    "pdr-added": "Document ajouté à la récupération de document parent",
    "pdr-removed": "Document supprimé de la récupération de document parent",
    "unsaved-changes":
      "Vous avez des modifications non enregistrées. Êtes-vous sûr de vouloir annuler?",
  },
  // =========================
  // TOAST MESSAGES
  // =========================
  toast: {
    export: {
      success: "{{name}} exporté avec succès",
      failed: "Échec de l'exportation",
      "embed-chats-success":
        "Chats intégrés exportés avec succès sous le nom {{name}}",
      "embed-chats-failed": "Échec de l'exportation des chats intégrés",
    },
    profile: {
      "upload-failed":
        "Échec du téléchargement de la photo de profil : {{error}}",
      "remove-failed":
        "Échec de la suppression de la photo de profil : {{error}}",
    },
    workspace: {
      "create-failed":
        "Échec de la création de l'espace de travail : {{error}}",
      "update-failed":
        "Échec de la mise à jour de l'espace de travail : {{error}}",
      "thread-create-failed": "Impossible de créer le fil : {{error}}",
      "thread-update-failed": "Le fil n'a pas pu être mis à jour : {{message}}",
    },
    errors: {
      "file-move": "Erreur lors du déplacement des fichiers : {{message}}",
      "thread-create": "Impossible de créer le fil - {{error}}",
      "connector-error": "{{error}}",
      "generic-error": "{{error}}",
      "failed-update-user":
        "Échec de la mise à jour de l'utilisateur : {{error}}",
      "failed-save-llm":
        "Échec de l'enregistrement des paramètres LLM : {{error}}",
      "failed-save-preferences":
        "Échec de l'enregistrement des préférences : {{error}}",
      "failed-clear-logs": "Échec de l'effacement des journaux : {{error}}",
      "failed-create-thread": "Impossible de créer le fil - {{error}}",
      "failed-move-files":
        "Erreur lors du déplacement des fichiers : {{message}}",
      streaming: {
        failed:
          "Une erreur s'est produite lors du streaming de la réponse, comme le moteur IA étant hors ligne ou surchargé.",
        code: "Code",
        unknown: "Erreur inconnue.",
      },
    },
    success: {
      "workspace-created":
        "Espace de travail créé avec succès ! Redirection vers l'accueil...",
      "generic-success": "{{message}}",
    },
    "prompt-output-logging": {
      enabled: "La journalisation de sortie des prompts a été activée",
      disabled: "La journalisation de sortie des prompts a été désactivée",
      error:
        "Erreur lors de la soumission des paramètres de journalisation de sortie des prompts.",
    },
    "welcome-messages": {
      "update-failed":
        "Échec de la mise à jour des messages de bienvenue : {{error}}",
      "drafting-update-failed":
        "Échec de la mise à jour des messages de rédaction de documents : {{error}}",
    },
    info: { "file-moved": "{{message}}" },
    document: {
      "move-success": "{{count}} documents déplacés avec succès",
      "pdr-failed": "Échec du PDR du document : {{message}}",
      "watch-failed": "Échec de la surveillance du document : {{message}}",
      "pdr-added": "Document ajouté à la récupération de document parent",
      "pdr-removed": "Document supprimé de la récupération de document parent",
      "pin-success":
        "OBSOLÈTE - Utilisez document.pin-success-added ou document.pin-success-removed à la place",
      "pin-success-added": "Document épinglé à l'espace de travail",
      "pin-success-removed": "Document détaché de l'espace de travail",
    },
    settings: {
      "preferences-failed":
        "Échec de l'enregistrement des préférences : {{error}}",
      "multi-user-failed":
        "Échec de l'activation du mode multi-utilisateur : {{error}}",
      "public-user-failed":
        "Échec de l'activation du mode utilisateur public : {{error}}",
      "password-failed": "Échec de la mise à jour du mot de passe : {{error}}",
      "vector-db-failed":
        "Échec de l'enregistrement des paramètres de base de données vectorielle : {{error}}",
      "llm-failed": "Échec de l'enregistrement des paramètres LLM : {{error}}",
      "llm-save-failed":
        "Échec de l'enregistrement des paramètres {{name}} : {{error}}",
      "preferences-save-failed":
        "Échec de l'enregistrement des préférences : {{error}}",
      "transcription-save-failed":
        "Échec de l'enregistrement des paramètres de transcription : {{error}}",
      "embedding-save-failed":
        "Échec de l'enregistrement des paramètres d'intégration : {{error}}",
      "welcome-messages-failed":
        "Échec de l'enregistrement des messages de bienvenue : {{error}}",
      "welcome-messages-fetch-failed":
        "Impossible de récupérer les messages de bienvenue",
      "welcome-messages-empty": "Veuillez saisir un titre ou un texte",
      "welcome-messages-success":
        "Messages de bienvenue enregistrés avec succès",
      "user-update-failed":
        "Échec de la mise à jour de l'utilisateur : {{error}}",
      "logs-clear-failed": "Échec de l'effacement des journaux : {{error}}",
      "generic-error": "Erreur : {{message}}",
      "prompt-examples-failed":
        "Impossible d'enregistrer les exemples de prompts : {{error}}",
      "prompt-examples-success": "Exemples de prompts enregistrés",
      "prompt-examples-validation":
        "L'exemple {{number}} manque les champs requis : {{fields}}",
      "import-success": "Fil importé avec succès : {{threadName}}",
    },
    experimental: {
      "feature-enabled": "Fonctionnalités expérimentales activées",
      "feature-disabled": "Fonctionnalités expérimentales désactivées",
      "update-failed":
        "Échec de la mise à jour du statut de la fonctionnalité expérimentale",
      "features-enabled":
        "Fonctionnalités expérimentales activées. La page va se recharger.",
      "live-sync": {
        enabled: "Synchronisation des documents en direct activée",
        disabled: "Synchronisation des documents en direct désactivée",
      },
    },
  },
};
