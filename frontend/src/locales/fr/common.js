const TRANSLATIONS = {
  // ----------------------------
  // Common words and labels
  // ----------------------------
  common: {
    examples: "Exemples",
    "workspaces-name": "Nom de l'espace de travail",
    ok: "OK",
    error: "erreur",
    confirm: "Confirmer",
    confirmstart: "Confirmer et démarrer",
    savesuccess: "Paramètres enregistrés avec succès",
    saveerror: "Échec de l'enregistrement des paramètres",
    success: "succès",
    user: "Utilisateur",
    selection: "Sélection du modèle",
    saving: "Enregistrement...",
    save: "Enregistrer les modifications",
    previous: "Page précédente",
    next: "Page suivante",
    cancel: "Annuler",
    "search-placeholder": "Rechercher...",
    "more-actions": "Plus d'actions",
    "delete-message": "Supprimer le message",
    copy: "Copier",
    edit: "Modifier",
    regenerate: "Régénérer",
    "export-word": "Exporter vers Word",
    "stop-generating": "Arrêter la génération",
    "attach-file": "Joindre un fichier à cette conversation",
    home: "Accueil",
    settings: "Paramètres",
    support: "Support",
    "clear-reference": "Effacer la référence",
    "send-message": "Envoyer un message",
    "ask-legal": "Demander des informations juridiques",
    "stop-response": "Arrêter la génération de réponse",
    "contact-support": "Contacter le support",
    "copy-connection": "Copier la chaîne de connexion",
    "auto-connect": "Se connecter automatiquement à l'extension",
    back: "Retour",
    "back-to-workspaces": "Retour aux espaces de travail",
    off: "Désactivé",
    on: "Activé",
    continue: "Continuer",
    rename: "Renommer",
    delete: "Supprimer",
    "default-skill":
      "Cette compétence est activée par défaut et ne peut pas être désactivée.",
    timeframes: "Périodes",
    other: "Autres options",
    placeholder: {
      username: "Mon nom d'utilisateur",
      password: "Votre mot de passe",
      email: "Entrez votre email",
      "support-email": "<EMAIL>",
      website: "https://www.example.com",
      "site-name": "IST Legal",
      "search-llm": "Rechercher un fournisseur LLM spécifique",
      "search-providers": "Rechercher les fournisseurs disponibles",
      "message-heading": "En-tête du message",
      "message-content": "Message",
      "token-limit": "4096",
      "max-tokens": "Nombre maximum de tokens par requête (ex: 1024)",
      "api-key": "Clé API",
      "base-url": "URL de base",
      endpoint: "Point d'accès API",
    },
    tooltip: {
      copy: "Copier dans le presse-papiers",
      delete: "Supprimer cet élément",
      edit: "Modifier cet élément",
      save: "Enregistrer les modifications",
      cancel: "Annuler les modifications",
      search: "Rechercher des éléments",
      add: "Ajouter un nouvel élément",
      remove: "Supprimer l'élément",
      upload: "Télécharger un fichier",
      download: "Télécharger le fichier",
      refresh: "Actualiser les données",
      settings: "Ouvrir les paramètres",
      more: "Plus d'options",
    },
    "default.message": "Entrez votre message ici",
    preview: "Aperçu",
    prompt: "Invite",
    loading: "Chargement...",
    close: "Fermer",
    note: "Note",
    download: "Télécharger au format brut",
    open_in_new_tab: "Ouvrir dans un nouvel onglet avec formatage",
  },

  // ----------------------------
  // Chat Box Drag and Drop
  // ----------------------------
  chatboxdnd: {
    title: "Ajouter un fichier",
    description:
      "Déposez votre fichier ici pour l'ajouter à ce message. Il ne sera pas sauvegardé dans l'espace de travail comme source permanente.",
    "file-prefix": "Fichier :",
    "attachment-tooltip":
      "Ce fichier sera joint à votre message. Il ne sera pas sauvegardé dans l'espace de travail comme source permanente.",
    "uploaded-file-tag": "FICHIER UTILISATEUR TÉLÉCHARGÉ",
  },

  // ----------------------------
  // Binary LLM Selection
  // ----------------------------
  binary_llm_selection: {
    title: "Sélectionner LLM",
    description:
      "Choisissez quel LLM vous souhaitez utiliser pour cette action",
    local: "Local",
    remote: "Distant",
    "secondary-llm-toggle": "Sélection binaire LLM",
    "secondary-llm-toggle-description":
      "Activez cette option pour permettre aux administrateurs de choisir entre deux modèles LLM dans le module de rédaction de documents.",
    "secondary-llm-toggle-status": "Statut : ",
    "secondary-llm-user-level": "Niveau utilisateur LLM secondaire",
    "secondary-llm-user-level-description":
      "Activez cette option pour permettre à TOUS les utilisateurs de choisir entre deux modèles LLM dans l'espace de rédaction de documents.",
  },

  // ----------------------------
  // Popup Confirmations
  // ----------------------------
  deleteWorkspaceConfirmation:
    "Êtes-vous sûr de vouloir supprimer {{name}} ?\nAprès cela, il sera indisponible dans cette instance.\n\nCette action est irréversible.",
  deleteConfirmation:
    "Êtes-vous sûr de vouloir supprimer {{username}} ?\nAprès cela, ils seront déconnectés et ne pourront plus utiliser cette instance.\n\nCette action est irréversible.",
  suspendConfirmation:
    "Êtes-vous sûr de vouloir suspendre {{username}} ?\nAprès cela, ils seront déconnectés et ne pourront pas se reconnecter à cette instance tant qu'un administrateur ne les aura pas réactivés.",
  flushVectorCachesWorkspaceConfirmation:
    "Êtes-vous sûr de vouloir vider les caches vectoriels pour cet espace de travail ?",
  apiKeys: {
    "deactivate-title": "Désactiver la clé API",
    "deactivate-message":
      "Êtes-vous sûr de vouloir désactiver cette clé API ?\nAprès cela, elle ne sera plus utilisable.\n\nCette action est irréversible.",
  },
  // ----------------------------
  // Instance Settings
  // ----------------------------

  // Settings section moved to settings.js

  attachment_context: {
    title: "Fenêtre de Contexte des Pièces Jointes",
    desc: "Contrôlez la quantité de la fenêtre de contexte du LLM pouvant être utilisée pour les pièces jointes.",
    label: "Pourcentage de Contexte des Pièces Jointes",
    help: "Pourcentage de la fenêtre de contexte pouvant être utilisé pour les pièces jointes (10-80%).",
    "toast-success":
      "Pourcentage de contexte des pièces jointes mis à jour avec succès.",
    "toast-error":
      "Échec de la mise à jour du pourcentage de contexte des pièces jointes.",
    "validation-error":
      "Le pourcentage de contexte des pièces jointes doit être compris entre 10 et 80.",
  },

  // ----------------------------
  // CHAT UI SETTINGS
  // ----------------------------
  "chat-ui-settings": {
    title: "Paramètres de l'interface de chat",
    description: "Configurez les paramètres du chat.",
    auto_submit: {
      title: "Soumission automatique de la saisie vocale",
      description:
        "Soumettre automatiquement la saisie vocale après une période de silence",
    },
    auto_speak: {
      title: "Lecture automatique des réponses",
      description: "Lire automatiquement les réponses de l'IA",
    },
  },

  // ----------------------------
  // Qura Buttons
  // ----------------------------
  qura: {
    "copy-to-cora": "Vérification des sources Qura",
    "qura-status": "Le bouton Qura est ",
    "copy-option": "Option de copie",
    "option-quest": "Question",
    "option-resp": "Reponse",
    "role-description":
      "Ajoutez un bouton Qura pour demander des réponses sur Qura.law",
  },

  // ----------------------------
  // Rexor Integration
  // ----------------------------
  rexor: {
    "register-project": "Enregistrer le projet Rexor",
    "project-id": "ID du projet",
    "resource-id": "ID de la ressource",
    "activity-id": "ID de l'activité",
    register: "Enregistrer le projet",
    "invoice-text": "Nombre de recherches Foynet",
  },

  // ----------------------------
  // Login and Sign In Pages
  // ----------------------------
  login: {
    "multi-user": {
      welcome: "Bienvenue à",
      "placeholder-username": "Adresse e-mail",
      "placeholder-password": "Mot de passe",
      login: "Connexion",
      validating: "Validation...",
      "forgot-pass": "Mot de passe oublié",
      "back-to-login": "Retour à la connexion",
      "reset-password": "Réinitialiser le mot de passe",
      reset: "Réinitialiser",
      "reset-password-info":
        "Fournissez les informations nécessaires ci-dessous pour réinitialiser votre mot de passe.",
    },
    "sign-in": {
      start: "Connectez-vous à votre compte",
      end: "compte.",
    },
    button: "connexion",
    password: {
      forgot: "Mot de passe oublié?",
      contact: "Veuillez contacter l'administrateur système.",
    },
    publicMode: "Essayer sans compte",
  },

  // ----------------------------
  // Thread Name Validation Error
  // ----------------------------
  thread_name_error:
    "Le nom de la discussion doit comporter entre 3 et 255 caractères et ne peut contenir que des lettres, des chiffres, des espaces ou des traits d'union.",

  // ----------------------------
  // New Workspace
  // ----------------------------
  "new-workspace": {
    title: "Nouvel espace de travail",
    placeholder: "Mon espace de travail",
    "legal-areas": "Legal Areas",
    create: {
      title: "Créer un nouvel espace de travail",
      description:
        "Après avoir créé cet espace de travail, seuls les administrateurs pourront le voir. Vous pouvez ajouter des utilisateurs après sa création.",
      error: "Erreur : ",
      cancel: "Annuler",
      "create-workspace": "Créer un espace de travail",
    },
  },

  // ----------------------------
  // Workspace Chats
  // ----------------------------
  "workspace-chats": {
    welcome: "Bienvenue dans votre nouvel espace de travail.",
    "desc-start": "Pour commencer, vous pouvez soit",
    "desc-mid": "télécharger un document",
    "desc-or": "ou",
    start: "Pour commencer",
    "desc-end": "envoyer un message.",
    "attached-file": "Fichier joint",
    "attached-files": "Fichiers joints",
    "token-count": "Nombre de tokens",
    "total-tokens": "Nombre total de tokens",
    "context-window": "Fenêtre de contexte disponible",
    "remaining-tokens": "Restant",
    "view-files": "Voir les fichiers joints",
    prompt: {
      send: "Envoyer",
      "send-message": "Envoyer le message",
      placeholder: "Demander des informations juridiques",
      "change-size": "Changer la taille du texte",
      reset: "/réinitialiser",
      clear: "Effacer votre historique de chat et commencer un nouveau chat",
      command: "Commande",
      description: "Description",
      save: "sauver",
      small: "Petit",
      normal: "Normal",
      large: "Grand",
      larger: "Plus grand",
      attach: "Joindre un fichier à cette conversation",
      upgrade: "Améliorez votre prompt",
      upgrading: "Mise à jour de votre prompt",
      "original-prompt": "Invite original:",
      "upgraded-prompt": "Invite améliorée:",
      "edit-prompt":
        "Vous pouvez modifier le nouveau prompt avant de le soumettre",
      "shortcut-tip":
        "Astuce: Appuyez sur Entrée pour accepter les modifications. Utilisez Maj+Entrée pour les nouvelles lignes.",
      "speak-prompt": "Dictez votre prompt",
      "view-agents":
        "Voir tous les agents disponibles que vous pouvez utiliser pour chatter",
      "ability-tag": "Capacité",
      "deep-search": "Recherche Web",
      "deep-search-tooltip":
        "Rechercher sur le web des informations pour améliorer les réponses",
    },
  },

  // ----------------------------
  // Contextual Embedding Settings
  // ----------------------------
  contextual: {
    checkbox: {
      label: "Contextual Embedding",
      hint: "Activez l'incorporation contextuelle pour améliorer le processus d'intégration avec des paramètres supplémentaires",
    },
    systemPrompt: {
      label: "Invite Système",
      placeholder: "Entrez une valeur...",
      description:
        "Exemple : Veuillez fournir un bref contexte pour situer ce fragment dans le document global afin d'améliorer la recherche du fragment. Répondez uniquement avec le contexte succinct et rien d'autre.",
    },
    userPrompt: {
      label: "Invite Utilisateur",
      placeholder: "Entrez une valeur...",
      description:
        "Exemple : <document>\n{file}\n</document>\nVoici le fragment que nous souhaitons situer dans le document complet\n<chunk>\n{chunk}\n</chunk>",
    },
  },

  // ----------------------------
  // DEEP SEARCH SETTINGS
  // ----------------------------
  deep_search: {
    title: "Recherche Approfondie",
    description:
      "Configurez les capacités de recherche web pour les réponses de chat. Lorsqu'activé, le système peut rechercher sur le web des informations pour améliorer les réponses.",
    enable: "Activer la Recherche Approfondie",
    enable_description:
      "Permettre au système de rechercher sur le web des informations lors de la réponse aux requêtes.",
    provider_settings: "Paramètres du Fournisseur",
    provider: "Fournisseur de Recherche",
    model: "Modèle",
    api_key: "Clé API",
    api_key_placeholder: "Entrez votre clé API",
    api_key_placeholder_set:
      "La clé API est définie (entrez une nouvelle clé pour la modifier)",
    api_key_help:
      "Votre clé API est stockée de manière sécurisée et utilisée uniquement pour les requêtes de recherche web.",
    context_percentage: "Pourcentage de Contexte",
    context_percentage_help:
      "Pourcentage de la fenêtre de contexte du LLM à allouer pour les résultats de recherche web (5-20%).",
    fetch_error:
      "Échec de la récupération des paramètres de Recherche Approfondie",
    save_success: "Paramètres de Recherche Approfondie enregistrés avec succès",
    save_error:
      "Échec de l'enregistrement des paramètres de Recherche Approfondie: {{error}}",
    toast_success:
      "Paramètres de Recherche Approfondie enregistrés avec succès",
    toast_error:
      "Échec de l'enregistrement des paramètres de Recherche Approfondie: {{error}}",
    brave_recommended:
      "Brave Search est actuellement le fournisseur recommandé et le plus fiable.",
  },

  // ----------------------------
  // Header
  // ----------------------------
  header: {
    account: "Compte",
    login: "Connexion",
    "sign-out": "Déconnexion",
  },

  // ----------------------------
  // Workspace Overview
  // ----------------------------
  workspace: {
    title: "Espaces de travail de l'instance",
    description:
      "Voici tous les espaces de travail existants sur cette instance. La suppression d'un espace de travail supprimera tous ses chats et paramètres associés.",
    "new-workspace": "Nouveau Espace de Travail",
    name: "Nom",
    link: "Lien",
    users: "Utilisateurs",
    type: "Type",
    "created-on": "Créé le",
    save: "Enregistrer les modifications",
    cancel: "Annuler",
    "sort-by-name": "Trier par nom",
    sort: "Trier par ordre alphabétique",
    unsort: "Restaurer l'ordre initial",
    deleted: {
      title: "Espace de travail introuvable !",
      description:
        "Il semble qu'un espace de travail portant ce nom ne soit pas disponible.",
      homepage: "Retour à l'accueil",
    },
    "no-workspace": {
      title: "Aucun espace de travail disponible",
      description: "Vous n'avez pas encore accès aux espaces de travail.",
      "contact-admin":
        "Veuillez contacter votre administrateur pour demander l'accès.",
      "learn-more": "En savoir plus sur les espaces de travail",
    },
    "no-workspaces":
      "Vous n'avez pas encore d'espaces de travail. Choisissez un domaine juridique à gauche pour commencer.",
    "my-workspaces": "Mes espaces de travail",
    "show-my": "Afficher mes espaces de travail",
    "show-all": "Afficher tous les espaces de travail",
    "creator-id": "Créé par l'utilisateur ID : {{id}}",
    "loading-username": "Chargement du nom d'utilisateur...",
    "cloud-ai": "IA basée sur le cloud",
    "local-ai": "IA locale",
    "welcome-mobile":
      "Appuyez sur le bouton en haut à gauche pour sélectionner un domaine juridique.",
    "today-time": "Aujourd'hui, {{time}}",
    "date-time": "{{day}} {{month}}, {{time}}",
    "ai-type": "Module",
    "latest-activity": "Dernière activité",
  },

  // ----------------------------
  // Workspace Settings Menu
  // ----------------------------
  "workspaces-settings": {
    general: "Paramètres généraux",
    chat: "Paramètres du chat",
    vector: "Base de données vectorielle",
    members: "Membres",
    agent: "Configuration de l'agent",
    "general-settings": {
      "workspace-name": "Nom de l'espace de travail",
      "desc-name":
        "Cela ne changera que le nom d'affichage de votre espace de travail.",
      "assistant-profile": "Image de profil de l'assistant",
      "assistant-image":
        "Personnalisez l'image de profil de l'assistant pour cet espace de travail.",
      "workspace-image": "Image de l'espace de travail",
      "remove-image": "Supprimer l'image de l'espace de travail",
      delete: "Supprimer l'espace de travail",
      deleting: "Suppression de l'espace de travail...",
      update: "Mettre à jour l'espace de travail",
      updating: "Mise à jour de l'espace de travail...",
    },
    "chat-settings": {
      type: "Type de chat",
      private: "Privé",
      standard: "Standard",
      "private-desc-start": "accordera manuellement l'accès à",
      "private-desc-mid": "seulement",
      "private-desc-end": "certains utilisateurs spécifiques.",
      "standard-desc-start": "accordera automatiquement l'accès à",
      "standard-desc-mid": "tous",
      "standard-desc-end": "les nouveaux utilisateurs.",
    },
    users: {
      manage: "Gérer les utilisateurs",
      "workspace-member": "Aucun membre de l'espace de travail",
      username: "Adresse e-mail",
      role: "Rôle",
      date: "Date d'ajout",
      users: "Utilisateurs",
      search: "Rechercher un utilisateur",
      "no-user": "Aucun utilisateur trouvé",
      select: "Tout sélectionner",
      unselect: "Tout déselectionner",
      save: "Sauvegarder",
    },
    "linked-workspaces": {
      title: "Espaces Liés",
      description:
        "Si des espaces de travail sont liés, les données juridiques pertinentes pour la requête seront automatiquement récupérées de chaque domaine lié. Notez que les espaces de travail liés augmenteront le temps de traitement.",
      "linked-workspace": "Aucun espace lié",
      manage: "Gérer les Espaces",
      name: "Nom",
      slug: "Slug",
      date: "Date d'ajout",
      workspaces: "Espaces",
      search: "Rechercher un espace",
      "no-workspace": "Aucun espace trouvé",
      select: "Tout sélectionner",
      unselect: "Désélectionner",
      save: "Enregistrer",
    },
    "delete-workspace": "Supprimer l'espace de travail",
    "delete-workspace-message":
      "Vous êtes sur le point de supprimer l'intégralité de votre {{workspace}} espace de travail. Cela supprimera toutes les incorporations vectorielles de votre base de données vectorielle.\n\nLes fichiers sources d'origine resteront intacts. Cette action est irréversible.",
    "vector-database": {
      reset: {
        title: "Réinitialiser la base de données vectorielle",
        message:
          "Vous êtes sur le point de réinitialiser la base de données vectorielle de votre {{workspace}}. Cela supprimera toutes les incorporations vectorielles actuellement incorporées.\n\nLes fichiers sources d'origine resteront intacts. Cette action est irréversible.",
      },
    },
  },

  // ----------------------------
  // General Appearance
  // ----------------------------
  general: {
    vector: {
      title: "Nombre de vecteurs",
      description:
        "Nombre total de vecteurs dans votre base de données vectorielle.",
      vectors: "Nombre de vecteurs",
    },
    names: {
      description:
        "Cela ne changera que le nom d'affichage de votre espace de travail.",
    },
    message: {
      title: "Messages de chat suggérés",
      description:
        "Personnalisez les messages qui seront suggérés aux utilisateurs de votre espace de travail.",
      add: "Ajouter un nouveau message",
      save: "Enregistrer les messages",
      heading: "Expliquez-moi",
      body: "les avantages de la plateforme",
      message: "Message",
      "new-heading": "Titre",
    },
    pfp: {
      title: "Image de profil de l'assistant",
      description:
        "Personnalisez l'image de profil de l'assistant pour cet espace de travail.",
      image: "Image de l'espace de travail",
      remove: "Supprimer l'image de l'espace de travail",
    },
    delete: {
      delete: "Supprimer l'espace de travail",
      deleting: "Suppression de l'espace de travail...",
      "confirm-start":
        "Vous êtes sur le point de supprimer l'intégralité de votre",
      "confirm-end":
        "espace de travail. Cela supprimera toutes les incorporations vectorielles de votre base de données vectorielle.\n\nLes fichiers sources d'origine resteront intacts. Cette action est irréversible.",
    },
  },

  // ----------------------------
  // Chat Settings
  // ----------------------------
  chat: {
    llm: {
      title: "Fournisseur LLM de l'espace de travail",
      description:
        "Le fournisseur LLM spécifique & modèle qui sera utilisé pour cet espace de travail. Par défaut, il utilise le fournisseur LLM du système et les paramètres.",
      search: "Rechercher tous les fournisseurs LLM",
      "save-error":
        "Échec de l'enregistrement des paramètres {{provider}} : {{error}}",
      setup: "Configurer",
      use: "Pour utiliser",
      "need-setup":
        "vous devrez configurer les informations d'identification suivantes.",
      cancel: "Annuler",
      save: "Enregistrer",
      settings: "paramètres",
      "multi-model": "Ce fournisseur ne prend pas en charge plusieurs modèles.",
      "workspace-use":
        "Votre espace de travail utilisera le modèle configuré dans",
      "model-set": "paramètres système",
      "system-default": "Système par défaut",
      "system-default-desc":
        "Utilisez la préférence LLM du système pour cet espace de travail.",
      "no-selection": "Aucun LLM sélectionné",
      "select-provider": "Sélectionnez un fournisseur LLM",
      "system-standard-name": "LLM par défaut",
      "system-standard-desc":
        "Utilise le LLM principal défini dans les paramètres du système.",
    },
    "speak-prompt": "Dictez votre invite",
    "view-agents":
      "Voir tous les agents disponibles que vous pouvez utiliser pour chatter",
    "ability-tag": "Capacité",
    "change-text-size": "Changer la taille du texte",
    model: {
      title: "Modèle de chat de l'espace de travail",
      description:
        "Le modèle de chat spécifique qui sera utilisé pour cet espace de travail. S'il est vide, le système utilisera la préférence LLM par défaut.",
      wait: "-- en attente des modèles --",
    },
    mode: {
      title: "Mode de chat",
      chat: {
        title: "Chat",
        "desc-start":
          "fournira des réponses avec les connaissances générales du LLM",
        and: "et",
        "desc-end": "le contexte du document trouvé.",
      },
      query: {
        title: "Interrogation",
        "desc-start": "fournira des réponses",
        only: "seulement",
        "desc-end": "si un contexte de document est trouvé.",
      },
    },
    history: {
      title: "Historique des chats",
      "desc-start":
        "Le nombre de chats précédents qui seront inclus dans la mémoire à court terme de la réponse.",
      recommend: "Recommander 20. ",
      "desc-end":
        "Tout ce qui dépasse 45 risque de provoquer des échecs de chat en fonction de la taille du message.",
    },
    prompt: {
      title: "Prompt",
      description:
        "Le prompt qui sera utilisé sur cet espace de travail. Définissez le contexte et les instructions pour que l'IA génère une réponse. Vous devez fournir un prompt soigneusement élaboré pour que l'IA puisse générer une réponse pertinente et précise.",
      placeholder: "Posez votre question ici...",
    },
    refusal: {
      title: "Réponse de refus en mode interrogation",
      "desc-start": "En mode",
      query: "interrogation",
      "desc-end":
        ", vous pouvez souhaiter retourner une réponse de refus personnalisée lorsque aucun contexte n'est trouvé.",
      placeholder: "Désolé, je ne peux pas répondre à cette question",
    },
    temperature: {
      title: "Température du LLM",
      "desc-start":
        'Ce paramètre contrôle à quel point les réponses de votre LLM seront "créatives".',
      "desc-end":
        "Plus la valeur est élevée, plus la réponse sera créative. Pour certains modèles, une valeur trop élevée peut provoquer des réponses incohérentes.",
      hint: "La plupart des LLM acceptent une plage de valeurs. Consultez votre fournisseur LLM pour plus d'informations.",
    },
    max_tokens: {
      title: "Jetons maximum par utilisateur",
      desc: "Définissez le nombre maximum de jetons d'authentification actifs que chaque utilisateur peut avoir. Lorsque ce nombre est dépassé, les jetons plus anciens seront automatiquement supprimés.",
      label: "Jetons maximum",
      help: "La valeur doit être supérieure à 0",
    },
    "dynamic-pdr": {
      title: "PDR Dynamique pour l'Espace de Travail",
      description:
        "Activez ou désactivez le PDR Dynamique pour cet espace de travail.",
      "global-enabled":
        "Le PDR Dynamique est activé globalement et ne peut pas être désactivé pour un espace de travail individuel.",
    },
  },

  "cdb-steps": {
    "close-msg": "Êtes-vous sûr de vouloir annuler le processus ?",
    step1: {
      title: "Générer la liste des sections",
      desc: "Rédiger une liste de sections pour le document à créer.",
    },
    step2: {
      title: "Examiner et décrire les documents",
      desc: "Analyser chaque fichier téléchargé, rédiger des descriptions et les mapper.",
    },
    step3: {
      title: "Identifier le document principal",
      desc: "Déterminer le fichier qui servira de référence principale.",
    },
    step4: {
      title: "Attribuer les documents aux sections",
      desc: "Faire correspondre chaque section avec les documents contenant des informations pertinentes.",
    },
    step5: {
      title: "Identifier les questions juridiques",
      desc: "Extraire les questions juridiques clés pour chaque section avant de rechercher des mémos.",
    },
    step6: {
      title: "Récupérer les mémos juridiques",
      desc: "Générer ou récupérer des mémos juridiques pour les questions de chaque section.",
    },
    step7: {
      title: "Rédiger les sections",
      desc: "Composer chaque section en utilisant le document principal, les fichiers pertinents et les mémos.",
    },
    step8: {
      title: "Finaliser et nettoyer",
      desc: "Combiner toutes les sections, diffuser le document final et supprimer les fichiers temporaires.",
    },
  },

  // ----------------------------
  // Recorded Workspace Chats
  // ----------------------------
  recorded: {
    title: "Conversations de l'espace de travail",
    description:
      "Voici toutes les conversations et messages enregistrés, classés par date de création.",
    export: "Exporter",
    table: {
      id: "Id",
      by: "Envoyé par",
      workspace: "Espace de travail",
      prompt: "Prompt",
      response: "Réponse",
      at: "Envoyé le",
      invoice: "Réf. facture",
      "completion-token": "Jeton de Complétion",
      "prompt-token": "Jeton de Prompt",
    },
    "clear-chats": "Supprimer toutes les conversations actuelles",
    "confirm-clear-chats":
      "Êtes-vous sûr de vouloir effacer toutes les conversations?\n\nCette action est irréversible.",
    "fine-tune-modal": "Commander un modèle Fine-Tune",
    "confirm-delete.chat":
      "Êtes-vous sûr de vouloir supprimer cette conversation?\n\nCette action est irréversible.",
    next: "Page suivante",
    previous: "Page précédente",
    filters: {
      "by-name": "Filtrer par nom d'utilisateur",
      "by-reference": "Numéro de référence",
    },
    bulk_delete_title: "Suppression en masse d'anciennes conversations",
    bulk_delete_description:
      "Supprimer tous les journaux de conversation plus anciens que la période sélectionnée.",
    delete_old_chats: "Supprimer les anciennes conversations",
    total_logs: "Total des journaux",
    filtered_logs: "Journaux filtrés",
    reset_filters: "Réinitialiser les filtres",
    "no-chats-found": "Aucune conversation trouvée",
    "no-chats-description":
      "Aucune conversation ne correspond à vos filtres. Essayez de modifier vos critères de recherche ou de supprimer une période plus ancienne.",
    "deleted-old-chats": "{{count}} ancienne(s) conversation(s) supprimée(s)",
    two_days: "2 jours",
    one_week: "1 semaine",
    two_weeks: "2 semaines",
    one_month: "1 mois",
    two_months: "2 mois",
    three_months: "3 mois",
    total_deleted: "Total des journaux de conversation supprimés",
  },

  // ----------------------------
  // API Keys
  // ----------------------------
  api: {
    title: "Clés API",
    description:
      "Les clés API permettent d'accéder et de gérer cette instance de manière programmatique.",
    link: "Lire la documentation de l'API",
    generate: "Générer une nouvelle clé API",
    table: {
      key: "Clé API",
      by: "Créé par",
      created: "Créé",
    },
    new: {
      title: "Créer une nouvelle clé API",
      description:
        "Une fois créée, la clé API peut être utilisée pour accéder et configurer cette instance de manière programmatique.",
      doc: "Lire la documentation de l'API",
      cancel: "Annuler",
      "create-api": "Créer une clé API",
    },
  },

  // ----------------------------
  // Audio Preferences
  // ----------------------------
  audio: {
    title: "Préférences de Synthèse Vocale",
    provider: "Fournisseur",
    "system-native": "Système natif",
    "desc-speech":
      "Ici, vous pouvez spécifier le type de fournisseurs de synthèse vocale et de reconnaissance vocale à utiliser dans votre expérience. Par défaut, nous utilisons le support intégré du navigateur, mais vous pouvez en utiliser d'autres.",
    "title-text": "Préférences de Synthèse Vocale",
    "desc-text":
      "Ici, vous pouvez spécifier le type de fournisseurs de synthèse vocale à utiliser. Par défaut, nous utilisons le support intégré du navigateur, mais vous pouvez en utiliser d'autres.",
    "desc-config": "Aucune configuration n'est nécessaire pour ce fournisseur.",
    "placeholder-stt": "Rechercher des fournisseurs de reconnaissance vocale",
    "placeholder-tts": "Rechercher des fournisseurs de synthèse vocale",
    "native-stt":
      "Utilise le service STT intégré de votre navigateur s'il est pris en charge.",
    "native-tts":
      "Utilise le service TTS intégré de votre navigateur s'il est pris en charge.",
    "piper-tts":
      "Exécutez les modèles TTS localement dans votre navigateur en privé.",
    "openai-description":
      "Utiliser les voix et la technologie de synthèse vocale d'OpenAI.",
    openai: {
      "api-key": "Clé API",
      "api-key-placeholder": "Clé API OpenAI",
      "voice-model": "Modèle vocal",
    },
    elevenlabs: "Utiliser la technologie de synthèse vocale d'ElevenLabs.",
  },

  // ----------------------------
  // Transcription Preferences
  // ----------------------------
  transcription: {
    title: "Préférence du modèle de transcription",
    description:
      "Ces identifiants et paramètres concernent votre fournisseur de modèle de transcription préféré. Il est important que ces clés soient à jour et correctes pour que la transcription fonctionne.",
    provider: "Fournisseur de transcription",
    "warn-start":
      "L'utilisation du modèle Whisper local sur des machines avec une RAM ou un CPU limité peut bloquer la plateforme lors du traitement de fichiers multimédias.",
    "warn-recommend":
      "Nous recommandons au moins 2 Go de RAM et des fichiers de moins de 10 Mo.",
    "warn-end":
      "Le modèle intégré se téléchargera automatiquement à la première utilisation.",
    "search-audio": "Rechercher des fournisseurs de transcription audio",
    "api-key": "Clé API",
    "api-key-placeholder": "Clé API OpenAI",
    "whisper-model": "Modèle Whisper",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Intégré par défaut",
    "default-built-in-desc":
      "Exécutez un modèle whisper intégré sur cette instance en privé.",
    "openai-name": "OpenAI",
    "openai-desc":
      "Utilisez le modèle OpenAI Whisper-large avec votre clé API.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Nouveau nom de modèle
    "model-size-turbo": "(~810mb)", // Nouvelle taille de modèle
  },

  // ----------------------------
  // Embedding Preferences
  // ----------------------------
  embedding: {
    title: "Préférence d'incorporation",
    "desc-start":
      "Lorsqu'un LLM ne prend pas en charge nativement un moteur d'incorporation, vous devrez peut-être spécifier des identifiants supplémentaires pour incorporer le texte.",
    "desc-end":
      "L'incorporation est le processus de conversion du texte en vecteurs. Ces identifiants sont nécessaires pour transformer vos fichiers et prompts dans un format utilisable par la plateforme.",
    provider: {
      title: "Fournisseur d'incorporation",
      description:
        "Aucune configuration n'est nécessaire lors de l'utilisation du moteur d'incorporation natif de la plateforme.",
      "search-embed": "Rechercher tous les fournisseurs d'incorporation",
      select: "Sélectionner un fournisseur d'incorporation",
      search: "Rechercher tous les fournisseurs d'incorporation",
    },
    workspace: {
      title: "Préférence d'intégration de l'espace de travail",
      description:
        "Le fournisseur d'intégration et le modèle spécifiques qui seront utilisés pour cet espace de travail. Par défaut, il utilise la préférence d'intégration du système.",
      "multi-model":
        "Le support multi-modèles n'est pas encore pris en charge pour ce fournisseur.",
      "workspace-use": "Cet espace de travail utilisera",
      "model-set": "le modèle défini pour le système.",
      embedding: "Modèle d'intégration de l'espace de travail",
      model:
        "Le modèle d'intégration spécifique qui sera utilisé pour cet espace de travail. S'il est vide, la préférence d'intégration du système sera utilisée.",
      wait: "-- en attente des modèles --",
      setup: "Configurer",
      use: "Pour utiliser",
      "need-setup":
        "en tant qu'intégrateur pour cet espace de travail, vous devez le configurer d'abord.",
      cancel: "Annuler",
      save: "Enregistrer",
      settings: "Paramètres",
      search: "Rechercher tous les fournisseurs d'intégration",
      "need-llm":
        "en tant que LLM pour cet espace de travail, vous devez le configurer d'abord.",
      "save-error":
        "Échec de l'enregistrement des paramètres {{provider}} : {{error}}",
      "system-default": "Paramètres système",
      "system-default-desc":
        "Utiliser les préférences d'intégration système pour cet espace de travail.",
    },
    warning: {
      "switch-model":
        "Le changement du modèle d'intégration brisera les documents incorporés précédemment qui fonctionnent lors du chat. Ils devront être désincorporés de chaque espace de travail et complètement supprimés et rechargés pour pouvoir être incorporés par le nouveau modèle d'intégration.",
    },
  },

  // ----------------------------
  // Vector Database Settings
  // ----------------------------
  vector: {
    title: "Base de données vectorielle",
    description:
      "Ce sont les identifiants et les paramètres qui déterminent le fonctionnement de votre instance de plateforme. Il est important que ces clés soient à jour et correctes.",
    provider: {
      title: "Fournisseur de base de données vectorielle",
      description: "Aucune configuration n'est nécessaire pour LanceDB.",
      "search-db":
        "Rechercher tous les fournisseurs de bases de données vectorielles",
    },
    search: {
      title: "Mode de recherche vectorielle",
      mode: {
        "globally-enabled":
          "Ce paramètre est contrôlé globalement dans les paramètres système. Visitez les paramètres système pour modifier le comportement de reclassement.",
        default: "Recherche standard",
        "default-desc":
          "Recherche de similarité vectorielle standard sans reclassement.",
        "accuracy-optimized": "Optimisé pour la précision",
        "accuracy-desc":
          "Reclasse les résultats pour améliorer la précision en utilisant l'attention croisée.",
      },
    },
    "max-context": "Nombre maximal d'extraits de contexte",
  },

  // =========================
  // VECTOR DATABASE (WORKSPACE)
  // =========================
  "vector-workspace": {
    identifier: "Identifiant de la base de données vectorielle",
    snippets: {
      title: "Nombre maximal d'extraits de contexte",
      description:
        "Ce paramètre contrôle le nombre maximal d'extraits de contexte qui seront envoyés au LLM par chat ou requête.",
      recommend:
        "La valeur recommandée est d'au moins 30. Définir des valeurs beaucoup plus élevées augmentera le temps de traitement sans nécessairement améliorer la précision selon la capacité du LLM utilisé.",
    },
    doc: {
      title: "Seuil de similarité des documents",
      description:
        "Le score de similarité minimal requis pour qu'une source soit considérée comme liée au chat. Plus le nombre est élevé, plus la source doit être similaire au chat.",
      zero: "Aucune restriction",
      low: "Faible (score de similarité ≥ 0,25)",
      medium: "Moyen (score de similarité ≥ 0,50)",
      high: "Élevé (score de similarité ≥ 0,75)",
    },
  },

  // ----------------------------
  // Embeddable Chat Widgets
  // ----------------------------
  embeddable: {
    title: "Widgets de chat intégrables",
    description:
      "Les widgets de chat intégrables sont des interfaces de chat publiques liées à un seul espace de travail. Ils vous permettent de créer des espaces de travail que vous pouvez publier à l'extérieur.",
    create: "Créer une intégration",
    table: {
      workspace: "Espace de travail",
      chats: "Chats envoyés",
      Active: "Domaines actifs",
    },
  },

  // ----------------------------
  // Embedded Chats History
  // ----------------------------
  "embed-chats": {
    title: "Chats intégrés",
    export: "Exporter",
    description:
      "Voici toutes les conversations et messages enregistrés provenant de toute intégration publiée.",
    table: {
      embed: "Intégration",
      sender: "Expéditeur",
      message: "Message",
      response: "Réponse",
      at: "Envoyé à",
    },
    delete: {
      title: "Supprimer le chat",
      message:
        "Êtes-vous sûr de vouloir supprimer ce chat ? Cette action est irréversible.",
    },
    config: {
      "delete-title": "Intégration supprimer",
      "delete-message":
        "Êtes-vous sûr de vouloir supprimer cette intégration ? Cette action est irréversible.",
      "disable-title": "Intégration désactiver",
      "disable-message":
        "Êtes-vous sûr de vouloir désactiver cette intégration ? Cette action est irréversible.",
      "enable-title": "Intégration activer",
      "enable-message":
        "Êtes-vous sûr de vouloir activer cette intégration ? Cette action est irréversible.",
    },
  },

  // ----------------------------
  // Multi-User Mode
  // ----------------------------
  multi: {
    title: "Mode multi-utilisateur",
    description:
      "Configurez votre instance pour prendre en charge votre équipe en activant le mode multi-utilisateur.",
    enable: {
      "is-enable": "Le mode multi-utilisateur est activé",
      enable: "Activer le mode multi-utilisateur",
      description:
        "Par défaut, vous serez le seul administrateur. En tant qu'administrateur, vous devrez créer des comptes pour tous les nouveaux utilisateurs ou administrateurs. Ne perdez pas votre mot de passe, car seul un administrateur peut réinitialiser les mots de passe.",
      username: "Adresse e-mail du compte administrateur",
      password: "Mot de passe du compte administrateur",
    },
    password: {
      title: "Protection par mot de passe",
      description:
        "Protégez votre instance avec un mot de passe. Si vous l'oubliez, il n'existe pas de méthode de récupération, alors assurez-vous de le sauvegarder.",
    },
    instance: {
      title: "Protéger l'instance par mot de passe",
      description:
        "Par défaut, vous serez le seul administrateur. En tant qu'administrateur, vous devrez créer des comptes pour tous les nouveaux utilisateurs ou administrateurs. Ne perdez pas votre mot de passe, car seul un administrateur peut réinitialiser les mots de passe.",
      password: "Mot de passe de l'instance",
    },
  },

  // ----------------------------
  // Event Logs
  // ----------------------------
  event: {
    title: "Journaux d'événements",
    description:
      "Consultez toutes les actions et événements sur cette instance pour la surveillance.",
    clear: "Effacer les journaux d'événements",
    table: {
      type: "Type d'événement",
      user: "Utilisateur",
      occurred: "Survenu à",
    },
  },

  // ----------------------------
  // Privacy & Data Handling
  // ----------------------------
  privacy: {
    title: "Confidentialité & Gestion des données",
    description:
      "Voici la configuration de la manière dont les fournisseurs tiers et notre plateforme gèrent vos données.",
    llm: "Sélection LLM",
    embedding: "Préférence d'intégration",
    vector: "Base de données vectorielle",
    anonymous: "Télémétrie anonyme activée",
    "desc-event":
      "Les événements n'enregistrent pas l'adresse IP et ne contiennent aucune",
    "desc-id": "information identifiante",
    "desc-cont":
      "données, paramètres, chats ou autres informations non liées à l'utilisation. Pour consulter la liste des balises d'événements collectées, consultez",
    "desc-git": "Github ici",
    "desc-end":
      "En tant que projet open-source, nous respectons votre droit à la confidentialité. Si vous décidez de désactiver la télémétrie, merci de nous faire part de vos retours afin que nous puissions améliorer la plateforme.",
  },

  // ----------------------------
  // Text Splitting & Chunking Preferences
  // ----------------------------
  text: {
    title: "Préférences de découpage et segmentation de texte",
    "desc-start":
      "Parfois, vous devrez peut-être modifier la méthode par défaut de découpage et de segmentation des nouveaux documents avant leur ajout à votre base de données vectorielle.",
    "desc-end":
      "Ne modifiez ceci que si vous comprenez le fonctionnement du découpage de texte et ses implications.",
    "warn-start": "Les modifications ici ne s'appliquent qu'aux",
    "warn-center": "documents nouvellement intégrés",
    "warn-end": ", pas aux documents existants.",
    method: {
      title: "Méthode de découpage de texte",
      "native-explain":
        "Utiliser la taille et le chevauchement de chunks locaux pour le découpage.",
      "jina-explain":
        "Déléguer le chunking/segmentation à la méthode intégrée de Jina.",
      size: {
        title: "Taille des chunks",
        description: "Le nombre maximum de tokens par chunk.",
      },
      jina: {
        api_key: "Clé API Jina",
        api_key_desc:
          "Nécessaire pour utiliser le service de segmentation de Jina. La clé sera stockée dans votre environnement.",
        max_tokens: "Jina : Tokens maximum par chunk",
        max_tokens_desc:
          "Définit les tokens maximum dans chaque chunk pour le segmenteur Jina (maximum 2000 tokens).",
        return_tokens: "Retourner les informations de tokens",
        return_tokens_desc:
          "Inclure le nombre de tokens et les informations du tokenizer dans la réponse.",
        return_chunks: "Retourner les informations de chunks",
        return_chunks_desc:
          "Inclure des informations détaillées sur les chunks générés dans la réponse.",
      },
      "jina-info": "Chunking Jina actif.",
    },
    size: {
      title: "Taille des chunks de texte",
      description:
        "Il s'agit du nombre maximum de caractères autorisés dans un seul vecteur.",
      recommend: "La longueur maximale du modèle d'intégration est",
    },
    overlap: {
      title: "Chevauchement des chunks de texte",
      description:
        "Il s'agit du chevauchement maximum de caractères entre deux chunks de texte adjacents.",
    },
  },

  // ----------------------------
  // Default Chat
  // ----------------------------
  "default-chat": {
    welcome: "Bienvenue à IST Legal.",
    "choose-legal": "Choisissez une zone juridique à gauche.",
  },

  // ----------------------------
  // Invitations
  // ----------------------------
  invites: {
    title: "Invitations",
    description:
      "Créez des liens d'invitation pour permettre aux personnes de votre organisation de s'inscrire. Chaque invitation ne peut être utilisée qu'une seule fois.",
    link: "Créer un lien d'invitation",
    accept: "Accepté par",
    usage: "Utilisation",
    "created-by": "Créé par",
    created: "Créé",
    new: {
      title: "Créer une nouvelle invitation",
      "desc-start":
        "Après création, vous pourrez copier l'invitation et l'envoyer à un nouvel utilisateur qui créera un compte avec le",
      "desc-mid": "rôle par défaut",
      "desc-end":
        "et sera automatiquement ajouté aux espaces de travail sélectionnés.",
      "auto-add": "Ajouter automatiquement l'invité aux espaces de travail",
      "desc-add":
        "Vous pouvez également assigner automatiquement l'utilisateur aux espaces de travail ci-dessous. Par défaut, aucun espace de travail ne sera visible. Vous pourrez assigner des espaces ultérieurement.",
      cancel: "Annuler",
      "create-invite": "Créer l'invitation",
      error: "Erreur : ",
    },
    "link-copied": "Lien d'invitation copié",
    "copy-link": "Copier le lien d'invitation",
    "delete-invite-title": "Désactiver l'invitation",
    "delete-invite-confirmation":
      "Êtes-vous sûr de vouloir désactiver cette invitation ?\nAprès cela, elle ne sera plus utilisable.\n\nCette action est irréversible.",
    status: {
      label: "Statut",
      pending: "En attente",
      disabled: "Désactivé",
      claimed: "Accepté",
    },
  },

  // ----------------------------
  // User Menu
  // ----------------------------
  "user-menu": {
    edit: "Modifier le compte",
    profile: "Photo de profil",
    size: "800 x 800",
    "remove-profile": "Supprimer la photo de profil",
    username: "Adresse e-mail",
    "username-placeholder": "Entrez votre adresse e-mail",
    "new-password": "Nouveau mot de passe",
    "new-password-placeholder": "nouveau mot de passe",
    cancel: "Annuler",
    update: "Mettre à jour le compte",
    language: "Langue préférée",
    email: "Adresse e-mail",
    "email-placeholder": "Entrez votre adresse e-mail",
  },

  feedback: {
    thankYou: "Merci ! Votre retour a été soumis avec succès.",
    emailSendError: "Erreur lors de l'envoi de l'e-mail : ",
    submitFeedbackError: "Erreur lors de la soumission du retour : ",
    attachFile: "Joindre un fichier",
    improvePlatform: "Aidez-nous à améliorer la plateforme !",
    suggestionOrQuestion: "Des suggestions ? Ou des questions ?",
    clickToWrite: "Veuillez cliquer pour nous écrire",
    noFeedback: "Aucun retour disponible",
    previewImage: "Aperçu de l'image",
    filePreview: "Aperçu du fichier",
    noFile: "Aucun fichier joint",
    fullName: "Nom complet",
    fullNamePlaceholder: "Entrez votre nom complet",
    message: "Message",
    messagePlaceholder: "Entrez votre message",
    attachment: "Pièce jointe",
    submit: "Envoyer le feedback",
    submitting: "Envoi en cours...",
    submitSuccess: "Feedback envoyé avec succès",
    submitError: "Échec de l'envoi du feedback",
    imageLoadError: "Impossible de charger l'image",
    unsupportedFile: "Type de fichier non pris en charge",
    validation: {
      fullNameMinLength: "Le nom complet doit comporter au moins 2 caractères",
      fullNameMaxLength:
        "Le nom complet ne peut pas comporter plus de 100 caractères",
      fullNameFormat:
        "Le nom complet ne peut contenir que des lettres, des chiffres, des espaces, des traits de soulignement (_), des points (.) et des tirets (-)",
      messageMinLength: "Le message doit comporter au moins 12 caractères",
      messageMaxLength:
        "Le message ne peut pas comporter plus de 1000 caractères",
      messageMinWords: "Le message doit contenir au moins 4 mots",
      fileType: "Le fichier joint doit être une image JPEG, PNG ou PDF",
      fileSize: "Le fichier joint doit être inférieur à 5MB",
    },
  },

  "feedback-settings": {
    "delete-feedback": "Retour supprimé avec succès !",
    "delete-error": "Le retour n'a pas été supprimé",
    "header-title": "Liste des retours",
    "header-description":
      "Voici la liste complète des retours pour cette instance. Veuillez noter que la suppression des retours est définitive et ne peut être annulée.",
    title: "Bouton de retour des utilisateurs",
    description:
      "Activez ou désactivez le bouton permettant aux utilisateurs de soumettre leurs retours.",
    successMessage: "Le bouton de feedback des utilisateurs a été mis à jour",
    failureUpdateMessage:
      "Échec de la mise à jour du statut du bouton de retour des utilisateurs.",
    errorSubmitting: "Erreur lors de l’envoi des paramètres de retour.",
    errorFetching: "Erreur lors de la récupération des paramètres de retour.",
  },

  // ----------------------------
  // User Settings (Administration)
  // ----------------------------
  "user-setting": {
    description:
      "Voici tous les comptes qui existent sur cette instance. La suppression d'un compte supprimera immédiatement leur accès.",
    "add-user": "Ajouter un utilisateur",
    username: "Adresse e-mail",
    role: "Rôle",
    "economy-id": "ID Économique",
    "economy-id-ph": "Entrez l'identifiant du système économique",
    "economy-id-hint":
      "ID utilisé pour les intégrations avec des systèmes économiques externes (par ex., Rexor)",
    default: "Par défaut",
    manager: "Responsable",
    admin: "Administrateur",
    superuser: "Superuser",
    "date-added": "Date d'ajout",
    "all-domains": "Tous les domaines",
    "other-users": "Autres utilisateurs (Aucun domaine)",
    // Options de tri pour la liste des utilisateurs
    "sort-username": "Trier par nom d'utilisateur",
    "sort-organization": "Trier par organisation",
    edit: "Modifier : ",
    "new-password": "Nouveau mot de passe",
    "password-rule": "Le mot de passe doit comporter au moins 8 caractères.",
    "update-user": "Mettre à jour l'utilisateur",
    placeholder: "Entrez l'adresse e-mail",
    cancel: "Annuler",
    "remove-user": "Supprimer l'utilisateur",
    "remove-user-title": "Supprimer l'utilisateur",
    "remove-user-confirmation":
      "Êtes-vous sûr de vouloir supprimer cet utilisateur ?",
    error: "Erreur : ",
  },

  "login-ui": {
    "show-toast": {
      "update-failed": "Échec de la mise à jour de l'interface de connexion",
      "updated-login-ui": "L'interface de connexion a été mise à jour",
    },
    "visit-website": "Visiter le site web",
    loading: "Chargement ...",
    "rw-login-description":
      "Maximisez la productivité juridique avec notre plateforme alimentée par l'IA !",
  },

  // ----------------------------
  // Sidebar (Threads)
  // ----------------------------
  sidebar: {
    thread: {
      "load-thread": "Chargement des discussions...",
      delete: "Supprimer les discussions sélectionnées",
      deleted: "supprimées",
      "empty-thread": "Discussion",
      default: "Standard",
      "starting-thread": "Démarrage de la discussion...",
      thread: "Nouvelle discussion",
      "rename-message": "Entrez un nouveau nom pour la discussion :",
      "delete-message":
        "Êtes-vous sûr de vouloir supprimer cette discussion ? Cette action ne peut pas être annulée.",
      rename: "Renommer",
      "delete-thread": "Supprimer la discussion",
      "rename-thread-title": "Renommer la discussion",
      "new-name-placeholder": "Entrez le nouveau nom de la discussion",
      "delete-thread-title": "Supprimer la discussion ?",
      "delete-confirmation-message":
        'Êtes-vous sûr de vouloir supprimer la discussion "{{name}}" ? Cette action ne peut pas être annulée.',
    },
  },

  // ----------------------------
  // Embeder (Repository/Content loaders)
  // ----------------------------
  embeder: {
    allm: "Utilisez le fournisseur d'intégration intégré. Aucune configuration !",
    openai: "L'option standard pour la plupart des usages non commerciaux.",
    azure: "L'option entreprise d'OpenAI hébergée sur Azure.",
    localai: "Exécutez des modèles d'intégration localement sur votre machine.",
    ollama: "Exécutez des modèles d'intégration localement sur votre machine.",
    lmstudio:
      "Découvrez, téléchargez et exécutez des milliers de LLM en quelques clics.",
    cohere: "Exécutez des modèles d'intégration puissants de Cohere.",
    voyageai: "Exécutez des modèles d'intégration puissants de Voyage AI.",
    "generic-openai": "Utilisez un modèle d'intégration OpenAI générique.",
    "default.embedder": "Fournisseur d'intégration par défaut",
    jina: "Exécutez des modèles d'intégration puissants de Jina.",
    litellm: "Exécutez des modèles d'intégration puissants de LiteLLM.",
  },

  // ----------------------------
  // Vector DB Descriptions
  // ----------------------------
  vectordb: {
    lancedb:
      "Base de données vectorielle 100 % locale qui fonctionne sur la même instance que la plateforme.",
    chroma:
      "Base de données vectorielle open-source que vous pouvez héberger vous-même ou sur le cloud.",
    pinecone:
      "Base de données vectorielle 100 % cloud pour des cas d'utilisation en entreprise.",
    zilliz:
      "Base de données vectorielle hébergée sur le cloud, conçue pour les entreprises avec conformité SOC 2.",
    qdrant:
      "Base de données vectorielle open-source, locale ou distribuée sur le cloud.",
    weaviate:
      "Base de données vectorielle multimodale open-source, locale ou hébergée sur le cloud.",
    milvus: "Open-source, hautement évolutif et extrêmement rapide.",
    astra: "Recherche vectorielle pour GenAI dans le monde réel.",
  },

  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================

  // Document Builder Page
  "document-builder": {
    title: "Générateur de documents",
    description:
      "Configurez et gérez les invites système pour la génération de documents et les tâches juridiques.",
    save: "Enregistrer les modifications",
    saving: "Enregistrement...",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Invites pour Résumé de Document",
          description:
            "Configurez les invites système et utilisateur pour le Résumé de Document.",
        },
        document_relevance: {
          title: "Invites pour Pertinence du Document",
          description:
            "Configurez les invites système et utilisateur pour la Pertinence du Document.",
        },
        section_drafting: {
          title: "Invites pour Rédaction de Section",
          description:
            "Configurez les invites système et utilisateur pour la Rédaction de Section.",
        },
        section_legal_issues: {
          title: "Invites pour Problèmes Juridiques de Section",
          description:
            "Configurez les invites système et utilisateur pour les Problèmes Juridiques de Section.",
        },
        memo_creation: {
          title: "Invites pour Création de Mémo",
          description: "Configurez les invites pour la Création de Mémo.",
        },
        section_index: {
          title: "Invites pour Index des Sections",
          description: "Configurez les invites pour l'Index des Sections.",
        },
        select_main_document: {
          title: "Invites pour Sélection du Document Principal",
          description:
            "Configurez les invites système et utilisateur pour la Sélection du Document Principal.",
        },
        section_list_from_main: {
          title:
            "Invites pour Liste des Sections à partir du Document Principal",
          description:
            "Configurez les invites système et utilisateur pour la Liste des Sections à partir du Document Principal.",
        },
        section_list_from_summaries: {
          title: "Invites pour Liste des Sections à partir des Résumés",
          description:
            "Configurez les invites système et utilisateur pour la Liste des Sections à partir des Résumés.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Résumé de document (Système)",
      "document-summary-system-description":
        "Invite système instruisant l'IA sur la façon de résumer le contenu d'un document et sa pertinence pour une tâche juridique.",
      "document-summary-user-label": "Résumé de document (Utilisateur)",
      "document-summary-user-description":
        "Modèle d'invite utilisateur pour générer un résumé détaillé du contenu du document par rapport à une tâche juridique spécifique.",

      // Document Relevance
      "document-relevance-system-label": "Pertinence du document (Système)",
      "document-relevance-system-description":
        "Invite système pour évaluer si un document est pertinent pour une tâche juridique, avec une réponse vrai/faux attendue.",
      "document-relevance-user-label": "Pertinence du document (Utilisateur)",
      "document-relevance-user-description":
        "Modèle d'invite utilisateur pour vérifier si le contenu du document est pertinent pour une tâche juridique donnée.",

      // Section Drafting
      "section-drafting-system-label": "Rédaction de section (Système)",
      "section-drafting-system-description":
        "Invite système pour générer une section de document unique dans un style juridique professionnel en utilisant des documents et un contexte spécifiés.",
      "section-drafting-user-label": "Rédaction de section (Utilisateur)",
      "section-drafting-user-description":
        "Modèle d'invite utilisateur pour générer une section spécifique d'un document juridique, en tenant compte du titre, de la tâche, des documents sources et des sections adjacentes.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Identification des problèmes juridiques de section (Système)",
      "section-legal-issues-system-description":
        "Invite système pour identifier des sujets juridiques spécifiques pour lesquels des informations factuelles devraient être récupérées pour soutenir la rédaction d'une section de document.",
      "section-legal-issues-user-label":
        "Identification des problèmes juridiques de section (Utilisateur)",
      "section-legal-issues-user-description":
        "Modèle d'invite utilisateur pour lister les sujets juridiques ou les points de données pour récupérer des informations contextuelles pertinentes pour une section de document spécifique et une tâche juridique.",

      // Memo Creation
      "memo-creation-template-label": "Modèle de création de mémo par défaut",
      "memo-creation-template-description":
        "Modèle d'invite pour créer un mémorandum juridique abordant un problème juridique spécifique, en tenant compte des documents fournis et du contexte de la tâche.",

      // Section Index
      "section-index-system-label": "Index des sections (Système)",
      "section-index-system-description":
        "Invite système pour générer un index structuré des sections pour un document juridique.",

      // Select Main Document
      "select-main-document-system-label":
        "Sélection du document principal (Système)",
      "select-main-document-system-description":
        "Invite système pour identifier le document principal le plus pertinent pour une tâche juridique à partir de plusieurs résumés de documents.",
      "select-main-document-user-label":
        "Sélection du document principal (Utilisateur)",
      "select-main-document-user-description":
        "Modèle d'invite utilisateur pour identifier le document principal pour une tâche juridique basé sur des résumés de plusieurs documents.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Liste des sections à partir du document principal (Système)",
      "section-list-from-main-system-description":
        "Invite système pour créer une liste structurée JSON des sections pour un document juridique basée sur le contenu du document principal et la tâche juridique.",
      "section-list-from-main-user-label":
        "Liste des sections à partir du document principal (Utilisateur)",
      "section-list-from-main-user-description":
        "Modèle d'invite utilisateur pour fournir la tâche juridique et le contenu du document principal afin de générer une liste de sections.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Liste des sections à partir des résumés (Système)",
      "section-list-from-summaries-system-description":
        "Invite système pour créer une liste structurée JSON des sections basée sur des résumés de documents et la tâche juridique, lorsqu'aucun document principal n'existe.",
      "section-list-from-summaries-user-label":
        "Liste des sections à partir des résumés (Utilisateur)",
      "section-list-from-summaries-user-description":
        "Modèle d'invite utilisateur pour fournir la tâche juridique et les résumés de documents afin de générer une liste de sections, lorsqu'aucun document principal n'existe.",
    },

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================

    "toast-success":
      "Invites du générateur de documents mises à jour avec succès.",
    "toast-fail":
      "Échec de la mise à jour des invites du générateur de documents.",
    "toast-fail-load-prompts":
      "Échec du chargement des configurations d'invite.",
    "override-prompt-placeholder":
      "Saisissez une invite personnalisée ici ou laissez vide pour utiliser la valeur par défaut.",
    "view-categories": "Afficher tâches et catégories",
    "hide-categories": "Masquer tâches et catégories",
    "add-task": "Ajouter une tâche juridique",
    "legal-task-generator": {
      title: "Générateur de prompt pour tâche juridique",
      description:
        "Définissez les spécificités d'une tâche juridique et générez un prompt utilisateur personnalisé. Vous pourrez ensuite créer une tâche juridique formelle à partir de ce prompt généré.",
      "task-description-label": "Description principale de la tâche",
      "task-description-desc":
        "Décrivez brièvement l'objectif principal ou le résultat attendu de la tâche juridique (ex. 'Rédiger une lettre de mise en demeure', 'Résumer la jurisprudence sur X'). Cela correspond à la sous-catégorie de la tâche juridique.",
      "task-description-placeholder":
        "ex. Rédiger un accord de confidentialité",
      "specific-instructions-label": "Instructions spécifiques ou savoir-faire",
      "specific-instructions-desc":
        "Fournissez des exigences détaillées, du contexte, des points à inclure/exclure ou des connaissances spécifiques pertinentes pour l'exécution de cette tâche. Cela fera partie de la description détaillée de la tâche juridique.",
      "specific-instructions-placeholder":
        "ex. L'accord de confidentialité doit être mutuel, couvrir le développement logiciel et avoir une durée de 3 ans...",
      "upload-template-label": "Télécharger un modèle DOCX (Optionnel)",
      "upload-template-desc":
        "Si vous avez un fichier .docx qui sert de modèle ou d'exemple pour cette tâche, téléchargez-le ici. Son contenu sera ajouté au prompt généré comme exemple.",
      "upload-button-text": "Télécharger un modèle .docx",
      "template-uploaded":
        "Le contenu du modèle DOCX a été chargé et sera inclus dans le prompt.",
      generating: "Génération du prompt...",
      "generate-button": "Générer une proposition de prompt",
      "suggested-prompt-label":
        "Prompt utilisateur suggéré pour la tâche juridique",
      "create-task-button":
        "Créer une tâche juridique à partir de cette suggestion",
    },
    "create-task": {
      title: "Créer une nouvelle tâche juridique",
      category: {
        name: "Nom de la catégorie",
        desc: "Entrez le nom de la catégorie principale.",
        placeholder: "Entrez le nom de la catégorie",
        type: "Type de catégorie",
        new: "Créer une nouvelle catégorie",
        existing: "Utiliser une catégorie existante",
        select: "Sélectionner une catégorie",
        "select-placeholder": "Sélectionner une catégorie existante",
      },
      subcategory: {
        name: "Nom de la tâche juridique",
        desc: "Entrez le nom de la tâche juridique.",
        placeholder: "Entrez le nom de la tâche juridique",
      },
      description: {
        name: "Description et instructions de l'utilisateur",
        desc: "Les informations et instructions que l'utilisateur verra.",
        placeholder:
          "Décrivez, par exemple, quels types de documents doivent être téléchargés dans l'espace de travail pour obtenir le meilleur résultat possible",
      },
      prompt: {
        name: "Prompt de la tâche juridique",
        desc: "Entrez le prompt qui sera utilisé pour cette tâche juridique. Vous pouvez également télécharger des documents d'exemple en utilisant les boutons ci-dessus pour ajouter des exemples de contenu à votre prompt.",
        placeholder:
          "Entrez le prompt de la tâche juridique ou téléchargez des documents d'exemple pour améliorer votre prompt...",
      },
      submitting: "Soumission...",
      submit: "Soumettre",
      validation: {
        "category-required": "Le nom de la catégorie est requis.",
        "subcategory-required": "Le nom de la tâche juridique est requis.",
        "description-required": "La description est requise.",
        "prompt-required": "Le nom du prompt est requis.",
      },
    },
    "edit-task": {
      title: "Modifier la tâche juridique",
      submitting: "Mise à jour...",
      submit: "Mettre à jour la tâche",
      subcategory: {
        name: "Nom de la tâche juridique",
        desc: "Entrez un nouveau nom pour cette tâche juridique",
        placeholder: "Entrez la tâche juridique...",
      },
      description: {
        name: "Description et instructions de l'utilisateur",
        desc: "Fournissez une description et des instructions pour cette tâche juridique",
        placeholder:
          "Entrez la description et les instructions de l'utilisateur...",
      },
      prompt: {
        name: "Prompt de la tâche juridique",
        desc: "Entrez le prompt qui sera utilisé pour cette tâche juridique. Vous pouvez également télécharger des documents d'exemple en utilisant les boutons ci-dessus pour ajouter des exemples de contenu à votre prompt.",
        placeholder:
          "Entrez le prompt de la tâche juridique ou téléchargez des documents d'exemple pour améliorer votre prompt...",
      },
      validation: {
        "subcategory-required": "Le nom de la tâche juridique est requis",
        "description-required": "La description est requise",
        "prompt-required": "L'invite de tâche juridique est requise",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Sélection du document principal requise",
      "requires-main-doc-description":
        "Si cette case est cochée, l'utilisateur doit sélectionner le document principal parmi les fichiers téléchargés lors de l'exécution de cette tâche. Ceci est fortement recommandé pour les tâches juridiques impliquant une réponse à une lettre ou à une soumission au tribunal ou similaire, car cela structure la sortie en fonction du document auquel on répond.",
      "requires-main-doc-placeholder": "Oui ou Non",
      "requires-main-doc-explanation-default":
        "Un choix est nécessaire car cela détermine comment le document sera construit.",
      "requires-main-doc-explanation-yes":
        "Si 'Oui', l'utilisateur devra sélectionner un document principal lors du démarrage de cette tâche juridique. Ce document sera central dans le flux de travail de la tâche.",
      "requires-main-doc-explanation-no":
        "Si 'Non', la tâche juridique se déroulera sans nécessiter un document principal présélectionné. La tâche créera plus dynamiquement une sortie basée sur tous les documents téléchargés et la tâche juridique.",
    },
    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Prompt de génération",
    reviewGeneratorPromptButtonTooltip:
      "Afficher le prompt exact utilisé pour générer le suggérer de tâche juridique. (Admin uniquement)",
    reviewGeneratorPromptTitle: "Prompt de génération",
    reviewPromptLabel: "Le prompt suivant a été utilisé pour la génération :",
    reviewPromptTextareaLabel: "Contenu du Generatorprompt",
  },

  // ----------------------------
  // System Preferences
  // ----------------------------
  system: {
    title: "Préférences du système",
    "desc-start":
      "Voici les paramètres et configurations généraux de votre instance.",
    context_window: {
      title: "Fenêtre de contexte dynamique",
      desc: "Contrôlez la quantité de la fenêtre de contexte LLM utilisée pour les sources supplémentaires.",
      label: "Pourcentage de la fenêtre de contexte",
      help: "Pourcentage de la fenêtre de contexte qui peut être utilisé pour l'enrichissement (10-100%).",
      "toast-success":
        "Pourcentage de la fenêtre de contexte mis à jour avec succès.",
      "toast-error":
        "Échec de la mise à jour du pourcentage de la fenêtre de contexte.",
    },
    "change-login-ui": {
      title: "Sélectionner l'interface de connexion par défaut",
      status: "Actuel",
      subtitle:
        "L'interface sera appliquée comme interface de connexion par défaut pour l'application",
    },
    attachment_context: {
      title: "Fenêtre de Contexte des Pièces Jointes",
      desc: "Contrôlez la quantité de la fenêtre de contexte du LLM pouvant être utilisée pour les pièces jointes.",
      label: "Pourcentage de Contexte des Pièces Jointes",
      help: "Pourcentage de la fenêtre de contexte pouvant être utilisé pour les pièces jointes (10-80%).",
      "toast-success":
        "Pourcentage de contexte des pièces jointes mis à jour avec succès.",
      "toast-error":
        "Échec de la mise à jour du pourcentage de contexte des pièces jointes.",
      "validation-error":
        "Le pourcentage de contexte des pièces jointes doit être compris entre 10 et 80.",
    },
    user: "Les utilisateurs peuvent supprimer des espaces de travail",
    "desc-delete":
      "Permettre aux utilisateurs non administrateurs de supprimer des espaces de travail auxquels ils appartiennent. Cela supprimera l'espace de travail pour tout le monde.",
    limit: {
      title: "Limiter les messages par utilisateur par jour",
      "desc-limit":
        "Limitez le nombre de messages qu'un utilisateur peut envoyer en 24 heures afin d'éviter des coûts excessifs.",
      "per-day": "Messages par jour",
      label: "Limite de messages : ",
    },
    state: {
      enabled: "Activé",
      disabled: "Désactivé",
    },
    "source-highlighting": {
      title: "Activer / Désactiver la mise en évidence des sources",
      description:
        "Masquer ou afficher la mise en évidence des sources pour les utilisateurs.",
      label: "Citation : ",
      "toast-success":
        "Le paramètre de mise en évidence des sources a été mis à jour",
      "toast-error":
        "Échec de la mise à jour du paramètre de mise en évidence des sources",
    },
    "usage-registration": {
      title: "Enregistrement de l'utilisation pour la facturation",
      description:
        "Activez ou désactivez l'enregistrement des factures pour la surveillance du système.",
      label: "La journalisation des factures est ",
    },
    "forced-invoice-logging": {
      title: "Journalisation des factures forcée",
      description:
        "Activez pour exiger une référence de facturation avant d'utiliser la plateforme.",
      label: "La journalisation forcée des factures est ",
    },
    "rexor-linkage": {
      title: "Liaison Rexor",
      description:
        "Activez la liaison Rexor pour obtenir les références des cas actifs du service Rexor.",
      label: "La liaison Rexor est ",
      "activity-id": "ID d'activité",
      "activity-id-description":
        "Entrez l'ID d'activité pour l'intégration Rexor",
    },
    rerank: {
      title: "Paramètres de reclassement",
      description:
        "Configurez les paramètres de reclassement pour améliorer la pertinence des résultats de recherche avec LanceDB.",
      "enable-title": "Activer le reclassement",
      "enable-description":
        "Activez le reclassement pour améliorer la pertinence des résultats de recherche en tenant compte de plus de contexte.",
      status: "Statut du reclassement",
      "vector-count-title": "Vecteurs supplémentaires pour le reclassement",
      "vector-count-description":
        "Nombre de vecteurs supplémentaires à récupérer au-delà du paramètre de nombre de vecteurs de l'espace de travail. Par exemple, si l'espace de travail est configuré pour récupérer 30 vecteurs et que ce paramètre est fixé à 50, un total de 80 vecteurs sera pris en compte pour le reclassement. Un nombre plus élevé peut améliorer la précision mais augmentera le temps de traitement.",
      "lancedb-only": "LanceDB uniquement",
      "lancedb-notice":
        "Cette fonctionnalité n'est disponible que lors de l'utilisation de LanceDB comme base de données vectorielle.",
    },
    save: "Enregistrer les modifications",
  },

  // ----------------------------
  // Support Email
  // ----------------------------
  support: {
    title: "Email de Support",
    description:
      "Définissez l'adresse email de support qui apparaît dans le menu utilisateur lors de la connexion à cette instance.",
    clear: "Effacer",
    save: "Enregistrer",
  },

  // ----------------------------
  // Public Mode
  // ----------------------------
  "public-mode": {
    enable: "Activer le Mode Public-Utilisateur",
    enabled: "Le Mode Public-Utilisateur est activé",
  },

  // ----------------------------
  // Button Labels
  // ----------------------------
  button: {
    delete: "Supprimer",
    edit: "Modifier",
    suspend: "Suspendre",
    unsuspend: "Réactiver",
    accept: "Accepter",
    decline: "Décliner",
    save: "Sauvegarder",
    "flush-vector-caches": "Vider les caches vectoriels",
    saving: "Enregistrement",
    save_llm: "Enregistrer la sélection LLM",
    save_template: "Enregistrer le modèle",
    "reset-to-default": "Réinitialiser par défaut",
    create: "Créer",
    enable: "Activer",
    disable: "Désactiver",
    reset: "Réinitialiser",
    revoke: "Révoquer",
  },

  // ----------------------------
  // New User (Creation)
  // ----------------------------
  "new-user": {
    title: "Ajouter un utilisateur à l'instance",
    username: "Adresse e-mail",
    "username-ph": "Entrez l'adresse e-mail de l'utilisateur",
    password: "Mot de passe",
    "password-ph": "Mot de passe initial de l'utilisateur",
    role: "Rôle",
    default: "Par défaut",
    manager: "Responsable",
    admin: "Administrateur",
    superuser: "Superuser",
    description:
      "Après création, l'utilisateur devra se connecter avec son mot de passe initial pour obtenir l'accès.",
    cancel: "Annuler",
    "add-User": "Ajouter un utilisateur",
    error:
      "Impossible de créer l'utilisateur. Cela peut être dû au fait que l'utilisateur existe déjà ou qu'une erreur système s'est produite.",
    "invalid-email": "Veuillez saisir une adresse e-mail valide.",
    permissions: {
      title: "Autorisations",
      default: [
        "Peut seulement envoyer des chats dans les espaces de travail auxquels il a été ajouté par un administrateur ou responsable.",
        "Ne peut modifier aucun paramètre.",
      ],
      manager: [
        "Peut voir, créer et supprimer des espaces de travail ainsi que modifier les paramètres spécifiques.",
        "Peut créer, mettre à jour et inviter de nouveaux utilisateurs.",
        "Ne peut pas modifier les connexions LLM, vectorDB, intégration, etc.",
      ],
      admin: [
        "Le niveau d'utilisateur le plus élevé.",
        "Peut tout voir et tout faire sur le système.",
      ],
      superuser: [
        "Peut accéder à des pages de paramètres spécifiques comme le Constructeur de Documents et l'Amélioration des Prompts.",
        "Ne peut pas modifier les paramètres globaux comme les configurations LLM, vectorDB.",
        "Peut envoyer des chats dans les espaces de travail auxquels il a été ajouté par un administrateur ou responsable.",
      ],
    },
  },

  // ----------------------------
  // New Embed (Chat Widget)
  // ----------------------------
  "new-embed": {
    title: "Créer une intégration pour l'espace de travail",
    error: "Erreur : ",
    "desc-start":
      "Après création, un lien vous sera fourni que vous pourrez publier sur votre site web avec un simple",
    script: "script",
    tag: "tag.",
    cancel: "Annuler",
    "create-embed": "Créer l'intégration",
    workspace: "Espace de travail",
    "desc-workspace":
      "Ceci est l'espace de travail sur lequel votre fenêtre de chat sera basée. Les paramètres par défaut seront hérités de l'espace de travail, sauf s'ils sont remplacés ici.",
    "allowed-chat": "Méthode de chat autorisée",
    "desc-query":
      "Définissez comment votre chatbot doit fonctionner. Le mode « Interrogation » signifie qu'il répondra uniquement si un document aide à répondre à la requête.",
    "desc-chat":
      "Le mode « Chat » permet de répondre à des questions générales, même si elles ne sont pas liées à l'espace de travail.",
    "desc-response":
      "Chat : Répondre à toutes les questions, quel que soit le contexte",
    "query-response":
      "Interrogation : Répondre uniquement aux messages liés aux documents de l'espace de travail",
    restrict: "Restreindre les demandes par domaine",
    filter:
      "Ce filtre bloquera toute demande provenant d'un domaine non autorisé dans la liste ci-dessous.",
    "use-embed":
      "Laisser ce champ vide signifie que n'importe qui peut utiliser votre intégration sur n'importe quel site.",
    "max-chats": "Chats maximum par jour",
    "limit-chats":
      "Limitez le nombre de chats que cette intégration peut traiter en 24 heures. Zéro signifie illimité.",
    "chats-session": "Chats maximum par session",
    "limit-chats-session":
      "Limitez le nombre de chats qu'un utilisateur peut envoyer dans le cadre de cette intégration en 24 heures. Zéro signifie illimité.",
    "enable-dynamic": "Activer l'utilisation dynamique du modèle",
    "llm-override":
      "Permet de définir un modèle LLM préféré pour remplacer le paramètre par défaut de l'espace de travail.",
    "llm-temp": "Activer la température LLM dynamique",
    "desc-temp":
      "Permet de définir la température du LLM pour remplacer le paramètre par défaut de l'espace de travail.",
    "prompt-override": "Activer le remplacement de l'invite",
    "desc-override":
      "Permet de définir une invite système personnalisée pour remplacer celle par défaut de l'espace de travail.",
  },

  // ----------------------------
  // Toast Notifications
  // ----------------------------

  // Moved to showToast.js

  // ----------------------------
  // LLM Selection Privacy Descriptions
  // ----------------------------
  "llm-selection-privacy": {
    openai: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par OpenAI",
      ],
    },
    azure: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Votre texte et le texte d'intégration ne sont pas visibles par OpenAI ou Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par Anthropic",
      ],
    },
    gemini: {
      description: [
        "Vos conversations sont anonymisées et utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par Google",
      ],
    },
    lmstudio: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur le serveur exécutant LMStudio",
      ],
    },
    localai: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur le serveur exécutant LocalAI",
      ],
    },
    ollama: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur la machine exécutant les modèles Ollama",
      ],
    },
    native: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur cette instance de la plateforme",
      ],
    },
    togetherai: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par TogetherAI",
      ],
    },
    mistral: {
      description: [
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par Mistral",
      ],
    },
    huggingface: {
      description: [
        "Vos invites et le texte des documents utilisés pour créer les réponses sont envoyés à votre point de terminaison HuggingFace géré",
      ],
    },
    perplexity: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par OpenRouter",
      ],
    },
    groq: {
      description: [
        "Vos conversations ne seront pas utilisées pour l'entraînement",
        "Vos invites et le texte des documents utilisés pour créer les réponses sont visibles par Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur le serveur exécutant KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur le serveur exécutant l'interface Text Generation Web UI d'Oobabooga",
      ],
    },
    "generic-openai": {
      description: [
        "Les données sont partagées conformément aux conditions de service de votre fournisseur de point de terminaison générique.",
      ],
    },
    cohere: {
      description: [
        "Les données sont partagées conformément aux conditions de service de cohere.com et aux lois sur la confidentialité locales.",
      ],
    },
    litellm: {
      description: [
        "Votre modèle et vos conversations ne sont accessibles que sur le serveur exécutant LiteLLM",
      ],
    },
  },

  // ----------------------------
  // Vector DB Privacy
  // ----------------------------
  "vector-db-privacy": {
    chroma: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre instance Chroma",
        "L'accès à votre instance est géré par vous",
      ],
    },
    pinecone: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur les serveurs de Pinecone",
        "L'accès à vos données est géré par Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre instance Qdrant (cloud ou auto-hébergée)",
      ],
    },
    weaviate: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre instance Weaviate (cloud ou auto-hébergée)",
      ],
    },
    milvus: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre instance Milvus (cloud ou auto-hébergée)",
      ],
    },
    zilliz: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre cluster cloud Zilliz.",
      ],
    },
    astra: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés sur votre base de données cloud AstraDB.",
      ],
    },
    lancedb: {
      description: [
        "Vos vecteurs et le texte des documents sont stockés de manière privée sur cette instance de la plateforme",
      ],
    },
  },

  // ----------------------------
  // Embedding Engine Privacy
  // ----------------------------
  "embedding-engine-privacy": {
    native: {
      description: [
        "Le texte de vos documents est intégré de manière privée sur cette instance de la plateforme",
      ],
    },
    openai: {
      description: [
        "Le texte de vos documents est envoyé aux serveurs d'OpenAI",
        "Vos documents ne sont pas utilisés pour l'entraînement",
      ],
    },
    azure: {
      description: [
        "Le texte de vos documents est envoyé à votre service Microsoft Azure",
        "Vos documents ne sont pas utilisés pour l'entraînement",
      ],
    },
    localai: {
      description: [
        "Le texte de vos documents est intégré de manière privée sur le serveur exécutant LocalAI",
      ],
    },
    ollama: {
      description: [
        "Le texte de vos documents est intégré de manière privée sur le serveur exécutant Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Le texte de vos documents est intégré de manière privée sur le serveur exécutant LMStudio",
      ],
    },
    cohere: {
      description: [
        "Les données sont partagées conformément aux conditions de service de cohere.com et aux lois locales sur la confidentialité.",
      ],
    },
    voyageai: {
      description: [
        "Les données envoyées aux serveurs de Voyage AI sont partagées conformément aux conditions de service de voyageai.com.",
      ],
    },
  },

  // ----------------------------
  // Prompt Validation UI
  // ----------------------------
  "prompt-validate": {
    edit: "Modifier",
    response: "Réponse",
    prompt: "Invite",
    regenerate: "Régénérer la réponse",
    good: "Bonne réponse",
    bad: "Mauvaise réponse",
    copy: "Copier",
    more: "Plus d'actions",
    fork: "Dupliquer",
    delete: "Supprimer",
    cancel: "Annuler",
    save: "Enregistrer & Soumettre",
  },

  // ----------------------------
  // Citations
  // ----------------------------
  citations: {
    show: "Afficher les citations",
    hide: "Masquer les citations",
    chunk: "Extraits de citation",
    pdr: "Document parent",
    "pdr-h": "Surlignage du document",
    referenced: "Référencé",
    times: "fois.",
    citation: "Citation",
    match: "correspondance",
    download:
      "Ce navigateur ne prend pas en charge les PDF. Veuillez télécharger le PDF pour le visualiser :",
    "download-btn": "Télécharger le PDF",
    view: "Voir les citations",
    sources: "Voir les sources et citations",
    "pdf-collapse-tip":
      "Astuce : Vous pouvez réduire cet onglet PDF en utilisant le bouton dans le coin supérieur gauche",
    "open-in-browser": "Ouvrir dans le navigateur",
    "loading-pdf": "-- chargement du PDF --",
    "error-loading": "Erreur lors du chargement du PDF",
    "no-valid-path": "Aucun chemin PDF valide trouvé",
    "web-search": "Recherche Web",
    "web-search-summary": "Résumé de la recherche Web",
    "web-search-results": "Résultats de la recherche Web",
    "no-web-search-results": "Aucun résultat de recherche Web trouvé",
    "previous-highlight": "Surlignage précédent",
    "next-highlight": "Surlignage suivant",
    "try-alternative-view": "Essayer la vue alternative",
  },

  // ----------------------------
  // Document Drafting Settings
  // ----------------------------
  "document-drafting": {
    title: "Rédaction de Documents",
    description: "Contrôlez vos paramètres de rédaction de documents.",
    configuration: "Configuration",
    "drafting-model": "Modèle LLM de Rédaction",
    enabled: "La rédaction de documents est activée",
    disabled: "La rédaction de documents est désactivée",
    "enabled-toast": "Rédaction de documents activée",
    "disabled-toast": "Rédaction de documents désactivée",
    "desc-settings":
      "L'administrateur peut modifier les paramètres de rédaction de documents pour tous les utilisateurs.",
    "drafting-llm": "Préférence LLM de Rédaction",
    saving: "Enregistrement...",
    save: "Enregistrer les modifications",
    "chat-settings": "Paramètres du Chat",
    "drafting-chat-settings": "Paramètres de chat pour la rédaction",
    "chat-settings-desc":
      "Contrôlez le comportement du chat dans la rédaction de documents.",
    "drafting-prompt": "Invite du système pour la rédaction",
    "drafting-prompt-desc":
      "L'invite du système pour la rédaction est distincte de celle des questions juridiques. Elle définit le contexte et les instructions pour que l'IA génère une réponse. Vous devez fournir un prompt soigneusement élaboré pour que l'IA puisse générer une réponse pertinente.",
    linking: "Lien de document",
    "legal-issues-prompt":
      "Quelles questions juridiques se posent dans le contexte du prompt ?",
    "legal-issues-prompt-desc":
      "Entrez l'invite pour les questions juridiques.",
    "memo-prompt": "Invite pour le mémo",
    "memo-prompt-desc": "Entrez l'invite pour le mémo.",
    message: {
      title: "Messages suggérés pour la rédaction",
      description:
        "Ajoutez des messages suggérés que les utilisateurs peuvent rapidement sélectionner lors de la rédaction.",
      heading: "En-tête de message par défaut",
      body: "Corps de message par défaut",
      "new-heading": "En-tête de message",
      message: "Contenu du message",
      add: "Ajouter un message",
      save: "Enregistrer les messages",
    },
    "combine-prompt": "Invite de Combinaison",
    "combine-prompt-desc":
      "Fournissez l'invite système pour combiner plusieurs réponses en une seule réponse. Cette invite est utilisée à la fois pour combiner les réponses et les mémos DD Linkage, et pour combiner les différentes réponses du traitement Infinity Context.",
    "page-description":
      "Cette page permet d'ajuster les différentes invites utilisées dans les différentes fonctionnalités du module de rédaction de documents. Dans chaque champ de saisie, l'invite par défaut est affichée, qui sera utilisée à moins qu'une invite personnalisée ne soit appliquée sur cette page.",
    "dd-linkage-steps": "Invites appliquées pour les étapes de liaison DD",
    "general-combination-prompt": "Invite de combinaison générale",
    "import-memo": {
      title: "Importer depuis Legal QA",
      "button-text": "Importer la note",
      "search-placeholder": "Rechercher des discussions...",
      import: "Importer",
      importing: "Importation...",
      "no-threads": "Aucune discussion Legal QA trouvée",
      "no-matching-threads":
        "Aucune discussion ne correspond à votre recherche",
      "thread-not-found": "Discussion sélectionnée introuvable",
      "empty-thread": "La discussion sélectionnée n'a aucun contenu à importer",
      "import-success": "Contenu de la discussion importé avec succès",
      "import-error": "Échec de l'importation du contenu de la discussion",
      "import-error-details": "Erreur lors de l'importation: {{details}}",
      "fetch-error":
        "Échec de la récupération des discussions. Veuillez réessayer plus tard.",
      "imported-from": "Importé depuis la discussion Legal QA",
      "unnamed-thread": "Discussion sans nom",
      "unknown-workspace": "Espace de travail inconnu",
      "no-threads-available": "Aucune discussion disponible à importer",
      "create-conversations-first":
        "Créez d'abord des conversations dans un espace de travail Legal QA, puis vous pourrez les importer ici.",
      "no-legal-qa-workspaces":
        "Aucun espace de travail Legal QA avec des discussions actives n'a été trouvé. Créez d'abord des conversations dans un espace de travail Legal QA pour les importer.",
      "empty-workspaces-with-names":
        "Espaces de travail Legal QA trouvés ({{workspaceNames}}) mais ils ne contiennent pas encore de discussions actives. Créez d'abord des conversations dans ces espaces de travail pour les importer.",
      "import-success-with-name":
        "Fil de discussion importé avec succès : {{threadName}}",
    },
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Générateur de prompt utilisateur pour les tâches juridiques",
    description:
      "Proposer automatiquement un prompt personnalisé pour une tâche juridique",
    "task-description": "Description de la tâche juridique",
    "task-description-placeholder":
      "Décrivez la tâche juridique que vous souhaitez accomplir...",
    "specific-instructions": "Instructions spécifiques ou compétences",
    "specific-instructions-description":
      "Incluez toutes les instructions spécifiques ou les compétences spécifiques à cette tâche juridique",
    "specific-instructions-placeholder":
      "Ajoutez des instructions spécifiques, des compétences ou des connaissances pour traiter cette tâche juridique...",
    "suggested-prompt": "Prompt utilisateur suggéré",
    "generation-prompt": "Prompt pour la génération",
    "create-task": "Créer une tâche juridique basée sur cette suggestion",
    generating: "Génération...",
    generate: "Générer une proposition",
    "toast-success": "Prompt généré avec succès",
    "toast-fail": "Échec de la génération du prompt",
    button: "Générer un prompt",
    success: "Prompt généré avec succès",
    error: "Veuillez entrer un nom ou une sous-catégorie d'abord",
    failed: "Échec de la génération du prompt",
  },

  // ----------------------------
  // Document Drafting Dynamic Settings
  // ----------------------------
  "dd-settings": {
    title: "Paramètres de Liaison des Espaces de Travail",
    description:
      "Contrôler les limites de jetons et le comportement des espaces de travail liés",
    "vector-search": {
      title: "Recherche vectorielle",
      description:
        "Lorsque cette fonctionnalité est activée, des recherches vectorielles sémantiques sont effectuées sur tous les espaces de travail liés pour trouver des documents juridiques pertinents. Le système convertit les requêtes des utilisateurs en embeddings vectoriels et les associe aux vecteurs de documents dans la base de données de chaque espace de travail lié. Cette fonctionnalité sert de solution de repli lorsque la génération de mémos est activée mais ne produit pas de résultats. Lorsque la génération de mémos est désactivée, la recherche vectorielle devient la méthode principale pour récupérer des informations des espaces de travail liés. La profondeur de recherche est contrôlée par le paramètre de limite de jetons vectoriels.",
    },
    "memo-generation": {
      title: "Génération de mémos",
      description:
        "Cette fonctionnalité génère automatiquement des mémos juridiques concis à partir des documents trouvés dans les espaces de travail liés. Lorsqu'elle est activée, le système analyse les documents récupérés pour créer des résumés structurés des points juridiques clés, de la jurisprudence et du contexte pertinent. Ces mémos servent de méthode principale pour incorporer des connaissances à partir d'espaces de travail liés. Si la génération de mémos échoue ou ne produit pas de résultats, le système reviendra automatiquement à la recherche vectorielle (si activée) pour garantir que les informations pertinentes sont tout de même récupérées. La longueur et le niveau de détail de ces mémos sont régis par le paramètre de limite de jetons de mémo.",
    },
    "linked-workspace-impact": {
      title: "Impact des jetons des espaces de travail liés",
      description:
        "Contrôle comment le système gère son budget de jetons sur plusieurs espaces de travail liés. Lorsque cette fonctionnalité est activée, le système ajuste dynamiquement les jetons disponibles pour chaque espace de travail en fonction du nombre total d'espaces de travail liés, assurant une répartition équitable des ressources de données. Cela empêche un seul espace de travail de dominer la fenêtre contextuelle tout en maintenant une couverture complète sur tous les domaines juridiques pertinents. Ce paramètre réserve une capacité de jetons spécifiquement pour les résultats de génération de mémos et/ou de recherche vectorielle de chaque espace de travail lié, ce qui peut réduire le nombre total de jetons disponibles pour l'espace de travail principal lorsque de nombreux espaces de travail sont liés.",
    },
    "vector-token-limit": {
      title: "Limite de jetons vectoriels",
      description:
        "Spécifie le nombre maximum de jetons alloués pour les résultats de recherche vectorielle de chaque espace de travail lié. Cette limite s'applique lorsque la recherche vectorielle est utilisée, soit comme méthode principale (lorsque la génération de mémos est désactivée), soit comme solution de repli (lorsque la génération de mémos échoue). Des limites plus élevées permettent une récupération de documents plus complète mais réduisent les jetons disponibles pour d'autres opérations.",
    },
    "memo-token-limit": {
      title: "Limite de jetons de mémo",
      description:
        "Contrôle la longueur maximale des mémos juridiques générés à partir de chaque espace de travail lié. En tant que méthode principale d'intégration des connaissances, ces mémos résument les points juridiques clés des documents de l'espace de travail lié. Si un mémo dépasse cette limite de jetons, il sera rejeté et le système reviendra à la recherche vectorielle (si activée). Des limites plus élevées permettent une analyse juridique plus détaillée mais peuvent réduire le nombre d'espaces de travail liés qui peuvent être incorporés.",
    },
  },

  "workspace-linking": {
    title: "Paramètres de liaison d'espaces de travail",
    description:
      "Contrôler les limites de jetons et le comportement des espaces de travail liés",
    "vector-search": {
      title: "Recherche vectorielle",
      description:
        "Méthode de secours pour trouver des documents pertinents lorsque la génération de mémos échoue ou est désactivée",
    },
    "memo-generation": {
      title: "Génération de mémos",
      description:
        "Méthode principale pour incorporer des connaissances à partir d'espaces de travail liés",
    },
    "linked-workspace-impact": {
      title: "Impact des jetons des espaces de travail liés",
      description:
        "Réserver des jetons pour chaque espace de travail lié proportionnellement à leur nombre",
    },
  },

  // ----------------------------
  // Modale / Document Picker
  // ----------------------------
  modale: {
    document: {
      "doc-processor": "Processeur de documents",
      "processor-offline":
        "Le processeur de documents est actuellement hors ligne. Veuillez réessayer plus tard.",
      "drag-drop": "Cliquez pour télécharger ou glissez-déposez",
      "supported-files": "Fichiers pris en charge : PDF",
      "failed-uploads": "Téléchargements échoués",
      "submit-link": "Ou soumettez un lien vers un document",
      fetch: "Récupérer",
      "file-desc":
        "Note : Le document sera traité et ajouté à votre espace de travail. Cela peut prendre quelques instants.",
      "loading-message":
        "Cela peut prendre du temps pour les documents volumineux",
      "uploading-file": "Téléchargement du fichier...",
      "scraping-link": "Traitement du lien...",
      "moving-documents":
        "Déplacement de {{count}} documents. Veuillez patienter.",
      "exceeds-prompt-limit":
        "Remarque : Le contenu téléchargé dépasse ce qui peut tenir dans une seule requête. Le système traitera les demandes par requêtes multiples, ce qui augmentera le temps de génération de la réponse et pourrait affecter la précision.",
    },
    "justify-betweening": "Traitement...",
    connectors: {
      title: "Connecteurs de données",
      search: "Rechercher des connecteurs de données",
      empty: "Aucun connecteur trouvé.",
    },
  },

  // ----------------------------
  // Data Connectors
  // ----------------------------
  dataConnectors: {
    github: {
      name: "Dépôt GitHub",
      description: "Importez un dépôt GitHub public ou privé en un seul clic.",
      url: "URL du dépôt GitHub",
      "collect-url": "URL du dépôt à collecter",
      "access-token": "Jeton d'accès GitHub",
      optional: "facultatif",
      "rate-limiting": "Jeton d'accès pour éviter la limitation de débit.",
      "desc-picker":
        "Une fois terminé, tous les fichiers seront disponibles pour être intégrés dans les espaces de travail via le sélecteur de documents.",
      branch: "Branche",
      "branch-desc": "Branche à partir de laquelle collecter les fichiers.",
      "branch-loading": "-- chargement des branches disponibles --",
      "desc-start": "Sans renseigner le",
      "desc-token": "jeton d'accès GitHub",
      "desc-connector":
        "ce connecteur ne pourra collecter que les fichiers de niveau supérieur en raison des limites de l'API publique GitHub.",
      "desc-level": "niveau supérieur",
      "desc-end": "",
      "personal-token":
        "Obtenez un jeton d'accès personnel gratuit avec un compte GitHub ici.",
      without: "Sans",
      "personal-token-access": "jeton d'accès personnel",
      "desc-api":
        ", l'API GitHub peut limiter le nombre de fichiers collectés. Vous pouvez",
      "temp-token": "créer un jeton temporaire",
      "avoid-issue": "pour éviter ce problème.",
      submit: "Soumettre",
      "collecting-files": "Collecte des fichiers...",
    },
    "youtube-transcript": {
      name: "Transcription YouTube",
      description:
        "Importez la transcription d'une vidéo YouTube complète à partir d'un lien.",
      url: "URL de la vidéo YouTube",
      "url-video": "URL de la vidéo à transcrire",
      collect: "Collecter la transcription",
      collecting: "Collecte de la transcription...",
      "desc-end":
        "Une fois terminé, la transcription sera disponible pour être intégrée via le sélecteur de documents.",
    },
    "website-depth": {
      name: "Extraction de Liens en Masse",
      description:
        "Scrutez un site web et ses sous-liens jusqu'à une certaine profondeur.",
      url: "URL du site web",
      "url-scrape": "URL du site à scruter",
      depth: "Profondeur",
      "child-links":
        "Nombre de sous-liens à suivre à partir de l'URL d'origine.",
      "max-links": "Liens maximum",
      "links-scrape": "Nombre maximum de liens à extraire",
      scraping: "Scrutation du site web...",
      submit: "Soumettre",
      "desc-scrap":
        "Une fois terminé, toutes les pages récupérées seront disponibles pour être intégrées dans les espaces de travail.",
    },
    confluence: {
      name: "Confluence",
      description: "Importez une page Confluence entière en un seul clic.",
      url: "URL de la page Confluence",
      "url-page": "URL d'une page dans l'espace Confluence",
      username: "Nom d'utilisateur Confluence",
      "own-username": "Votre nom d'utilisateur Confluence",
      token: "Jeton d'accès Confluence",
      "desc-start":
        "Vous devez fournir un jeton d'accès pour l'authentification. Vous pouvez générer un jeton",
      here: "ici",
      access: "Jeton d'accès pour l'authentification.",
      collecting: "Collecte des pages...",
      submit: "Soumettre",
      "desc-end":
        "Une fois terminé, toutes les pages seront disponibles pour l'intégration dans les espaces de travail.",
    },
  },

  // ----------------------------
  // Module Names
  // ----------------------------
  module: {
    "legal-qa": "Questions Juridiques",
    "document-drafting": "Rédaction de Documents",
    "active-case": "Dossier Actif",
  },

  // ----------------------------
  // Fine-Tune Notification
  // ----------------------------
  "fine-tune": {
    title: "Vous avez suffisamment de données pour un ajustement fin !",
    link: "cliquez pour en savoir plus",
    dismiss: "rejeter",
  },

  // ----------------------------
  // Mobile Disclaimer
  // ----------------------------
  mobile: {
    disclaimer:
      "AVERTISSEMENT : Pour une expérience optimale et un accès complet à toutes les fonctionnalités, veuillez utiliser un ordinateur pour accéder à l'application.",
  },

  // ----------------------------
  // SHARE MODAL
  // ----------------------------
  shareModal: {
    title: "Partager {type}",
    titleWorkspace: "Partager l'espace de travail",
    titleThread: "Partager la discussion",
    shareWithUsers: "Partager avec des utilisateurs",
    shareWithOrg: "Partager avec toute l'organisation",
    searchUsers: "Rechercher des utilisateurs...",
    noUsersFound: "Aucun utilisateur trouvé",
    loadingUsers: "Chargement des utilisateurs...",
    errorLoadingUsers: "Erreur lors du chargement des utilisateurs",
    errorLoadingStatus: "Erreur lors du chargement du statut de partage",
    userAccessGranted: "Accès utilisateur accordé avec succès",
    userAccessRevoked: "Accès utilisateur révoqué avec succès",
    orgAccessGranted: "Accès organisation accordé avec succès",
    orgAccessRevoked: "Accès organisation révoqué avec succès",
    errorUpdateUser: "Erreur lors de la mise à jour de l'accès utilisateur",
    errorNoOrg: "Impossible de partager : compte non lié à une organisation",
    errorUpdateOrg: "Erreur lors de la mise à jour de l'accès organisation",
    close: "Fermer",
    grantAccess: "Accorder l'accès",
    revokeAccess: "Révoquer l'accès",
  },

  // ----------------------------
  // Onboarding (New Instance Setup)
  // ----------------------------
  onboarding: {
    welcome: "Bienvenue sur IST Legal LLM",
    "get-started": "Commencez",
    "llm-preference": {
      title: "Préférence LLM",
      description:
        "ISTLLM peut fonctionner avec de nombreux fournisseurs LLM. Ce service gérera le chat.",
      "LLM-search": "Rechercher des fournisseurs LLM",
    },
    "user-setup": {
      title: "Configuration de l'utilisateur",
      description: "Configurez les paramètres de votre compte utilisateur.",
      "sub-title": "Combien de personnes utiliseront cette instance ?",
      "single-user": "Juste moi",
      "multiple-user": "Mon équipe",
      "setup-password": "Souhaitez-vous configurer un mot de passe ?",
      "password-requirment":
        "Les mots de passe doivent comporter au moins 8 caractères.",
      "save-password":
        "Il est important de sauvegarder ce mot de passe car il n'existe pas de méthode de récupération.",
      "password-label": "Mot de passe de l'instance",
      username: "Email de l'administrateur",
      password: "Mot de passe de l'administrateur",
      "account-requirment":
        "L'email doit être valide et ne contenir que des lettres minuscules, chiffres, underscores et tirets sans espaces. Le mot de passe doit comporter au moins 8 caractères.",
      "password-note":
        "Par défaut, vous serez le seul administrateur. Une fois l'onboarding terminé, vous pourrez inviter d'autres utilisateurs ou administrateurs. Ne perdez pas votre mot de passe car seul un administrateur peut réinitialiser les mots de passe.",
    },
    "data-handling": {
      title: "Gestion des données & Confidentialité",
      description:
        "Nous nous engageons à la transparence et au contrôle concernant vos données personnelles.",
      "llm-label": "Sélection LLM",
      "embedding-label": "Préférence d'intégration",
      "database-lablel": "Base de données vectorielle",
      "reconfigure-option":
        "Ces paramètres peuvent être reconfigurés à tout moment dans les paramètres.",
    },
    survey: {
      title: "Bienvenue sur IST Legal LLM",
      description:
        "Aidez-nous à adapter IST Legal LLM à vos besoins. (Facultatif)",
      email: "Quelle est votre adresse e-mail ?",
      usage: "Pour quoi utiliserez-vous la plateforme ?",
      work: "Pour le travail",
      "personal-use": "Pour un usage personnel",
      other: "Autre",
      comment: "Avez-vous des commentaires pour l'équipe ?",
      optional: "(Facultatif)",
      feedback: "Merci pour vos retours !",
    },
    button: {
      yes: "Oui",
      no: "Non",
      "skip-survey": "Passer le sondage",
    },
    placeholder: {
      "admin-password": "Votre mot de passe administrateur",
      "admin-username": "Votre email administrateur",
      "email-example": "<EMAIL>",
      comment:
        "Si vous avez des questions ou commentaires, laissez-les ici et nous vous répondrons. Vous pouvez également envoyer un email à <EMAIL>",
    },
  },

  // ----------------------------
  // Default Settings for Legal Q&A
  // ----------------------------
  "default-settings": {
    "canvas-prompt": "Invite système Canvas",
    "canvas-prompt-desc":
      "Invite pour le système de chat canvas. Utilisée comme invite système pour les interactions de chat canvas.",
    title: "Paramètres par défaut pour Questions/Réponses Juridiques",
    "default-desc":
      "Contrôlez le comportement par défaut des espaces de travail pour les Q&R Juridiques.",
    prompt: "Invite du système Q&R Juridiques",
    "prompt-desc":
      "Le prompt par défaut qui sera utilisé pour les nouveaux espaces de travail Legal Q&A. Définissez le contexte et les instructions pour que l'IA génère une réponse. Vous devez fournir un prompt soigneusement élaboré pour que l'IA puisse générer une réponse pertinente et précise. Pour appliquer ce paramètre à tous les espaces de travail existants et remplacer leurs paramètres personnalisés, utilisez le bouton ci-dessous.",
    "prompt-placeholder": "Saisissez votre prompt ici",
    "toast-success": "Prompt système par défaut mis à jour",
    "toast-fail": "Échec de la mise à jour du prompt système par défaut",
    "apply-all-confirm":
      "Êtes-vous sûr de vouloir appliquer ce prompt à tous les espaces de travail Legal Q&A existants ? Cette action ne peut pas être annulée et remplacera tous les paramètres personnalisés.",
    "apply-to-all":
      "Appliquer à tous les espaces de travail Legal Q&A existants",
    applying: "Application en cours...",
    "toast-apply-success":
      "Prompt par défaut appliqué à {{count}} espaces de travail",
    "toast-apply-fail":
      "Échec de l'application du prompt par défaut aux espaces de travail",
    snippets: {
      title: "Nombre maximal d'extraits de contexte par défaut",
      description:
        "Ce paramètre contrôle le nombre maximum d'extraits de contexte envoyés au LLM pour les nouveaux espaces de travail. Pour appliquer ce paramètre à tous les espaces de travail existants et remplacer leurs paramètres personnalisés, utilisez le bouton ci-dessous.",
      recommend:
        "La valeur recommandée est d'au moins 30. Définir des valeurs beaucoup plus élevées augmentera le temps de traitement sans nécessairement améliorer la précision selon la capacité du LLM utilisé.",
    },
    "rerank-limit": {
      title: "Limite maximale de reclassement",
      description:
        "Ce paramètre contrôle le nombre maximum de documents considérés pour le reclassement. Une valeur plus élevée peut améliorer les résultats mais augmente le temps de traitement.",
      recommend: "Recommandé : 50",
    },
    "validation-prompt": {
      title: "Invite de validation",
      description:
        "Ce paramètre contrôle l'invite de validation par défaut envoyée au LLM pour valider la réponse générée.",
      placeholder:
        "Veuillez valider la réponse suivante en vérifiant l'exactitude des références juridiques et citations par rapport au contexte fourni. Énumérez toute inexactitude ou erreur trouvée.",
    },
    "canvas-upload-prompt": "Invite système pour l'upload Canvas",
    "canvas-upload-prompt-desc":
      "L'invite système utilisée lors du traitement des fichiers téléchargés via le canvas. Cette invite guide le comportement du LLM pour le contenu téléchargé.",
  },

  // ----------------------------
  // Confirm Messages
  // ----------------------------
  "confirm-message": {
    "delete-doc-title": "Supprimer les fichiers et dossiers",
    "delete-doc":
      "Êtes-vous sûr de vouloir supprimer ces fichiers et dossiers ?\nCela supprimera les fichiers du système et les retirera automatiquement de tous les espaces de travail.\nCette action est irréversible.",
  },

  // ----------------------------
  // Perform Legal Task Button
  // ----------------------------
  performLegalTask: {
    title: "Exécuter une tâche juridique",
    noTaskfund: "Aucune tâche juridique disponible",
    noSubtskfund: "Aucune sous-tâche disponible",
    "loading-subcategory": "Chargement des sous-catégories...",
    "select-category": "Sélectionner une catégorie",
    "choose-task": "Choisir une tâche juridique à exécuter",
    "duration-info":
      "Le temps d'exécution d'une tâche juridique dépend du nombre de documents dans l'espace de travail. Avec de nombreux documents et une tâche complexe, cela peut prendre très longtemps.",
    description:
      "Activer ou désactiver le bouton d'exécution de tâche juridique dans la rédaction de documents.",
    successMessage: "La tâche juridique a été {{status}}",
    failureUpdateMessage:
      "Échec de la mise à jour du paramètre de tâche juridique.",
    errorSubmitting:
      "Erreur lors de la soumission des paramètres de tâche juridique.",
    "additional-instructions-label": "Instructions supplémentaires :",
    "custom-instructions-placeholder":
      "Entrez des instructions supplémentaires pour la tâche juridique (facultatif)",
    "warning-title": "Attention",
    "no-files-title": "Aucun fichier disponible",
    "no-files-description":
      "Il n'y a pas de fichiers dans cet espace de travail. Veuillez télécharger au moins un fichier avant d'exécuter une tâche juridique.",
    "settings-button": "Ajouter ou modifier les tâches juridiques disponibles",
    settings: "Paramètres des tâches juridiques",
    subStep: "Sous-tâche en cours ou en attente",
  },

  // ----------------------------
  // Canvas Chat
  // ----------------------------
  canvasChat: {
    title: "Canvas",
    "input-placeholder": "Demandez des informations juridiques",
    chatboxinstruction: "Donnez des instructions pour ajuster la réponse",
    explanation:
      "Cette outil est pour l'édition de la réponse par l'IA dans diverses façons. Les sources pour la réponse sous-jacente sont appliquées, ce qui signifie que vous pouvez demander des clarifications supplémentaires en utilisant le même matériel source que pour la réponse sous-jacente.",
    editAnswer: "Modifier la réponse",
  },

  // ----------------------------
  // Status Labels
  // ----------------------------
  statuses: {
    enabled: "activé",
    disabled: "désactivé",
  },

  // ----------------------------
  // Answer Upgrade Flow
  // ----------------------------

  // Moved to answerUpgrade.js

  // ----------------------------
  // PDR Settings
  // ----------------------------
  "pdr-settings": {
    title: "Paramètres PDR",
    description:
      "Configurez les paramètres de Parent Document Retrieval pour vos espaces de travail.",
    "desc-end":
      "Ces paramètres influent sur le traitement et l'utilisation des documents PDR dans les réponses du chat.",
    "global-override": {
      title: "Remplacement global du PDR dynamique",
      description:
        "Lorsqu'activé, tous les documents de l'espace de travail seront traités comme PDR-activés pour le contexte dans les réponses. Lorsque désactivé, seuls les documents explicitement marqués comme PDR seront utilisés, ce qui peut réduire le contexte disponible et entraîner des réponses de qualité bien inférieure puisque seuls des fragments vectoriels de recherche seront utilisés comme sources dans ces cas.",
    },
    "toast-success": "Paramètres PDR mis à jour",
    "toast-fail": "Échec de la mise à jour des paramètres PDR",
    "adjacent-vector-limit": "Limite de vecteurs adjacents",
    "adjacent-vector-limit-desc":
      "Nombre de vecteurs adjacents utilisés dans l'algorithme PDR.",
    "keep-pdr-vectors": "Conserver les vecteurs PDR",
    "keep-pdr-vectors-desc":
      "Si activé, les documents PDR complets et leurs fragments vectoriels seront inclus dans le contexte, ce qui peut améliorer la qualité mais consommer plus de tokens.",
    "pdr-token-limit-placeholder": "Entrez la limite de tokens PDR",
    "input-prompt-token-limit-placeholder":
      "Entrez la limite de tokens pour le prompt d'entrée",
    "response-token-limit-placeholder":
      "Entrez la limite de tokens pour la réponse",
  },

  // ----------------------------
  // Validation Response
  // ----------------------------
  "validate-response": {
    title: "Résultat de validation",
    "toast-fail": "Impossible de valider la réponse",
    validating: "Validation de la réponse",
    button: "Valider la réponse",
    "adjust-prefix":
      "Appliquez les modifications indiquées à la réponse en fonction de ce retour : ",
    "adjust-button": "Appliquer les changements suggérés",
  },

  // ----------------------------
  // Workspace Names (Predefined)
  // ----------------------------
  "workspace-names": {
    "Administrative Law": "Droit administratif",
    "Business Law": "Droit des affaires",
    "Civil Law": "Droit civil",
    "Criminal Law": "Droit pénal",
    "Diplomatic Law": "Droit diplomatique",
    "Fundamental Law": "Droit fondamental",
    "Human Rights Law": "Droit des droits de l'homme",
    "Judicial Laws": "Lois judiciaires",
    "Security Laws": "Lois sur la sécurité",
    "Taxation Laws": "Lois fiscales",
  },

  // ----------------------------
  // Validate Answer LLM Settings
  // ----------------------------
  "validate-answer": {
    setting: "Validation LLM",
    title: "Préférence de Validation LLM",
    description:
      "Ces identifiants et paramètres concernent votre fournisseur LLM pour la validation. Il est important que ces clés soient correctes pour le bon fonctionnement du système.",
    "toast-success": "Paramètres de validation LLM mis à jour",
    "toast-fail": "Échec de la mise à jour des paramètres de validation LLM",
    saving: "Enregistrement...",
    "save-changes": "Enregistrer les modifications",
  },

  // ----------------------------
  // Active Case
  // ----------------------------
  "active-case": {
    title: "Cas actif",
    placeholder: "Entrez le numéro de référence",
    "select-reference": "Sélectionner la référence",
    "warning-title": "Numéro de référence manquant",
    "warning-message":
      "Aucun numéro de référence n'a été défini. Voulez-vous continuer sans numéro de référence ?",
  },

  // ----------------------------
  // Security (Additional)
  // ----------------------------
  security: {
    "multi-user-mode-permanent":
      "Le mode multi-utilisateur est activé en permanence pour des raisons de sécurité",
  },

  // ----------------------------
  // Errors
  // ----------------------------
  errors: {
    "fetch-models": "Échec de la récupération des modèles personnalisés",
    "fetch-models-error": "Erreur lors de la récupération des modèles",
    "upgrade-error": "Erreur lors de l'amélioration de la réponse",
    "thread-creation-failed":
      "Échec de la création d'un nouveau fil. Veuillez réessayer.",
    common: {
      error: "Erreur",
    },
    streaming: {
      failed:
        "Une erreur s'est produite lors de la diffusion de la réponse, comme par exemple le moteur d'IA qui est hors ligne ou en surcapacité.",
      code: "Code",
      unknown: "Erreur inconnue.",
    },
    workspace: {
      "already-exists": "Un espace de travail avec ce nom existe déjà",
    },
    env: {
      "anthropic-key-format":
        "La clé API Anthropic doit commencer par 'sk-ant-'",
      "openai-key-format": "La clé API OpenAI doit commencer par 'sk-'",
      "jina-key-format": "La clé API Jina doit commencer par 'jina_'",
    },
    auth: {
      "invalid-credentials": "Identifiants de connexion invalides.",
      "account-suspended": "Compte suspendu par l'administrateur.",
      "invalid-password": "Mot de passe invalide fourni",
    },
    "invalid-token-count": "Nombre de tokens invalide",
  },

  // ----------------------------
  // Loading States
  // ----------------------------
  loading: {
    models: "-- chargement des modèles disponibles --",
    "waiting-url": "-- en attente de l'URL --",
    "waiting-api-key": "-- en attente de la clé API --",
    "waiting-models": "-- en attente des modèles --",
  },

  // ----------------------------
  // Charts
  // ----------------------------
  charts: {
    downloading: "Téléchargement de l'image...",
    download: "Télécharger l'image du graphique",
  },

  // ----------------------------
  // LiteLLM Specifics
  // ----------------------------

  // ----------------------------
  // Modals
  // ----------------------------
  modals: {
    warning: {
      title: "Avertissement",
      proceed: "Êtes-vous sûr de vouloir continuer ?",
      cancel: "Annuler",
      confirm: "Confirmer",
      "got-it": "D'accord, compris",
    },
  },

  // ----------------------------
  // Documents
  // ----------------------------
  documents: {
    "pin-info-button": "À propos de l'ancrage",
    "pin-title": "Qu'est-ce que l'ancrage de document ?",
    "pin-desc-1":
      "Lorsque vous ancrez un document, la plateforme injecte son contenu complet dans votre zone de prompt afin que le LLM puisse le comprendre entièrement.",
    "pin-desc-2":
      "Cela fonctionne mieux avec des modèles à grand contexte ou pour de petits fichiers essentiels à la base de connaissances.",
    "pin-desc-3":
      "Si vous n'obtenez pas les réponses souhaitées par défaut, l'ancrage est un excellent moyen d'obtenir des réponses de meilleure qualité en un clic.",
    "pin-add": "Ancrer à l'espace de travail",
    "pin-unpin": "Détacher de l'espace de travail",
    "watch-title": "Que fait la surveillance d'un document ?",
    "watch-desc-1":
      "Lorsque vous surveillez un document, la plateforme synchronise automatiquement son contenu à partir de la source originale à intervalles réguliers. Cela mettra à jour le contenu dans tous les espaces de travail où le document est géré.",
    "watch-desc-2":
      "Cette fonctionnalité est disponible pour le contenu en ligne et ne s'applique pas aux documents téléchargés manuellement.",
    "watch-desc-3": "Vous pouvez gérer la surveillance depuis le",
    "file-manager": "gestionnaire de fichiers",
    "admin-view": "vue administrateur",
    "pdr-add": "Tous les documents ont été ajoutés pour le PDR",
    "pdr-remove": "Tous les documents ont été retirés du PDR",
    empty: "Aucun document trouvé",
    tooltip: {
      date: "Date : ",
      type: "Type : ",
      cached: "En cache",
    },
    actions: {
      removing: "Suppression du fichier de l'espace de travail",
    },
    costs: {
      estimate: "Coût estimé : $",
      minimum: "< $0.01",
    },
    "new-folder": {
      title: "Créer un nouveau dossier",
      "name-label": "Nom du dossier",
      "name-placeholder": "Entrez le nom du dossier",
      create: "Créer le dossier",
    },
    error: {
      "create-folder": "Échec de création du dossier",
    },
  },

  // ----------------------------
  // Legal Questions (FAQ)
  // ----------------------------
  "legal-question": {
    "category-one": "Catégorie Un",
    "category-two": "Catégorie Deux",
    "category-three": "Catégorie Trois",
    "category-four": "Catégorie Quatre",
    "category-five": "Catégorie Cinq",
    "item-one": "Article Un",
    "item-two": "Article Deux",
    "item-three": "Article Trois",
    "item-four": "Article Quatre",
    "item-five": "Article Cinq",
    "item-six": "Article Six",
    "item-seven": "Article Sept",
    example: {
      breakfast: {
        title: "1. Options de Petit-Déjeuner Sain",
        items: [
          "Flocons d'avoine avec fruits frais et miel",
          "Parfait de yaourt grec avec granola",
          "Toast à l'avocat avec œufs pochés",
          "Smoothie vert avec épinards, banane et lait d'amande",
          "Pancakes aux céréales complètes avec sirop d'érable",
        ],
      },
      lunch: {
        title: "2. Idées de Déjeuner Rapide et Facile",
        items: [
          "Wrap de poulet grillé avec salade variée",
          "Salade de quinoa avec légumes rôtis",
          "Sandwich à la dinde sur pain complet",
          "Sauté de légumes avec riz brun",
          "Soupe et salade en accompagnement",
        ],
      },
      dinner: {
        title: "3. Recettes Délicieuses pour le Dîner",
        items: [
          "Saumon cuit au four avec citron et asperges",
          "Spaghetti avec sauce marinara et boulettes",
          "Curry de légumes servi avec riz basmati",
          "Steak grillé avec pommes de terre rôties",
          "Poivrons farcis au quinoa et fromage",
        ],
      },
    },
  },

  // ----------------------------
  // Azure Settings
  // ----------------------------

  // Moved to llmComponents.js

  // ----------------------------
  // KoboldCPP Settings
  // ----------------------------

  // ----------------------------
  // Options (Yes/No)
  // ----------------------------
  options: {
    yes: "Oui",
    no: "Non",
  },

  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "Logo de {{provider}}",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "Modèle d'intégration LM Studio",
      "max-chunk-length": "Longueur maximale du segment",
      "max-chunk-length-help":
        "Longueur maximale des segments de texte pour l'intégration.",
      "hide-endpoint": "Masquer la saisie manuelle du point de terminaison",
      "show-endpoint": "Afficher la saisie manuelle du point de terminaison",
      "base-url": "URL de base de LM Studio",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Entrez l'URL sur laquelle LM Studio fonctionne.",
      "auto-detect": "Détection automatique",
      "loading-models": "--chargement des modèles disponibles--",
      "enter-url-first": "Entrez d'abord l'URL de LM Studio",
      "model-help":
        "Sélectionnez le modèle LM Studio pour l'intégration. Les modèles se chargeront après avoir saisi une URL LM Studio valide.",
      "loaded-models": "Vos modèles chargés",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Modèle d'intégration Ollama",
      "max-chunk-length": "Longueur maximale du segment",
      "max-chunk-length-help":
        "Longueur maximale des segments de texte pour l'intégration.",
      "hide-endpoint": "Masquer la saisie manuelle du point de terminaison",
      "show-endpoint": "Afficher la saisie manuelle du point de terminaison",
      "base-url": "URL de base d'Ollama",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Entrez l'URL sur laquelle Ollama fonctionne.",
      "auto-detect": "Détection automatique",
      "loading-models": "--chargement des modèles disponibles--",
      "enter-url-first": "Entrez d'abord l'URL d'Ollama",
      "model-help":
        "Sélectionnez le modèle Ollama pour l'intégration. Les modèles se chargeront après avoir saisi une URL d'Ollama valide.",
      "loaded-models": "Vos modèles chargés",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Sélection du modèle d'intégration",
      "max-chunk-length": "Longueur maximale du segment",
      "max-chunk-length-help":
        "Longueur maximale des segments de texte pour l'intégration.",
      "api-key": "Clé API",
      optional: "optionnel",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- chargement des modèles disponibles --",
      "waiting-url": "-- en attente de l'URL --",
      "loaded-models": "Vos modèles chargés",
      "model-tooltip": "Consultez les modèles d'intégration supportés sur",
      "model-tooltip-link": "la documentation de LiteLLM",
      "model-tooltip-more":
        "pour plus d'informations sur les modèles disponibles.",
    },
    // Cohere Embedding Options
    cohere: {},
    // Jina Embedding Options
    jina: {
      "api-key": "Clé API Jina",
      "api-key-format": "La clé API Jina doit commencer par 'jina_'",
      "api-key-placeholder": "Entrez votre clé API Jina",
      "api-key-error": "La clé API doit commencer par 'jina_'",
      "model-label": "Sélection du modèle",
      "available-models": "Modèles d'intégration disponibles",
      "embedding-type": "Type d'intégration",
      "available-types": "Types d'intégration disponibles",
      dimensions: "Dimensions",
      "available-dimensions": "Dimensions disponibles",
      task: "Tâche",
      "available-tasks": "Tâches disponibles",
      "late-chunking": "Découpage tardif",
      "late-chunking-help":
        "Activez le découpage tardif pour le traitement des documents",
    },
    // LocalAI Embedding Options

    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "URL de base",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Entrez l'URL de base de votre point d'accès API compatible OpenAI.",
      "model-label": "Modèle d'intégration",
      "model-placeholder":
        "Entrez le nom du modèle (par ex. text-embedding-ada-002)",
      "model-help":
        "Précisez l'identifiant du modèle pour générer des intégrations.",
      "api-key": "Clé API",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Entrez votre clé API pour l'authentification.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "Clé API OpenAI",
      "api-key-placeholder": "Entrez votre clé API OpenAI",
      "model-label": "Sélection du modèle",
      "available-models": "Modèles d'intégration disponibles",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "Clé API VoyageAI",
      "api-key-placeholder": "Entrez votre clé API VoyageAI",
      "model-label": "Sélection du modèle",
      "available-models": "Modèles d'intégration disponibles",
    },
    // Azure OpenAI Embedding Options

    // Native Embedding Options
    native: {
      description:
        "Utilisation du fournisseur d'intégration natif pour le traitement de texte",
    },
  },

  "appearance.siteSettings.tabIcon": "Icône de l'onglet",
  "appearance.siteSettings.fabIconUrl": "URL du favicon",
  "appearance.siteSettings.placeholder": "Entrez l'URL du favicon",
  "appearance.siteSettings.title-placeholder": "Entrez le titre du site",
  "appearance.welcome.heading": "Bienvenue sur IST Legal",
  "appearance.welcome.text": "Sélectionnez le domaine juridique à gauche",

  // =========================
  // AGENTS
  // =========================
  agents: {
    title: "Compétences de l'Agent",
    "agent-skills": "Configurer et gérer les capacités de l'agent",
    "custom-skills": "Compétences Personnalisées",
    back: "Retour",
    "back-to-workspaces": "Retour aux espaces de travail",
    "select-skill": "Sélectionner une compétence à configurer",
    "preferences-saved": "Préférences de l'agent enregistrées avec succès",
    "preferences-failed":
      "Échec de l'enregistrement des préférences de l'agent",
    "skill-status": {
      on: "Activé",
      off: "Désactivé",
    },
  },

  // =========================
  // QURA BUTTONS
  // =========================

  // ----------------------------
  // Browser Extension API Keys
  // ----------------------------
  "browser-extension-api": {
    title: "Clés API",
    description: "Gérez les clés API pour la connexion à cette instance.",
    "generate-key": "Générer une nouvelle clé API",
    "table-headers": {
      "connection-string": "Chaîne de connexion",
      "created-by": "Créé par",
      "created-at": "Créé le",
      actions: "Actions",
    },
    "no-keys": "Aucune clé API trouvée",
    modal: {
      title: "Nouvelle clé API d'extension de navigateur",
      "multi-user-warning":
        "Attention : Vous êtes en mode multi-utilisateurs. Cette clé API permettra l'accès à tous les espaces de travail associés à votre compte. Veuillez la partager avec précaution.",
      "create-description":
        'Après avoir cliqué sur "Créer une clé API", cette instance tentera de créer une nouvelle clé API pour l\'extension de navigateur.',
      "connection-help":
        "Si vous voyez \"Connecté à IST Legal\" dans l'extension, la connexion a réussi. Sinon, veuillez copier la chaîne de connexion et la coller manuellement dans l'extension.",
      cancel: "Annuler",
      "create-key": "Créer une clé API",
      "copy-key": "Copier la clé API",
      "key-copied": "Clé API copiée !",
    },
    tooltips: {
      "copy-connection": "Copier la chaîne de connexion",
      "auto-connect": "Se connecter automatiquement à l'extension",
    },
    confirm: {
      revoke:
        "Êtes-vous sûr de vouloir révoquer cette clé API d'extension de navigateur ?\nAprès cela, elle ne sera plus utilisable.\n\nCette action est irréversible.",
    },
    toasts: {
      "key-revoked":
        "Clé API d'extension de navigateur révoquée définitivement",
      "revoke-failed": "Échec de la révocation de la clé API",
      copied: "Chaîne de connexion copiée dans le presse-papiers",
      connecting: "Tentative de connexion à l'extension de navigateur...",
    },
    "revoke-title": "Révoquer la clé API d'extension de navigateur",
    "revoke-message":
      "Êtes-vous sûr de vouloir révoquer cette clé API d'extension de navigateur ?\nAprès cela, elle ne sera plus utilisable.\n\nCette action est irréversible.",
  },

  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Fonctionnalités Expérimentales",
    description: "Fonctionnalités actuellement en phase de test bêta",
    "live-sync": {
      title: "Synchronisation de Documents en Direct",
      description:
        "Activer la synchronisation automatique du contenu depuis des sources externes",
      "manage-title": "Documents surveillés",
      "manage-description":
        "Voici tous les documents actuellement surveillés dans votre instance. Le contenu de ces documents sera synchronisé périodiquement.",
      "document-name": "Nom du document",
      "last-synced": "Dernière synchronisation",
      "next-refresh": "Temps jusqu'à la prochaine actualisation",
      "created-on": "Créé le",
      "auto-sync": "Synchronisation Automatique du Contenu",
      "sync-description":
        'Activez la possibilité de spécifier une source de contenu à "surveiller". Le contenu surveillé sera régulièrement récupéré et mis à jour dans cette instance.',
      "sync-workspace-note":
        "Le contenu surveillé sera automatiquement mis à jour dans tous les espaces de travail où il est référencé.",
      "sync-limitation":
        "Cette fonctionnalité s'applique uniquement au contenu web, comme les sites web, Confluence, YouTube et les fichiers GitHub.",
      documentation: "Documentation et Avertissements",
      "manage-content": "Gérer le Contenu Surveillé",
    },
    tos: {
      title: "Conditions d'utilisation des fonctionnalités expérimentales",
      description:
        "Les fonctionnalités expérimentales de cette plateforme sont des fonctionnalités que nous testons et qui sont optionnelles. Nous vous informerons de manière proactive de toute préoccupation potentielle avant l'approbation d'une fonctionnalité.",
      "possibilities-title":
        "L'utilisation d'une fonctionnalité sur cette page peut entraîner, sans s'y limiter, les possibilités suivantes :",
      possibilities: {
        "data-loss": "Perte de données.",
        "quality-change": "Changement dans la qualité des résultats.",
        "storage-increase": "Augmentation du stockage.",
        "resource-consumption":
          "Augmentation de la consommation de ressources.",
        "cost-increase":
          "Augmentation des coûts ou de l'utilisation des fournisseurs LLM ou d'embedding.",
        "potential-bugs":
          "Bugs ou problèmes potentiels lors de l'utilisation de cette application.",
      },
      "conditions-title":
        "L'utilisation d'une fonctionnalité expérimentale est accompagnée de la liste non exhaustive de conditions suivantes :",
      conditions: {
        "future-updates":
          "La fonctionnalité peut ne pas exister dans les futures mises à jour.",
        stability: "La fonctionnalité utilisée n'est actuellement pas stable.",
        availability:
          "La fonctionnalité peut ne pas être disponible dans les futures versions, configurations ou abonnements de cette instance.",
        privacy:
          "Vos paramètres de confidentialité seront respectés lors de l'utilisation d'une fonctionnalité bêta.",
        changes:
          "Ces conditions peuvent changer dans les futures mises à jour.",
      },
      "read-more": "Si vous souhaitez en savoir plus, vous pouvez consulter",
      contact: "ou contacter",
      reject: "Refuser & Fermer",
      accept: "Je comprends",
    },
  },

  gemini: {},

  promptLogging: {
    title: "Journalisation des sorties de prompts",
    description:
      "Activer ou désactiver la journalisation des sorties de prompts pour la surveillance du système.",
    label: "Journalisation des sorties de prompts : ",
    state: {
      enabled: "Activé",
      disabled: "Désactivé",
    },
  },

  userAccess: {
    title: "Autoriser l'accès utilisateur",
    description:
      "Activez pour permettre aux utilisateurs réguliers d'accéder aux tâches juridiques. Par défaut, seuls les superusers, les gestionnaires et les administrateurs ont accès.",
    label: "Accès utilisateur : ",
    state: {
      enabled: "Activé",
      disabled: "Désactivé",
    },
  },
  "cdb-llm-preference": {
    title: "Préférence LLM CDB",
    settings: "LLM CDB",
    description: "Configurer le fournisseur LLM pour CDB",
  },
  "template-llm-preference": {
    title: "Préférence LLM de modèle",
    settings: "LLM de modèle",
    description:
      "Sélectionnez le fournisseur LLM utilisé pour générer des modèles de documents. Par défaut, le fournisseur système est utilisé.",
    "toast-success": "Paramètres du LLM de modèle mis à jour",
    "toast-fail": "Échec de la mise à jour du LLM de modèle",
    saving: "Sauvegarde...",
    "save-changes": "Enregistrer les modifications",
  },
  "custom-user-ai": {
    title: "IA utilisateur personnalisée",
    settings: "IA utilisateur personnalisée",
    description: "Configurer le fournisseur d'IA personnalisé",
    "custom-model-reference": "Nom et description du modèle personnalisé",
    "custom-model-reference-description":
      "Ajoutez une référence personnalisée pour ce modèle. Elle sera visible lors de l'utilisation du sélecteur de moteur d'IA utilisateur personnalisé dans le panneau de prompt.",
    "custom-model-reference-name": "Nom du modèle personnalisé",
    "custom-model-reference-description-label":
      "Description du modèle (Optionnel)",
    "custom-model-reference-description-placeholder":
      "Entrez une description optionnelle pour ce modèle",
    "custom-model-reference-name-placeholder":
      "Entrez un nom personnalisé pour ce modèle",
    "model-ref-placeholder":
      "Entrez un nom ou une description personnalisée pour cette configuration de modèle",
    "enter-custom-model-reference": "Entrez un nom personnalisé pour ce modèle",
    "standard-engine": "Moteur IA standard",
    "standard-engine-description":
      "Notre moteur par défaut utile pour la plupart des tâches",
    "dynamic-context-window-percentage":
      "Pourcentage de fenêtre de contexte dynamique",
    "dynamic-context-window-percentage-desc":
      "Contrôle la quantité de la fenêtre de contexte du LLM qui peut être utilisée pour des sources supplémentaires (10-100%)",
    "no-alternative-title": "Aucun modèle alternatif sélectionné",
    "no-alternative-desc":
      "Lorsque cette option est sélectionnée, les utilisateurs n'ont pas la possibilité de choisir un modèle alternatif.",
    "select-option": "Sélectionner un profil IA personnalisé",
    tab: {
      "custom-1": "Moteur personnalisé 1",
      "custom-2": "Moteur personnalisé 2",
      "custom-3": "Moteur personnalisé 3",
      "custom-4": "Moteur personnalisé 4",
      "custom-5": "Moteur personnalisé 5",
      "custom-6": "Moteur personnalisé 6",
    },
    engine: {
      "custom-1": "Moteur personnalisé 1",
      "custom-2": "Moteur personnalisé 2",
      "custom-3": "Moteur personnalisé 3",
      "custom-4": "Moteur personnalisé 4",
      "custom-5": "Moteur personnalisé 5",
      "custom-6": "Moteur personnalisé 6",
      "custom-1-title": "Moteur personnalisé 1",
      "custom-2-title": "Moteur personnalisé 2",
      "custom-3-title": "Moteur personnalisé 3",
      "custom-4-title": "Moteur personnalisé 4",
      "custom-5-title": "Moteur personnalisé 5",
      "custom-6-title": "Moteur personnalisé 6",
      "custom-1-description":
        "Configurer les paramètres du Moteur personnalisé 1",
      "custom-2-description":
        "Configurer les paramètres du Moteur personnalisé 2",
      "custom-3-description":
        "Configurer les paramètres du Moteur personnalisé 3",
      "custom-4-description":
        "Configurer les paramètres du Moteur personnalisé 4",
      "custom-5-description":
        "Configurer les paramètres du Moteur personnalisé 5",
      "custom-6-description":
        "Configurer les paramètres du Moteur personnalisé 6",
    },
    "option-number": "Option {{number}}",
    "llm-provider-selection": "Sélection du fournisseur LLM",
    "llm-provider-selection-desc":
      "Choisissez le fournisseur LLM pour cette configuration IA personnalisée",
    "custom-option": "Option personnalisée",
    saving: "Enregistrement...",
    "save-changes": "Enregistrer les modifications",
    "model-ref-saved":
      "Paramètres du modèle personnalisé enregistrés avec succès",
    "model-ref-save-failed":
      "Échec de l'enregistrement des paramètres du modèle personnalisé : {{error}}",
    "llm-settings-save-failed":
      "Échec de l'enregistrement des paramètres LLM : {{error}}",
    "settings-fetch-failed": "Échec de la récupération des paramètres",
    "llm-saved": "Paramètres LLM enregistrés avec succès",
    "select-provider-first":
      "Veuillez sélectionner un fournisseur LLM pour configurer les paramètres du modèle. Une fois configurée, cette option sera sélectionnable comme moteur d'IA personnalisé dans l'interface utilisateur.",
  },
  toast: {
    settings: {
      "welcome-messages-failed":
        "Échec de l'enregistrement des messages de bienvenue : {{error}}",
      "welcome-messages-fetch-failed":
        "Impossible de récupérer les messages de bienvenue",
      "welcome-messages-empty": "Veuillez saisir un titre ou un texte",
      "welcome-messages-success":
        "Messages de bienvenue enregistrés avec succès",
      "prompt-examples-failed":
        "Impossible d'enregistrer les exemples de prompts : {{error}}",
      "prompt-examples-success": "Exemples de prompts enregistrés",
      "prompt-examples-validation":
        "L'exemple {{number}} manque les champs requis : {{fields}}",
    },
    document: {
      "move-success": "Documents déplacés avec succès",
      "pdr-failed": "Échec de la PDR: {{message}}",
      "watch-failed": "Échec de la surveillance: {{message}}",
      "pdr-added": "Document ajouté à la PDR",
      "pdr-removed": "Document retiré de la PDR",
    },
    experimental: {
      "feature-enabled": "Fonctionnalités expérimentales activées",
      "feature-disabled": "Fonctionnalités expérimentales désactivées",
      "update-failed":
        "Échec de la mise à jour du statut de la fonctionnalité expérimentale",
      "features-enabled":
        "Fonctionnalités expérimentales activées. La page va se recharger.",
      "live-sync": {
        enabled: "Synchronisation des documents en direct activée",
        disabled: "Synchronisation des documents en direct désactivée",
      },
    },
  },
  // =========================
  // CHAT LOGS & PREVIEW
  // =========================
  chat_logs: {
    display_description:
      "Afficher la journalisation des données brutes, ouvrir et télécharger le fichier",
    display_prompt_output: "Afficher les données brutes",
    loading_prompt_output: "Chargement des données brutes...",
    not_available:
      "*** Les données brutes ne sont pas disponibles pour ce chat.",
    token_count: "Tokens (dans toutes les données brutes) : {{count}}",
    token_count_detailed:
      "Tokens vers LLM : {{promptTokens}} | Tokens dans la réponse LLM : {{completionTokens}} | Total des tokens : {{totalTokens}}",
  },

  // LLM Provider specific translations
  "llm-provider.textgenwebui":
    "Connecter à une instance Text Generation WebUI.",
  "llm-provider.litellm": "Connecter à n'importe quel LLM via LiteLLM.",
  "llm-provider.openai-generic":
    "Connecter à n'importe quel point de terminaison API OpenAI compatible.",
  "llm-provider.system-default": "Utiliser le modèle intégré.",

  // =========================
  // INVITATION STRINGS
  // =========================
  invite: {
    "accept-button": "Accepter l'invitation",
    newUser: {
      title: "Créer un nouveau compte",
      usernameLabel: "Nom d'utilisateur",
      passwordLabel: "Mot de passe",
      description:
        "Après avoir créé votre compte, vous pourrez vous connecter avec ces identifiants et commencer à utiliser les espaces de travail.",
    },
  },

  // =========================
  // DOCX EDITOR
  // =========================
  "docx-edit": {
    "edit-instructions":
      "Entrez des instructions sur la façon dont vous souhaitez modifier le document. Soyez précis sur les modifications que vous souhaitez apporter.",
    "instructions-placeholder":
      "par ex., Corriger les erreurs grammaticales, rendre le ton plus formel, ajouter un paragraphe de conclusion...",
    "process-button": "Traiter le document",
    "upload-docx": "Téléverser DOCX",
    "processing-upload": "Traitement...",
    "content-extracted": "Contenu extrait du fichier DOCX",
    "file-type-note": "Seuls les fichiers .docx sont pris en charge",
    "upload-error": "Erreur lors du téléversement du fichier: ",
    "no-instructions": "Veuillez entrer des instructions de modification",
    "process-error": "Erreur lors du traitement du document: ",
    "changes-highlighted": "Document avec modifications surlignées",
    "download-button": "Télécharger le document",
    "start-over-button": "Recommencer",
    "no-document": "Aucun document disponible pour le téléchargement",
    "download-error": "Erreur lors du téléchargement du document: ",
    "download-success": "Document téléchargé avec succès",
    processing: "Traitement du document...",
    "instructions-used": "Instructions utilisées",
    "import-success": "Contenu DOCX importé avec succès",
    "edit-success": "Contenu DOCX mis à jour avec succès",
    "canvas-document-title": "Document Canvas",
    "upload-button": "Importer DOCX",
    "download-as-docx": "Télécharger en DOCX",
    "output-example": "Exemple de sortie",
    "output-example-desc":
      "Téléverser un fichier DOCX pour ajouter un exemple de contenu à votre prompt",
    "content-examples-tag-open": "<EXEMPLE_DE_CONTENU>",
    "content-examples-tag-close": "</EXEMPLE_DE_CONTENU>",
    "content-examples-info":
      "<INFO>Ceci est un exemple du contenu à produire, provenant d'une tâche juridique similaire. Notez que cet exemple de contenu peut être beaucoup plus court ou plus long que le contenu qui doit maintenant être produit.</INFO>",
    "contains-example-content": "[Contient un exemple de contenu]",
  },

  // =========================
  // AGENT CONFIGURATION
  // =========================
  agent: {
    "performance-warning":
      "Les performances des LLMs qui ne supportent pas explicitement l'appel d'outils dépendent fortement de la capacité du modèle à générer des sorties structurées.",
    provider: {
      title: "Fournisseur LLM de l'agent de l'espace de travail",
      description:
        "Le fournisseur et modèle LLM spécifique qui sera utilisé pour l'agent @agent de cet espace de travail.",
      "need-setup":
        "To use {{name}} as this workspace's agent LLM you need to set it up first.", // TODO: Translate
    },
    mode: {
      title: "Mode de l'agent",
      description:
        "Contrôle comment l'agent @agent répondra aux demandes. 'Chat' tentera de générer une réponse conversationnelle, tandis que 'Query' tentera de fournir uniquement une réponse courte à une demande.",
      options: {
        query: "Requête",
        chat: "Chat",
      },
    },
    skill: {
      title: "Compétences de l'agent",
      description:
        "Les agents peuvent utiliser certains outils ou compétences pour accomplir des tâches. L'activation des compétences permet à l'agent d'utiliser des outils pour fournir des réponses plus riches ou contextuelles.",
      "search-skill": "Rechercher une compétence...",
      "no-results": "Aucune compétence trouvée pour cette recherche.",
      "available-skills": "Compétences disponibles",
      "edit-skill": "Modifier cette compétence",
      "skill-name": "Nom de la compétence",
      "skill-description": "Description de la compétence",
      "skill-options": "Options de la compétence",
      "skill-badge-private":
        "Compétence privée : Seuls les administrateurs peuvent modifier ou voir cette compétence",
      "skill-badge-enabled": "Compétence activée",
      "skill-enabled": "Activer la compétence",
    },
    skilloptions: {
      websearch: {
        provider: "Fournisseur de recherche",
        providerDescription:
          "Le fournisseur de recherche spécifique que l'agent utilisera pour rechercher sur le Web.",
        "max-results": "Nombre maximum de résultats de recherche",
        "max-results-description":
          "Le nombre maximum de résultats de recherche à retourner.",
      },
      sqlagent: {
        connection: "Chaîne de connexion à la base de données SQL",
        connectionDescription:
          "La chaîne de connexion complète pour cette base de données SQL.",
        type: "Type de base de données",
        typeDescription: "Le type de base de données SQL auquel se connecter.",
      },
    },
    error: {
      provider: "Impossible de trouver le LLM de l'agent.",
    },
  },

  // =========================
  // AUTH CONFIGURATION
  // =========================
  auth: {
    "multi-user-mode-permanent":
      "Le mode multi-utilisateur est activé en permanence pour des raisons de sécurité",
  },

  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Fenêtre de contexte",
    "max-output-tokens": "Tokens de sortie max",
    "output-limit": "Limite de sortie",
    tokens: "tokens",
    "fallback-value": "Valeur par défaut utilisée",
  },

  // =========================
  // RECENT UPLOADS COMPONENT
  // =========================

  // moved to recentUploads.js

  // =========================
  // LEGAL TEMPLATES MODAL
  // =========================

  // moved to legalTemplates.js

  // =========================
  // CUSTOM LEGAL TEMPLATES MODAL
  // =========================

  // moved to customLegalTemplates.js

  // =========================
  // MCP SERVER PAGE
  // =========================
  mcp: {
    title: "Gestion des serveurs MCP",
    description:
      "Gérer les configurations des serveurs Multi-Component Processing (MCP).",
    currentServers: "Serveurs MCP actuels",
    noServers: "Aucun serveur MCP configuré.",
    fetchError: "Échec de la récupération des serveurs : {{error}}",
    addServerButton: "Ajouter un nouveau serveur",
    addServerModalTitle: "Ajouter un nouveau serveur MCP",
    addServerModalDesc:
      "Définir la configuration pour le nouveau processus serveur MCP.",
    serverName: "Nom du serveur (ID unique)",
    configJson: "Configuration (JSON)",
    addButton: "Ajouter le serveur",
    addSuccess: "Serveur ajouté avec succès.",
    addError: "Échec de l'ajout du serveur : {{error}}",
  },

  cdbProgress: {
    closeMsg: "Êtes-vous sûr de vouloir annuler le processus?",
    general: {
      placeholderSubTask: "Traitement de l'élément {{index}}...",
    },
    main: {
      step1: {
        label: "Générer la Liste des Sections",
        desc: "Utilisation du document principal pour créer une structure initiale.",
      },
      step2: {
        label: "Traiter les Documents",
        desc: "Génération des descriptions et vérification de la pertinence.",
      },
      step3: {
        label: "Mapper les Documents aux Sections",
        desc: "Attribution des documents pertinents à chaque section.",
      },
      step4: {
        label: "Identifier les Problèmes Juridiques",
        desc: "Extraction des problèmes juridiques clés pour chaque section.",
      },
      step5: {
        label: "Générer les Mémos Juridiques",
        desc: "Création de mémorandums juridiques pour les problèmes identifiés.",
      },
      step6: {
        label: "Rédiger les Sections",
        desc: "Composition du contenu pour chaque section individuelle.",
      },
      step7: {
        label: "Combiner & Finaliser le Document",
        desc: "Assemblage de toutes les sections dans le document juridique final.",
      },
    },
    noMain: {
      step1: {
        label: "Traitement des Documents",
        desc: "Génération des descriptions pour tous les fichiers téléchargés.",
      },
      step2: {
        label: "Générer la Liste des Sections",
        desc: "Création d'une liste structurée de sections à partir des résumés de documents.",
      },
      step3: {
        label: "Finaliser le Mappage des Documents",
        desc: "Confirmation de la pertinence des documents pour chaque section planifiée.",
      },
      step4: {
        label: "Identifier les Problèmes Juridiques",
        desc: "Extraction des problèmes juridiques clés pour chaque section.",
      },
      step5: {
        label: "Générer les Mémos Juridiques",
        desc: "Création de mémorandums juridiques pour les problèmes identifiés.",
      },
      step6: {
        label: "Rédiger les Sections",
        desc: "Composition du contenu pour chaque section individuelle.",
      },
      step7: {
        label: "Combiner & Finaliser le Document",
        desc: "Assemblage de toutes les sections dans le document juridique final.",
      },
    },
  },
  // END OF NEW ddProgress block, ensure comma before next top-level key

  // =========================
  // VARIOUS MODALS AND NEW FEATURES
  // =========================

  // Organization & Organizations translations for user management
  organization: {
    label: "Organisation",
    select: "-- Sélectionner une organisation --",
    none: "Aucune",
    "create-new": "+ Créer une nouvelle organisation",
    "new-name": "Nom de la nouvelle organisation",
    "new-name-ph": "Entrez le nom de la nouvelle organisation",
  },
  organizations: {
    "fetch-error": "Échec de la récupération des organisations",
  },
  // ----------------------------
  // Confluence Connector
  // ----------------------------
  confluence: {
    "space-key": "Clé d'espace Confluence",
    "space-key-desc":
      "C'est la clé de votre espace Confluence qui sera utilisée. Commence généralement par ~",
    "space-key-placeholder": "ex: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "ex: https://example.atlassian.net, http://localhost:8211, etc...",
    "token-tooltip": "Vous pouvez créer un jeton API",
    "token-tooltip-here": "ici",
  },
  // =========================
  // REQUEST LEGAL ASSISTANCE
  // =========================
  "request-legal-assistance": {
    title: "Demander une assistance juridique",
    description:
      "Configurer la visibilité du bouton pour demander une assistance juridique.",
    enable: "Activer la demande d'assistance juridique",
    "law-firm-name": "Nom du cabinet d'avocats",
    "law-firm-placeholder": "Entrez le nom du cabinet d'avocats",
    "law-firm-help":
      "Nom du cabinet d'avocats qui traitera les demandes d'assistance juridique",
    email: "Email d'assistance juridique",
    "email-placeholder": "Entrez l'adresse email d'assistance juridique",
    "email-help":
      "Adresse email où les demandes d'assistance juridique seront envoyées",
    "settings-saved":
      "Paramètres d'assistance juridique enregistrés avec succès",
    "save-error":
      "Échec de l'enregistrement des paramètres d'assistance juridique",
    status: "Bouton d'assistance juridique : ",
    "load-error": "Échec du chargement des paramètres d'assistance juridique",
    "save-button": "Enregistrer les modifications",
    request: {
      title: "Demander une assistance juridique",
      description:
        "Envoyer une demande à {{lawFirmName}} pour une assistance juridique, la finalisation de recherches ou d'autres consultations. Vous serez informé par email lorsque la demande sera traitée.",
      button: "Demander une assistance juridique",
      message: "Message",
      "message-placeholder":
        "Entrez des instructions spécifiques ou des informations pour l'équipe d'assistance juridique",
      send: "Envoyer la demande",
      cancel: "Annuler",
      error: "Échec de l'envoi de la demande d'assistance juridique",
      success: "Demande d'assistance juridique envoyée avec succès",
      submitting: "Envoi de la demande...",
      submit: "Soumettre la demande",
      partyName: "Nom de la partie",
      partyOrgId: "Numéro d'organisation de la partie",
      partyNamePlaceholder: "Entrez le nom de votre organisation",
      partyOrgIdPlaceholder: "Entrez votre numéro d'organisation",
      partyNameRequired: "Le nom de la partie est requis",
      partyOrgIdRequired: "Le numéro d'organisation de la partie est requis",
      opposingPartyName: "Nom de la partie opposante (si applicable)",
      opposingPartyOrgId:
        "Numéro d'organisation de la partie opposante (si connu)",
      opposingPartyNamePlaceholder: "Entrez le nom de la partie opposante",
      opposingPartyOrgIdPlaceholder:
        "Entrez le numéro d'organisation de la partie opposante",
    },
  },

  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Progression de la génération de réponse",
    description:
      "Affiche le progrès en temps réel des tâches pour terminer le prompt, en fonction de la liaison avec d'autres espaces de travail et de la taille des fichiers. Le modal se fermera automatiquement lorsque toutes les étapes seront terminées.",
    step_fetching_memos:
      "Récupération des données juridiques sur les sujets actuels",
    step_processing_chunks: "Traitement des documents téléchargés",
    step_combining_responses: "Finalisation de la réponse",
    sub_step_chunk_label: "Traitement du groupe de documents {{index}}",
    sub_step_memo_label: "Données juridiques récupérées de {{workspaceSlug}}",
    placeholder_sub_task: "Étape en file d'attente",
    desc_fetching_memos:
      "Récupération des informations juridiques pertinentes des espaces de travail liés",
    desc_processing_chunks:
      "Analyse et extraction d'informations à partir des groupes de documents",
    desc_combining_responses:
      "Synthèse des informations en une réponse complète",
  },

  // =========================
  // ADMIN SYSTEM SETTINGS
  // =========================
  admin: {
    system: {
      universityMode: {
        title: "Mode Université",
        description:
          "Lorsque activé, masque les outils de validation, de mise à niveau de prompt, de modèle et de recherche web pour tous les utilisateurs.",
        enable: "Activer le mode université",
        saved: "Paramètres du mode université enregistrés.",
        error: "Échec de l'enregistrement des paramètres du mode université.",
        saveChanges: "Enregistrer les paramètres du mode université",
      },
    },
  },

  // Months
  "month.1": "janv.",
  "month.2": "févr.",
  "month.3": "mars",
  "month.4": "avril",
  "month.5": "mai",
  "month.6": "juin",
  "month.7": "juil.",
  "month.8": "août",
  "month.9": "sept.",
  "month.10": "oct.",
  "month.11": "nov.",
  "month.12": "déc.",

  // =========================
  // FEATURE CARDS
  // =========================
  featureCards: {
    "draft-from-template-title":
      "Créer une ébauche de document à partir d'un modèle",
    "draft-from-template-description":
      "Utilisez cette fonction pour, par exemple, créer une politique LCB (lutte contre le blanchiment d'argent), un procès-verbal d'assemblée générale ou une convention d'arbitrage standardisée.",
    "complex-document-builder-title": "Effectuer une tâche juridique complexe",
    "complex-document-builder-description":
      "Parfait lorsque, par exemple, vous devez examiner des centaines de documents avant une acquisition d'entreprise ou rédiger une assignation détaillée.",
  },

  // =========================
  // WORKSPACE SELECTOR MODAL
  // =========================
  workspaceSelector: {
    chooseWorkspace: "Démarrer une nouvelle conversation",
    selectAiType:
      "Sélection du module et de l'espace de travail pour initier la fonctionnalité",
    cloudAiDescription:
      "Utilise un modèle d'IA basé sur le cloud pour le chat et les questions-réponses. Vos documents seront traités et stockés en toute sécurité dans le cloud.",
    localAiDescription:
      "Utilise un modèle d'IA local pour le chat et la rédaction de documents. Vos documents seront traités et stockés sur votre machine locale.",
    cloudAiDescriptionTemplateFeature:
      "Créer un modèle dans un espace de travail existant avec des données juridiques, le modèle peut extraire des données juridiques en fonction de la requête.",
    localAiDescriptionTemplateFeature:
      "Créer un modèle dans votre propre espace de travail, en utilisant une IA entièrement locale si celle-ci est activée sur le serveur.",
    cloudAiDescriptionComplexFeature:
      "La rédaction de documents complexes n'est pas disponible pour ces espaces de travail, car l'utilisateur doit télécharger des documents dans l'espace de travail avant de commencer",
    localAiDescriptionComplexFeature:
      "Choisissez l'un de vos espaces de travail pour initier une tâche juridique, et assurez-vous que les documents nécessaires sont téléchargés dans l'espace de travail avant de commencer.",
    newWorkspaceComplexTaskInfo:
      "Si vous créez un nouvel espace de travail, vous accéderez à la vue de téléchargement pour télécharger tous les documents nécessaires, ce qui est indispensable pour effectuer la génération de documents de tâches juridiques.",
    selectExistingWorkspace: "Sélectionner un espace de travail existant",
    selectExistingDocumentDraftingWorkspace:
      "Sélectionner un espace de travail de rédaction de documents existant",
    orCreateNewBelow:
      "Ou, créer un nouvel espace de travail de rédaction de documents ci-dessous.",
    newWorkspaceName: "Entrez un nom pour votre nouvel espace de travail",
    newWorkspaceNameOptional:
      "Entrez un nom pour votre nouvel espace de travail (si vous n'utilisez pas un espace de travail existant)",
    workspaceNamePlaceholder: "par ex. Mon nouvel espace de travail",
    next: "Suivant",
    pleaseSelectWorkspace: "Veuillez sélectionner un espace de travail.",
    workspaceNameRequired: "Le nom de l'espace de travail est requis.",
    workspaceNameOrExistingWorkspaceRequired:
      "Veuillez entrer un nom d'espace de travail ou sélectionner un espace de travail existant.",
    workspaceNameMustBeMoreThanOneCharacter:
      "Le nom de l'espace de travail doit comporter plus d'un caractère.",
    noWorkspacesAvailable: "Aucun espace de travail disponible",
    selectWorkspacePlaceholder: "Veuillez sélectionner",
    featureUnavailable: {
      title: "Fonction non disponible",
      description:
        "Cette fonction n'est pas activée pour votre compte ou est désactivée dans ce système. Veuillez contacter un administrateur pour activer cette fonction si nécessaire.",
      close: "Fermer",
    },
    createNewWorkspace: {
      title: "Créer un nouvel espace de travail de rédaction de documents",
      description:
        "Cela créera un nouvel espace de travail spécifiquement pour la rédaction de documents complexes en utilisant le modèle sélectionné.",
      workspaceName: "Nom de l'espace de travail",
      create: "Créer l'espace de travail",
    },
    selectExisting: {
      title: "Sélectionner un espace de travail pour les questions juridiques",
      description:
        "Choisissez un espace de travail existant pour démarrer une session de questions-réponses juridiques.",
      selectWorkspace: "Sélectionner l'espace de travail",
    },
  },
};

export default TRANSLATIONS;
