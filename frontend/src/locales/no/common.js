const TRANSLATIONS = {
  // =========================
  // COMMON STRINGS & PLACEHOLDERS
  // =========================
  common: {
    examples: "Eksempler",
    "workspaces-name": "Navn på arbeidsområde",
    ok: "OK",
    error: "feil",
    confirm: "Bekreft",
    confirmstart: "Bekreft og start",
    savesuccess: "Innstillinger lagret",
    saveerror: "Kunne ikke lagre innstillinger",
    success: "suksess",
    user: "Bruker",
    selection: "Modellvalg",
    saving: "Lagrer...",
    save: "Lagre endringer",
    cancel: "Avbryt",
    previous: "Forrige side",
    next: "Neste side",
    "search-placeholder": "Søk...",
    "more-actions": "Flere handlinger",
    "delete-message": "<PERSON>lett melding",
    copy: "<PERSON>pier",
    edit: "<PERSON><PERSON>",
    regenerate: "Regenerer",
    "export-word": "Eksporter til Word",
    "stop-generating": "Stopp generering",
    "attach-file": "Legg ved en fil til denne chatten",
    home: "Hjem",
    settings: "Innstillinger",
    support: "Support",
    "clear-reference": "Fjern referanse",
    "send-message": "Send melding",
    "ask-legal": "Spør om juridisk informasjon",
    "stop-response": "Stopp generering av svar",
    "contact-support": "Kontakt support",
    "copy-connection": "Kopier tilkoblingsstreng",
    "auto-connect": "Koble automatisk til utvidelsen",
    back: "Tilbake",
    "back-to-workspaces": "Tilbake til arbeidsområder",
    off: "Av",
    on: "På",
    continue: "Fortsett",
    rename: "Gi nytt navn",
    delete: "Slett",
    "default-skill":
      "Denne ferdigheten er aktivert som standard og kan ikke deaktiveres.",
    placeholder: {
      username: "Mitt brukernavn",
      password: "Ditt passord",
      email: "Skriv inn din e-post",
      "support-email": "<EMAIL>",
      website: "https://www.eksempel.no",
      "site-name": "IST Legal",
      "search-llm": "Søk etter en spesifikk LLM-leverandør",
      "search-providers": "Søk tilgjengelige leverandører",
      "message-heading": "Meldingsoverskrift",
      "message-content": "Melding",
      "token-limit": "4096",
      "max-tokens": "Maksimalt antall tokens per forespørsel (f.eks: 1024)",
      "api-key": "API-nøkkel",
      "base-url": "Base-URL",
      endpoint: "API-endepunkt",
    },
    tooltip: {
      copy: "Kopier til utklippstavlen",
      delete: "Slett dette elementet",
      edit: "Rediger dette elementet",
      save: "Lagre endringer",
      cancel: "Avbryt endringer",
      search: "Søk elementer",
      add: "Legg til nytt element",
      remove: "Fjern element",
      upload: "Last opp fil",
      download: "Last ned fil",
      refresh: "Oppdater data",
      settings: "Åpne innstillinger",
      more: "Flere alternativer",
    },
    "default.message": "Skriv inn meldingen din her",
    preview: "Forhåndsvisning",
    prompt: "Prompt",
    loading: "Laster...",
    download: "Last ned",
    open_in_new_tab: "Åpne i ny fane",
    timeframes: "Tidsperioder",
    other: "Andre alternativer",
    close: "Lukk",
    note: "Merk",
  },

  // =========================
  // SHOW TOAST MESSAGES
  // =========================

  // moved to showToast.js

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Tekstdeling & Chunking-preferanser",
    "desc-start":
      "Noen ganger må du kanskje endre standardmetoden for hvordan nye dokumenter deles opp og chunkes før de legges til i vektordatabasen din.",
    "desc-end":
      "Endre dette bare hvis du forstår hvordan tekstdeling fungerer og hvilke konsekvenser det kan ha.",
    "warn-start": "Endringer her gjelder kun for",
    "warn-center": "nylig innebygde dokumenter",
    "warn-end": ", ikke eksisterende dokumenter.",
    method: {
      title: "Tekstdelingsmetode",
      "native-explain":
        "Bruk lokal chunkstørrelse & overlapping for oppdeling.",
      "jina-explain":
        "Deleger chunking/segmentering til Jinas innebygde metode.",
      size: {
        title: "Chunkstørrelse",
        description: "Maksimalt antall tokens per chunk.",
      },
      jina: {
        api_key: "Jina API-nøkkel",
        api_key_desc:
          "Nødvendig for å bruke Jinas segmenteringstjeneste. Nøkkelen vil bli lagret i miljøet ditt.",
        max_tokens: "Jina: Maks tokens per chunk",
        max_tokens_desc:
          "Definerer maks tokens i hver chunk for Jinas segmenterer (maks 2000 tokens).",
        return_tokens: "Returner token-informasjon",
        return_tokens_desc:
          "Inkluder token-antall og tokenizer-informasjon i svaret.",
        return_chunks: "Returner chunk-informasjon",
        return_chunks_desc:
          "Inkluder detaljert informasjon om genererte chunks i svaret.",
      },
      "jina-info": "Jina-chunking aktiv.",
    },
    size: {
      title: "Tekstchunkstørrelse",
      description:
        "Dette er maksimalt antall tegn som kan være i en enkelt vektor.",
      recommend: "Maksimal lengde for innbyggingsmodellen er",
    },
    overlap: {
      title: "Tekstchunk-overlapping",
      description:
        "Dette er maksimal overlapping av tegn mellom to tilstøtende tekstchunks.",
    },
    "example-prompt-error": "Kunne ikke sende eksempelprompt: {{error}}",
    "files-processed": "{{count}} fil(er) behandlet vellykket",
    "token-validation-failed":
      "Filvalidering mislyktes - token-antall eller størrelsesbegrensning overskredet",
    "deleted-old-chats": "Slettede {{count}} gamle prompt(s)",
  },

  // =========================
  // RECENT UPLOADS COMPONENT
  // =========================

  // moved to recentUploads.js

  // =========================
  // CHAT BOX DRAG & DROP COMPONENT
  // =========================
  chatboxdnd: {
    title: "Legg til en fil til denne prompten",
    description:
      "Slipp filen din her for å legge den til for denne prompten. Den blir ikke lagret i arbeidsområdet som en permanent kilde.",
    "file-prefix": "Fil:",
    "attachment-tooltip":
      "Denne filen vil bli vedlagt meldingen din. Den vil ikke bli lagret i arbeidsområdet som en permanent kilde.",
    "uploaded-file-tag": "OPPLASTET BRUKERFIL",
  },

  // =========================
  // LMSTUDIO COMPONENT (added/updated example)
  // =========================

  // =========================
  // LOCALAI COMPONENT (added/updated example)
  // =========================

  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Confluence space-nøkkel",
    "space-key-desc":
      "Dette er nøkkelen til plassen i din Confluence-instans som vil bli brukt. Begynner vanligvis med ~",
    "space-key-placeholder": "f.eks.: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "f.eks.: https://eksempel.atlassian.net, http://localhost:8211, etc...",
    "token-tooltip": "Du kan opprette en API-token",
    "token-tooltip-here": "her",
  },

  // =========================
  // CONFIRMATION MESSAGES
  // =========================
  deleteWorkspaceConfirmation:
    "Er du sikker på at du vil slette {{name}}?\nEtter sletting vil den ikke være tilgjengelig på denne instansen.\n\nDenne handlingen kan ikke angres.",
  deleteConfirmation:
    "Er du sikker på at du vil slette ${user.username}?\nEtter sletting vil de logges ut og ikke kunne bruke denne instansen.\n\nDenne handlingen kan ikke angres.",
  suspendConfirmation:
    "Er du sikker på at du vil suspendere {{username}}?\nEtter dette vil de logges ut og ikke kunne logge seg inn igjen før en administrator opphever suspensjonen.",
  flushVectorCachesWorkspaceConfirmation:
    "Er du sikker på at du vil tømme vector-cachene for dette arbeidsområdet?",
  apiKeys: {
    "deactivate-title": "Deaktiver API-nøkkel",
    "deactivate-message":
      "Er du sikker på at du vil deaktivere denne API-nøkkelen?\nEtter dette vil den ikke lenger være brukbar.\n\nDenne handlingen kan ikke angres.",
  },
  // =========================
  // SETTINGS SIDEBAR MENU ITEMS
  // =========================

  // Settings section moved to settings.js

  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Chatgrensesnitt-innstillinger",
    description: "Konfigurer chatinnstillingene.",
    auto_submit: {
      title: "Automatisk innsending av taleinndata",
      description: "Send taleinndata automatisk etter en periode med stillhet",
    },
    auto_speak: {
      title: "Automatisk opplesning av svar",
      description: "Les opp AI-svar automatisk",
    },
  },

  // =========================
  // QURA BUTTONS
  // =========================
  qura: {
    "copy-to-cora": "Qura kildekontroll",
    "qura-status": "Qura-knapp er ",
    "copy-option": "Kopier alternativ",
    "option-quest": "Spørsmål",
    "option-resp": "Svar",
    "role-description": "Legg til en Qura-knapp for å sende svar til Qura.law",
  },

  // =========================
  // LOGIN & SIGN-IN PAGES
  // =========================
  login: {
    "multi-user": {
      welcome: "Velkommen til",
      "placeholder-username": "E-postadresse",
      "placeholder-password": "Passord",
      login: "Logg inn",
      validating: "Validerer...",
      "forgot-pass": "Glemt passord",
      "back-to-login": "Tilbake til logg inn",
      "reset-password": "Tilbakestill passord",
      reset: "Tilbakestill",
      "reset-password-info":
        "Oppgi nødvendig informasjon nedenfor for å tilbakestille passordet ditt.",
    },
    "sign-in": {
      start: "Logg inn på din konto",
      end: "konto.",
    },
    button: "logg inn",
    password: {
      forgot: "Glemt passordet ditt?",
      contact: "Vennligst kontakt systemadministratoren.",
    },
    publicMode: "Prøv uten konto",
    logging: "Logger inn...",
  },

  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Binært LLM-valg",
    "secondary-llm-toggle-description":
      "Aktiver dette for å gi administratorer muligheten til å velge mellom to LLM-modeller i dokumentutkast-modulen.",
    "secondary-llm-toggle-status": "Status: ",
    "secondary-llm-user-level": "Sekundær LLM Brukernivå",
    "secondary-llm-user-level-description":
      "Aktiver dette for å gi ALLE brukere muligheten til å velge mellom to LLM-modeller i dokumentutkast-arbeidsområdet.",
  },

  // =========================
  // NEW WORKSPACE
  // =========================
  "new-workspace": {
    title: "Nytt arbeidsområde",
    placeholder: "Mitt arbeidsområde",
    "legal-areas": "Juridiske områder",
    create: {
      title: "Opprett nytt arbeidsområde",
      description:
        "Etter opprettelse vil kun administratorer kunne se dette arbeidsområdet. Du kan legge til brukere etter at det er opprettet.",
      error: "Feil: ",
      cancel: "Avbryt",
      "create-workspace": "Opprett arbeidsområde",
    },
  },

  // =========================
  // WORKSPACE CHATS
  // =========================
  "workspace-chats": {
    welcome: "Velkommen til ditt nye arbeidsområde.",
    "desc-start": "For å komme i gang kan du enten",
    "desc-mid": "laste opp et dokument",
    "desc-or": "eller",
    start: "For å komme i gang",
    "desc-end": "sende en chat.",
    "attached-file": "Vedlagt fil",
    "attached-files": "Vedlagte filer",
    "token-count": "Antall tokens",
    "total-tokens": "Totalt antall tokens",
    "context-window": "Kontekstvindu",
    "remaining-tokens": "Gjenværende",
    "view-files": "Vis vedlagte filer",
    prompt: {
      send: "Send",
      "send-message": "Send melding",
      placeholder: "Be om juridisk informasjon",
      "change-size": "Endre tekststørrelse",
      reset: "Tilbakestill chatten din",
      clear: "Tøm chathistorikken din og start en ny chat",
      command: "Kommando",
      description: "Beskrivelse",
      save: "lagre",
      small: "Liten",
      normal: "Normal",
      large: "Stor",
      larger: "Større",
      attach: "Legg ved en fil til denne chatten",
      upgrade: "Oppgrader prompten din",
      upgrading: "Oppgraderer prompt...",
      "original-prompt": "Original prompt:",
      "upgraded-prompt": "Oppgradert prompt:",
      "edit-prompt": "Du kan redigere den nye prompten før du sender den",
      "shortcut-tip":
        "Tips: Trykk Enter for å godta endringer. Bruk Shift+Enter for nye linjer.",
      "speak-prompt": "Snakk din prompt",
      "view-agents": "Vis alle tilgjengelige agenter for chatting",
      "ability-tag": "Ferdighet",
      "workspace-chats.prompt.view-agents": "Vis agenter",
      "workspace-chats.prompt.ability-tag": "Ferdighet",
      "workspace-chats.prompt.speak-prompt": "Snakk din prompt",
      "total-tokens": "Totalt antall tokens",
      "deep-search": "Nettsøk",
      "deep-search-tooltip":
        "Søk på nettet etter informasjon for å forbedre svar",
    },
  },

  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Dypsøk",
    description:
      "Konfigurer nettsøkfunksjoner for chattsvar. Når aktivert, kan systemet søke på nettet etter informasjon for å forbedre svar.",
    enable: "Aktiver Dypsøk",
    enable_description:
      "Tillat systemet å søke på nettet etter informasjon når det svarer på spørsmål.",
    provider_settings: "Leverandørinnstillinger",
    provider: "Søkeleverandør",
    model: "Modell",
    api_key: "API-nøkkel",
    api_key_placeholder: "Skriv inn din API-nøkkel",
    api_key_placeholder_set:
      "API-nøkkel er satt (skriv inn ny nøkkel for å endre)",
    api_key_help:
      "Din API-nøkkel lagres sikkert og brukes kun for nettsøkforespørsler.",
    context_percentage: "Kontekstprosent",
    context_percentage_help:
      "Prosentandel av LLM-kontekstvinduet som skal tildeles nettsøkresultater (5-20%).",
    fetch_error: "Kunne ikke hente Dypsøk-innstillinger",
    save_success: "Dypsøk-innstillinger lagret",
    save_error: "Kunne ikke lagre Dypsøk-innstillinger: {{error}}",
    toast_success: "Dypsøk-innstillinger lagret",
    toast_error: "Kunne ikke lagre Dypsøk-innstillinger: {{error}}",
    brave_recommended:
      "Brave Search er for øyeblikket den anbefalte og mest pålitelige leverandøralternativet.",
  },

  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Kontekstuell embedding",
      hint: "Aktiver kontekstuell embedding for å forbedre embeddingprosessen med ekstra parametere",
    },
    systemPrompt: {
      label: "Systemprompt",
      placeholder: "Skriv inn en verdi...",
      description:
        "Eksempel: Vennligst gi en kort, konsis kontekst for å situere denne segmenten i det overordnede dokumentet for å forbedre søk og gjenfinning. Svar kun med den konsise konteksten og ingenting annet.",
    },
    userPrompt: {
      label: "Brukerprompt",
      placeholder: "Skriv inn en verdi...",
      description:
        "Eksempel: <document>\n{file}\n</document>\nHer er segmentet vi ønsker å situere innenfor hele dokumentet\n<chunk>\n{chunk}\n</chunk>",
    },
  },

  // =========================
  // HEADER
  // =========================
  header: {
    account: "Konto",
    login: "Logg inn",
    "sign-out": "Logg ut",
  },

  // =========================
  // WORKSPACE OVERVIEW
  // =========================
  workspace: {
    title: "Instansens arbeidsområder",
    description:
      "Dette er alle arbeidsområdene som eksisterer på denne instansen. Fjerning av et arbeidsområde vil slette alle tilknyttede chatter og innstillinger.",
    "new-workspace": "Nytt arbeidsområde",
    name: "Navn",
    link: "Lenke",
    users: "Brukere",
    type: "Type",
    "created-on": "Opprettet",
    save: "Lagre endring",
    cancel: "Avbryt",
    "sort-by-name": "Sorter etter navn",
    sort: "Sorter alfabetisk",
    unsort: "Gjenopprett opprinnelig rekkefølge",
    deleted: {
      title: "Arbeidsområde ikke funnet!",
      description:
        "Det ser ut til at et arbeidsområde med dette navnet ikke er tilgjengelig.",
      homepage: "Gå tilbake til hjemmesiden",
    },
    "no-workspace": {
      title: "Ingen arbeidsområde tilgjengelig",
      description: "Du har ikke tilgang til noen arbeidsområder ennå.",
      "contact-admin":
        "Vennligst kontakt administratoren din for å be om tilgang.",
      "learn-more": "Lær mer om arbeidsområder",
    },
    "no-workspaces":
      "Du har ingen arbeidsområder ennå. Velg et rettsområde til venstre for å komme i gang.",
    "my-workspaces": "Mine arbeidsområder",
    "show-my": "Vis mine arbeidsområder",
    "show-all": "Vis alle arbeidsområder",
    "creator-id": "Opprettet av bruker-ID: {{id}}",
    "loading-username": "Laster brukernavn...",
    "cloud-ai": "Skybasert AI",
    "local-ai": "Lokal AI",
    "welcome-mobile":
      "Trykk på knappen øverst til venstre for å velge et rettsområde.",
    "today-time": "I dag, {{time}}",
    "date-time": "{{day}} {{month}}, {{time}}",
    "ai-type": "Modul",
    "latest-activity": "Siste aktivitet",
  },

  // =========================
  // WORKSPACES SETTINGS MENU
  // =========================
  "workspaces-settings": {
    general: "Generelle innstillinger",
    chat: "Chatinnstillinger",
    vector: "Vektordatabasen",
    members: "Medlemmer",
    agent: "Agentkonfigurasjon",
    "general-settings": {
      "workspace-name": "Arbeidsområdenes navn",
      "desc-name": "Dette vil kun endre visningsnavnet på arbeidsområdet ditt.",
      "assistant-profile": "Assistentens profilbilde",
      "assistant-image":
        "Tilpass assistentens profilbilde for dette arbeidsområdet.",
      "workspace-image": "Arbeidsområdets bilde",
      "remove-image": "Fjern arbeidsområdets bilde",
      delete: "Slett arbeidsområde",
      deleting: "Sletter arbeidsområde...",
      update: "Oppdater arbeidsområde",
      updating: "Oppdaterer arbeidsområde...",
    },
    "chat-settings": {
      type: "Chat-type",
      private: "Privat",
      standard: "Standard",
      "private-desc-start": "vil manuelt gi tilgang til",
      "private-desc-mid": "kun",
      "private-desc-end": "spesifikke brukere.",
      "standard-desc-start": "vil automatisk gi tilgang til",
      "standard-desc-mid": "alle",
      "standard-desc-end": "nye brukere.",
    },
    users: {
      manage: "Administrer brukere",
      "workspace-member": "Ingen medlemmer i arbeidsområdet",
      username: "E-postadresse",
      role: "Rolle",
      date: "Dato lagt til",
      users: "Brukere",
      search: "Søk etter en bruker",
      "no-user": "Ingen brukere funnet",
      select: "Velg alle",
      unselect: "Avvelg",
      save: "Lagre",
    },
    "linked-workspaces": {
      title: "Koblede arbeidsområder",
      description:
        "Hvis arbeidsområder er koblet, vil juridiske data relevant for prompten automatisk hentes fra hvert tilknyttet juridisk område. Merk at koblede arbeidsområder vil øke behandlingstiden.",
      "linked-workspace": "Ingen koblede arbeidsområder",
      manage: "Administrer arbeidsområder",
      name: "Navn",
      slug: "Slug",
      date: "Dato lagt til",
      workspaces: "Arbeidsområder",
      search: "Søk etter et arbeidsområde",
      "no-workspace": "Ingen arbeidsområder funnet",
      select: "Velg alle",
      unselect: "Avvelg",
      save: "Lagre",
    },
    "delete-workspace": "Slett arbeidsområde",
    "delete-workspace-message":
      "Du er i ferd med å slette hele ditt {{workspace}} arbeidsområde. Dette vil fjerne alle vektor-embeddingene i vektordatabasen din.\n\nDe originale kildefilene forblir upåvirket. Denne handlingen kan ikke angres.",
    "vector-database": {
      reset: {
        title: "Tilbakestill vektordatabasen",
        message:
          "Du er i ferd med å tilbakestille vektordatabasen for dette arbeidsområdet. Dette vil fjerne alle embeddingene som er lagret.\n\nDe originale kildefilene forblir upåvirket. Denne handlingen kan ikke angres.",
      },
    },
  },

  // =========================
  // GENERAL APPEARANCE & CUSTOMIZATION
  // =========================
  general: {
    vector: {
      title: "Antall vektorer",
      description: "Totalt antall vektorer i vektordatabasen din.",
      vectors: "Antall vektorer",
    },
    names: {
      description: "Dette vil kun endre visningsnavnet på arbeidsområdet ditt.",
    },
    message: {
      title: "Foreslåtte chatmeldinger",
      description:
        "Tilpass meldingene som skal foreslås til brukerne av arbeidsområdet ditt.",
      add: "Legg til ny melding",
      save: "Lagre meldinger",
      heading: "Forklar meg",
      body: "fordelene med plattformen",
      message: "Melding",
      "new-heading": "Overskrift",
    },
    pfp: {
      title: "Assistentens profilbilde",
      description: "Tilpass assistentens profilbilde for dette arbeidsområdet.",
      image: "Arbeidsområdets bilde",
      remove: "Fjern arbeidsområdets bilde",
    },
    delete: {
      delete: "Slett arbeidsområde",
      deleting: "Sletter arbeidsområde...",
      "confirm-start": "Du er i ferd med å slette hele ditt",
      "confirm-end":
        "arbeidsområde. Dette vil fjerne alle vektor-embeddingene i vektordatabasen din.\n\nDe originale kildefilene forblir upåvirket. Denne handlingen kan ikke angres.",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chat: {
    llm: {
      title: "Arbeidsområdets LLM-leverandør",
      description:
        "Den spesifikke LLM-leverandøren og modellen som vil bli brukt for dette arbeidsområdet. Som standard brukes systemets LLM-leverandør og innstillinger.",
      search: "Søk i alle LLM-leverandører",
      "save-error": "Kunne ikke lagre {{provider}}-innstillinger: {{error}}",
      setup: "Oppsett",
      use: "For å bruke",
      "need-setup": "må du sette opp følgende legitimasjon.",
      cancel: "Avbryt",
      save: "Lagre",
      settings: "innstillinger",
      "multi-model": "Denne leverandøren støtter ikke flere modeller.",
      "workspace-use": "Arbeidsområdet ditt vil bruke modellen konfigurert i",
      "model-set": "systeminnstillinger",
      "system-default": "Systemstandard",
      "system-default-desc":
        "Bruk systemets LLM-preferanse for dette arbeidsområdet.",
      "no-selection": "Ingen LLM valgt",
      "select-provider": "Velg en LLM-leverandør",
      "system-standard-name": "LLM standard",
      "system-standard-desc":
        "Bruk den primære LLM som er definert i systeminnstillingene.",
    },
    "speak-prompt": "Si prompten din",
    "view-agents": "Vis alle tilgjengelige agenter for chatting",
    "ability-tag": "Ferdighet",
    "change-text-size": "Endre tekststørrelse",
    "aria-text-size": "Endre tekststørrelse",
    model: {
      title: "Arbeidsområdets chatmodell",
      description:
        "Den spesifikke chatmodellen som vil bli brukt for dette arbeidsområdet. Hvis tom, vil systemets LLM-preferanse brukes.",
      wait: "-- venter på modeller --",
      general: "Generelle modeller",
      custom: "Tilpassede modeller",
    },
    mode: {
      title: "Chatmodus",
      chat: {
        title: "Chat",
        "desc-start": "vil gi svar med LLMs generelle kunnskap",
        and: "og",
        "desc-end": "dokumentkontekst som finnes.",
      },
      query: {
        title: "Spørring",
        "desc-start": "vil gi svar",
        only: "kun",
        "desc-end": "hvis dokumentkontekst finnes.",
      },
    },
    history: {
      title: "Chatthistorikk",
      "desc-start":
        "Antall tidligere chatter som vil bli inkludert i korttidshukommelsen til svaret.",
      recommend: "Anbefalt 20. ",
      "desc-end":
        "Alt over 45 kan føre til kontinuerlige chat-feil avhengig av meldingstørrelsen.",
    },
    prompt: {
      title: "Prompt",
      description:
        "Prompten som vil bli brukt på dette arbeidsområdet. Definer konteksten og instruksjonene for AI-en for å generere et svar. Du bør gi en nøye utformet prompt slik at AI-en kan generere et relevant og nøyaktig svar.",
    },
    refusal: {
      title: "Avvisningsrespons for spørringsmodus",
      "desc-start": "Når du er i",
      query: "spørring",
      "desc-end":
        "modus, kan du ønske å returnere en tilpasset avvisningsrespons hvis ingen kontekst finnes.",
    },
    temperature: {
      title: "LLM-temperatur",
      "desc-start":
        'Denne innstillingen styrer hvor "kreative" LLM-svarene dine blir.',
      "desc-end":
        "Jo høyere tallet er, desto mer kreativ. For noen modeller kan dette føre til usammenhengende svar hvis den settes for høyt.",
      hint: "De fleste LLM-er har forskjellige akseptable verdier. Konsulter din LLM-leverandør for den informasjonen.",
    },
    "dynamic-pdr": {
      title: "Dynamisk PDR for arbeidsområde",
      description:
        "Aktiver eller deaktiver dynamisk PDR for dette arbeidsområdet.",
      "global-enabled":
        "Dynamisk PDR er globalt aktivert og kan ikke deaktiveres for individuelle arbeidsområder.",
    },
  },

  // =========================
  // VECTOR DATABASE (WORKSPACE)
  // =========================
  "vector-workspace": {
    identifier: "Identifikator for vektordatabasen",
    snippets: {
      title: "Maks antall kontekstsnutter",
      description:
        "Denne innstillingen styrer det maksimale antallet kontekstsnutter som vil bli sendt til LLM-en per chat eller spørring.",
      recommend:
        "Anbefalt verdi er minst 30. Å sette mye høyere tall vil øke behandlingstiden uten nødvendigvis å forbedre presisjonen avhengig av kapasiteten til LLM-en som brukes.",
    },
    doc: {
      title: "Dokumentsimilaritetsterskel",
      description:
        "Den minste likhetspoengsummen som kreves for at en kilde skal betraktes som relatert til chatten. Jo høyere tallet er, desto mer lik må kilden være chatten.",
      zero: "Ingen restriksjon",
      low: "Lav (likhetspoeng ≥ 0,25)",
      medium: "Medium (likhetspoeng ≥ 0,50)",
      high: "Høy (likhetspoeng ≥ 0,75)",
    },
    reset: {
      reset: "Tilbakestill vektordatabasen",
      resetting: "Tømmer vektorer...",
      confirm:
        "Du er i ferd med å tilbakestille vektordatabasen for dette arbeidsområdet. Dette vil fjerne alle embeddingene som er lagret.\n\nDe originale kildefilene forblir upåvirket. Denne handlingen kan ikke angres.",
      error: "Kunne ikke tilbakestille vektordatabasen for arbeidsområdet!",
      success: "Vektordatabasen for arbeidsområdet ble tilbakestilt!",
    },
  },

  // =========================
  // AGENT CONFIGURATION
  // =========================
  agent: {
    "performance-warning":
      "Ytelsen til LLM-er som ikke eksplisitt støtter verktøykalling er sterkt avhengig av modellens kapasitet og nøyaktighet. Noen ferdigheter kan være begrenset eller ikke-funksjonelle.",
    provider: {
      title: "Arbeidsområdets Agent LLM-leverandør",
      description:
        "Den spesifikke LLM-leverandøren og modellen som vil bli brukt for dette arbeidsområdets @agent agent.",
      "need-setup":
        "For å bruke {{name}} som agentens LLM for dette arbeidsområdet, må du først sette det opp.",
    },
    mode: {
      chat: {
        title: "Arbeidsområdets Agent chatmodell",
        description:
          "Den spesifikke chatmodellen som vil bli brukt for agenten i dette arbeidsområdet.",
      },
      title: "Arbeidsområdets Agent-modell",
      description:
        "Den spesifikke LLM-modellen som vil bli brukt for agenten i dette arbeidsområdet.",
      wait: "-- venter på modeller --",
    },
    skill: {
      title: "Standard agentferdigheter",
      description:
        "Forbedre standardagentens naturlige ferdigheter med disse forhåndsbygde ferdighetene. Denne oppsettet gjelder for alle arbeidsområder.",
      rag: {
        title: "RAG og langtidsminne",
        description:
          'La agenten utnytte dine lokale dokumenter for å svare på en spørring eller be agenten "huske" deler av innholdet for langtidsminnegjenfinning.',
      },
      configure: {
        title: "Konfigurer agentferdigheter",
        description:
          "Tilpass og forbedre standardagentens evner ved å aktivere eller deaktivere spesifikke ferdigheter. Disse innstillingene vil gjelde for alle arbeidsområder.",
      },
      view: {
        title: "Vis og oppsummer dokumenter",
        description:
          "La agenten liste opp og oppsummere innholdet i dokumenter som er embeddet i arbeidsområdet.",
      },
      scrape: {
        title: "Skrap nettsteder",
        description: "La agenten besøke og hente innhold fra nettsider.",
      },
      generate: {
        title: "Generer diagrammer",
        description:
          "Aktiver standardagenten til å generere ulike typer diagrammer basert på data gitt eller oppgitt i chatten.",
      },
      save: {
        title: "Generer og lagre filer i nettleseren",
        description:
          "Aktiver standardagenten til å generere og skrive til filer som lagres og kan lastes ned i nettleseren.",
      },
      web: {
        title: "Live nettsøk og nettlesing",
        "desc-start":
          "Aktiver agenten til å søke på nettet for å besvare spørsmål ved å koble til en websøketjeneste (SERP).",
        "desc-end":
          "Nettlesing under agentsesjoner vil ikke fungere før dette er satt opp.",
      },
    },
  },

  cdbProgress: {
    "close-msg": "Er du sikker på at du vil avbryte prosessen?",
    general: {
      placeholderSubTask: "Behandler element {{index}}...",
    },
    main: {
      step1: {
        label: "Generer liste over seksjoner",
        desc: "Bruker hoveddokumentet for å lage en innledende struktur.",
      },
      step2: {
        label: "Behandle dokumenter",
        desc: "Genererer beskrivelser og sjekker relevans.",
      },
      step3: {
        label: "Knytt dokumenter til seksjoner",
        desc: "Tildeler relevante dokumenter til hver seksjon.",
      },
      step4: {
        label: "Identifiser juridiske spørsmål",
        desc: "Trekker ut sentrale juridiske spørsmål for hver seksjon.",
      },
      step5: {
        label: "Generer juridiske notater",
        desc: "Oppretter juridiske notater for de identifiserte spørsmålene.",
      },
      step6: {
        label: "Utform seksjoner",
        desc: "Komponerer innholdet for hver enkelt seksjon.",
      },
      step7: {
        label: "Kombiner & ferdigstill dokument",
        desc: "Sammenstiller seksjonene til det endelige dokumentet.",
      },
    },
    noMain: {
      step1: {
        label: "Behandler dokumenter",
        desc: "Genererer beskrivelser for alle opplastede filer.",
      },
      step2: {
        label: "Generer liste over seksjoner",
        desc: "Lager en strukturert liste over seksjoner fra dokumentsammendrag.",
      },
      step3: {
        label: "Ferdigstill dokumentkobling",
        desc: "Bekrefter dokumentenes relevans for hver planlagt seksjon.",
      },
      step4: {
        label: "Identifiser juridiske spørsmål",
        desc: "Trekker ut sentrale juridiske spørsmål for hver seksjon.",
      },
      step5: {
        label: "Generer juridiske notater",
        desc: "Oppretter juridiske notater for de identifiserte spørsmålene.",
      },
      step6: {
        label: "Utform seksjoner",
        desc: "Komponerer innholdet for hver enkelt seksjon.",
      },
      step7: {
        label: "Kombiner & ferdigstill dokument",
        desc: "Sammenstiller alle seksjoner til det endelige juridiske dokumentet.",
      },
    },
  },

  // =========================
  // RECORDED WORKSPACE CHATS
  // =========================
  recorded: {
    title: "Arbeidsområde-chatter",
    description:
      "Dette er alle registrerte chatter og meldinger som har blitt sendt av brukere, sortert etter opprettelsesdato.",
    export: "Eksporter",
    table: {
      id: "Id",
      by: "Sendt av",
      workspace: "Arbeidsområde",
      prompt: "Prompt",
      response: "Svar",
      at: "Sendt",
      invoice: "Fakturareferanse",
      "completion-token": "Fullføringstoken",
      "prompt-token": "Prompttoken",
    },
    "clear-chats": "Slett alle nåværende chatter",
    "confirm-clear-chats":
      "Er du sikker på at du vil slette alle chatter?\n\nDenne handlingen kan ikke angres.",
    "fine-tune-modal": "Bestill Fine-Tune-modell",
    "confirm-delete.chat":
      "Er du sikker på at du vil slette denne chatten?\n\nDenne handlingen kan ikke angres.",
    next: "Neste side",
    previous: "Forrige side",
    filters: {
      "by-name": "Filtrer etter brukernavn",
      "by-reference": "Referansenummer",
    },
    bulk_delete_title: "Massesletting av gamle chatter",
    bulk_delete_description:
      "Slett alle chattelogger eldre enn valgt tidsperiode.",
    delete_old_chats: "Slett gamle chatter",
    total_logs: "Totalt antall logger",
    filtered_logs: "Filtrerte logger",
    reset_filters: "Tilbakestill filtre",
    "no-chats-found": "Ingen chattelogger funnet",
    "no-chats-description":
      "Ingen chattelogger funnet som samsvarer med filtrene dine. Prøv å endre søkekriteriene eller slett en eldre tidsperiode.",
    "deleted-old-chats": "Slettet {{count}} gamle chat(ter)",
    two_days: "2 dager",
    one_week: "1 uke",
    two_weeks: "2 uker",
    one_month: "1 måned",
    two_months: "2 måneder",
    three_months: "3 måneder",
    total_deleted: "Totalt slettede chattelogger",
  },

  // =========================
  // API KEYS
  // =========================
  api: {
    title: "API-nøkler",
    description:
      "API-nøkler gir innehaveren mulighet til å programmere tilgang til og administrere denne instansen.",
    link: "Les API-dokumentasjonen",
    generate: "Generer ny API-nøkkel",
    table: {
      key: "API-nøkkel",
      by: "Opprettet av",
      created: "Opprettet",
    },
    new: {
      title: "Opprett ny API-nøkkel",
      description:
        "Når opprettet kan API-nøkkelen brukes for å programmere tilgang til og konfigurere denne instansen.",
      doc: "Les API-dokumentasjonen",
      cancel: "Avbryt",
      "create-api": "Opprett API-nøkkel",
    },
  },

  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "Standardvalget for de fleste ikke-kommersielle bruksområder.",
    azure: "Enterprise-valget av OpenAI hostet på Azure-tjenester.",
    anthropic: "En vennlig AI-assistent hostet av Anthropic.",
    gemini: "Googles største og mest kapable AI-modell",
    huggingface:
      "Få tilgang til 150 000+ åpen kildekode LLMer og verdens AI-fellesskap",
    ollama: "Kjør LLMer lokalt på din egen maskin.",
    lmstudio:
      "Oppdag, last ned og kjør tusenvis av banebrytende LLMer med få klikk.",
    localai: "Kjør LLMer lokalt på din egen maskin.",
    togetherai: "Kjør åpen kildekode-modeller fra Together AI.",
    mistral: "Kjør åpen kildekode-modeller fra Mistral AI.",
    perplexityai:
      "Kjør kraftige og internett-tilkoblede modeller hostet av Perplexity AI.",
    openrouter: "Et enhetlig grensesnitt for LLMer.",
    groq: "Den raskeste LLM-inferensen tilgjengelig for sanntids-AI-applikasjoner.",
    koboldcpp: "Kjør lokale LLMer ved hjelp av koboldcpp.",
    oobabooga:
      "Kjør lokale LLMer ved hjelp av Oobaboogas Text Generation Web UI.",
    cohere: "Kjør Coheres kraftige Command-modeller.",
    lite: "Kjør LiteLLMs OpenAI-kompatible proxy for ulike LLMer.",
    "generic-openai":
      "Koble til enhver OpenAI-kompatibel tjeneste via tilpasset konfigurasjon",
    native:
      "Bruk en nedlastet tilpasset Llama-modell for chatting på denne instansen.",
    xai: "Kjør xAIs kraftige LLMer som Grok-2 og mer.",
    "aws-bedrock": "Kjør kraftige grunnmodeller privat med AWS Bedrock.",
    deepseek: "Kjør DeepSeeks kraftige LLMer.",
    fireworksai:
      "Den raskeste og mest effektive inferensmotoren for å bygge produksjonsklare, sammensatte AI-systemer.",
    bedrock: "Kjør kraftige grunnmodeller privat med AWS Bedrock.",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Preferanse for tale-til-tekst",
    provider: "Leverandør",
    "system-native": "System innebygd",
    "desc-speech":
      "Her kan du spesifisere hvilken type tekst-til-tale og tale-til-tekst leverandører du vil bruke i plattformopplevelsen. Som standard bruker vi nettleserens innebygde støtte for disse tjenestene, men du kan ønske å bruke andre.",
    "title-text": "Preferanse for tekst-til-tale",
    "desc-text":
      "Her kan du spesifisere hvilken type tekst-til-tale leverandører du vil bruke i plattformopplevelsen. Som standard bruker vi nettleserens innebygde støtte for disse tjenestene, men du kan ønske å bruke andre.",
    "desc-config":
      "Ingen konfigurasjon nødvendig for nettleserens innebygde tekst-til-tale.",
    "placeholder-stt": "Søk etter tale-til-tekst leverandører",
    "placeholder-tts": "Søk etter tekst-til-tale leverandører",
    "native-stt": "Bruker nettleserens innebygde STT-tjeneste hvis støttet.",
    "native-tts": "Bruker nettleserens innebygde TTS-tjeneste hvis støttet.",
    "piper-tts": "Kjør TTS-modeller lokalt i nettleseren din privat.",
    "openai-description": "Bruk OpenAIs tekst-til-tale stemmer og teknologi.",
    openai: {
      "api-key": "API-nøkkel",
      "api-key-placeholder": "OpenAI API-nøkkel",
      "voice-model": "Stemmemodell",
    },
    elevenlabs: "Bruk ElevenLabs sine tekst-til-tale stemmer og teknologi.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Preferanse for transkripsjonsmodell",
    description:
      "Dette er legitimasjonen og innstillingene for din foretrukne transkripsjonsmodell-leverandør. Det er viktig at disse nøklene er oppdaterte og korrekte, ellers vil ikke mediefiler og lyd bli transkribert.",
    provider: "Transkripsjonsleverandør",
    "warn-start":
      "Bruk av den lokale whisper-modellen på maskiner med begrenset RAM eller CPU kan stanse plattformen under behandling av mediefiler.",
    "warn-recommend":
      "Vi anbefaler minst 2GB RAM og filstørrelser under 10 MB.",
    "warn-end":
      "Den innebygde modellen vil automatisk lastes ned ved første bruk.",
    "search-audio": "Søk etter leverandører for lydtranskripsjon",
    "api-key": "API-nøkkel",
    "api-key-placeholder": "OpenAI API-nøkkel",
    "whisper-model": "Whisper-modell",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Standard innebygd",
    "default-built-in-desc":
      "Kjør en innebygd whisper-modell på denne instansen privat.",
    "openai-name": "OpenAI",
    "openai-desc": "Bruk OpenAI Whisper-large-modellen med din API-nøkkel.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Ny modellnavn
    "model-size-turbo": "(~810mb)", // Ny modellstørrelse
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Preferanse for embedding",
    "desc-start":
      "Når du bruker en LLM som ikke støtter en innebygd embedding-motor, kan det hende du må spesifisere legitimasjon for å embede tekst.",
    "desc-end":
      "Embedding er prosessen med å omforme tekst til vektorer. Disse legitimasjonene kreves for å konvertere filene og promptene dine til et format som plattformen kan bruke til behandling.",
    provider: {
      title: "Leverandør for embedding",
      description:
        "Ingen oppsett kreves når du bruker plattformens innebygde embedding-motor.",
      "search-embed": "Søk i alle embedding-leverandører",
      select: "Velg en embedding-leverandør",
      search: "Søk i alle embedding-leverandører",
    },
    workspace: {
      title: "Arbeidsområdets embeddingpreferanse",
      description:
        "Den spesifikke embedding-leverandøren og modellen som vil bli brukt for dette arbeidsområdet. Som standard brukes systemets embedding-leverandør og innstillinger.",
      "multi-model":
        "Støtte for flere modeller er ennå ikke tilgjengelig for denne leverandøren.",
      "workspace-use": "Dette arbeidsområdet vil bruke",
      "model-set": "modellsettet for systemet.",
      embedding: "Arbeidsområdets embedding-modell",
      model:
        "Den spesifikke embedding-modellen som vil bli brukt for dette arbeidsområdet. Hvis tom, vil systemets embeddingpreferanse brukes.",
      wait: "-- venter på modeller --",
      setup: "Oppsett",
      use: "For å bruke",
      "need-setup": "som denne arbeidsområdets embedder må du sette opp først.",
      cancel: "Avbryt",
      save: "lagre",
      settings: "innstillinger",
      search: "Søk i alle embedding-leverandører",
      "need-llm": "som denne arbeidsområdets LLM må du sette opp først.",
      "save-error": "Kunne ikke lagre {{provider}}-innstillinger: {{error}}",
      "system-default": "Systemstandard",
      "system-default-desc":
        "Bruk systemets embedding-preferanse for dette arbeidsområdet.",
    },
    warning: {
      "switch-model":
        "Bytte embedding-modellen vil bryte forrige innbyggede dokumenter fra å fungere under chat. De må un-embed fra alle arbeidsområder og fjernes og lastes opp på nytt slik at de kan embeddes av den nye embedding-modellen.",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Vektordatabase",
    description:
      "Dette er legitimasjon og innstillinger for hvordan plattforminstansen din skal fungere. Det er viktig at disse nøklene er oppdaterte og korrekte.",
    provider: {
      title: "Vektordatabaseleverandør",
      description: "Ingen konfigurasjon er nødvendig for LanceDB.",
      "search-db": "Søk alle vektordatabaseleverandører",
    },
    search: {
      title: "Vektorsøkemodus",
      mode: {
        "globally-enabled":
          "Denne innstillingen styres globalt i systeminnstillingene. Besøk systeminnstillinger for å endre rangeringsatferd.",
        default: "Standardsøk",
        "default-desc": "Standard vektorlikhetssøk uten rangering.",
        "accuracy-optimized": "Nøyaktighetsoptimalisert",
        "accuracy-desc":
          "Rangerer resultatene på nytt for å forbedre nøyaktigheten ved hjelp av kryssoppmerksomhet.",
      },
    },
  },

  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Embedde chat-vinduer",
    description:
      "Embedde chat-vinduer er offentlige chat-grensesnitt knyttet til et enkelt arbeidsområde. Disse lar deg bygge arbeidsområder som du deretter kan publisere til verden.",
    create: "Opprett embed",
    table: {
      workspace: "Arbeidsområde",
      chats: "Sendte chatter",
      Active: "Aktive domener",
    },
  },

  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Embedde chatter",
    export: "Eksporter",
    description:
      "Dette er alle de innspilte chatter og meldingene fra alle embed du har publisert.",
    table: {
      embed: "Embed",
      sender: "Avsender",
      message: "Melding",
      response: "Svar",
      at: "Sendt",
    },
    delete: {
      title: "Slett chat",
      message:
        "Er du sikker på at du vil slette denne chatten?\n\nDenne handlingen kan ikke angres.",
    },
    config: {
      "delete-title": "Slett embed",
      "delete-message":
        "Er du sikker på at du vil slette dette embed?\n\nDenne handlingen kan ikke angres.",
      "disable-title": "Deaktiver embed",
      "disable-message":
        "Er du sikker på at du vil deaktivere dette embed?\n\nDenne handlingen kan ikke angres.",
      "enable-title": "Aktiver embed",
      "enable-message":
        "Er du sikker på at du vil aktivere dette embed?\n\nDenne handlingen kan ikke angres.",
    },
  },

  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Multi-brukermodus",
    description:
      "Sett opp instansen din for å støtte teamet ditt ved å aktivere multi-brukermodus.",
    enable: {
      "is-enable": "Multi-brukermodus er aktivert",
      enable: "Aktiver multi-brukermodus",
      description:
        "Som standard vil du være den eneste administratoren. Som administrator må du opprette kontoer for alle nye brukere eller administratorer. Ikke mist passordet ditt siden kun en administrator kan tilbakestille passord.",
      username: "Admin-kontoens e-post",
      password: "Admin-kontoens passord",
      "username-placeholder": "Ditt admin-brukernavn",
      "password-placeholder": "Ditt admin-passord",
    },
    password: {
      title: "Passordbeskyttelse",
      description:
        "Beskytt instansen din med et passord. Hvis du glemmer dette, finnes det ingen gjenopprettingsmetode, så sørg for å lagre passordet.",
    },
    instance: {
      title: "Passordbeskytt instansen",
      description:
        "Som standard vil du være den eneste administratoren. Som administrator må du opprette kontoer for alle nye brukere eller administratorer. Ikke mist passordet ditt siden kun en administrator kan tilbakestille passord.",
      password: "Instanspassord",
    },
  },

  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Hendelseslogger",
    description:
      "Se alle handlinger og hendelser som skjer på denne instansen for overvåking.",
    clear: "Fjern hendelseslogger",
    table: {
      type: "Hendelsestype",
      user: "Bruker",
      occurred: "Skjedde",
    },
  },

  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Personvern & databehandling",
    description:
      "Dette er din konfigurasjon for hvordan tilkoblede tredjepartsleverandører og vår plattform håndterer dataene dine.",
    llm: "LLM-valg",
    embedding: "Embedding-preferanse",
    vector: "Vektordatabasen",
    anonymous: "Anonym telemetri aktivert",
    "desc-event": "Alle hendelser registrerer ikke IP-adresse og inneholder",
    "desc-id": "ingen identifiserende",
    "desc-cont":
      "innhold, innstillinger, chatter eller annen ikke-bruksbasert informasjon. For å se listen over hendelses-tagger som samles inn, kan du se på",
    "desc-git": "Github her",
    "desc-end":
      "Som et open source-prosjekt respekterer vi din rett til personvern. Vi er dedikert til å bygge den beste løsningen for å integrere AI og dokumenter privat og sikkert. Hvis du bestemmer deg for å slå av telemetri, ber vi deg vurdere å sende oss tilbakemeldinger slik at vi kan fortsette å forbedre plattformen for deg.",
  },

  // =========================
  // DEFAULT CHAT
  // =========================
  "default-chat": {
    welcome: "Velkommen til Foynet.",
    "choose-legal": "Velg et juridisk område til venstre.",
  },

  // =========================
  // INVITES
  // =========================
  invites: {
    title: "Invitasjoner",
    description:
      "Opprett invitasjonslenker for personer i organisasjonen din for å registrere seg. Invitasjoner kan kun brukes av én bruker.",
    link: "Opprett invitasjonslenke",
    accept: "Akseptert av",
    usage: "Bruk",
    "created-by": "Opprettet av",
    created: "Opprettet",
    new: {
      title: "Opprett ny invitasjon",
      "desc-start":
        "Etter opprettelsen kan du kopiere invitasjonen og sende den til en ny bruker, slik at de kan opprette en konto som",
      "desc-mid": "standard",
      "desc-end": "rolle og automatisk bli lagt til i valgte arbeidsområder.",
      "auto-add": "Legg automatisk til inviterte i arbeidsområder",
      "desc-add":
        "Du kan eventuelt automatisk tildele brukeren til arbeidsområdene nedenfor ved å velge dem. Som standard vil brukeren ikke ha noen synlige arbeidsområder. Du kan tildele arbeidsområder etter at invitasjonen er akseptert.",
      cancel: "Avbryt",
      "create-invite": "Opprett invitasjon",
      error: "Feil: ",
    },
    "link-copied": "Invitasjonslenke kopiert",
    "copy-link": "Kopier invitasjonslenke",
    "delete-invite-title": "Deaktiver invitasjon",
    "delete-invite-confirmation":
      "Er du sikker på at du vil deaktivere denne invitasjonen?\nEtter dette vil den ikke lenger være brukbar.\n\nDenne handlingen kan ikke angres.",
    status: {
      label: "Status",
      pending: "Venter",
      disabled: "Deaktivert",
      claimed: "Akseptert",
    },
  },

  // =========================
  // USER MENU
  // =========================
  "user-menu": {
    edit: "Rediger konto",
    profile: "Profilbilde",
    size: "800 x 800",
    "remove-profile": "Fjern profilbilde",
    username: "E-postadresse",
    "username-placeholder": "Skriv inn e-postadresse",
    "new-password": "Nytt passord",
    "new-password-placeholder": "Nytt passord",
    cancel: "Avbryt",
    update: "Oppdater konto",
    language: "Foretrukket språk",
    email: "E-postadresse",
    "email-placeholder": "Skriv inn e-postadresse",
  },

  // =========================
  // SIDEBAR (THREADS)
  // =========================
  sidebar: {
    thread: {
      "load-thread": "Laster tråder...",
      "empty-thread": "Ny tråd",
      default: "Standard",
      "starting-thread": "Starter tråd...",
      thread: "Ny tråd",
      delete: "Slett valgte tråder",
      deleted: "slettet",
      "rename-message": "Skriv inn nytt navn for tråden:",
      "delete-message":
        "Er du sikker på at du vil slette denne tråden? Denne handlingen kan ikke angres.",
      rename: "Gi nytt navn",
      "delete-thread": "Slett tråd",
      "rename-thread-title": "Gi tråden nytt navn",
      "new-name-placeholder": "Skriv inn nytt trådnavn",
      "delete-thread-title": "Slette tråd?",
      "delete-confirmation-message":
        'Er du sikker på at du vil slette tråden "{{name}}"? Denne handlingen kan ikke angres.',
    },
  },

  // =========================
  // THREAD NAME ERROR
  // =========================
  thread_name_error:
    "Trådnavnet må være mellom 3 og 255 tegn og kan bare inneholde bokstaver, tall, mellomrom eller bindestreker.",

  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Bruk den innebygde embedding-leverandøren. Ingen oppsett!",
    openai: "Standard alternativ for de fleste ikke-kommersielle bruk.",
    azure: "Enterprise-alternativet til OpenAI, hostet på Azure-tjenester.",
    localai: "Kjør embedding-modeller lokalt på din egen maskin.",
    ollama: "Kjør embedding-modeller lokalt på din egen maskin.",
    lmstudio:
      "Oppdag, last ned og kjør tusenvis av banebrytende LLM-er med noen få klikk.",
    cohere: "Kjør kraftige embedding-modeller fra Cohere.",
    voyageai: "Kjør kraftige embedding-modeller fra Voyage AI.",
    "generic-openai": "Bruk et generelt OpenAI embedding-modell.",
    "default.embedder": "Standard Embedder",
    jina: "Kjør kraftige embedding-modeller fra Jina.",
    litellm: "Kjør kraftige embedding-modeller fra LiteLLM.",
  },

  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb:
      "100% lokal vektordatabase som kjører på samme instans som plattformen.",
    chroma: "Open source vektordatabase som du kan hoste selv eller i skyen.",
    pinecone: "100% skybasert vektordatabase for bedriftsbruk.",
    zilliz:
      "Skyhostet vektordatabase bygget for bedrifter med SOC 2-sertifisering.",
    qdrant: "Open source lokal og distribuert skyvektordatabase.",
    weaviate: "Open source lokal og skyhostet multimodal vektordatabase.",
    milvus: "Open source, svært skalerbar og lynrask.",
    astra: "Vektorsøk for reell GenAI.",
  },

  // =========================
  // SYSTEM PREFERENCES
  // =========================
  system: {
    title: "Systempreferanser",
    "desc-start":
      "Dette er de overordnede innstillingene og konfigurasjonene for instansen din.",
    context_window: {
      title: "Dynamisk Kontekstvindu",
      desc: "Kontroller hvor mye av LLM-ens kontekstvindu som brukes for ytterligere kilder.",
      label: "Kontekstvindu Prosent",
      help: "Prosentandel av kontekstvinduet som kan brukes for berikelse (10-100%).",
      "toast-success": "Kontekstvindu prosent oppdatert med suksess.",
      "toast-error": "Kunne ikke oppdatere kontekstvindu prosent.",
    },
    "change-login-ui": {
      title: "Velg standard UI for innlogging",
      status: "Gjeldende",
      subtitle:
        "Grensesnittet vil bli brukt som standard innloggings-UI for applikasjonen",
    },
    attachment_context: {
      title: "Vedlegg Kontekstvindu",
      desc: "Kontroller hvor mye av LLM-kontekstvinduet som kan brukes til vedlegg.",
      label: "Vedlegg Kontekst Prosent",
      help: "Prosentandel av kontekstvinduet som kan brukes til vedlegg (10-80%).",
      "toast-success": "Vedlegg kontekst prosent oppdatert.",
      "toast-error": "Kunne ikke oppdatere vedlegg kontekst prosent.",
      "validation-error": "Vedlegg kontekst prosent må være mellom 10 og 80.",
    },
    user: "Brukere kan slette arbeidsområder",
    "desc-delete":
      "Tillat ikke-administratorbrukere å slette arbeidsområder de er en del av. Dette vil slette arbeidsområdet for alle.",
    limit: {
      title: "Meldingsgrense",
      "desc-limit": "Begrens antall meldinger en bruker kan sende per dag.",
      "per-day": "Meldinger per dag",
      label: "Meldingsgrensen er for øyeblikket ",
    },
    max_tokens: {
      title: "Maksimalt antall innloggings-tokens per bruker",
      desc: "Angi det maksimale antallet aktive autentiseringstokens hver bruker kan ha samtidig. Når grensen overskrides, fjernes eldre tokens automatisk.",
      label: "Maksimale tokens",
      help: "Verdien må være større enn 0",
    },
    state: {
      enabled: "Aktivert",
      disabled: "Deaktivert",
    },
    "source-highlighting": {
      title: "Aktiver / Deaktiver kildemarkering",
      description: "Skjul eller vis kildemarkering for brukere.",
      label: "Sitat: ",
      "toast-success": "Kildemarkering-innstillingen har blitt oppdatert",
      "toast-error": "Kunne ikke oppdatere kildemarkering-innstillingen",
    },
    "usage-registration": {
      title: "Bruksregistrering for fakturering",
      description: "Aktiver eller deaktiver fakturalogg for systemovervåking.",
      label: "Fakturalogg er ",
    },
    "forced-invoice-logging": {
      title: "Tvungen fakturaregistrering",
      description:
        "Aktiver for å kreve en fakturareferanse før bruk av plattformen.",
      label: "Tvungen fakturaregistrering er ",
    },
    "rexor-linkage": {
      title: "Rexor-kobling",
      description:
        "Aktiver Rexor-kobling for å få aktive saksreferanser fra Rexor-tjenesten.",
      label: "Rexor-kobling er ",
      "activity-id": "Aktivitets-ID",
      "activity-id-description":
        "Skriv inn aktivitets-ID for Rexor-integrasjon",
    },
    rerank: {
      title: "Rerangeringsinnstillinger",
      description:
        "Konfigurer rerangeringsinnstillinger for å forbedre søkeresultatenes relevans med LanceDB.",
      "enable-title": "Aktiver rerangering",
      "enable-description":
        "Aktiver rerangering for å forbedre søkeresultatenes relevans ved å ta hensyn til mer kontekst.",
      status: "Rerangeringsstatus",
      "vector-count-title": "Ekstra vektorer for rerangering",
      "vector-count-description":
        "Antall ekstra vektorer å hente utover arbeidsområdets vektorantall. For eksempel, hvis arbeidsområdet er satt til å hente 30 vektorer og dette er satt til 50, vil totalt 80 vektorer vurderes for rerangering. Et høyere antall kan forbedre nøyaktigheten men vil øke prosesseringstiden.",
      "lancedb-only": "Kun LanceDB",
      "lancedb-notice":
        "Denne funksjonen er kun tilgjengelig når du bruker LanceDB som vektordatabase.",
    },
    save: "Lagre endringer",
  },

  feedback: {
    thankYou: "Takk! Tilbakemeldingen din er sendt inn vellykket.",
    emailSendError: "Feil ved sending av e-post: ",
    submitFeedbackError: "Feil ved sending av tilbakemelding: ",
    attachFile: "Legg ved en fil",
    improvePlatform: "Hjelp oss med å forbedre plattformen!",
    suggestionOrQuestion: "Noen forslag? Eller spørsmål?",
    clickToWrite: "Vennligst klikk for å skrive til oss",
    noFeedback: "Ingen tilbakemeldinger funnet",
    previewImage: "Forhåndsvis bilde",
    filePreview: "Forhåndsvisning av fil",
    noFile: "Ingen fil vedlagt",
    fullName: "Fullt navn",
    fullNamePlaceholder: "Skriv inn ditt fulle navn",
    message: "Melding",
    messagePlaceholder: "Skriv inn din tilbakemelding",
    attachment: "Vedlegg",
    submit: "Send tilbakemelding",
    submitting: "Sender...",
    submitSuccess: "Tilbakemelding sendt",
    submitError: "Kunne ikke sende tilbakemelding",
    imageLoadError: "Kunne ikke laste bilde",
    unsupportedFile: "Filtypen støttes ikke",
    validation: {
      fullNameMinLength: "Fullt navn må være minst 2 tegn",
      fullNameMaxLength: "Fullt navn kan ikke være lenger enn 100 tegn",
      fullNameFormat:
        "Fullt navn kan kun inneholde bokstaver, tall, mellomrom, understreker (_), punktummer (.) og bindestreker (-)",
      messageMinLength: "Melding må være minst 12 tegn",
      messageMaxLength: "Melding kan ikke være lenger enn 1000 tegn",
      messageMinWords: "Melding må være minst 4 ord",
      fileType: "Filen må være en JPEG, PNG eller PDF",
      fileSize: "Filen må være mindre enn 5MB",
    },
  },

  "feedback-settings": {
    "delete-feedback": "Tilbakemelding slettet med suksess!",
    "delete-error": "Tilbakemelding kunne ikke slettes",
    "header-title": "Tilbakemeldingsliste",
    "header-description":
      "Dette er den komplette listen over tilbakemeldinger for denne instansen. Vær oppmerksom på at sletting av tilbakemeldinger er permanent og ikke kan angres.",
    title: "Brukertilbakemeldingsknapp",
    description: "Aktiver eller deaktiver tilbakemeldingsknappen for brukere.",
    successMessage: "Brukertilbakemeldingsknappen har blitt oppdatert",
    failureUpdateMessage:
      "Kunne ikke oppdatere statusen til brukertilbakemeldingsknappen.",
    errorSubmitting: "Feil ved sending av tilbakemeldingsinnstillinger.",
    errorFetching: "Feil ved henting av tilbakemeldingsinnstillinger.",
  },

  // =========================
  // USER SETTINGS (INSTANCE USERS)
  // =========================
  "user-setting": {
    description:
      "Dette er alle kontoene som finnes på denne instansen. Fjerning av en konto vil umiddelbart fjerne deres tilgang til denne instansen.",
    "add-user": "Legg til bruker",
    username: "E-postadresse",
    role: "Rolle",
    "economy-id": "Økonomi-ID",
    "economy-id-ph": "Skriv inn økonomisystem-identifikator",
    "economy-id-hint":
      "ID brukt for integrasjoner med eksterne økonomisystemer (f.eks. Rexor)",
    default: "Standard",
    manager: "Leder",
    admin: "Administrator",
    superuser: "Superuser",
    "date-added": "Dato lagt til",
    "all-domains": "Alle domener",
    "other-users": "Andre brukere (ingen domene)",
    // Sorteringsalternativer for brukerlisten
    "sort-username": "Sorter etter brukernavn",
    "sort-organization": "Sorter etter organisasjon",
    edit: "Rediger: ",
    "new-password": "Nytt passord",
    "password-rule": "Passord må være minst 8 tegn langt.",
    "update-user": "Oppdater bruker",
    placeholder: "Skriv inn e-postadresse",
    cancel: "Avbryt",
    "remove-user": "Fjern bruker",
    "remove-user-title": "Fjern bruker",
    "remove-user-confirmation":
      "Er du sikker på at du vil fjerne denne brukeren?",
    error: "Feil: ",
  },

  "login-ui": {
    "show-toast": {
      "update-failed": "Kunne ikke oppdatere innloggingsgrensesnittet",
      "updated-login-ui": "Innloggingsgrensesnittet har blitt oppdatert",
    },
    "visit-website": "Besøk nettsiden",
    loading: "Laster ...",
    "rw-login-description":
      "Maksimer juridisk produktivitet med vår AI-drevne plattform!",
  },

  // =========================
  // SUPPORT EMAIL
  // =========================
  support: {
    title: "Support-e-post",
    description:
      "Angi support-e-postadressen som vises i brukermenyen når du er logget inn på denne instansen.",
    clear: "Fjern",
    save: "Lagre",
  },

  // =========================
  // PUBLIC MODE
  // =========================
  "public-mode": {
    enable: "Aktiver offentlig brukermodus",
    enabled: "Offentlig brukermodus er aktivert",
  },

  // =========================
  // BUTTON LABELS
  // =========================
  button: {
    delete: "Slett",
    edit: "Rediger",
    suspend: "Suspender",
    unsuspend: "Gjenopprett",
    save: "Lagre",
    accept: "Aksepter",
    decline: "Avslå",
    ok: "OK",
    "flush-vector-caches": "Tøm vector-cacher",
    cancel: "Avbryt",
    saving: "Lagrer",
    save_llm: "Lagre LLM-valg",
    save_template: "Lagre mal",
    "reset-to-default": "Tilbakestill til standard",
    create: "Opprett",
    enable: "Aktiver",
    disable: "Deaktiver",
    reset: "Tilbakestill",
    revoke: "Tilbakekall",
  },

  // =========================
  // NEW USER (INSTANCE)
  // =========================
  "new-user": {
    title: "Legg til bruker på instansen",
    username: "E-postadresse",
    "username-ph": "Skriv inn e-postadresse",
    password: "Passord",
    "password-ph": "Brukerens første passord",
    role: "Rolle",
    default: "Standard",
    manager: "Leder",
    admin: "Administrator",
    superuser: "Superuser",
    description:
      "Etter opprettelse må brukeren logge inn med sitt første passord for å få tilgang.",
    cancel: "Avbryt",
    "add-User": "Legg til bruker",
    error: "Feil: ",
    "invalid-email": "Vennligst oppgi en gyldig e-postadresse.",
    permissions: {
      title: "Tillatelser",
      default: [
        "Kan kun sende chatter med arbeidsområder de er lagt til av administrator eller ledere.",
        "Kan ikke endre noen innstillinger i det hele tatt.",
      ],
      manager: [
        "Kan se, opprette og slette arbeidsområder og endre arbeidsområdespesifikke innstillinger.",
        "Kan opprette, oppdatere og invitere nye brukere til instansen.",
        "Kan ikke endre LLM-, vectorDB-, embedding- eller andre tilkoblinger.",
      ],
      admin: [
        "Høyeste brukernivåprivilegium.",
        "Kan se og gjøre alt på tvers av systemet.",
      ],
      superuser: [
        "Kan få tilgang til spesifikke innstillingssider som Dokumentbygger og Prompt-oppgradering.",
        "Kan ikke endre systemomfattende innstillinger som LLM-, vektorDB-konfigurasjoner.",
        "Kan sende chatter med arbeidsområder de er lagt til av administrator eller ledere.",
      ],
    },
  },

  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Opprett nytt embed for arbeidsområde",
    error: "Feil: ",
    "desc-start":
      "Etter opprettelse vil du få en lenke som du kan publisere på nettstedet ditt med et enkelt",
    script: "script",
    tag: "tag.",
    cancel: "Avbryt",
    "create-embed": "Opprett embed",
    workspace: "Arbeidsområde",
    "desc-workspace":
      "Dette er arbeidsområdet som chat-vinduet ditt vil baseres på. Alle standarder vil bli arvet fra arbeidsområdet med mindre de overskrives av denne konfigurasjonen.",
    "allowed-chat": "Tillatt chat-metode",
    "desc-query":
      "Angi hvordan chatbotten din skal operere. Spørring betyr at den kun vil svare hvis et dokument hjelper til med å besvare spørringen.",
    "desc-chat":
      "Chat åpner for chatting om generelle spørsmål og kan svare på spørsmål som ikke er relatert til arbeidsområdet.",
    "desc-response": "Chat: Svar på alle spørsmål uavhengig av kontekst",
    "query-response":
      "Spørring: Svar kun på chatter relatert til dokumenter i arbeidsområdet",
    restrict: "Begrens forespørsler fra domener",
    filter:
      "Dette filteret vil blokkere forespørsler som kommer fra et annet domene enn de oppgitte nedenfor.",
    "use-embed":
      "Hvis denne er tom, betyr det at hvem som helst kan bruke ditt embed på hvilket som helst nettsted.",
    "max-chats": "Maks chatter per dag",
    "limit-chats":
      "Begrens antallet chatter dette embed kan prosessere i løpet av 24 timer. Null er ubegrenset.",
    "chats-session": "Maks chatter per sesjon",
    "limit-chats-session":
      "Begrens antallet chatter en sesjonsbruker kan sende med dette embed i løpet av 24 timer. Null er ubegrenset.",
    "enable-dynamic": "Aktiver dynamisk modellbruk",
    "llm-override":
      "Tillat å sette foretrukket LLM-modell for å overstyre arbeidsområdets standard.",
    "llm-temp": "Aktiver dynamisk LLM-temperatur",
    "desc-temp":
      "Tillat å sette LLM-temperaturen for å overstyre arbeidsområdets standard.",
    "prompt-override": "Aktiver prompt-overstyring",
    "desc-override":
      "Tillat å sette systemprompten for å overstyre arbeidsområdets standard.",
  },

  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    openai: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for OpenAI",
      ],
    },
    azure: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Din tekst og embedding-tekst er ikke synlig for OpenAI eller Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for Anthropic",
      ],
    },
    gemini: {
      description: [
        "Dine chatter er anonymisert og brukes i trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for Google",
      ],
    },
    lmstudio: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på serveren som kjører LMStudio",
      ],
    },
    localai: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på serveren som kjører LocalAI",
      ],
    },
    ollama: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på maskinen som kjører Ollama-modeller",
      ],
    },
    native: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på denne instansen",
      ],
    },
    togetherai: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for TogetherAI",
      ],
    },
    mistral: {
      description: [
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for Mistral",
      ],
    },
    huggingface: {
      description: [
        "Dine prompt og dokumenttekst brukt i svar er sendt til din HuggingFace-administrerte endepunkt",
      ],
    },
    perplexity: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for OpenRouter",
      ],
    },
    groq: {
      description: [
        "Dine chatter vil ikke bli brukt til trening",
        "Dine prompt og dokumenttekst brukt i svargenerering er synlig for Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på serveren som kjører KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på serveren som kjører Oobabooga Text Generation Web UI",
      ],
    },
    "generic-openai": {
      description: [
        "Data deles i henhold til vilkårene for den generiske endepunktleverandøren.",
      ],
    },
    cohere: {
      description: [
        "Data deles i henhold til vilkårene for cohere.com og ditt lokale personvern.",
      ],
    },
    litellm: {
      description: [
        "Dine modeller og chatter er kun tilgjengelige på serveren som kjører LiteLLM",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din Chroma-instans",
        "Tilgang til din instans administreres av deg",
      ],
    },
    pinecone: {
      description: [
        "Dine vektorer og dokumenttekst lagres på Pinecones servere",
        "Tilgang til dine data administreres av Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din Qdrant-instans (skybasert eller selvhostet)",
      ],
    },
    weaviate: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din Weaviate-instans (skybasert eller selvhostet)",
      ],
    },
    milvus: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din Milvus-instans (skybasert eller selvhostet)",
      ],
    },
    zilliz: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din Zilliz sky-klynge.",
      ],
    },
    astra: {
      description: [
        "Dine vektorer og dokumenttekst lagres på din skybaserte AstraDB-database.",
      ],
    },
    lancedb: {
      description: [
        "Dine vektorer og dokumenttekst lagres privat på denne instansen av plattformen",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: [
        "Din dokumenttekst embedes privat på denne instansen av plattformen",
      ],
    },
    openai: {
      description: [
        "Din dokumenttekst sendes til OpenAI-servere",
        "Dokumentene dine brukes ikke til trening",
      ],
    },
    azure: {
      description: [
        "Din dokumenttekst sendes til din Microsoft Azure-tjeneste",
        "Dokumentene dine brukes ikke til trening",
      ],
    },
    localai: {
      description: [
        "Din dokumenttekst embedes privat på serveren som kjører LocalAI",
      ],
    },
    ollama: {
      description: [
        "Din dokumenttekst embedes privat på serveren som kjører Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Din dokumenttekst embedes privat på serveren som kjører LMStudio",
      ],
    },
    cohere: {
      description: [
        "Data deles i henhold til vilkårene for cohere.com og ditt lokale personvern.",
      ],
    },
    voyageai: {
      description: [
        "Data sendt til Voyage AI sine servere deles i henhold til vilkårene for voyageai.com.",
      ],
    },
  },

  // =========================
  // PROMPT VALIDATION
  // =========================
  "prompt-validate": {
    edit: "Rediger",
    response: "Svar",
    prompt: "Prompt",
    regenerate: "Generer svar på nytt",
    good: "Godt svar",
    bad: "Dårlig respons",
    copy: "Kopier",
    more: "Flere handlinger",
    fork: "Fork",
    delete: "Slett",
    cancel: "Avbryt",
    save: "Lagre og send",
    "export-word": "Eksporter til Word",
    exporting: "Eksporterer...",
  },

  // =========================
  // CITATIONS
  // =========================
  citations: {
    show: "Vis siteringer",
    hide: "Skjul siteringer",
    chunk: "Siteringssegmenter",
    pdr: "Hoveddokument",
    "pdr-h": "Dokumentmarkering",
    referenced: "Referert",
    times: "ganger.",
    citation: "Sitat",
    match: "treff",
    download:
      "Denne nettleseren støtter ikke PDF. Vennligst last ned PDF-en for å se den:",
    "download-btn": "Last ned PDF",
    view: "Vis kilder og sitater",
    sources: "Kilder",
    "pdf-collapse-tip":
      "Tips: Du kan minimere denne PDF-fanen ved å bruke knappen øverst til venstre",
    "open-in-browser": "Åpne i nettleser",
    "loading-pdf": "-- laster PDF --",
    "error-loading": "Feil ved lasting av PDF",
    "no-valid-path": "Ingen gyldig PDF-sti funnet",
    "web-search": "Nettsøk",
    "web-search-summary": "Nettsøk-sammendrag",
    "web-search-results": "Nettsøk-resultater",
    "no-web-search-results": "Ingen nettsøk-resultater funnet",
    "previous-highlight": "Forrige utheving",
    "next-highlight": "Neste utheving",
    "try-alternative-view": "Prøv alternativ visning",
  },

  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Dokumentutkast",
    description: "Kontroller dine innstillinger for dokumentutkast.",
    configuration: "Konfigurasjon",
    "drafting-model": "Utkast-LLM",
    enabled: "Dokumentutkast er aktivert",
    disabled: "Dokumentutkast er deaktivert",
    "enabled-toast": "Dokumentutkast aktivert",
    "disabled-toast": "Dokumentutkast deaktivert",
    "desc-settings":
      "Administrator kan endre innstillingene for dokumentutkast for alle brukere.",
    "drafting-llm": "Foretrukket LLM for utkast",
    saving: "Lagrer...",
    save: "Lagre endringer",
    "chat-settings": "Chatinnstillinger",
    "drafting-chat-settings": "Chatinnstillinger for dokumentutkast",
    "chat-settings-desc":
      "Kontroller oppførselen til chat-funksjonen for dokumentutkast.",
    "drafting-prompt": "Systemprompt for dokumentutkast",
    "drafting-prompt-desc":
      "Systemprompten som vil bli brukt i dokumentutkast er forskjellig fra systemprompten for juridisk spørsmål og svar. Denne definerer konteksten og instruksjonene for AI-en slik at den kan generere et relevant og nøyaktig svar.",
    linking: "Dokumentkobling",
    "legal-issues-prompt": "Prompt for juridiske problemstillinger",
    "legal-issues-prompt-desc":
      "Skriv inn prompt for juridiske problemstillinger.",
    "memo-prompt": "Memo-prompt",
    "memo-prompt-desc": "Skriv inn prompt for memo.",
    "desc-linkage":
      "Aktiver muligheten til å legge til ytterligere juridisk kontekst ved å kombinere Vector/PDR-søk med memo-henting",
    message: {
      title: "Foreslåtte meldinger for dokumentutkast",
      description:
        "Legg til foreslåtte meldinger som brukere raskt kan velge når de utarbeider dokumenter.",
      heading: "Standard meldingstittel",
      body: "Standard meldingstekst",
      "new-heading": "Meldingstittel",
      message: "Meldingstekst",
      add: "Legg til melding",
      save: "Lagre meldinger",
    },
    "combine-prompt": "Kombinasjonsprompt",
    "combine-prompt-desc":
      "Angi systemprompten for å kombinere flere svar til ett enkelt svar. Denne prompten brukes både for å kombinere svar og DD Linkage-memoer, og for å kombinere de forskjellige svarene fra Infinity Context-behandling.",
    "page-description":
      "Denne siden er for å justere de ulike promptene som brukes i forskjellige funksjoner i dokumentutkastmodulen. I hvert inntastingsfelt vises standardprompten, som vil bli brukt med mindre en tilpasset prompt anvendes på denne siden.",
    "dd-linkage-steps": "Prompter for DD-koblingstrinn",
    "general-combination-prompt": "Generell kombinasjonsprompt",
    "import-memo": {
      title: "Importer fra Legal QA",
      "button-text": "Importer notat",
      "search-placeholder": "Søk i tråder...",
      import: "Importer",
      importing: "Importerer...",
      "no-threads": "Ingen Legal QA-tråder funnet",
      "no-matching-threads": "Ingen tråder samsvarer med søket ditt",
      "thread-not-found": "Valgt tråd ikke funnet",
      "empty-thread": "Den valgte tråden har ingen innhold å importere",
      "import-success": "Trådinnhold importert vellykket",
      "import-error": "Kunne ikke importere trådinnhold",
      "import-error-details": "Feil under import: {{details}}",
      "fetch-error": "Kunne ikke hente tråder. Vennligst prøv igjen senere.",
      "imported-from": "Importert fra Legal QA-tråd",
      "unnamed-thread": "Navnløs tråd",
      "unknown-workspace": "Ukjent arbeidsområde",
      "no-threads-available": "Ingen tråder tilgjengelige å importere",
      "create-conversations-first":
        "Opprett først samtaler i et Legal QA-arbeidsområde, så kan du importere dem her.",
      "no-legal-qa-workspaces":
        "Ingen Legal QA-arbeidsområder med aktive tråder ble funnet. Opprett først samtaler i et Legal QA-arbeidsområde for å importere dem.",
      "empty-workspaces-with-names":
        "Legal QA-arbeidsområder funnet ({{workspaceNames}}), men de inneholder ennå ingen aktive tråder. Opprett først samtaler i disse arbeidsområdene for å importere dem.",
      "import-success-with-name": "Tråden ble importert: {{threadName}}",
    },
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Juridisk oppgavebrukerpromptgenerator",
    description:
      "Automatisk foreslåelse av personlig justert prompt for en juridisk oppgave",
    "task-description": "Juridisk oppgavebeskrivelse",
    "task-description-placeholder":
      "Beskriv den juridiske oppgaven du ønsker å gjøre...",
    "suggested-prompt": "Foreslått brukerprompt",
    "generation-prompt": "Prompt for generering",
    "create-task": "Opprett juridisk oppgave basert på dette forslaget",
    "specific-instructions": "Spesifikke instruksjoner eller kunnskap",
    "specific-instructions-description":
      "Inkluder alle spesifikke instruksjoner eller kunnskap som er relevant for denne juridiske oppgaven",
    "specific-instructions-placeholder":
      "Legg til spesifikke instruksjoner, kunnskap eller erfaring for å håndtere denne juridiske oppgaven...",
    generating: "Genererer...",
    generate: "Generer forslag",
    "toast-success": "Prompt generert med suksess",
    "toast-fail": "Kunne ikke generere prompt",
    button: "Generer prompt",
    success: "Prompt generert",
    error: "Vennligst oppgi et navn eller en underkategori først",
    failed: "Kunne ikke generere prompt",
  },

  // =========================
  // DD SETTINGS (WORKSPACE LINKING SETTINGS)
  // =========================
  "dd-settings": {
    title: "Innstillinger for arbeidsområdekobling",
    description:
      "Kontroller tokenbegrensninger og oppførsel for koblede arbeidsområder",
    "vector-search": {
      title: "Vektorsøk",
      description:
        "Når denne funksjonen er aktivert, utføres semantiske vektorsøk på tvers av alle koblede arbeidsområder for å finne relevante juridiske dokumenter. Systemet konverterer brukerforespørsler til vektorinnbeddinger og matcher dem mot dokumentvektorer i databasen til hvert koblet arbeidsområde. Denne funksjonen fungerer som en reserveløsning når notatgenerering er aktivert, men ikke gir resultater. Når notatgenerering er deaktivert, blir Vektorsøk den primære metoden for å hente informasjon fra koblede arbeidsområder. Søkedybden kontrolleres av innstillingen for Vektortokengrense.",
    },
    "memo-generation": {
      title: "Notatgenerering",
      description:
        "Denne funksjonen genererer automatisk konsise juridiske notater fra dokumenter funnet i koblede arbeidsområder. Når den er aktivert, analyserer systemet innhentede dokumenter for å lage strukturerte sammendrag av juridiske nøkkelpunkter, presedens og relevant kontekst. Disse notatene fungerer som den primære metoden for å inkorporere kunnskap fra koblede arbeidsområder. Hvis notatgenerering mislykkes eller ikke gir resultater, vil systemet automatisk falle tilbake til Vektorsøk (hvis aktivert) for å sikre at relevant informasjon likevel hentes. Lengden og detaljnivået på disse notatene styres av innstillingen for Notat-tokengrense.",
    },
    "linked-workspace-impact": {
      title: "Tokenpåvirkning for koblede arbeidsområder",
      description:
        "Kontrollerer hvordan systemet håndterer sitt tokenbudsjett på tvers av flere koblede arbeidsområder. Når denne funksjonen er aktivert, justerer systemet dynamisk tilgjengelige tokens for hvert arbeidsområde basert på det totale antallet koblede arbeidsområder, noe som sikrer rettferdig fordeling av dataressurser. Dette forhindrer at ett enkelt arbeidsområde dominerer kontekstvinduet, samtidig som det opprettholder omfattende dekning over alle relevante juridiske områder. Denne innstillingen reserverer tokenkapasitet spesifikt for notatgenerering og/eller vektorsøkresultater fra hvert koblet arbeidsområde, noe som kan redusere det totale antallet tokens tilgjengelig for det primære arbeidsområdet når mange arbeidsområder er koblet.",
    },
    "vector-token-limit": {
      title: "Vektortokengrense",
      description:
        "Angir maksimalt antall tokens som tildeles for vektorsøkresultater fra hvert koblet arbeidsområde. Denne grensen gjelder når Vektorsøk brukes, enten som primærmetode (når notatgenerering er deaktivert) eller som reserveløsning (når notatgenerering mislykkes). Høyere grenser muliggjør mer omfattende dokumenthenting, men reduserer tokens tilgjengelig for andre operasjoner.",
    },
    "memo-token-limit": {
      title: "Notat-tokengrense",
      description:
        "Kontrollerer maksimal lengde på genererte juridiske notater fra hvert koblet arbeidsområde. Som den primære metoden for kunnskapsintegrering, oppsummerer disse notatene juridiske nøkkelpunkter fra dokumentene i det koblede arbeidsområdet. Hvis et notat overstiger denne tokengrensen, vil det bli avvist og systemet vil falle tilbake til Vektorsøk (hvis aktivert). Høyere grenser muliggjør mer detaljert juridisk analyse, men kan redusere antallet koblede arbeidsområder som kan inkorporeres.",
    },
    "base-token-limit": {
      title: "Base-token-grense",
      description:
        "Bestemmer maksimal token-lengde for det innledende juridiske analyserammeverket. Denne grensen påvirker hvor omfattende grunnanalysen kan være før informasjon fra tilkoblede arbeidsområder inkorporeres. En høyere grense muliggjør mer detaljert innledende analyse men etterlater mindre plass for inkorporering av innhold fra tilkoblede arbeidsområder.",
    },
    "toast-success": "Innstillinger oppdatert",
    "toast-fail": "Kunne ikke oppdatere innstillingene",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Innstillinger for arbeidsområdekobling",
    description:
      "Kontroller tokenbegrensninger og oppførsel for koblede arbeidsområder",
    "vector-search": {
      title: "Vektorsøk",
      description:
        "Reservemetode for å finne relevante dokumenter når notatgenerering mislykkes eller er deaktivert",
    },
    "memo-generation": {
      title: "Notatgenerering",
      description:
        "Primær metode for å inkorporere kunnskap fra koblede arbeidsområder",
    },
    "linked-workspace-impact": {
      title: "Tokenpåvirkning for koblede arbeidsområder",
      description:
        "Reserver tokens for hvert koblet arbeidsområde proporsjonalt med antallet",
    },
    "vector-token-limit": {
      title: "Token-grense for vector-søk",
      description:
        "Maksimalt antall tokens per koblet arbeidsområde for vector-søk",
    },
    "memo-token-limit": {
      title: "Token-grense for memoer",
      description: "Maksimalt antall tokens for generering av juridiske memoer",
    },
    "base-token-limit": {
      title: "Grunnleggende token-grense",
      description:
        "Maksimalt antall tokens for innhenting av grunnleggende innhold",
    },
    "toast-success": "Innstillinger oppdatert med suksess",
    "toast-fail": "Kunne ikke oppdatere innstillingene",
  },

  // =========================
  // MODALE (DOCUMENT & CONNECTORS)
  // =========================
  modale: {
    document: {
      title: "Mine dokumenter",
      document: "Dokumenter",
      search: "Søk etter dokument",
      folder: "Ny mappe",
      name: "Navn",
      empty: "Ingen dokumenter",
      "move-workspace": "Flytt til arbeidsområde",
      "doc-processor": "Dokumentprosessor",
      "processor-offline":
        "Dokumentprosessoren er for øyeblikket offline. Prøv igjen senere.",
      "drag-drop": "Klikk for å laste opp eller dra og slipp",
      "supported-files": "Støttede filer: PDF",
      "submit-link": "Eller send en lenke til et dokument",
      fetch: "Hent",
      fetching: "Henter...",
      "file-desc":
        "Merk: Dokumentet vil bli behandlet og lagt til i arbeidsområdet ditt. Dette kan ta noen øyeblikk.",
      cost: "*Engangskostnad for embeddings",
      "save-embed": "Lagre og innlegg",
      "failed-uploads": "Mislykkede opplastinger",
      "loading-message": "Dette kan ta litt tid for store dokumenter",
      "uploading-file": "Laster opp fil(er)...",
      "scraping-link": "Behandler lenke...",
      "moving-documents": "Flytter {{count}} dokumenter. Vennligst vent.",
      "exceeds-prompt-limit":
        "Merk: Det opplastede innholdet overstiger det som kan passe i én forespørsel. Systemet vil behandle forespørsler gjennom flere prompter, noe som vil øke tiden for å generere svaret, og presisjonen kan bli påvirket.",
    },
    connectors: {
      title: "Datakoblinger",
      search: "Søk etter datakoblinger",
      empty: "Ingen datakoblinger funnet.",
    },
    "justify-betweening": "Behandler...",
  },

  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "GitHub-repo",
      description:
        "Importer et helt offentlig eller privat GitHub-repositorium med ett klikk.",
      url: "GitHub-repo URL",
      "collect-url": "URL til GitHub-repoen du ønsker å hente.",
      "access-token": "GitHub tilgangstoken",
      optional: "valgfritt",
      "rate-limiting": "Tilgangstoken for å unngå rate limiting.",
      "desc-picker":
        "Når fullført vil alle filer være tilgjengelige for embedding i arbeidsområder i dokumentvelgeren.",
      branch: "Gren",
      "branch-desc": "Grenen du ønsker å hente filer fra.",
      "branch-loading": "-- laster tilgjengelige grener --",
      "desc-start": "Uten å fylle ut",
      "desc-token": "GitHub tilgangstoken",
      "desc-connector": "vil denne datakoblingen kun kunne hente",
      "desc-level": "topplevelds",
      "desc-end":
        "filer fra repoen på grunn av GitHubs offentlige API rate limits.",
      "personal-token":
        "Få en gratis personlig tilgangstoken med en GitHub-konto her.",
      without: "Uten en",
      "personal-token-access": "personlig tilgangstoken",
      "desc-api":
        ", kan GitHub API begrense antall filer som kan hentes på grunn av rate limits. Du kan",
      "temp-token": "opprette en midlertidig tilgangstoken",
      "avoid-issue": "for å unngå dette problemet.",
      submit: "Send",
      "collecting-files": "Henter filer...",
    },
    "youtube-transcript": {
      name: "YouTube-transkripsjon",
      description:
        "Importer transkripsjonen av en hel YouTube-video fra en lenke.",
      url: "YouTube Video URL",
      "url-video": "URL til YouTube-videoen du ønsker å transkribere.",
      collect: "Hent transkripsjon",
      collecting: "Henter transkripsjon...",
      "desc-end":
        "når fullført vil transkripsjonen være tilgjengelig for embedding i arbeidsområder i dokumentvelgeren.",
    },
    "website-depth": {
      name: "Bulk link-skrabber",
      description:
        "Skrap et nettsted og dets underlenker opp til en viss dybde.",
      url: "Nettsteds URL",
      "url-scrape": "URL til nettstedet du ønsker å skrape.",
      depth: "Dybde",
      "child-links":
        "Dette er antallet underlenker som skrabberen skal følge fra den opprinnelige URL-en.",
      "max-links": "Maks antall lenker",
      "links-scrape": "Maks antall lenker å skrape.",
      scraping: "Skraper nettsted...",
      submit: "Send",
      "desc-scrap":
        "Når fullført vil alle skrapede sider være tilgjengelige for embedding i arbeidsområder i dokumentvelgeren.",
    },
    confluence: {
      name: "Confluence",
      description: "Importer en hel Confluence-side med ett klikk.",
      url: "Confluence side URL",
      "url-page": "URL til en side i Confluence-plassen.",
      username: "Confluence brukernavn",
      "own-username": "Ditt Confluence-brukernavn.",
      token: "Confluence tilgangstoken",
      "desc-start":
        "Du må oppgi en tilgangstoken for autentisering. Du kan generere en tilgangstoken",
      here: "her",
      access: "Tilgangstoken for autentisering.",
      collecting: "Henter sider...",
      submit: "Send",
      "desc-end":
        "Når fullført vil alle sider være tilgjengelige for embedding i arbeidsområder.",
    },
  },

  // =========================
  // MODULE DEFINITIONS
  // =========================
  module: {
    "legal-qa": "Juridisk Q&A",
    "document-drafting": "Dokumentutkast",
    "active-case": "Aktiv sak",
  },

  // =========================
  // FINE-TUNE NOTIFICATION
  // =========================
  "fine-tune": {
    title: "Du har nok data til finjustering!",
    link: "klikk for å lære mer",
    dismiss: "avvis",
  },

  // =========================
  // MOBILE DISCLAIMER
  // =========================
  mobile: {
    disclaimer:
      "ANSVARSFRASKRIVELSE: For best opplevelse og full tilgang til alle funksjoner, vennligst bruk en datamaskin for å få tilgang til appen.",
  },
  // =========================
  // SHARE MODAL
  // =========================
  shareModal: {
    title: "Del {type}",
    titleWorkspace: "Del arbeidsområde",
    titleThread: "Del tråd",
    shareWithUsers: "Del med brukere",
    shareWithOrg: "Del med hele organisasjonen",
    searchUsers: "Søk etter brukere...",
    noUsersFound: "Ingen brukere funnet",
    loadingUsers: "Laster brukere...",
    errorLoadingUsers: "Feil ved lasting av brukere",
    errorLoadingStatus: "Feil ved lasting av delingsstatus",
    userAccessGranted: "Brukertilgang innvilget",
    userAccessRevoked: "Brukertilgang tilbakekalt",
    orgAccessGranted: "Organisasjonstilgang innvilget",
    orgAccessRevoked: "Organisasjonstilgang tilbakekalt",
    errorUpdateUser: "Feil ved oppdatering av brukertilgang",
    errorNoOrg: "Kan ikke dele: kontoen er ikke knyttet til en organisasjon",
    errorUpdateOrg: "Feil ved oppdatering av organisasjonstilgang",
    close: "Lukk",
    grantAccess: "Gi tilgang",
    revokeAccess: "Tilbakekall tilgang",
  },

  // =========================
  // ONBOARDING
  // =========================
  onboarding: {
    welcome: "Velkommen til",
    "get-started": "Kom i gang",
    "llm-preference": {
      title: "LLM-preferanse",
      description:
        "ISTLLM kan jobbe med mange LLM-leverandører. Dette vil være tjenesten som håndterer chatting.",
      "LLM-search": "Søk etter LLM-leverandører",
    },
    "user-setup": {
      title: "Brukeroppsett",
      description: "Konfigurer dine brukerinnstillinger.",
      "sub-title": "Hvor mange personer vil bruke instansen din?",
      "single-user": "Bare meg",
      "multiple-user": "Mitt team",
      "setup-password": "Ønsker du å sette opp et passord?",
      "password-requirment": "Passord må være minst 8 tegn lange.",
      "save-password":
        "Det er viktig å lagre dette passordet fordi det ikke finnes noen gjenopprettingsmetode.",
      "password-label": "Instanspassord",
      username: "Admin-kontoens e-post",
      password: "Admin-kontoens passord",
      "account-requirment":
        "E-posten må være gyldig og kun inneholde små bokstaver, tall, understreker og bindestreker uten mellomrom. Passordet må være minst 8 tegn langt.",
      "password-note":
        "Som standard vil du være den eneste administratoren. Når onboarding er fullført, kan du opprette og invitere andre til å bli brukere eller administratorer. Ikke mist passordet ditt, siden kun administratorer kan tilbakestille passord.",
    },
    "data-handling": {
      title: "Databehandling & personvern",
      description:
        "Vi er forpliktet til åpenhet og kontroll når det gjelder dine personlige data.",
      "llm-label": "LLM-valg",
      "embedding-label": "Embedding-preferanse",
      "database-lablel": "Vektordatabasen",
      "reconfigure-option":
        "Disse innstillingene kan omkonfigureres når som helst i innstillingene.",
    },
    survey: {
      title: "Velkommen til IST Legal LLM",
      description:
        "Hjelp oss å gjøre IST Legal LLM tilpasset dine behov. Valgfritt.",
      email: "Hva er din e-post?",
      usage: "Hva vil du bruke plattformen til?",
      work: "For jobb",
      "personal-use": "For personlig bruk",
      other: "Annet",
      comment: "Noen kommentarer til teamet?",
      optional: "(Valgfritt)",
      feedback: "Takk for tilbakemeldingen!",
    },
    button: {
      yes: "Ja",
      no: "Nei",
      "skip-survey": "Hopp over undersøkelsen",
    },
    placeholder: {
      "admin-password": "Ditt admin-passord",
      "admin-username": "Din admin-e-post",
      "email-example": "<EMAIL>",
      comment:
        "Hvis du har spørsmål eller kommentarer nå, kan du skrive dem her, så tar vi kontakt. Du kan også sende e-<NAME_EMAIL>",
    },
  },

  // =========================
  // DEFAULT SETTINGS FOR LEGAL Q&A
  // =========================
  "default-settings": {
    "canvas-prompt": "Canvas-systemprompt",
    "canvas-prompt-desc":
      "Prompt for lerret-chattsystemet. Brukes som systemprompt for lerret-chatinteraksjoner.",
    title: "Standardinnstillinger for Legal Q&A",
    "default-desc":
      "Kontroller standardoppførselen til arbeidsområder for Legal Q&A",
    prompt: "Legal Q&A systemprompt",
    "prompt-desc":
      "Standardprompten som vil bli brukt for nye Legal Q&A arbeidsområder. Definer kontekst og instruksjoner for AI for å generere et svar. Du bør gi en nøye utformet prompt slik at AI kan generere et relevant og nøyaktig svar. For å bruke denne innstillingen på alle eksisterende arbeidsområder og overstyre deres tilpassede innstillinger, bruk knappen nedenfor.",
    "prompt-placeholder": "Skriv inn din prompt her",
    "toast-success": "Standard systemprompt oppdatert",
    "toast-fail": "Kunne ikke oppdatere systemprompt",
    "apply-all-confirm":
      "Er du sikker på at du vil bruke denne prompten på alle eksisterende Legal Q&A arbeidsområder? Denne handlingen kan ikke angres og vil overstyre alle tilpassede innstillinger.",
    "apply-to-all": "Bruk på alle eksisterende Legal Q&A arbeidsområder",
    applying: "Bruker...",
    "toast-apply-success": "Standard prompt brukt på {{count}} arbeidsområder",
    "toast-apply-fail": "Kunne ikke bruke standard prompt på arbeidsområder",
    snippets: {
      title: "Standard maks antall kontekstsnutter",
      description:
        "Denne innstillingen styrer standard maksimalt antall kontekstsnutter som vil bli sendt til LLM-en for nye arbeidsområder. For å bruke denne innstillingen på alle eksisterende arbeidsområder og overstyre deres tilpassede innstillinger, bruk knappen nedenfor.",
      recommend:
        "Anbefalt verdi er minst 30. Å sette mye høyere tall vil øke behandlingstiden uten nødvendigvis å forbedre presisjonen avhengig av kapasiteten til LLM-en som brukes.",
    },
    "rerank-limit": {
      title: "Maks antall dokumenter for omrangering",
      description:
        "Denne innstillingen styrer maksimalt antall dokumenter som vil bli vurdert for omrangering. Et høyere tall kan gi bedre resultater, men vil øke behandlingstiden.",
      recommend: "Anbefalt: 50",
    },
    "validation-prompt": {
      title: "Valideringsprompt",
      description:
        "Denne innstillingen styrer standard valideringsprompten som vil bli sendt til LLM-en for å validere det gitte svaret.",
      placeholder:
        "Vennligst valider følgende svar, og sjekk alle juridiske referanser og siteringer for nøyaktighet mot den oppgitte konteksten. List opp eventuelle unøyaktigheter eller feiltolkninger funnet.",
    },
    "apply-vector-search-to-all":
      "Bruk på alle eksisterende Legal Q&A arbeidsområder",
    "apply-vector-search-all-confirm":
      "Er du sikker på at du vil bruke denne vektorsøk-innstillingen på alle eksisterende Legal Q&A arbeidsområder? Denne handlingen kan ikke angres.",
    "toast-vector-search-apply-success":
      "Vektorsøk-innstilling brukt på {{count}} arbeidsområder",
    "toast-vector-search-apply-fail":
      "Kunne ikke bruke vektorsøk-innstilling på arbeidsområder",
    "canvas-upload-prompt": "Systemprompt for Canvas-opplasting",
    "canvas-upload-prompt-desc":
      "Systemprompten som brukes når innhold lastes opp via canvas. Denne prompten styrer LLMs oppførsel for opplastet innhold.",
  },

  // =========================
  // CONFIRM MESSAGE
  // =========================
  "confirm-message": {
    "delete-doc-title": "Slett filer og mapper",
    "delete-doc":
      "Er du sikker på at du vil slette disse filene og mappene?\nDette vil fjerne filene fra systemet og automatisk fjerne dem fra alle eksisterende arbeidsområder.\nDenne handlingen kan ikke reverseres.",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Utfør juridisk oppgave",
    noTaskfund: "Ingen juridiske oppgaver tilgjengelig",
    noSubtskfund: "Ingen underoppgaver tilgjengelige.",
    "loading-subcategory": "Laster underkategorier...",
    "select-category": "Velg kategori",
    "choose-task": "Velg juridisk oppgave å utføre",
    "duration-info":
      "Tiden det tar å utføre en juridisk oppgave avhenger av antall dokumenter i arbeidsområdet. Med mange dokumenter og en kompleks oppgave kan dette ta veldig lang tid.",
    "action-btn": "Opprett en juridisk oppgave",
    description:
      "Aktiver eller deaktiver knappen for å utføre juridisk oppgave i dokumentutkast.",
    successMessage: "Utføring av juridisk oppgave har blitt {{status}}",
    failureUpdateMessage:
      "Kunne ikke oppdatere innstillingene for juridisk oppgave.",
    errorSubmitting:
      "Feil under innsending av innstillinger for juridisk oppgave.",
    "custom-instructions-placeholder":
      "Ange ytterligare instruktioner för den juridiska uppgiften (valfritt)...",
    "additional-instructions-label": "Ytterligare instruktioner:",
    "warning-title": "Advarsel",
    "no-files-title": "Ingen filer tilgjengelig",
    "no-files-description":
      "Det er ingen filer i dette arbeidsområdet. Vennligst last opp minst en fil før du utfører en juridisk oppgave.",
    "settings-button": "Legg til eller endre tilgjengelige juridiske oppgaver",
    settings: "Juridiske oppgaver",
    subStep: "Pågående eller køet deloppgave",
  },

  // =========================
  // CANVAS CHAT
  // =========================
  canvasChat: {
    title: "Tegnebrett",
    "input-placeholder": "Spør om juridisk informasjon",
    chatboxinstruction: "Gi instruksjoner for justering av svaret",
    explanation:
      "Dette verktøyet er for AI-redigering av svaret i ulike måter. Kildeene for den underliggende svaret er anvendt, noe som betyr at du kan spørre om ytterligere klarhet ved å bruke samme kildemateriale som for den underliggende svaret.",
    editAnswer: "Rediger svaret",
  },

  // =========================
  // STATUSES
  // =========================
  statuses: {
    enabled: "aktivert",
    disabled: "deaktivert",
  },

  // =========================
  // ANSWER UPGRADE
  // =========================

  // Moved to answerUpgrade.js

  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "PDR-innstillinger",
    description:
      "Konfigurer innstillingene for Parent Document Retrieval for arbeidsområdene dine.",
    "desc-end":
      "Disse innstillingene påvirker hvordan PDR-dokumenter behandles og brukes i chatsvar.",
    "global-override": {
      title: "Global overstyring for dynamisk PDR",
      description:
        "Når aktivert vil dette behandle alle arbeidsområdedokumenter som PDR-aktiverte for kontekst i svar. Når deaktivert vil kun dokumenter som er eksplisitt markert som PDR bli brukt, noe som kan redusere tilgjengelig kontekst og føre til svar av betydelig lavere kvalitet siden kun vektorchunks fra søk vil bli brukt som kilder i disse tilfellene.",
    },
    "toast-success": "PDR-innstillinger oppdatert",
    "toast-fail": "Kunne ikke oppdatere PDR-innstillinger",
    "adjacent-vector-limit": "Grense for tilstøtende vektorer",
    "adjacent-vector-limit-desc": "Begrensning for tilstøtende vektorer.",
    "adjacent-vector-limit-placeholder":
      "Skriv inn grense for tilstøtende vektorer",
    "keep-pdr-vectors": "Behold PDR-vektorer",
    "keep-pdr-vectors-desc": "Alternativ for å beholde PDR-vektorer.",
  },

  // =========================
  // VALIDATE RESPONSE
  // =========================
  "validate-response": {
    title: "Valideringsresultat",
    "toast-fail": "Kunne ikke validere svar",
    validating: "Validerer svar",
    button: "Valider svar",
    "adjust-prefix":
      "Gjør alle angitte endringer i svaret basert på denne tilbakemeldingen: ",
    "adjust-button": "Bruk foreslåtte endringer",
  },

  // =========================
  // WORKSPACE NAMES (LEGAL AREAS)
  // =========================
  "workspace-names": {
    "Administrative Law": "Forvaltningsrett",
    "Business Law": "Forretningsrett",
    "Civil Law": "Sivilrett",
    "Criminal Law": "Strafferett",
    "Diplomatic Law": "Diplomatisk rett",
    "Fundamental Law": "Grunnlovsrett",
    "Human Rights Law": "Menneskerettigheter",
    "Judicial Laws": "Rettspraksis",
    "Security Laws": "Sikkerhetslover",
    "Taxation Laws": "Skatterett",
  },

  // =========================
  // VALIDATE ANSWER
  // =========================
  "validate-answer": {
    setting: "Validerings-LLM",
    title: "Foretrukket LLM for validering",
    description:
      "Dette er legitimasjonen og innstillingene for din foretrukne validerings-LLM chat- og embedding-leverandør. Det er viktig at disse nøklene er oppdaterte og korrekte, ellers vil ikke systemet fungere riktig.",
    "toast-success": "Validerings-LLM-innstillinger oppdatert",
    "toast-fail": "Kunne ikke oppdatere validerings-LLM-innstillinger",
    saving: "Lagrer...",
    "save-changes": "Lagre endringer",
  },

  // =========================
  // ACTIVE CASE
  // =========================
  "active-case": {
    title: "Skriv inn saksreferansenummer",
    placeholder: "f.eks.: 1000-01",
    "select-reference": "Velg saksreferanse",
    "warning-message": "Advarsel: Dette vil påvirke den aktive saken",
    "warning-title": "Aktiv sak advarsel",
  },

  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Multi-brukermodus er permanent aktivert av sikkerhetsgrunner",
    "password-validation": {
      "restricted-chars":
        "Passordet ditt inneholder ugyldige tegn. Tillatte symboler er _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "Når aktivert, kan alle brukere få tilgang til de offentlige arbeidsområdene uten å logge inn.",
    },
    button: {
      saving: "Lagrer...",
      "save-changes": "Lagre endringer",
    },
  },

  // =========================
  // ERRORS
  // =========================
  errors: {
    "fetch-models": "Kunne ikke hente tilpassede modeller",
    "fetch-models-error": "Feil ved henting av modeller",
    "upgrade-error": "Feil under oppgradering",
    "failed-extract-content": "Kunne ikke hente innhold",
    "failed-process-attachment": "Kunne ikke behandle vedlegg",
    "failed-process-content": "Kunne ikke behandle innhold",
    "failed-process-file": "Kunne ikke behandle fil",
    common: {
      error: "Feil",
    },
    streaming: {
      failed:
        "Det oppstod en feil under streaming av svaret, for eksempel at AI-motoren er offline eller overbelastet.",
      code: "Kode",
      unknown: "Ukjent feil.",
    },
    workspace: {
      "already-exists": "Et arbeidsområde med dette navnet finnes allerede",
    },
    env: {
      "anthropic-key-format":
        "Ugyldig Anthropic API-nøkkelformat. Nøkkelen må starte med 'sk-ant-'",
      "openai-key-format": "OpenAI API-nøkkelen må starte med 'sk-'",
      "jina-key-format": "Jina API-nøkkelen må starte med 'jina_'",
    },
    auth: {
      "invalid-credentials": "Ugyldige påloggingsdetaljer.",
      "account-suspended": "Konto suspendert av administrator.",
      "invalid-password": "Ugyldig passord oppgitt",
    },
    "invalid-token-count": "Ugyldig antall tokens",
  },

  // =========================
  // LOADING STATES
  // =========================
  loading: {
    models: "-- laster tilgjengelige modeller --",
    "waiting-url": "-- venter på URL --",
    "waiting-api-key": "-- venter på API-nøkkel --",
    "waiting-models": "-- venter på modeller --",
  },

  // =========================
  // CHARTS
  // =========================
  charts: {
    downloading: "Laster ned bilde...",
    download: "Last ned grafbilde",
  },

  // =========================
  // MODALS
  // =========================
  modals: {
    warning: {
      title: "Advarsel",
      proceed: "Er du sikker på at du vil fortsette?",
      cancel: "Avbryt",
      confirm: "Bekreft",
      "got-it": "OK, forstår",
    },
  },

  // =========================
  // DOCUMENTS & PINNING
  // =========================
  documents: {
    "pin-info-button": "Om festering",
    "pin-title": "Hva er dokumentfeste?",
    "pin-desc-1":
      "Når du fester et dokument, vil plattformen injisere hele dokumentinnholdet i din prompt slik at LLM-en kan forstå det fullt ut.",
    "pin-desc-2":
      "Dette fungerer best med modeller med stor kontekst eller små filer som er kritiske for kunnskapsbasen.",
    "pin-desc-3":
      "Hvis du ikke får de svarene du ønsker som standard, er dokumentfeste en god måte å oppnå høyere kvalitet på svarene med et enkelt klikk.",
    "pin-add": "Fest til arbeidsområde",
    "pin-unpin": "Fjern fra arbeidsområde",
    "watch-title": "Hva gjør det å overvåke et dokument?",
    "watch-desc-1":
      "Når du overvåker et dokument, vil vi automatisk synkronisere dokumentinnholdet ditt fra den opprinnelige kilden med jevne mellomrom. Dette vil oppdatere innholdet i alle arbeidsområder der filen administreres.",
    "watch-desc-2":
      "Denne funksjonen støtter for øyeblikket kun nettbasert innhold og vil ikke være tilgjengelig for manuelt opplastede dokumenter.",
    "watch-desc-3": "Du kan administrere hvilke dokumenter som overvåkes fra",
    "file-manager": "Filbehandler",
    "admin-view": "Administratorvisning",
    "pdr-add": "La til alle dokumenter i Parent Document Retrieval",
    "pdr-remove": "Fjernet alle dokumenter fra Parent Document Retrieval",
    empty: "Ingen dokumenter funnet",
    tooltip: {
      date: "Dato: ",
      type: "Type: ",
      cached: "Mellomlagret",
    },
    actions: {
      removing: "Fjerner fil fra arbeidsområdet",
    },
    costs: {
      estimate: "Estimert kostnad: $",
      minimum: "< $0.01",
    },
    "new-folder": {
      title: "Opprett ny mappe",
      "name-label": "Mappenavn",
      "name-placeholder": "Skriv inn mappenavn",
      create: "Opprett mappe",
    },
    error: {
      "create-folder": "Kunne ikke opprette mappe",
    },
  },

  // =========================
  // LEGAL QUESTION
  // =========================
  "legal-question": {
    "category-one": "Kategori én",
    "category-two": "Kategori to",
    "category-three": "Kategori tre",
    "category-four": "Kategori fire",
    "category-five": "Kategori fem",
    "item-one": "Punkt én",
    "item-two": "Punkt to",
    "item-three": "Punkt tre",
    "item-four": "Punkt fire",
    "item-five": "Punkt fem",
    "item-six": "Punkt seks",
    "item-seven": "Punkt sju",
    "example-title": "Spis godt: En guide til å nyte mat",
    example: {
      breakfast: {
        title: "1. Sunn frokost",
        items: [
          "Havregrøt med fersk frukt og honning",
          "Gresk yoghurtparfait med granola",
          "Avokadotoast med posjerte egg",
          "Grønn smoothie med spinat, banan og mandelmelk",
          "Fullkornspannekaker med lønnesirup",
        ],
      },
      lunch: {
        title: "2. Enkle lunsjalternativer",
        items: [
          "Grillet kyllingwrap med blandede grønnsaker",
          "Quinoasalat med ovnsstekte grønnsaker",
          "Kalkunsandwich på fullkornsbrød",
          "Wok med grønnsaker og brun ris",
          "Suppe med sidesalat",
        ],
      },
      dinner: {
        title: "3. Smakfulle middager",
        items: [
          "Bakt laks med sitron og asparges",
          "Spaghetti med tomatsaus og kjøttboller",
          "Grønnsakscurry med basmatiris",
          "Grillet biff med ovnsstekte poteter",
          "Fylte paprika med quinoa og ost",
        ],
      },
    },
  },

  // =========================
  // PRESETS
  // =========================
  presets: {
    "edit-title": "Rediger Standard-Prompt",
    description: "Beskrivelse av prompten",
    "description-placeholder": "Lager en oppsummering av vedleggene.",
    deleting: "Sletter...",
    "delete-preset": "Slett Standard-Prompt",
    cancel: "Avbryt",
    save: "Lagre",
    "add-title": "Legg til Standard-Prompt",
    "command-label": "Navn på prompten, ett enkelt ord",
    "command-placeholder": "Oppsummering",
    "command-desc":
      "Navnet er også chatboksen rasktast, starter med /, for å bruke denne prompten uten å trykke på knapper.",
    "prompt-label": "Prompt",
    "prompt-placeholder": "Lager en oppsummering av vedleggene.",
    "prompt-desc": "Prompten som vil bli sendt når denne kommandoen brukes.",
    "tooltip-add": "Legg til ny Standard-Prompt",
    "tooltip-hover": "Se dine egne Standard-Prompts.",
    "confirm-delete": "Bekreft sletting av standard ledetekstforvalg.",
  },

  // =========================
  // LEGAL CATEGORIES
  // =========================
  "legal-categories": {
    process: "Prosess",
    "process-stamning": "Erstatningskrav",
    "process-svaromal": "Forsvarsuttalelse",
    "process-yrkanden": "Krav og presentasjon",
    avtal: "Avtaler",
    "avtal-anstallning": "Ansettelsesavtaler",
    "avtal-finansiering": "Finansierings- og sikkerhetsavtaler",
    "avtal-licens": "Lisensavtaler",
    "due-diligence": "Due diligence",
    "due-diligence-avtal": "Kontraktsgjennomgang",
    "due-diligence-checklista": "Due diligence sjekkliste",
    "due-diligence-compliance": "Samsvarssjekk",
  },

  // =========================
  // VALIDATION
  // =========================
  validation: {
    responseHeader: "Her er svaret som ble generert",
    contextHeader: "Opprinnelig kontekst og kilder",
  },

  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================

  // Document Builder Page
  "document-builder": {
    title: "Innstillinger for dokumentbygger",
    description: "Kontroller innstillingene for dokumentbyggeren.",
    "toast-success": "Innstillinger oppdatert",
    "toast-fail": "Kunne ikke oppdatere innstillingene",
    save: "Lagre",
    saving: "Lagrer...",

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================

    "view-categories": "Vis alle kategorier",
    "hide-categories": "Skjul listen",
    "add-task": "Legg til juridisk oppgave",
    loading: "Laster...",
    table: {
      title: "Juridiske oppgaver",
      name: "Navn",
      "sub-category": "Underkategori",
      description: "Beskrivelse",
      prompt: "Juridisk oppgaveprompt",
      actions: "Handlinger",
      edit: "Rediger",
      "no-tasks": "Ingen juridiske oppgaver tilgjengelige.",
      delete: "Slett",
      "delete-confirm": "Er du sikker på at du vil slette denne kategorien?",
      "delete-success": "Kategorien ble slettet",
      "delete-error": "Kunne ikke slette kategorien",
      "delete-title": "Slett juridisk oppgave",
    },
    "create-task": {
      title: "Opprett juridisk oppgave",
      category: {
        name: "Kategorinavn",
        desc: "Angi hovedkategoriens navn.",
        placeholder: "Skriv inn kategorinavn",
        type: "Kategoritype",
        new: "Opprett ny kategori",
        existing: "Bruk eksisterende kategori",
        select: "Velg kategori",
        "select-placeholder": "Velg en eksisterende kategori",
      },
      subcategory: {
        name: "Juridisk oppgavenavn",
        desc: "Angi navnet på den juridiske oppgaven.",
        placeholder: "Skriv inn juridisk oppgavenavn",
      },
      description: {
        name: "Beskrivelse",
        desc: "Gi en beskrivelse av kategorien og den juridiske oppgaven.",
        placeholder: "Skriv inn en kort beskrivelse",
      },
      prompt: {
        name: "Juridisk oppgaveprompt",
        desc: "Skriv inn prompten som skal brukes for denne juridiske oppgaven. Du kan også laste opp eksempeldokumenter ved hjelp av knappene ovenfor for å legge til innholdseksempler i prompten din.",
        placeholder:
          "Skriv inn juridisk oppgaveprompt eller last opp eksempeldokumenter for å forbedre prompten...",
      },
      submitting: "Sender...",
      submit: "Send",
      validation: {
        "category-required": "Kategorinavn er påkrevd.",
        "subcategory-required": "Juridisk oppgavenavn er påkrevd.",
        "description-required": "Beskrivelse er påkrevd.",
        "prompt-required": "Juridisk prompt er påkrevd.",
      },
    },
    "edit-task": {
      title: "Rediger juridisk oppgave",
      submitting: "Oppdaterer...",
      submit: "Oppdater oppgave",
      subcategory: {
        name: "Navn på juridisk oppgave",
        desc: "Skriv inn et nytt navn for denne juridiske oppgaven",
        placeholder: "Skriv inn juridisk oppgave...",
      },
      description: {
        name: "Beskrivelse og brukerinstruksjoner",
        desc: "Skriv inn beskrivelse og brukerinstruksjoner for denne juridiske oppgaven",
        placeholder: "Skriv inn beskrivelse og brukerinstruksjoner...",
      },
      prompt: {
        name: "Juridisk oppgaveprompt",
        desc: "Skriv inn prompten som skal brukes for denne juridiske oppgaven. Du kan også laste opp eksempeldokumenter ved hjelp av knappene ovenfor for å legge til innholdseksempler i prompten din.",
        placeholder:
          "Skriv inn juridisk oppgaveprompt eller last opp eksempeldokumenter for å forbedre prompten...",
      },
      validation: {
        "subcategory-required": "Navn på juridisk oppgave er påkrevd",
        "description-required": "Beskrivelse er påkrevd",
        "prompt-required": "Juridisk oppgaveprompt er påkrevd",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Krever valg av hoveddokument",
      "requires-main-doc-description":
        "Hvis avkrysset, må brukeren velge hoveddokumentet fra de opplastede filene når denne oppgaven utføres. Dette er sterkt anbefalt for juridiske oppgaver som involverer svar på et brev eller en rettsinnlevering eller lignende, siden det strukturerer resultatet basert på dokumentet som besvares.",
      "requires-main-doc-placeholder": "Ja eller Nei",
      "requires-main-doc-explanation-default":
        "Et valg er nødvendig da dette bestemmer hvordan dokumentet vil bli bygget.",
      "requires-main-doc-explanation-yes":
        "Hvis 'Ja', vil brukeren måtte velge et hoveddokument når denne juridiske oppgaven startes. Dette dokumentet vil være sentralt for oppgavens arbeidsflyt.",
      "requires-main-doc-explanation-no":
        "Hvis 'Nei', vil den juridiske oppgaven fortsette uten å kreve et forhåndsvalgt hoveddokument. Oppgaven vil mer dynamisk skape output basert på alle opplastede dokumenter og den juridiske oppgaven.",
    },
    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Se generatorprompt",
    reviewGeneratorPromptButtonTooltip:
      "Se den nøyaktige promptmalen som ble brukt for å generere forslaget til juridisk oppgave. (Kun administrator)",
    reviewGeneratorPromptTitle: "Gjennomgang av generatorprompt",
    reviewPromptLabel: "Følgende prompt ble brukt for generering:",
    reviewPromptTextareaLabel: "Innhold i generatorprompt",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Prompter for dokumentsammendrag",
          description:
            "Konfigurer system- og brukerprompter for dokumentsammendrag.",
        },
        document_relevance: {
          title: "Prompter for dokumentrelevans",
          description:
            "Konfigurer system- og brukerprompter for dokumentrelevans.",
        },
        section_drafting: {
          title: "Prompter for seksjonsutkast",
          description:
            "Konfigurer system- og brukerprompter for seksjonsutkast.",
        },
        section_legal_issues: {
          title: "Prompter for juridiske problemer i seksjoner",
          description:
            "Konfigurer system- og brukerprompter for juridiske problemer i seksjoner.",
        },
        memo_creation: {
          title: "Prompter for memo-opprettelse",
          description: "Konfigurer prompter for memo-opprettelse.",
        },
        section_index: {
          title: "Prompter for seksjonsindeks",
          description: "Konfigurer prompter for seksjonsindeks.",
        },
        select_main_document: {
          title: "Prompter for valg av hoveddokument",
          description:
            "Konfigurer system- og brukerprompter for valg av hoveddokument.",
        },
        section_list_from_main: {
          title: "Prompter for seksjonsliste fra hoveddokument",
          description:
            "Konfigurer system- og brukerprompter for seksjonsliste fra hoveddokument.",
        },
        section_list_from_summaries: {
          title: "Prompter for seksjonsliste fra sammendrag",
          description:
            "Konfigurer system- og brukerprompter for seksjonsliste fra sammendrag.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Dokumentsammendrag (System)",
      "document-summary-system-description":
        "System-prompt som instruerer AI om hvordan man oppsummerer innholdet i et dokument og dets relevans for en juridisk oppgave.",
      "document-summary-user-label": "Dokumentsammendrag (Bruker)",
      "document-summary-user-description":
        "Bruker-prompt-mal for å generere en detaljert oppsummering av dokumentinnhold i forhold til en spesifikk juridisk oppgave.",

      // Document Relevance
      "document-relevance-system-label": "Dokumentrelevans (System)",
      "document-relevance-system-description":
        "System-prompt for å vurdere om et dokument er relevant for en juridisk oppgave, med forventning om et sant/usant svar.",
      "document-relevance-user-label": "Dokumentrelevans (Bruker)",
      "document-relevance-user-description":
        "Bruker-prompt-mal for å sjekke om dokumentinnholdet er relevant for en gitt juridisk oppgave.",

      // Section Drafting
      "section-drafting-system-label": "Seksjonsutkast (System)",
      "section-drafting-system-description":
        "System-prompt for å generere en enkelt dokumentseksjon i profesjonell juridisk stil ved bruk av spesifiserte dokumenter og kontekst.",
      "section-drafting-user-label": "Seksjonsutkast (Bruker)",
      "section-drafting-user-description":
        "Bruker-prompt-mal for å generere en spesifikk seksjon av et juridisk dokument, med hensyn til tittel, oppgave, kildedokumenter og tilstøtende seksjoner.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Identifisering av juridiske problemer for seksjon (System)",
      "section-legal-issues-system-description":
        "System-prompt for å identifisere spesifikke juridiske temaer som det bør hentes faktainformasjon for å støtte utarbeidelsen av en dokumentseksjon.",
      "section-legal-issues-user-label":
        "Identifisering av juridiske problemer for seksjon (Bruker)",
      "section-legal-issues-user-description":
        "Bruker-prompt-mal for å liste opp juridiske temaer eller datapunkter for å hente kontekstuell informasjon relevant for en spesifikk dokumentseksjon og juridisk oppgave.",

      // Memo Creation
      "memo-creation-template-label": "Standard memo-opprettelsesmal",
      "memo-creation-template-description":
        "Prompt-mal for å lage et juridisk notat som adresserer et spesifikt juridisk problem, med hensyn til de medfølgende dokumentene og oppgavekonteksten.",

      // Section Index
      "section-index-system-label": "Seksjonsindeks (System)",
      "section-index-system-description":
        "System-prompt for å generere en strukturert indeks av seksjoner for et juridisk dokument.",

      // Select Main Document
      "select-main-document-system-label": "Velg hoveddokument (System)",
      "select-main-document-system-description":
        "System-prompt for å identifisere det mest relevante hoveddokumentet for en juridisk oppgave fra flere dokumentsammendrag.",
      "select-main-document-user-label": "Velg hoveddokument (Bruker)",
      "select-main-document-user-description":
        "Bruker-prompt-mal for å identifisere hoveddokumentet for en juridisk oppgave basert på sammendrag av flere dokumenter.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Seksjonsliste fra hoveddokument (System)",
      "section-list-from-main-system-description":
        "System-prompt for å lage en JSON-strukturert liste over seksjoner for et juridisk dokument basert på innholdet i hoveddokumentet og den juridiske oppgaven.",
      "section-list-from-main-user-label":
        "Seksjonsliste fra hoveddokument (Bruker)",
      "section-list-from-main-user-description":
        "Bruker-prompt-mal for å gi den juridiske oppgaven og hoveddokumentinnholdet for å generere en seksjonsliste.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Seksjonsliste fra sammendrag (System)",
      "section-list-from-summaries-system-description":
        "System-prompt for å lage en JSON-strukturert liste over seksjoner basert på dokumentsammendrag og den juridiske oppgaven, når det ikke finnes et hoveddokument.",
      "section-list-from-summaries-user-label":
        "Seksjonsliste fra sammendrag (Bruker)",
      "section-list-from-summaries-user-description":
        "Bruker-prompt-mal for å gi den juridiske oppgaven og dokumentsammendrag for å generere en seksjonsliste, når det ikke finnes et hoveddokument.",
    },
  },

  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Registrer Rexor-prosjekt",
    "project-id": "Prosjekt-ID",
    "resource-id": "Ressurs-ID",
    "activity-id": "Aktivitets-ID",
    register: "Registrer prosjekt",
    registering: "registrerer...",
    "not-active": "Denne saken er ikke aktiv for registrering",
    account: {
      title: "Logg inn på Rexor",
      username: "Brukernavn",
      password: "Passord",
      "no-token": "Ingen token mottatt i handleLoginSuccess",
      logout: "Logg ut",
      "no-user": "Vennligst logg inn først",
      connected: "Koblet til Rexor",
      "not-connected": "Ikke tilkoblet",
      "change-account": "Bytt konto",
      "session-expired": "Økt utløpt. Vennligst logg inn igjen.",
    },
    "hide-article-transaction": "Skjul tidstransaksjons-skjema",
    "show-article-transaction": "Vis tidstransaksjons-skjema",
    "article-transaction-title": "Legg til tidstransaksjon",
    "registration-date": "Registreringsdato",
    description: "Beskrivelse",
    "description-internal": "Intern beskrivelse",
    "hours-worked": "Timer arbeidet",
    "invoiced-hours": "Fakturerte timer",
    invoiceable: "Fakturerbar",
    "sending-article-transaction": "Sender tidstransaksjon...",
    "save-article-transaction": "Lagre tidstransaksjon",
    "project-not-register": "Prosjektet må registreres først.",
    "article-transaction-error": "Kunne ikke skrive tidstransaksjon",
    "not-exist": "Denne saken kunne ikke finnes",
    "invoice-text": "Foynet antall oppslag",
  },

  // =========================
  // OPTIONS
  // =========================
  options: {
    yes: "Ja",
    no: "Nei",
  },

  // =========================
  // AZURE AI
  // =========================

  // =========================
  // COHERE
  // =========================

  // =========================
  // JINA
  // =========================
  jina: {
    "api-key": "Jina API-nøkkel",
    "api-key-placeholder": "Skriv inn din Jina API-nøkkel",
    "model-preference": "Modellpreferanse",
  },

  // =========================
  // OLLAMA
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Maks embedding chunk lengde",
  },

  // =========================
  // DOCX EDITOR
  // =========================
  "docx-edit": {
    "edit-instructions":
      "Skriv inn instruksjoner for hvordan du vil redigere dokumentet. Vær spesifikk om hvilke endringer du vil gjøre.",
    "instructions-placeholder":
      "f.eks. Rett grammatiske feil, gjør tonen mer formell, legg til et konklusjonsavsnitt...",
    "process-button": "Behandle dokument",
    "upload-docx": "Last opp DOCX",
    "processing-upload": "Behandler...",
    "content-extracted": "Innhold hentet fra DOCX-fil",
    "file-type-note": "Kun .docx-filer støttes",
    "upload-error": "Feil ved opplasting av fil: ",
    "no-instructions": "Vennligst skriv inn redigeringsinstruksjoner",
    "process-error": "Feil ved behandling av dokument: ",
    "changes-highlighted": "Dokument med uthevede endringer",
    "download-button": "Last ned dokument",
    "start-over-button": "Start på nytt",
    "no-document": "Ingen dokument tilgjengelig for nedlasting",
    "download-error": "Feil ved nedlasting av dokument: ",
    "download-success": "Dokument lastet ned",
    processing: "Behandler dokument...",
    "instructions-used": "Brukte instruksjoner",
    "import-success": "DOCX-innhold importert",
    "edit-success": "DOCX-innhold oppdatert",
    "canvas-document-title": "Canvas-dokument",
    "upload-button": "Last opp DOCX",
    "download-as-docx": "Last ned som DOCX",
    "output-example": "Eksempel på utdata",
    "output-example-desc":
      "Last opp en DOCX-fil for å legge til eksempelinnhold i prompten din",
    "content-examples-tag-open": "<INNHOLDS_EKSEMPEL>",
    "content-examples-tag-close": "</INNHOLDS_EKSEMPEL>",
    "content-examples-info":
      "<INFO>Dette er et eksempel på innholdet som skal produseres, fra en lignende juridisk oppgave. Merk at dette eksempelinnholdet kan være mye kortere eller lengre enn innholdet som nå skal produseres.</INFO>",
    "contains-example-content": "[Inneholder eksempelinnhold]",
  },

  // =========================
  // OPENAI
  // =========================

  // =========================
  // VOYAGEAI
  // =========================
  voyageai: {
    "api-key": "VoyageAI API-nøkkel",
    "api-key-placeholder": "Skriv inn din VoyageAI API-nøkkel",
    "model-preference": "Modellpreferanse",
  },

  // =========================
  // ANTHROPIC
  // =========================

  // =========================
  // FIREWORKSAI
  // =========================

  // =========================
  // PERPLEXITY
  // =========================

  // =========================
  // TOGETHERAI
  // =========================

  // =========================
  // METRICS VISIBILITY
  // =========================
  metrics: {
    visibility: {
      hover: "Metrikker er synlige.",
      available: "Metrikker er tilgjengelige.",
    },
  },

  // =========================
  // PROMPT ERRORS
  // =========================
  prompt: {
    error: {
      empty: "Prompt kan ikke være tom",
      upgrade: "Feil ved oppgradering av prompt",
    },
    decline: "Avslå",
  },

  // =========================
  // AGENT MENU
  // =========================
  "agent-menu": {
    ability: {
      "chart-generation": "Diagramgenerering",
      "list-documents": "Liste dokumenter",
      "rag-search": "RAG-søk",
      "save-file-to-browser": "Lagre fil til nettleser",
      "summarize-document": "Oppsummer dokument",
      "web-browsing": "Nettsurfing",
      "web-scraping": "Nettscraping",
    },
    "default-agent": "Standard agent",
  },

  // =========================
  // PIPER TTS OPTIONS
  // =========================
  piperTTS: {
    description:
      "Alle PiperTTS-modeller kjører lokalt i nettleseren din. Dette kan være ressurskrevende på enheter med lav ytelse.",
    "voice-model": "Velg stemmemodell",
    "loading-models": "-- laster tilgjengelige modeller --",
    "stored-indicator":
      'Tegnet "✔" indikerer at modellen allerede er lagret lokalt og ikke trenger nedlasting ved kjøring.',
    "flush-cache": "Tøm stemmecache",
    "flush-success": "Alle stemmer tømt fra nettleserlageret",
    demo: {
      stop: "Stopp demo",
      loading: "Laster stemme",
      play: "Spill av eksempel",
      text: "Hei, velkommen til IST Legal!",
    },
  },

  // =========================
  // ADMIN AGENTS
  // =========================
  agents: {
    title: "Agent-ferdigheter",
    "agent-skills": "Konfigurer og administrer agent-funksjoner",
    "custom-skills": "Tilpassede ferdigheter",
    back: "Tilbake",
    "select-skill": "Velg en ferdighet å konfigurere",
    "preferences-saved": "Agent-preferanser lagret",
    "preferences-failed": "Kunne ikke lagre agent-preferanser",
    "skill-status": {
      on: "På",
      off: "Av",
    },
    "skill-config-updated": "Agent-ferdighetskonfigurasjon oppdatert",
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chatSettings: {
    placeholder: {
      drafting:
        "Gitt følgende samtale, relevant kontekst og et oppfølgingsspørsmål, svar på hva brukeren spør om nå. Returner kun ditt svar på spørsmålet basert på informasjonen over, og følg brukerens instruksjoner ved behov.",
      "legal-questions":
        "Hvilke juridiske spørsmål oppstår ut fra den gitte konteksten med prompten",
      "legal-memo": "Gi et memo om hver av disse juridiske problemstillingene",
    },
  },

  // =========================
  // LOGGING
  // =========================
  logging: {
    show: "vis",
    hide: "skjul",
    "event-metadata": "Hendelsesmetadata",
  },

  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Vis kode",
    enable: "Aktiver",
    disable: "Deaktiver",
    "all-domains": "alle",
    "disable-confirm":
      "Er du sikker på at du vil deaktivere denne embed?\nNår den er deaktivert, vil ikke embed lenger svare på noen chatforespørsler.",
    "delete-confirm":
      "Er du sikker på at du vil slette denne embed?\nNår den er slettet, vil ikke embed lenger svare på chatter eller være aktiv.\n\nDenne handlingen kan ikke angres.",
    "disabled-toast": "Embed er deaktivert",
    "enabled-toast": "Embed er aktiv",
  },

  badges: {
    default: {
      text: "Standard",
      tooltip:
        "Denne ferdigheten er aktivert som standard og kan ikke slås av.",
    },
  },

  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "{{provider}} logo",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "LM Studio-innbyggingsmodell",
      "max-chunk-length": "Maksimal segmentlengde",
      "max-chunk-length-help":
        "Maksimal lengde på tekstsegmenter for innebygging.",
      "hide-endpoint": "Skjul manuell inndata for endepunkt",
      "show-endpoint": "Vis manuell inndata for endepunkt",
      "base-url": "LM Studio Base-URL",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Skriv inn URL-en der LM Studio kjører.",
      "auto-detect": "Automatisk oppdag",
      "loading-models": "--laster tilgjengelige modeller--",
      "enter-url-first": "Skriv inn LM Studio-URL først",
      "model-help":
        "Velg LM Studio-modellen for innebygging. Modellene lastes etter at du har angitt en gyldig LM Studio-URL.",
      "loaded-models": "Dine lastede modeller",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Ollama-innbyggingsmodell",
      "max-chunk-length": "Maksimal segmentlengde",
      "max-chunk-length-help":
        "Maksimal lengde på tekstsegmenter for innebygging.",
      "hide-endpoint": "Skjul manuell inndata for endepunkt",
      "show-endpoint": "Vis manuell inndata for endepunkt",
      "base-url": "Ollama Base-URL",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Skriv inn URL-en der Ollama kjører.",
      "auto-detect": "Automatisk oppdag",
      "loading-models": "--laster tilgjengelige modeller--",
      "enter-url-first": "Skriv inn Ollama-URL først",
      "model-help":
        "Velg Ollama-modellen for innebygging. Modellene lastes etter at du har angitt en gyldig Ollama-URL.",
      "loaded-models": "Dine lastede modeller",
    },
    // LiteLLM Embedding Options

    // Cohere Embedding Options
    cohere: {
      "api-key": "Cohere API-nøkkel",
      "api-key-placeholder": "Skriv inn din Cohere API-nøkkel",
      "model-label": "Modellvalg",
      "available-models": "Tilgjengelige innebyggingsmodeller",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Jina API-nøkkel",
      "api-key-format": "Jina API-nøkkel må starte med 'jina_'",
      "api-key-placeholder": "Skriv inn din Jina API-nøkkel",
      "api-key-error": "Ugyldig Jina API-nøkkel",
      "model-label": "Modellvalg",
      "available-models": "Tilgjengelige innebyggingsmodeller",
      "embedding-type": "Type innebygging",
      "available-types": "Tilgjengelige typer innebygging",
      dimensions: "Dimensjoner",
      "available-dimensions": "Tilgjengelige dimensjoner",
      task: "Oppgave",
      "available-tasks": "Tilgjengelige oppgaver",
      "late-chunking": "Forsinket segmentering",
      "late-chunking-help":
        "Aktiver forsinket segmentering for dokumentbehandling",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Navn på innebyggingsmodell",
      "hide-endpoint": "Skjul avanserte innstillinger",
      "show-endpoint": "Vis avanserte innstillinger",
      "base-url": "LocalAI Base-URL",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Skriv inn URL-en der LocalAI kjører.",
      "auto-detect": "Automatisk oppdag",
      "loading-models": "-- laster tilgjengelige modeller --",
      "waiting-url": "-- venter på URL --",
      "loaded-models": "Dine lastede modeller",
    },
    // Generic OpenAI-Compatible Embedding Options

    // OpenAI Embedding Options

    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "VoyageAI API-nøkkel",
      "api-key-placeholder": "Skriv inn din VoyageAI API-nøkkel",
      "model-label": "Modellvalg",
      "available-models": "Tilgjengelige innebyggingsmodeller",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Azure OpenAI-tjenesteendepunkt",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help":
        "Skriv inn URL-en til ditt Azure OpenAI-tjenesteendepunkt",
      "api-key": "Azure OpenAI API-nøkkel",
      "api-key-placeholder": "Skriv inn din Azure OpenAI API-nøkkel",
      "api-key-help": "Skriv inn din Azure OpenAI API-nøkkel for autentisering",
      "deployment-name": "Distribusjonsnavn for innebyggingsmodell",
      "deployment-name-placeholder":
        "Skriv inn navnet på distribusjonen av din Azure OpenAI-innebyggingsmodell",
      "deployment-name-help":
        "Distribusjonsnavnet for din Azure OpenAI-innebyggingsmodell",
    },
    // Native Embedding Options
    native: {
      description: "Bruker innfødt innebyggingsleverandør for tekstbehandling",
    },
  },

  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "API-nøkler",
    description: "Administrer API-nøkler for tilkobling til denne instansen.",
    "generate-key": "Generer ny API-nøkkel",
    "table-headers": {
      "connection-string": "Tilkoblingsstreng",
      "created-by": "Opprettet av",
      "created-at": "Opprettet",
      actions: "Handlinger",
    },
    "no-keys": "Ingen API-nøkler funnet",
    modal: {
      title: "Ny nettleserutvidelse API-nøkkel",
      "multi-user-warning":
        "Advarsel: Du er i flerbrukermodus. Denne API-nøkkelen vil gi tilgang til alle arbeidsområder tilknyttet kontoen din. Vær forsiktig med deling.",
      "create-description":
        'Etter å ha klikket på "Opprett API-nøkkel", vil denne instansen prøve å opprette en ny API-nøkkel for nettleserutvidelsen.',
      "connection-help":
        'Hvis du ser "Koblet til IST Legal" i utvidelsen, var tilkoblingen vellykket. Hvis ikke, vennligst kopier tilkoblingsstrengen og lim den inn i utvidelsen manuelt.',
      cancel: "Avbryt",
      "create-key": "Opprett API-nøkkel",
      "copy-key": "Kopier API-nøkkel",
      "key-copied": "API-nøkkel kopiert!",
    },
    tooltips: {
      "copy-connection": "Kopier tilkoblingsstreng",
      "auto-connect": "Koble til utvidelsen automatisk",
    },
    confirm: {
      revoke:
        "Er du sikker på at du vil tilbakekalle denne nettleserutvidelsens API-nøkkel?\nEtter dette vil den ikke lenger være brukbar.\n\nDenne handlingen kan ikke angres.",
    },
    toasts: {
      "key-revoked": "Nettleserutvidelsens API-nøkkel er permanent tilbakekalt",
      "revoke-failed": "Kunne ikke tilbakekalle API-nøkkel",
      copied: "Tilkoblingsstreng kopiert til utklippstavlen",
      connecting: "Prøver å koble til nettleserutvidelsen...",
    },
    "revoke-title": "Tilbakekall nettleserutvidelsens API-nøkkel",
    "revoke-message":
      "Er du sikker på at du vil tilbakekalle denne nettleserutvidelsens API-nøkkel?\nEtter dette vil den ikke lenger være brukbar.\n\nDenne handlingen kan ikke angres.",
  },

  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Eksperimentelle Funksjoner",
    description: "Funksjoner som for tiden er i betatesting",
    "live-sync": {
      title: "Sanntidsdokumentsynkronisering",
      description:
        "Aktiver automatisk innholdssynkronisering fra eksterne kilder",
      "manage-title": "Overvåkede dokumenter",
      "manage-description":
        "Dette er alle dokumentene som for tiden overvåkes i din instans. Innholdet i disse dokumentene vil bli synkronisert regelmessig.",
      "document-name": "Dokumentnavn",
      "last-synced": "Sist synkronisert",
      "next-refresh": "Tid til neste oppdatering",
      "created-on": "Opprettet den",
      "auto-sync": "Automatisk innholdssynkronisering",
      "sync-description":
        'Aktiver muligheten til å spesifisere en innholdskilde som skal "overvåkes". Overvåket innhold vil regelmessig bli hentet og oppdatert i denne instansen.',
      "sync-workspace-note":
        "Overvåket innhold vil automatisk oppdateres i alle arbeidsområder der de er referert.",
      "sync-limitation":
        "Denne funksjonen gjelder kun for webbasert innhold, som nettsteder, Confluence, YouTube og GitHub-filer.",
      documentation: "Funksjonsdokumentasjon og advarsler",
      "manage-content": "Administrer overvåket innhold",
    },
    tos: {
      title: "Bruksvilkår for eksperimentelle funksjoner",
      description:
        "Eksperimentelle funksjoner på denne plattformen er funksjoner som vi tester og som er valgfrie. Vi vil proaktivt informere om eventuelle bekymringer som kan oppstå før godkjenning av en funksjon.",
      "possibilities-title":
        "Bruk av en funksjon på denne siden kan resultere i, men er ikke begrenset til, følgende muligheter:",
      possibilities: {
        "data-loss": "Tap av data.",
        "quality-change": "Endring i resultatkvalitet.",
        "storage-increase": "Økt lagringsplass.",
        "resource-consumption": "Økt ressursforbruk.",
        "cost-increase":
          "Økte kostnader eller bruk av tilkoblede LLM- eller innbyggingsleverandører.",
        "potential-bugs":
          "Potensielle feil eller problemer ved bruk av denne applikasjonen.",
      },
      "conditions-title":
        "Bruk av en eksperimentell funksjon kommer med følgende ikke-uttømmende liste over betingelser:",
      conditions: {
        "future-updates":
          "Funksjonen eksisterer kanskje ikke i fremtidige oppdateringer.",
        stability: "Funksjonen som brukes er for tiden ikke stabil.",
        availability:
          "Funksjonen kan være utilgjengelig i fremtidige versjoner, konfigurasjoner eller abonnementer av denne instansen.",
        privacy:
          "Dine personverninnstillinger vil bli respektert ved bruk av en betafunksjon.",
        changes: "Disse betingelsene kan endres i fremtidige oppdateringer.",
      },
      "read-more": "Hvis du vil lese mer kan du henvise til",
      contact: "eller kontakt",
      reject: "Avvis & Lukk",
      accept: "Jeg forstår",
    },
    "update-failed": "Oppdatering av eksperimentelle funksjoner mislyktes",
  },

  // =========================
  // AZURE AI
  // =========================

  // =========================
  // BEDROCK
  // =========================

  // =========================
  // DEEPSEEK
  // =========================

  // =========================
  // GEMINI
  // =========================

  // =========================
  // GENERIC
  // =========================
  generic: {
    "api-key": "API-nøkkel",
    "api-key-placeholder": "Skriv inn din API-nøkkel",
    "base-url": "Base URL",
    "base-url-placeholder": "Skriv inn base URL",
  },

  // =========================
  // GROQ
  // =========================

  // =========================
  // HUGGINGFACE
  // =========================

  // =========================
  // KOBOLDCPP
  // =========================

  // =========================
  // LITELLM
  // =========================
  litellm: {
    "api-key": "LiteLLM API-nøkkel",
    "api-key-placeholder": "Skriv inn din LiteLLM API-nøkkel",
  },

  // =========================
  // LOADING MODELS
  // =========================
  "loading-models": "Laster modeller...",

  // =========================
  // MISTRAL
  // =========================

  // =========================
  // MODEL SELECTION
  // =========================
  "model-selection": "Modellvalg",

  // =========================
  // NATIVE
  // =========================

  // =========================
  // OLLAMALLMSELECTION
  // =========================

  // =========================
  // OPENROUTER
  // =========================

  // =========================
  // SAFETY SETTING
  // =========================
  "safety-setting": "Sikkerhetsinnstilling",

  // =========================
  // STABLE
  // =========================
  stable: "Stabil",

  // =========================
  // TOAST
  // =========================
  toast: {
    success: "Suksess",
    error: "Feil",
    warning: "Advarsel",
    info: "Informasjon",
    settings: {
      "welcome-messages-failed":
        "Kunne ikke lagre velkomstmeldinger: {{error}}",
      "welcome-messages-fetch-failed": "Kunne ikke hente velkomstmeldinger",
      "welcome-messages-empty":
        "Vennligst skriv inn enten en overskrift eller tekstmelding",
      "welcome-messages-success": "Velkomstmeldinger lagret",
      "prompt-examples-failed": "Kunne ikke lagre promptexempel: {{error}}",
      "prompt-examples-success": "Promptexempel lagret",
      "prompt-examples-validation":
        "Eksempel {{number}} saknar obligatoriske felt: {{fields}}",
    },
    document: {
      "move-success": "Dokumenter flyttet",
      "pdr-failed": "Kunne ikke PDR-dokument: {{message}}",
      "watch-failed": "Kunne ikke overvåke dokument: {{message}}",
      "pdr-added": "Dokument tilført til Parent Document Retrieval",
      "pdr-removed": "Dokument fjernet fra Parent Document Retrieval",
      "pin-success": "Dokument {{action}} workspace",
    },
    experimental: {
      "feature-enabled": "Eksperimentelle funksjoner aktivert",
      "feature-disabled": "Eksperimentelle funksjoner deaktivert",
      "update-failed":
        "Kunne ikke oppdatere status for eksperimentell funksjon",
      "features-enabled":
        "Eksperimentelle funksjoner aktivert. Siden lastes på nytt.",
      "live-sync": {
        enabled: "Live-dokumentsynkronisering aktivert",
        disabled: "Live-dokumentsynkronisering deaktivert",
      },
    },
  },

  // =========================
  // XAI
  // =========================

  // =========================
  // TEXTGENWEBUI
  // =========================
  textgenwebui: {
    "api-key": "TextGenWebUI API-nøkkel",
    "api-key-placeholder": "Skriv inn din TextGenWebUI API-nøkkel",
    "base-url": "Base URL",
    "base-url-placeholder": "http://localhost:5000/v1",
  },

  promptLogging: {
    title: "Loggføring av prompt-utdata",
    description:
      "Aktiver eller deaktiver loggføring av prompt-utdata for systemovervåking.",
    label: "Loggføring av prompt-utdata: ",
    state: {
      enabled: "Aktivert",
      disabled: "Deaktivert",
    },
  },

  userAccess: {
    title: "Tillat brukertilgang",
    description:
      "Aktiver for å la vanlige brukere få tilgang til juridiske oppgaver. Som standard har bare superbrukere, ledere og administratorer tilgang.",
    label: "Brukertilgang: ",
    state: {
      enabled: "Aktivert",
      disabled: "Deaktivert",
    },
  },
  "cdb-llm-preference": {
    title: "CDB LLM-preferanse",
    settings: "CDB LLM",
    description: "Konfigurer LLM-leverandøren for CDB",
  },
  "template-llm-preference": {
    title: "Mal-LLM-preferanse",
    settings: "Malen LLM",
    description:
      "Velg LLM-leverandøren som brukes for å generere dokumentmaler. Standard er systemleverandøren.",
    "toast-success": "Mal-LLM-innstillinger oppdatert",
    "toast-fail": "Kunne ikke oppdatere Mal-LLM-innstillinger",
    saving: "Lagrer...",
    "save-changes": "Lagre endringer",
  },
  "custom-user-ai": {
    title: "Tilpasset bruker-AI",
    settings: "Tilpasset bruker-AI",
    description: "Konfigurer tilpasset AI-leverandør",
    "custom-model-reference": "Tilpasset modellnavn og beskrivelse",
    "custom-model-reference-description":
      "Legg til en tilpasset referanse for denne modellen. Dette vil være synlig når du bruker den tilpassede bruker-AI-motorvelgeren i promptpanelet.",
    "custom-model-reference-name": "Tilpasset modellnavn",
    "custom-model-reference-description-label": "Modellbeskrivelse (Valgfritt)",
    "custom-model-reference-description-placeholder":
      "Skriv inn en valgfri beskrivelse for denne modellen",
    "custom-model-reference-name-placeholder":
      "Skriv inn et tilpasset navn for denne modellen",
    "model-ref-placeholder":
      "Skriv inn et tilpasset navn eller beskrivelse for dette modelloppsettet",
    "enter-custom-model-reference": "Angi et tilpasset navn for denne modellen",
    "standard-engine": "Standard AI-motor",
    "standard-engine-description":
      "Vår standardmotor som er nyttig for de fleste oppgaver",
    "dynamic-context-window-percentage":
      "Prosentandel for dynamisk kontekstvindu",
    "dynamic-context-window-percentage-desc":
      "Kontrollerer hvor mye av LLM-kontekstvinduet som kan brukes til ytterligere kilder (10-100%)",
    "no-alternative-title": "Ingen alternativ modell valgt",
    "no-alternative-desc":
      "Når dette alternativet er valgt, har ikke brukerne mulighet til å velge en alternativ modell.",
    "select-option": "Velg tilpasset AI-profil",
    "option-number": "Alternativ {{number}}",
    "llm-provider-selection": "LLM-leverandørvalg",
    "llm-provider-selection-desc":
      "Velg LLM-leverandøren for denne tilpassede AI-konfigurasjonen",
    "custom-option": "Tilpasset alternativ",
    tab: {
      "custom-1": "Tilpasset motor 1",
      "custom-2": "Tilpasset motor 2",
      "custom-3": "Tilpasset motor 3",
      "custom-4": "Tilpasset motor 4",
      "custom-5": "Tilpasset motor 5",
      "custom-6": "Tilpasset motor 6",
    },
    engine: {
      "custom-1": "Tilpasset motor 1",
      "custom-2": "Tilpasset motor 2",
      "custom-3": "Tilpasset motor 3",
      "custom-4": "Tilpasset motor 4",
      "custom-5": "Tilpasset motor 5",
      "custom-6": "Tilpasset motor 6",
      "custom-1-title": "Tilpasset motor 1",
      "custom-2-title": "Tilpasset motor 2",
      "custom-3-title": "Tilpasset motor 3",
      "custom-4-title": "Tilpasset motor 4",
      "custom-5-title": "Tilpasset motor 5",
      "custom-6-title": "Tilpasset motor 6",
      "custom-1-description": "Konfigurer innstillinger for Tilpasset motor 1",
      "custom-2-description": "Konfigurer innstillinger for Tilpasset motor 2",
      "custom-3-description": "Konfigurer innstillinger for Tilpasset motor 3",
      "custom-4-description": "Konfigurer innstillinger for Tilpasset motor 4",
      "custom-5-description": "Konfigurer innstillinger for Tilpasset motor 5",
      "custom-6-description": "Konfigurer innstillinger for Tilpasset motor 6",
    },
    saving: "Lagrer...",
    "save-changes": "Lagre endringer",
    "model-ref-saved": "Tilpassede modellinnstillinger lagret",
    "model-ref-save-failed":
      "Kunne ikke lagre tilpassede modellinnstillinger: {{error}}",
    "llm-settings-save-failed": "Kunne ikke lagre LLM-innstillinger: {{error}}",
    "settings-fetch-failed": "Kunne ikke hente innstillinger",
    "llm-saved": "LLM-innstillinger lagret",
    "select-provider-first":
      "Vennligst velg en LLM-leverandør for å konfigurere modellinnstillinger. Når den er konfigurert, vil dette alternativet være valgbart som en tilpasset AI-motor i brukergrensesnittet.",
  },

  // =========================
  // CHAT LOGS & PREVIEW
  // =========================
  chat_logs: {
    display_description: "Vis loggføring av rådata, åpne og last ned filen",
    display_prompt_output: "Vis rådata",
    loading_prompt_output: "Laster rådata...",
    not_available: "*** Rådata er ikke tilgjengelig for denne chatten.",
    token_count: "Tokens (i alle rådata): {{count}}",
    token_count_detailed:
      "Tokens til LLM: {{promptTokens}} | Tokens i LLM-svar: {{completionTokens}} | Totalt antall tokens: {{totalTokens}}",
  },

  // LLM Provider specific translations
  "llm-provider.textgenwebui": "Koble til en Text Generation WebUI-instans.",
  "llm-provider.litellm": "Koble til en LLM via LiteLLM.",
  "llm-provider.openai-generic":
    "Koble til en OpenAI-kompatibel API-slutpunkt.",
  "llm-provider.system-default": "Bruk den innebygde Native-modellen.",

  // =========================
  // INVITATION RELATED KEYS
  // =========================
  invite: {
    "accept-button": "Aksepter invitasjon",
    newUser: {
      title: "Opprett et nytt konto",
      usernameLabel: "Brukernavn",
      passwordLabel: "Passord",
      description:
        "Etter at du har opprettet ditt konto, vil du kunne logge inn med disse legitimasjonsopplysningene og starte å bruke arbeidsområder.",
    },
  },

  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Kontekstvindu",
    "max-output-tokens": "Maks utdata-tokens",
    "output-limit": "Utdata-grense",
    tokens: "tokens",
    "fallback-value": "Fallback-verdi brukt",
  },

  // =========================
  // LEGAL TEMPLATES MODAL
  // =========================

  //moved to legalTemplates

  // =========================
  // CUSTOM LEGAL TEMPLATES MODAL
  // =========================

  // moved to customLegalTemplates.js

  // =========================
  // VARIOUS MODALS AND NEW FEATURES
  // =========================

  // Organization & Organizations translations for user management
  organization: {
    label: "Organisasjon",
    select: "-- Velg organisasjon --",
    none: "Ingen",
    "create-new": "+ Opprett ny organisasjon",
    "new-name": "Nytt organisasjonsnavn",
    "new-name-ph": "Skriv inn nytt organisasjonsnavn",
  },
  organizations: {
    "fetch-error": "Kunne ikke hente organisasjoner",
  },

  // =========================
  // REQUEST LEGAL ASSISTANCE
  // =========================
  "request-legal-assistance": {
    title: "Be om rettshjelp",
    description: "Konfigurer synligheten av knappen for å be om rettshjelp.",
    enable: "Aktiver forespørsel om rettshjelp",
    "law-firm-name": "Advokatfirmaets navn",
    "law-firm-placeholder": "Skriv inn advokatfirmaets navn",
    "law-firm-help":
      "Navnet på advokatfirmaet som vil håndtere forespørsler om rettshjelp",
    email: "E-post for rettshjelp",
    "email-placeholder": "Skriv inn e-postadresse for rettshjelp",
    "email-help": "E-postadresse dit forespørsler om rettshjelp vil bli sendt",
    "settings-saved": "Innstillinger for rettshjelp lagret vellykket",
    "save-error": "Kunne ikke lagre innstillinger for rettshjelp",
    status: "Knapp for rettshjelp: ",
    "load-error": "Kunne ikke laste innstillinger for rettshjelp",
    "save-button": "Lagre endringer",
    request: {
      title: "Be om rettshjelp",
      description:
        "Send en forespørsel til {{lawFirmName}} for rettshjelp, ferdigstilling av research eller andre konsultasjoner. Du vil bli varslet via e-post når forespørselen er behandlet.",
      button: "Be om rettshjelp",
      message: "Melding",
      "message-placeholder":
        "Skriv inn spesifikke instruksjoner eller informasjon for rettshjelpsteamet",
      send: "Send forespørsel",
      cancel: "Avbryt",
      error: "Kunne ikke sende forespørsel om rettshjelp",
      success: "Forespørsel om rettshjelp sendt vellykket",
      submitting: "Sender forespørsel...",
      submit: "Send forespørsel",
      partyName: "Partnavn",
      partyOrgId: "Organisasjonsnummer for part",
      partyNamePlaceholder: "Skriv inn navnet på din organisasjon",
      partyOrgIdPlaceholder: "Skriv inn ditt organisasjonsnummer",
      partyNameRequired: "Partnavn er påkrevd",
      partyOrgIdRequired: "Organisasjonsnummer for part er påkrevd",
      opposingPartyName: "Navn på motpart (hvis relevant)",
      opposingPartyOrgId: "Organisasjonsnummer for motpart (hvis kjent)",
      opposingPartyNamePlaceholder: "Skriv inn navnet på motparten",
      opposingPartyOrgIdPlaceholder: "Skriv inn motpartens organisasjonsnummer",
    },
  },

  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Fremgang for svarsgenerering",
    description:
      "Viser fremgangen for oppgaver for å fullføre prompten, basert på kobling til andre arbeidsområder og filstørrelser. Vinduet lukkes automatisk når alle trinn er fullført.",
    step_fetching_memos: "Henter juridiske data om aktuelle emner",
    step_processing_chunks: "Behandler opplastede dokumenter",
    step_combining_responses: "Ferdigstiller svar",
    sub_step_chunk_label: "Behandler dokumentgruppe {{index}}",
    sub_step_memo_label: "Hentet juridiske data fra {{workspaceSlug}}",
    placeholder_sub_task: "Køet steg",
    desc_fetching_memos:
      "Henter relevant juridisk informasjon fra tilkoblede arbeidsområder",
    desc_processing_chunks:
      "Analyserer og trekker ut informasjon fra dokumentgrupper",
    desc_combining_responses:
      "Sammenstiller informasjon til et omfattende svar",
  },

  // =========================
  // MCP SERVER PAGE
  // =========================
  mcp: {
    title: "MCP-serverhåndtering",
    description:
      "Administrer konfigurasjoner for Multi-Component Processing (MCP) server.",
    currentServers: "Nåværende servere",
    noServers: "Ingen MCP-servere konfigurert.",
    fetchError: "Kunne ikke hente servere: {{error}}",
    addServerButton: "Legg til ny server",
    addServerModalTitle: "Legg til ny MCP-server",
    addServerModalDesc:
      "Definer konfigurasjonen for den nye MCP-serverprosessen.",
    serverName: "Servernavn (Unik ID)",
    configJson: "Konfigurasjon (JSON)",
    addButton: "Legg til server",
    addSuccess: "Server lagt til.",
    addError: "Kunne ikke legge til server: {{error}}",
  },

  // =========================
  // ADMIN SYSTEM SETTINGS (UNIVERSITY MODE)
  // =========================
  admin: {
    system: {
      universityMode: {
        title: "Universitetsmodus",
        description:
          "Når aktiveres, skjuler validering, prompt-oppdatering, maler og web-søk for alle brukere.",
        enable: "Aktiver Universitetsmodus",
        saved: "Universitetsmodus-innstillinger lagret.",
        error: "Kunne ikke lagre Universitetsmodus-innstillinger.",
        saveChanges: "Lagre Universitetsmodus-innstillinger",
      },
    },
  },

  // Months
  "month.1": "jan.",
  "month.2": "feb.",
  "month.3": "mar.",
  "month.4": "apr.",
  "month.5": "mai",
  "month.6": "jun.",
  "month.7": "jul.",
  "month.8": "aug.",
  "month.9": "sep.",
  "month.10": "okt.",
  "month.11": "nov.",
  "month.12": "des.",

  // =========================
  // FEATURE CARDS
  // =========================
  featureCards: {
    "draft-from-template-title": "Opprett dokumentutkast fra mal",
    "draft-from-template-description":
      "Bruk funksjonen til for eksempel å lage en AML-policy, protokoll for generalforsamling eller en standardisert voldgiftsavtale.",
    "complex-document-builder-title": "Utfør kompleks juridisk oppgave",
    "complex-document-builder-description":
      "Perfekt når du for eksempel trenger å gjennomgå hundrevis av dokumenter før et bedriftsoppkjøp eller utforme en detaljert stevning.",
  },

  // =========================
  // WORKSPACE SELECTOR MODAL
  // =========================
  workspaceSelector: {
    chooseWorkspace: "Start en ny chat",
    selectAiType: "Valg av modul og arbeidsområde for å starte funksjonen",
    cloudAiDescription:
      "Bruker en skybasert AI-modell for chat og spørsmålsbesvarelse. Dokumentene dine vil bli behandlet og lagret sikkert i skyen.",
    localAiDescription:
      "Bruker en lokal AI-modell for chat og dokumentutforming. Dokumentene dine vil bli behandlet og lagret på din lokale maskin.",
    cloudAiDescriptionTemplateFeature:
      "Opprett mal i eksisterende arbeidsområde med juridiske data, malen kan hente juridiske data avhengig av forespørselen.",
    localAiDescriptionTemplateFeature:
      "Opprett mal i eget arbeidsområde, med helt lokal AI hvis det er aktivert på serveren.",
    cloudAiDescriptionComplexFeature:
      "Utforming av komplekse dokumenter er ikke tilgjengelig for disse arbeidsområdene, siden brukeren må laste opp dokumenter til arbeidsområdet før oppstart",
    localAiDescriptionComplexFeature:
      "Velg et av arbeidsområdene dine for å starte en juridisk oppgave, og sørg for at nødvendige dokumenter er lastet opp i arbeidsområdet før oppstart.",
    newWorkspaceComplexTaskInfo:
      "Hvis du oppretter et nytt arbeidsområde, vil du gå til opplastingsvisningen for å laste opp alle nødvendige dokumenter, noe som er nødvendig for å utføre generering av juridiske oppgavedokumenter.",
    selectExistingWorkspace: "Velg et eksisterende arbeidsområde",
    selectExistingDocumentDraftingWorkspace:
      "Velg et eksisterende arbeidsområde for dokumentutforming",
    orCreateNewBelow:
      "Eller opprett et nytt arbeidsområde for dokumentutforming nedenfor.",
    newWorkspaceName: "Skriv inn et navn for ditt nye arbeidsområde",
    newWorkspaceNameOptional:
      "Skriv inn et navn for ditt nye arbeidsområde (hvis du ikke bruker et eksisterende arbeidsområde)",
    workspaceNamePlaceholder: "f.eks. Mitt nye arbeidsområde",
    next: "Neste",
    pleaseSelectWorkspace: "Vennligst velg et arbeidsområde.",
    workspaceNameRequired: "Navn på arbeidsområde er påkrevd.",
    workspaceNameOrExistingWorkspaceRequired:
      "Vennligst skriv inn et navn på arbeidsområdet eller velg et eksisterende arbeidsområde.",
    workspaceNameMustBeMoreThanOneCharacter:
      "Navnet på arbeidsområdet må være lengre enn ett tegn.",
    noWorkspacesAvailable: "Ingen arbeidsområder tilgjengelig",
    selectWorkspacePlaceholder: "Vennligst velg",
    featureUnavailable: {
      title: "Funksjonen er ikke tilgjengelig",
      description:
        "Denne funksjonen er ikke aktivert for din konto eller er deaktivert i dette systemet. Kontakt en administrator for å aktivere denne funksjonen om nødvendig.",
      close: "Lukk",
    },
    createNewWorkspace: {
      title: "Opprett nytt arbeidsområde for dokumentutforming",
      description:
        "Dette vil opprette et nytt arbeidsområde spesifikt for kompleks dokumentutforming ved bruk av den valgte malen.",
      workspaceName: "Navn på arbeidsområde",
      create: "Opprett arbeidsområde",
    },
    selectExisting: {
      title: "Velg arbeidsområde for juridiske spørsmål",
      description:
        "Velg et eksisterende arbeidsområde for å starte en juridisk spørsmål og svar-økt.",
      selectWorkspace: "Velg arbeidsområde",
    },
  },
};

export default TRANSLATIONS;
