export default {
  // Bsed on this order of pages in system settings:
  // Customization (Appearance)
  //Admin: System settings
  //Admin: Users
  //Admin: Invites
  //Admin: Workspaces
  //Admin: MCP Servers
  //Admin: Feedback List
  //Admin: Workspace chats
  //Admin: Default system
  //Admin: Document Builder
  //Admin: DD Prompt settings
  //Admin: Chat UI settings
  //AI-Providers: LLM
  //AI-Providers: Prompt Upgrade LLM
  //AI-Providers: Validation LLM
  //AI-Providers: CDB LLM
  //AI-Providers: Custom User AI
  //AI-Providers:  Vector Database
  //AI-Providers: Re-ranking settings
  //AI-Providers: Deep search
  //AI-Providers: Embedder
  //AI-Providers: Text Splitter & Chunking
  //AI-Providers: Voice & Speech
  //AI-Providers: transcription
  //AI-Providers: PDR System
  //Agent skills
  //Tools: Chat Embed History
  //Tools: Chat Embed
  //Tools: Event logs
  //Tools: Developer API
  //Tools: Browser Extension
  //Security
  //Document Drafting: Configuration
  //Document Drafting: Drafting LLM
  //Document Drafting: Chat Settings

  // =========================
  // APPEARANCE PAGE
  // =========================
  appearance: {
    title: "Appearance",
    description: "Customize the appearance settings of your platform.",
    logo: {
      title: "Customize logo",
      description: "Upload your custom logo for light mode.",
      add: "Add a custom logo",
      recommended: "Recommended size: 800 x 200",
      remove: "Remove",
    },
    logoDark: {
      title: "Customize dark mode logo",
      description: "Upload your custom logo for dark mode.",
    },
    "welcome-message": {
      title: "Customize welcome messages",
      description:
        "Customize the heading and text that's displayed on the home page.",
      heading: "Heading",
      text: "Text",
      save: "Save messages",
    },
    icons: {
      title: "Custom Footer Icons",
      description:
        "Customize the footer icons displayed on the bottom of the sidebar.",
      icon: "Icon",
      link: "Link",
    },
    display: {
      title: "Display Language",
      description: "Select the preferred language.",
    },
    color: {
      title: "Custom colors",
      reset: "Reset",
      "desc-start": "Customize the",
      "desc-mid": "*background-color, *primary-color",
      "desc-and": "and",
      "desc-end": "*text-color",
      red: "Red",
      gray: "Gray",
      foynet: "Foynet",
      brown: "Brown",
      green: "Green",
      yellow: "Yellow",
      cyan: "Cyan",
      magenta: "Magenta",
      orange: "TenderFlow",
      purple: "Purple",
      navy: "Navy",
      black: "Black",
    },
    login: {
      title: "Custom Login Text",
      description: "Customize the paragraph text displayed on the login page.",
      placeholder: "Please contact the admin of the system",
      website: {
        title: "Customize the Application Website",
        description: "Customize the URL of your application's website.",
        toggle: "Display Website Link",
      },
      validation: { invalidURL: "Please enter a valid URL." },
    },
    siteSettings: {
      title: "Custom Site Settings",
      description:
        "Change the content of the browser tab for customization and branding.",
      tabTitle: "Tab Title",
      tabDescription:
        "Set a custom tab title when the app is open in a browser.",
      tabIcon: "Tab Favicon",
      fabIconUrl: "Define a url to an image to use for your favicon",
      placeholder: "url to your image",
      "invalid-file-type":
        "Invalid file type. Please use PNG, JPEG, GIF, WebP, SVG, or ICO files.",
      "file-too-large": "File too large. Maximum size is 5MB.",
      "default-title": "IST Legal",
    },
    appName: {
      title: "Custom App Name",
      description: "Set a custom app name that is displayed on the login page.",
    },
    customTab: {
      title: "Customize Tab Name",
      tab1: "Tab 1",
      tab2: "Tab 2",
      tab3: "Tab 3",
    },
    template: {
      title: "System DOCX Template",
      description:
        "Upload a default Word template (.docx) that will be used for Canvas exports.",
      tags: "Required merge tags inside the document: {{title}} for the document title and {{body}} for the main content. Optional tags: {{author}}, {{date}}, {{createdAt}}, {{updatedAt}}, {{header}}, {{footer}}, {{pageNumber}}, {{totalPages}}, {{company}}, {{reference}}.",
      remove: "Remove",
      upload: "Upload template",
      invalid: "Only .docx files are allowed",
    },
    "prompt-examples": {
      title: "Prompt examples",
      description: "Manage the example prompts shown on the main page.",
      example: "Example {{number}}",
      remove: "Remove",
      "title-field": "Title",
      area: "Legal area",
      prompt: "Prompt",
      icon: "Select icon",
      workspace: "Workspace",
      add: "Add example",
      save: "Save the UI",
    },
  },
  // =========================
  // APPEARANCE SITE SETTINGS
  // =========================
  "appearance.siteSettings.tabIcon": "Tab Icon",
  "appearance.siteSettings.fabIconUrl": "Favicon URL",
  "appearance.siteSettings.placeholder": "Enter favicon URL",
  "appearance.siteSettings.title-placeholder": "Enter site title",
  "appearance.welcome.heading": "Welcome to IST Legal",
  "appearance.welcome.text": "Select legal area on the left",

  // =========================
  // SYSTEM PREFERENCES
  // =========================
  system: {
    title: "System Preferences",
    "desc-start":
      "These are the overall settings and configurations of your instance.",
    context_window: {
      title: "Dynamic Context Window",
      desc: "Control how much of the LLM's context window is used for additional sources.",
      label: "Context Window Percentage",
      help: "Percentage of the context window that can be used for enrichment (10-100%).",
      "toast-success": "Context window percentage updated successfully.",
      "toast-error": "Failed to update context window percentage.",
    },
    "change-login-ui": {
      title: "Select the Login Default UI",
      status: "Current",
      subtitle:
        "The UI will be applied as the default login UI for the application",
    },
    attachment_context: {
      title: "Attachment Context Window",
      desc: "Control how much of the Dynamic Context Window can be used for attachments.",
      label: "Attachment Context Percentage",
      help: "Percentage of the Dynamic Context Window that can be used for attachments (10-80%).",
      "toast-success": "Attachment context percentage updated successfully.",
      "toast-error": "Failed to update attachment context percentage.",
      "validation-error":
        "Attachment context percentage must be between 10 and 80.",
    },
    user: "Users can delete workspaces",
    "desc-delete":
      "Allow non-admin users to delete workspaces that they are a part of. This would delete the workspace for everyone.",
    limit: {
      title: "Message Limit",
      "desc-limit": "Limit the number of messages a user can send per day.",
      "per-day": "Messages per day",
      label: "Message limit is currently ",
    },
    max_tokens: {
      title: "Maximum Login Tokens per User",
      desc: "Set the maximum number of active authentication tokens each user can have at once. When exceeded, older tokens will be automatically removed.",
      label: "Maximum tokens",
      help: "Value must be greater than 0",
    },
    state: { enabled: "Enabled", disabled: "Disabled" },
    "source-highlighting": {
      title: "Enable / Disable Source highlighting",
      description: "Hide or display the source highlighting to users.",
      label: "Citation: ",
      "toast-success": "Source highlighting setting has been updated",
      "toast-error": "Failed to update source highlighting setting",
    },
    "usage-registration": {
      title: "Usage registration for invoicing",
      description: "Enable or disable invoice logging for system monitoring.",
      label: "Invoicing logging is ",
    },
    "forced-invoice-logging": {
      title: "Forced Invoice Logging",
      description:
        "Enable to require an invoicing reference before using the platform.",
      label: "Force invoicing logging is ",
    },
    "rexor-linkage": {
      title: "Rexor linkage",
      description:
        "Enable rexor linkage to get active cases references from rexor service.",
      label: "Rexor linkage is ",
      "activity-id": "Activity ID",
      "activity-id-description": "Enter the activity ID for Rexor integration",
    },
    save: "Save changes",
    rerank: {
      title: "Re-ranking Settings",
      description:
        "Configure re-ranking settings to improve search result relevance with LanceDB.",
      "enable-title": "Enable Re-ranking",
      "enable-description":
        "Enable re-ranking to improve search result relevance by considering more context.",
      status: "Re-ranking Status",
      "vector-count-title": "Additional Vectors for Re-ranking",
      "vector-count-description":
        "Number of additional vectors to retrieve beyond the workspace's vector count setting. For example, if workspace is set to retrieve 30 vectors and this is set to 50, a total of 80 vectors will be considered for re-ranking. A higher number may improve accuracy but will increase processing time.",
      "lancedb-only": "LanceDB Only",
      "lancedb-notice":
        "This feature is only available when using LanceDB as your vector database.",
    },
  },
  admin: {
    system: {
      universityMode: {
        title: "University Mode",
        description:
          "When enabled, hides validation, prompt upgrade, template and web search tools for all users.",
        enable: "Enable University Mode",
        saved: "University Mode settings saved.",
        error: "Failed to save University Mode settings.",
        saveChanges: "Save University Mode Settings",
      },
    },
  },
  // =========================
  // PUBLIC MODE
  // =========================
  "public-mode": {
    enable: "Enable Public-User Mode",
    enabled: "Public-User Mode is Enabled",
  },
  // =========================
  // USER ACCESS
  // =========================
  userAccess: {
    title: "Allow User Access",
    description:
      "Enable to allow regular users to access legal tasks. By default, only superusers, managers and admins have access.",
    label: "User Access: ",
    state: { enabled: "Enabled", disabled: "Disabled" },
  },
  // =========================
  // SUPPORT EMAIL
  // =========================
  support: {
    title: "Support Email",
    description:
      "Set the support email address that shows up in the user menu while logged into this instance.",
    clear: "Clear",
    save: "Save",
  },
  // =========================
  // PROMPT LOGGING
  // =========================
  promptLogging: {
    title: "Prompt output logging",
    description:
      "Enable or disable logging of prompt outputs for system monitoring.",
    label: "Prompt output logging: ",
    state: { enabled: "Enabled", disabled: "Disabled" },
  },

  // =========================
  // USER SETTINGS (INSTANCE USERS)
  // =========================
  "user-setting": {
    description:
      "These are all the accounts which have an account on this instance. Removing an account will instantly remove their access to this instance.",
    "add-user": "Add User",
    username: "Email Address",
    role: "Role",
    "economy-id": "Economy ID",
    "economy-id-ph": "Enter economy system identifier",
    "economy-id-hint":
      "ID used for integrations with external economy systems (e.g., Rexor)",
    default: "Default",
    manager: "Manager",
    admin: "Admin",
    superuser: "Superuser",
    "date-added": "Date Added",
    "all-domains": "All Domains",
    "other-users": "Other Users (No Domain)",
    // Sorting options for user list
    "sort-username": "Sort by Username",
    "sort-organization": "Sort by Organization",
    edit: "Edit: ",
    "new-password": "New Password",
    "password-rule": "Password must be at least 8 characters long.",
    "update-user": "Update User",
    placeholder: "Enter email address",
    cancel: "Cancel",
    "remove-user": "Remove User",
    "remove-user-title": "Remove User",
    "remove-user-confirmation": "Are you sure you want to remove this user?",
    error: "Error: ",
  },
  // =========================
  // NEW USER (INSTANCE)
  // =========================
  "new-user": {
    title: "Add user to instance",
    username: "Email Address",
    "username-ph": "Enter email address",
    password: "Password",
    "password-ph": "User's initial password",
    role: "Role",
    default: "Default",
    manager: "Manager",
    admin: "Administrator",
    superuser: "Superuser",
    description:
      "After creating a user they will need to login with their initial login to get access.",
    cancel: "Cancel",
    "add-User": "Add User",
    error: "Error: ",
    "invalid-email": "Please enter a valid email address.",
    permissions: {
      title: "Permissions",
      default: [
        "Can only send chats with workspaces they are added to by admin or managers.",
        "Cannot modify any settings at all.",
      ],
      manager: [
        "Can view, create, and delete any workspaces and modify workspace-specific settings.",
        "Can create, update and invite new users to the instance.",
        "Cannot modify LLM, vectorDB, embedding, or other connections.",
      ],
      admin: [
        "Highest user level privilege.",
        "Can see and do everything across the system.",
      ],
      superuser: [
        "Can access specific settings pages like the Document Builder and Prompt Upgrade.",
        "Cannot modify system-wide settings like LLM, vectorDB configurations.",
        "Can send chats with workspaces they are added to by admin or managers.",
      ],
    },
  },
  // =========================
  // INVITES
  // =========================
  invites: {
    title: "Invitations",
    description:
      "Create invitation links for people in your organization to accept and sign up with. Invitations can only be used by a single user.",
    link: "Create Invite Link",
    accept: "Accepted By",
    usage: "Usage",
    "created-by": "Created By",
    created: "Created",
    new: {
      title: "Create new invite",
      "desc-start":
        "After creation you will be able to copy the invite and send it to a new user where they can create an account as the",
      "desc-mid": "default",
      "desc-end": "role and automatically be added to workspaces selected.",
      "auto-add": "Auto-add invitee to workspaces",
      "desc-add":
        "You can optionally automatically assign the user to the workspaces below by selecting them. By default, the user will not have any workspaces visible. You can assign workspaces later post-invite acceptance.",
      cancel: "Cancel",
      "create-invite": "Create Invite",
      error: "Error: ",
    },
    "link-copied": "Invite link copied",
    "copy-link": "Copy Invite Link",
    "delete-invite-title": "Deactivate Invite",
    "delete-invite-confirmation":
      "Are you sure you want to deactivate this invite?\nAfter you do this it will not longer be useable.\n\nThis action is irreversible.",
    status: {
      label: "Status",
      pending: "Pending",
      disabled: "Disabled",
      claimed: "Accepted",
    },
  },
  // =========================
  // INVITATION RELATED KEYS
  // =========================
  invite: {
    "accept-button": "Accept Invitation",
    newUser: {
      title: "Create a new account",
      usernameLabel: "Username",
      passwordLabel: "Password",
      description:
        "After creating your account you will be able to login with these credentials and start using workspaces.",
    },
  },
  // =========================
  // WORKSPACE OVERVIEW PAGE
  // =========================
  workspace: {
    title: "Instance Workspaces",
    description:
      "These are all the workspaces that exist on this instance. Removing  workspace will delete all of its associated chats and settings.",
    "new-workspace": "New Workspace",
    name: "Name",
    link: "Link",
    users: "Users",
    type: "Type",
    "created-on": "Created On",
    save: "Save change",
    cancel: "Cancel",
    "sort-by-name": "Sort by name",
    sort: "Sort alphabetically",
    unsort: "Restore original order",
    deleted: {
      title: "Workspace not found!",
      description: "It looks like a workspace by this name is not available.",
      homepage: "Go back to homepage",
    },
    "no-workspace": {
      title: "No Workspace Available",
      description: "You don't have access to any workspaces yet.",
      "contact-admin": "Please contact your administrator to request access.",
      "learn-more": "Learn more about workspaces",
    },
    "no-workspaces":
      "You don't have any workspaces yet. Choose a legal area on the left to get started.",
    "my-workspaces": "My Workspaces",
    "show-my": "Show my workspaces",
    "show-all": "Show all workspaces",
    "creator-id": "Created by user ID: {{id}}",
    "cloud-ai": "Cloud-based AI",
    "local-ai": "Local AI",
    "welcome-mobile": "Press the top left button to select a legal area.",
    "loading-username": "Loading username...",
    "ai-type": "Module",
    "latest-activity": "Latest activity",
    "today-time": "Today, {{time}}",
    "date-time": "{{day}} {{month}}, {{time}}",
  },
  // =========================
  // CHAT LOGS & PREVIEW
  // =========================
  chat_logs: {
    display_description:
      "Display the raw data logging, open or download the file",
    display_prompt_output: "Display raw data",
    loading_prompt_output: "Loading raw data...",
    not_available: "*** Raw data is not available for this chat.",
    token_count: "Tokens (in all raw data): {{count}}",
    token_count_detailed:
      "Tokens to LLM: {{promptTokens}} | Tokens in LLM response: {{completionTokens}} | Total tokens: {{totalTokens}}",
  },
  // =========================
  // LOGGING
  // =========================
  logging: { show: "show", hide: "hide", "event-metadata": "Event Metadata" },

  // =========================
  // MCP SERVER SETTINGS PAGE
  // =========================

  mcp: {
    title: "MCP Server Management",
    description:
      "Manage Multi-Component Processing (MCP) server configurations.",
    currentServers: "Current Servers",
    noServers: "No MCP servers configured.",
    fetchError: "Failed to fetch servers: {{error}}",
    addServerButton: "Add New Server",
    addServerModalTitle: "Add New MCP Server",
    addServerModalDesc:
      "Define the configuration for the new MCP server process.",
    serverName: "Server Name (Unique ID)",
    configJson: "Configuration (JSON)",
    addButton: "Add Server",
    addSuccess: "Server added successfully.",
    addError: "Failed to add server: {{error}}",
  },

  // =========================
  //  WORKSPACE CHAT LOGS PAGE
  // =========================
  recorded: {
    title: "Workspace Chats",
    description:
      "These are all the recorded chats and messages that have been sent by users ordered by their creation date.",
    export: "Export",
    table: {
      id: "Id",
      by: "Sent By",
      workspace: "Workspace",
      prompt: "Prompt",
      response: "Response",
      at: "Sent At",
      invoice: "Invoice ref",
      "completion-token": "Completion Token",
      "prompt-token": "Prompt Token",
    },
    "clear-chats": "Delete All Current Logs",
    "confirm-clear-chats":
      "Are you sure you want to clear all logs?\n\nThis action is irreversible.",
    "fine-tune-modal": "Order Fine-Tune Model",
    "confirm-delete.chat":
      "Are you sure you want to delete this chat?\n\nThis action is irreversible.",
    next: "Next Page",
    previous: "Previous Page",
    filters: {
      "by-name": "Filter by username",
      "by-reference": "Reference number",
    },
    bulk_delete_title: "Bulk Delete Old Logs",
    bulk_delete_description:
      "Delete all prompt logs older than the selected timeframe.",
    delete_old_chats: "Delete Old Prompt Logs",
    total_logs: "Total log items",
    filtered_logs: "Filtered log items",
    reset_filters: "Reset filters",
    "no-chats-found": "No prompt logs found",
    "no-chats-description":
      "No prompt logs found matching your filters. Try changing your search criteria or delete older time period.",
    "deleted-old-chats": "Deleted {{count}} old prompt(s)",
    two_days: "2 Days",
    one_week: "1 Week",
    two_weeks: "2 Weeks",
    one_month: "1 Month",
    two_months: "2 Months",
    three_months: "3 Months",
    total_deleted: "Total Deleted Prompt Logs",
  },
  // =========================
  // DEFAULT SETTINGS PAGE FOR LEGAL Q&A
  // =========================
  "default-settings": {
    "canvas-prompt": "Canvas System Prompt",
    "canvas-prompt-desc":
      "Prompt for the canvas chat system. Used as the system prompt for canvas chat interactions.",
    title: "Default Settings for Legal Q&A",
    "default-desc": "Control the default behavior of workspaces for Legal Q&A",
    prompt: "Legal Q&A system Prompt",
    "prompt-desc":
      "The default prompt that will be used for new Legal Q&A workspaces. Define the context and instructions for the AI to generate a response. You should provide a carefully crafted prompt so the AI can generate a relevant and accurate response. To apply this setting to all existing workspaces, overriding their custom settings, use the apply button below.",
    "prompt-placeholder": "Enter your prompt here",
    "toast-success": "Default system prompt updated",
    "toast-fail": "Failed to updated system prompt",
    "apply-all-confirm":
      "Are you sure you want to apply this prompt to all existing Legal Q&A workspaces? This action cannot be undone and will override any custom settings.",
    "apply-to-all": "Apply to all existing Legal Q&A workspaces",
    applying: "Applying...",
    "toast-apply-success": "Default prompt applied to {{count}} workspaces",
    "toast-apply-fail": "Failed to apply default prompt to workspaces",
    snippets: {
      title: "Default Max Context Snippets",
      description:
        "The number of relevant document snippets to include in the context for new workspaces. To apply this setting to all existing workspaces, overriding their custom settings, use the apply button below.",
      recommend:
        "Recommended value is at least 30. Setting much higher numbers will increase processing time without necessarily improving precision depending on the capacity of the LLM used.",
    },
    "rerank-limit": {
      title: "Maximum Rerank Limit",
      description: "The maximum number of snippets to rerank",
      recommend: "Recommended value is between 20-50",
    },
    "validation-prompt": {
      title: "Validation Prompt",
      description:
        "This setting controls the validation prompt that will be sent to the LLM to validate the given answer.",
      placeholder:
        "Please validate the following response, checking all legal references and citations for accuracy against the provided context. List any inaccuracies or misrepresentations found.",
    },
    "apply-vector-search-to-all": "Apply to all existing Legal Q&A workspaces",
    "apply-vector-search-all-confirm":
      "Are you sure you want to apply this vector search setting to all existing Legal Q&A workspaces? This action cannot be undone.",
    "toast-vector-search-apply-success":
      "Vector search setting applied to {{count}} workspaces",
    "toast-vector-search-apply-fail":
      "Failed to apply vector search setting to workspaces",
    "canvas-upload-prompt": "Canvas Upload System Prompt",
    "canvas-upload-prompt-desc":
      "The system prompt used when processing uploads via the canvas. This prompt guides the LLM's behavior for uploaded content.",
    saving: "Saving...",
    save: "Save Changes",
    "toast-fail-load-prompts": "Failed to load prompt configurations.", // New key added here
  },
};
