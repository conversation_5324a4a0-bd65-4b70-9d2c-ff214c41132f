export default {
  // =========================
  // MODALE (DOCUMENT & CONNECTORS)
  // =========================
  modale: {
    document: {
      title: "My Documents",
      document: "Documents",
      search: "Search for document",
      folder: "New Folder",
      name: "Name",
      empty: "No Documents",
      "move-workspace": "Move to Workspace",
      "doc-processor": "Document Processor",
      "processor-offline":
        "The document processor is currently offline. Please try again later.",
      "drag-drop": "Click to upload or drag and drop files here",
      "supported-files": "Supported files: PDF",
      "submit-link": "Or submit a link to process",
      fetch: "Fetch",
      fetching: "Fetching...",
      "file-desc":
        "Files will be processed and made available for chat context.",
      cost: "*One time cost for embeddings",
      "save-embed": "Save and Embed",
      "failed-uploads": "Failed Uploads",
      "loading-message": "This may take a while for large documents",
      "uploading-file": "Uploading file(s)...",
      "scraping-link": "Scraping link...",
      "moving-documents": "Moving {{count}} documents. Please wait.",
      "exceeds-prompt-limit":
        "Note: The uploaded content exceeds what can fit with one prompt. The system will process any requests by multiple prompts which will increase the time for generating the answer, and precision may be affected.",
    },
    connectors: {
      title: "Data Connectors",
      search: "Search data connectors",
      empty: "No data connectors found.",
    },
    "justify-betweening": "Processing...",
  },
  // =========================
  // MODALS
  // =========================
  modals: {
    warning: {
      title: "Warning",
      proceed: "Are you sure you want to proceed?",
      cancel: "Cancel",
      confirm: "Confirm",
      "got-it": "Okay, got it",
    },
  },
  // =========================
  // SHARE MODAL
  // =========================
  shareModal: {
    title: "Share {type}",
    titleWorkspace: "Share Workspace",
    titleThread: "Share Thread",
    shareWithUsers: "Share with users",
    shareWithOrg: "Share with entire organization",
    searchUsers: "Search users...",
    noUsersFound: "No users found",
    loadingUsers: "Loading users...",
    errorLoadingUsers: "Error loading users",
    errorLoadingStatus: "Error loading share status",
    userAccessGranted: "User access granted successfully",
    userAccessRevoked: "User access revoked successfully",
    orgAccessGranted: "Organization access granted successfully",
    orgAccessRevoked: "Organization access revoked successfully",
    errorUpdateUser: "Error updating user access",
    errorUpdateOrg: "Error updating organization access",
    errorNoOrg: "Unable to share: account not linked to an organization",
    close: "Close",
    grantAccess: "Grant Access",
    revokeAccess: "Revoke Access",
  },
  // =========================
  // VARIOUS MODALS AND NEW FEATURES
  // =========================

  // Organization & Organizations translations for user management
  organization: {
    label: "Organization",
    select: "-- Select Organization --",
    none: "None",
    "create-new": "+ Create New Organization",
    "new-name": "New Organization Name",
    "new-name-ph": "Enter new organization name",
  },
  organizations: {
    "fetch-error": "Failed to fetch organizations",
  },
  "request-legal-assistance": {
    title: "Request legal assistance",
    description:
      "Configure visibility of button for requesting legal assistance.",
    enable: "Enable Request Legal Assistance",
    "law-firm-name": "Law Firm Name",
    "law-firm-placeholder": "Enter law firm name",
    "law-firm-help":
      "Name of the law firm that will handle legal assistance requests",
    email: "Legal Assistance Email",
    "email-placeholder": "Enter legal assistance email address",
    "email-help": "Email address where legal assistance requests will be sent",
    "settings-saved": "Legal assistance settings saved successfully",
    "save-error": "Failed to save legal assistance settings",
    status: "Legal Assistance Button: ",
    "load-error": "Failed to load legal assistance settings",
    "save-button": "Save changes",
    request: {
      title: "Request legal assistance",
      description:
        "Send a request to {{lawFirmName}} for legal assistance, finishing research or other consultations. You will be notified by email when the request is processed.",
      button: "Request legal assistance",
      message: "Message",
      "message-placeholder":
        "Enter any specific instructions or information for the legal assistance team",
      send: "Send request",
      cancel: "Cancel",
      error: "Failed to submit legal assistance request",
      success: "Legal assistance request submitted successfully",
      submitting: "Submitting request...",
      submit: "Submit request",
      partyName: "Party name",
      partyOrgId: "Organization ID",
      partyNameRequired: "Party name is required",
      partyOrgIdRequired: "Organization ID is required",
      partyNamePlaceholder: "Enter the name of your organization",
      partyOrgIdPlaceholder: "Enter your organization ID",
      opposingPartyName: "Opposing party name (if applicable)",
      opposingPartyOrgId: "Opposing party organization ID (if known)",
      opposingPartyNamePlaceholder: "Enter the name of the opposing party",
      opposingPartyOrgIdPlaceholder:
        "Enter the opposing party's organization ID",
    },
  },
  // =========================
  // WORKSPACE SELECTOR MODAL
  // =========================
  workspaceSelector: {
    chooseWorkspace: "Start a new chat",
    selectAiType:
      "Selection of module and workspace for initiating the feature",
    cloudAiDescription:
      "Uses a cloud-based AI model for chat and question answering. Your documents will be processed and stored securely in the cloud.",
    localAiDescription:
      "Uses a local AI model for chat and document drafting. Your documents will be processed and stored on your local machine.",
    cloudAiDescriptionTemplateFeature:
      "Create template in existing workspace with legal data, template may draw legal data depending on the query.",
    localAiDescriptionTemplateFeature:
      "Create template in own workspace, using entirely local AI if that is enabled on server.",
    cloudAiDescriptionComplexFeature:
      "Drafting of complex documents are not available for these workspaces, since the user must upload documents to the workspace before initiating",
    localAiDescriptionComplexFeature:
      "Choose one of your workspaces for initiating a legal task, and ensure necessary documents are uploaded in the workspace before initiating.",
    newWorkspaceComplexTaskInfo:
      "If you are creating a new workspace, you will be entering the upload view to upload all necessary documents, which is necessary for performing a legal task document generation.",
    selectExistingWorkspace: "Select an existing workspace",
    selectExistingDocumentDraftingWorkspace:
      "Select an existing Document Drafting workspace",
    orCreateNewBelow: "Or, create a new Document Drafting workspace below.",
    newWorkspaceName: "Enter a name for your new workspace",
    newWorkspaceNameOptional:
      "Enter a name for your new workspace (if you are not using an existing workspace)",
    workspaceNamePlaceholder: "eg: My new workspace",
    next: "Next",
    pleaseSelectWorkspace: "Please select a workspace to continue.",
    workspaceNameRequired: "Workspace name is required.",
    workspaceNameOrExistingWorkspaceRequired:
      "Please enter a workspace name or select an existing workspace.",
    workspaceNameMustBeMoreThanOneCharacter:
      "Workspace name must be more than one character.",
    noWorkspacesAvailable: "No workspaces available for this type.",
    selectWorkspacePlaceholder: "Select a workspace",
    featureUnavailable: {
      title: "Feature Not Available",
      description:
        "This feature is not enabled for your account or is disabled in this system. Please contact an administrator to enable this feature if needed.",
      close: "Close",
    },
    createNewWorkspace: {
      title: "Create New Document Drafting Workspace",
      description:
        "This will create a new workspace specifically for complex document drafting using the selected template.",
      workspaceName: "Workspace Name",
      create: "Create Workspace",
    },
    selectExisting: {
      title: "Select Workspace for Legal Q&A",
      description:
        "Choose an existing workspace to start a Legal Q&A chat session.",
      selectWorkspace: "Select Workspace",
    },
  },
  // =========================
  // CANVAS CHAT
  // =========================
  canvasChat: {
    title: "Canvas",
    "input-placeholder": "Ask for legal information",
    chatboxinstruction: "Instruct any adjustment of the answer",
    explanation:
      "This tool is for AI-editing of the answer in various ways. The sources for the underlying answer are applied, which means that you can ask for additional clarifications using the same source material as for the underlying answer.",
    editAnswer: "Edit answer",
  },
  // =========================
  // USER MENU
  // =========================
  "user-menu": {
    edit: "Edit Account",
    profile: "Profile Picture",
    size: "800 x 800",
    "remove-profile": "Remove Profile Picture",
    username: "Email Address",
    "username-placeholder": "Enter email address",
    "new-password": "New Password",
    "new-password-placeholder": "New password",
    cancel: "Cancel",
    update: "Update Account",
    language: "Preferred language",
    email: "Email Address",
    "email-placeholder": "Enter email address",
  },
  // =========================
  // VALIDATE RESPONSE
  // =========================
  "validate-response": {
    title: "Validation Result",
    "toast-fail": "Could not validate response",
    validating: "Validating response",
    button: "Validate response",
    "adjust-prefix":
      "Make all indicated changes to the response based on this feedback: ",
    "adjust-button": "Apply suggested changes",
  },
};
