export default {
  feedback: {
    fullName: "Full Name",
    fullNamePlaceholder: "Enter your full name",
    message: "Message",
    messagePlaceholder: "Enter your feedback message",
    attachment: "Attachment",
    submit: "Submit Feedback",
    submitting: "Submitting...",
    submitSuccess: "Feedback submitted successfully",
    submitError: "Failed to submit feedback",
    imageLoadError: "Failed to load image",
    unsupportedFile: "Unsupported file type",
    filePreview: "File Preview",
    thankYou: "Thank you! Your feedback has been submitted successfully.",
    emailSendError: "Failed to send email: ",
    submitFeedbackError: "Failed to submit feedback: ",
    attachFile: "Attach a file",
    improvePlatform: "Help us improve the platform!",
    suggestionOrQuestion: "Any suggestions? Or questions?",
    clickToWrite: "Please click to write to us",
    noFeedback: "No feedback found",
    previewImage: "Preview image",
    noFile: "No file attached",
    validation: {
      fullNameMinLength: "Full name must be at least 2 characters",
      fullNameMaxLength: "Full name cannot be longer than 100 characters",
      fullNameFormat:
        "Full name can only include letters, numbers, spaces, underscores (_), dots (.), @ symbols, and hyphens (-)",
      messageMinLength: "Message must be at least 12 characters",
      messageMaxLength: "Message cannot be longer than 1000 characters",
      messageMinWords: "Message must contain at least 4 words",
      fileType: "File must be a JPEG, PNG, or PDF",
      fileSize: "File size must be less than 5MB",
    },
  },

  "feedback-settings": {
    "delete-feedback": "Feedback deleted successfully!",
    "delete-error": "Feedback was not deleted",

    "header-title": "Feedback list",
    "header-description":
      "This is the complete list of feedback for this instance. Please note that deleting feedback is permanent and cannot be undone.",
    title: "User Feedback Button",
    description: "Enable or disable the feedback submission button for users.",
    successMessage: "User feedback button has been updated.",
    failureUpdateMessage: "Failed to update user feedback button status.",
    errorSubmitting: "Error submitting feedback settings.",
    errorFetching: "Error fetching feedback settings.",
  },
};
