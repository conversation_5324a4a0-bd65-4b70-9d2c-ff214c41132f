const TRANSLATIONS = {
  // =========================
  // COMMON STRINGS & PLACEHOLDERS
  // =========================
  common: {
    examples: "Examples",
    "workspaces-name": "Workspaces Name",
    ok: "OK",
    error: "error",
    confirm: "Confirm",
    confirmstart: "Confirm and start",
    savesuccess: "Successfully saved settings",
    saveerror: "Failed to save settings",
    success: "success",
    user: "User",
    selection: "Model Selection",
    saving: "Saving...",
    save: "Save changes",
    previous: "Previous Page",
    next: "Next Page",
    cancel: "Cancel",
    "search-placeholder": "Search...",
    "more-actions": "More actions",
    "delete-message": "Delete message",
    copy: "Copy",
    edit: "Edit",
    regenerate: "Regenerate",
    "export-word": "Export to Word",
    "stop-generating": "Stop generating",
    "attach-file": "Attach a file to this chat",
    home: "Home",
    settings: "Settings",
    support: "Support",
    "clear-reference": "Clear reference",
    "send-message": "Send message",
    "ask-legal": "Ask for legal information",
    "stop-response": "Stop generating response",
    "contact-support": "Contact Support",
    "copy-connection": "Copy connection string",
    "auto-connect": "Automatically connect to extension",
    back: "Back",
    "back-to-workspaces": "Back to workspaces",
    off: "Off",
    on: "On",
    continue: "Continue",
    rename: "Rename",
    delete: "Delete",
    "default-skill":
      "This skill is enabled by default and cannot be turned off.",
    timeframes: "Time Periods",
    other: "Other Options",
    placeholder: {
      username: "My username",
      password: "Your password",
      email: "Enter your email",
      "support-email": "<EMAIL>",
      website: "https://www.example.com",
      "site-name": "IST Legal",
      "search-llm": "Search for a specific LLM provider",
      "search-providers": "Search available providers",
      "message-heading": "Message heading",
      "message-content": "Message",
      "token-limit": "4096",
      "max-tokens": "Maximum tokens per request (eg: 1024)",
      "api-key": "API Key",
      "base-url": "Base URL",
      endpoint: "API endpoint",
    },
    tooltip: {
      copy: "Copy to clipboard",
      delete: "Delete this item",
      edit: "Edit this item",
      save: "Save changes",
      cancel: "Cancel changes",
      search: "Search items",
      add: "Add new item",
      remove: "Remove item",
      upload: "Upload file",
      download: "Download file",
      refresh: "Refresh data",
      settings: "Open settings",
      more: "More options",
    },
    "default.message": "Enter your message here",
    preview: "Preview",
    prompt: "Prompt",
    loading: "Loading...",
    download: "Download in raw format",
    open_in_new_tab: "Open in new tab with formatting",
    close: "Close",
    note: "Note",
  },

  // =========================
  // RECENT UPLOADS COMPONENT
  // =========================

  // moved to recentUploads.js

  // =========================
  // CHAT BOX DRAG & DROP COMPONENT
  // =========================
  chatboxdnd: {
    title: "Add a file to this prompt",
    description:
      "Drop your file here to have it added for this prompt. It is not stored in the workspace as a permanent source.",
    "file-prefix": "File:",
    "attachment-tooltip":
      "This file will be attached to your message. It will not be saved in the workspace as a permanent source.",
    "uploaded-file-tag": "USER UPLOADED FILE",
  },

  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Confluence space key",
    "space-key-desc":
      "This is the spaces key of your confluence instance that will be used. Usually begins with ~",
    "space-key-placeholder": "eg: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "eg: https://example.atlassian.net, http://localhost:8211, etc...",
    "token-tooltip": "You can create an API token",
    "token-tooltip-here": "here",
  },

  // =========================
  // CONFIRMATION MESSAGES
  // =========================
  deleteWorkspaceConfirmation:
    "Are you sure you want to delete {{name}}?\nAfter you do this it will be unavailable in this instance.\n\nThis action is irreversible.",
  deleteConfirmation:
    "Are you sure you want to delete ${user.username}?\nAfter you do this they will be logged out and unable to use this instance.\n\nThis action is irreversible.",
  suspendConfirmation:
    "Are you sure you want to suspend {{username}}?\nAfter you do this they will be logged out and unable to log back into this instance until unsuspended by an admin.",
  flushVectorCachesWorkspaceConfirmation:
    "Are you sure you want to flush vector caches for this workspace?",
  apiKeys: {
    "deactivate-title": "Deactivate API Key",
    "deactivate-message":
      "Are you sure you want to deactivate this API key?\nAfter you do this it will not longer be useable.\n\nThis action is irreversible.",
  },

  // =========================
  // SETTINGS SIDEBAR MENU ITEMS
  // =========================

  // MOVED TO settings.js - This section has been migrated to settings.js

  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Chat UI Settings",
    description: "Configure the chat settings.",
    auto_submit: {
      title: "Auto-Submit Speech Input",
      description:
        "Automatically submit speech input after a period of silence",
    },
    auto_speak: {
      title: "Auto-Speak Responses",
      description: "Automatically speak responses from the AI",
    },
  },

  // =========================
  // QURA BUTTONS
  // =========================
  qura: {
    "copy-to-cora": "Qura source check",
    "qura-status": "Qura button is ",
    "copy-option": "Copy option",
    "option-quest": "Question",
    "option-resp": "Response",
    "role-description": "Add a Qura button to prompt responses on Qura.law",
  },

  // =========================
  // LOGIN & SIGN-IN PAGES
  // =========================
  login: {
    "multi-user": {
      welcome: "Welcome to",
      "placeholder-username": "Email Address",
      "placeholder-password": "Password",
      login: "Login",
      validating: "Validating...",
      "forgot-pass": "Forgot password",
      "back-to-login": "Back to login",
      "reset-password": "Reset Password",
      reset: "Reset",
      "reset-password-info":
        "Provide the necessary information below to reset your password.",
    },
    "sign-in": { start: "Sign in to your account", end: "account." },
    button: "login",
    password: {
      forgot: "Forgot your password?",
      contact: "Please contact the system admin.",
    },
    publicMode: "Try without account",
    logging: "Logging in...",
  },

  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Binary LLM selection",
    "secondary-llm-toggle-description":
      "Enable this to give admins the ability to choose between two LLM models in the document drafting module.",
    "secondary-llm-toggle-status": "Status: ",
    "secondary-llm-user-level": "Secondary LLM User Level",
    "secondary-llm-user-level-description":
      "Enable this to give ALL users the ability to choose between two LLM models in the document drafting workspace.",
  },

  // =========================
  // NEW WORKSPACE
  // =========================
  "new-workspace": {
    title: "New Workspace",
    placeholder: "My Workspace",
    "legal-areas": "Legal Areas",
    create: {
      title: "Create new workspace",
      description:
        "After creating this workspace only admins will be able to see it. You can add users after it has been created.",
      error: "Error: ",
      cancel: "Cancel",
      "create-workspace": "Create workspace",
    },
  },

  // =========================
  // WORKSPACE CHATS
  // =========================
  "workspace-chats": {
    welcome: "Welcome to your new workspace.",
    "desc-start": "To get started either",
    "desc-mid": "upload a document",
    "desc-or": "or",
    start: "To get started",
    "desc-end": "send a chat.",
    "attached-file": "Attached file",
    "attached-files": "Attached files",
    "token-count": "Token count",
    "total-tokens": "Total Tokens",
    "context-window": "Context Window",
    "remaining-tokens": "Remaining",
    "view-files": "View attached files",
    prompt: {
      send: "Send",
      "send-message": "Send message",
      placeholder: "Ask for legal information",
      "change-size": "Change text size",
      reset: "Reset your chat",
      clear: "Clear your chat history and begin a new chat",
      command: "Command",
      description: "Description",
      save: "save",
      small: "Small",
      normal: "Normal",
      large: "Large",
      larger: "Larger",
      attach: "Attach a file to this chat",
      upgrade: "Upgrade your prompt",
      upgrading: "Upgrading prompt...",
      "original-prompt": "Original Prompt:",
      "upgraded-prompt": "Upgraded Prompt:",
      "edit-prompt": "You can edit the new prompt before submitting",
      "shortcut-tip":
        "Tip: Press Enter to accept changes. Use Shift+Enter for new lines.",
      "speak-prompt": "Speak your prompt",
      "view-agents": "View all available agents you can use for chatting",
      "deep-search": "Web Search",
      "deep-search-tooltip":
        "Search the web for information to enhance responses",
      "ability-tag": "Ability",
      "workspace-chats.prompt.view-agents": "View Agents",
      "workspace-chats.prompt.ability-tag": "Ability",
      "workspace-chats.prompt.speak-prompt": "Speak your prompt",
      "total-tokens": "Total tokens",
    },
  },

  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Deep Search",
    description:
      "Configure web search capabilities for chat responses. When enabled, the system can search the web for information to enhance responses.",
    enable: "Enable Deep Search",
    enable_description:
      "Allow the system to search the web for information when responding to queries.",
    provider_settings: "Provider Settings",
    provider: "Search Provider",
    model: "Model",
    api_key: "API Key",
    api_key_placeholder: "Enter your API key",
    api_key_placeholder_set: "API key is set (enter new key to change)",
    api_key_help:
      "Your API key is stored securely and used only for web search requests.",
    context_percentage: "Context Percentage",
    context_percentage_help:
      "Percentage of the LLM's context window to allocate for web search results (5-20%).",
    fetch_error: "Failed to fetch Deep Search settings",
    save_success: "Deep Search settings saved successfully",
    save_error: "Failed to save Deep Search settings: {{error}}",
    toast_success: "Deep Search settings saved successfully",
    toast_error: "Failed to save Deep Search settings: {{error}}",
    brave_recommended:
      "Brave Search is currently the recommended and most reliable provider option.",
  },

  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Contextual Embedding",
      hint: "Enable contextual embedding to enhance the embedding process with additional parameters",
    },
    systemPrompt: {
      label: "System Prompt",
      placeholder: "Enter a value...",
      description:
        "Example: Please give a short succinct context to situate this chunk within the overall document for the purposes of improving search retrieval of the chunk. Answer only with the succinct context and nothing else.",
    },
    userPrompt: {
      label: "User Prompt",
      placeholder: "Enter a value...",
      description:
        "Example: <document>\n{file}\n</document>\nHere is the chunk we want to situate within the whole document\n<chunk>\n{chunk}\n</chunk>",
    },
  },

  // =========================
  // HEADER
  // =========================
  header: { account: "Account", login: "Login", "sign-out": "Sign out" },

  // =========================
  // WORKSPACE OVERVIEW
  // =========================
  workspace: {
    title: "Instance Workspaces",
    description:
      "These are all the workspaces that exist on this instance. Removing  workspace will delete all of its associated chats and settings.",
    "new-workspace": "New Workspace",
    name: "Name",
    link: "Link",
    users: "Users",
    type: "Type",
    "created-on": "Created On",
    save: "Save change",
    cancel: "Cancel",
    "sort-by-name": "Sort by name",
    sort: "Sort alphabetically",
    unsort: "Restore original order",
    deleted: {
      title: "Workspace not found!",
      description: "It looks like a workspace by this name is not available.",
      homepage: "Go back to homepage",
    },
    "no-workspace": {
      title: "No Workspace Available",
      description: "You don't have access to any workspaces yet.",
      "contact-admin": "Please contact your administrator to request access.",
      "learn-more": "Learn more about workspaces",
    },
    "no-workspaces":
      "You don't have any workspaces yet. Choose a legal area on the left to get started.",
    "my-workspaces": "My Workspaces",
    "show-my": "Show my workspaces",
    "show-all": "Show all workspaces",
    "creator-id": "Created by user ID: {{id}}",
    "cloud-ai": "Cloud-based AI",
    "local-ai": "Local AI",
    "welcome-mobile": "Press the top left button to select a legal area.",
    "loading-username": "Loading username...",
    "ai-type": "Module",
    "latest-activity": "Latest activity",
    "today-time": "Today, {{time}}",
    "date-time": "{{day}} {{month}}, {{time}}",
  },

  // =========================
  // WORKSPACES SETTINGS MENU
  // =========================
  "workspaces-settings": {
    general: "General Settings",
    chat: "Chat Settings",
    vector: "Vector Database",
    members: "Members",
    agent: "Agent Configuration",
    "general-settings": {
      "workspace-name": "Workspace Name",
      "desc-name": "This will only change the display name of your workspace.",
      "assistant-profile": "Assistant Profile Image",
      "assistant-image":
        "Customize the profile image of the assistant for this workspace.",
      "workspace-image": "Workspace Image",
      "remove-image": "Remove Workspace Image",
      delete: "Delete Workspace",
      deleting: "Deleting Workspace...",
      update: "Update workspace",
      updating: "Updating workspace...",
    },
    "chat-settings": {
      type: "Chat type",
      private: "Private",
      standard: "Standard",
      "private-desc-start": "will manually grant access to",
      "private-desc-mid": "only",
      "private-desc-end": "specific users.",
      "standard-desc-start": "will automatically grant access to",
      "standard-desc-mid": "all",
      "standard-desc-end": "new users.",
    },
    users: {
      manage: "Manage Users",
      "workspace-member": "No workspace members",
      username: "Email Address",
      role: "Role",
      default: "Default",
      manager: "Manager",
      admin: "Admin",
      superuser: "Superuser",
      "date-added": "Date Added",
      users: "Users",
      search: "Search for a user",
      "no-user": "No users found",
      select: "Select All",
      unselect: "Unselect",
      save: "Save",
    },
    "linked-workspaces": {
      title: "Linked Workspaces",
      description:
        "If workspaces are linked, legal data relevant for the prompt will be automatically fetched from each linked legal area. Note that linked workspaces will increase processing time",
      "linked-workspace": "No linked workspaces",
      manage: "Manage Workspaces",
      name: "Name",
      slug: "Slug",
      date: "Date Added",
      workspaces: "Workspaces",
      search: "Search for a workspace",
      "no-workspace": "No workspaces found",
      select: "Select All",
      unselect: "Unselect",
      save: "Save",
    },
    "delete-workspace": "Delete Workspace",
    "delete-workspace-message":
      "You are about to delete your entire {{workspace}} workspace. This will remove all vector embeddings on your vector database.\n\nThe original source files will remain untouched. This action is irreversible.",
    "vector-database": {
      reset: {
        title: "Reset Vector Database",
        message:
          "You are about to reset this workspace's vector database. This will remove all vector embeddings currently embedded.\n\nThe original source files will remain untouched. This action is irreversible.",
      },
    },
  },

  // =========================
  // GENERAL APPEARANCE & CUSTOMIZATION
  // =========================
  general: {
    vector: {
      title: "Vector Count",
      description: "Total number of vectors in your vector database.",
      vectors: "Number of vectors",
    },
    names: {
      description: "This will only change the display name of your workspace.",
    },
    message: {
      title: "Suggested Chat Messages",
      description:
        "Customize the messages that will be suggested to your workspace users.",
      add: "Add new message",
      save: "Save Messages",
      heading: "Explain to me",
      body: "the benefits of platform",
      message: "Message",
      "new-heading": "Heading",
    },
    pfp: {
      title: "Assistant Profile Image",
      description:
        "Customize the profile image of the assistant for this workspace.",
      image: "Workspace Image",
      remove: "Remove Workspace Image",
    },
    delete: {
      delete: "Delete Workspace",
      deleting: "Deleting Workspace...",
      "confirm-start": "You are about to delete your entire",
      "confirm-end":
        "workspace. This will remove all vector embeddings in your vector database.\n\nThe original source files will remain untouched. This action is irreversible.",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chat: {
    llm: {
      title: "Workspace LLM Provider",
      description:
        "The specific LLM provider & model that will be used for this workspace. By default, it uses the system LLM provider and settings.",
      search: "Search all LLM providers",
      "save-error": "Failed to save {{provider}} settings: {{error}}",
      setup: "Setup",
      use: "To use",
      "need-setup": "you'll need to set up the following credentials.",
      cancel: "Cancel",
      save: "Save",
      settings: "settings",
      "multi-model": "This provider does not support multiple models.",
      "workspace-use": "Your workspace will use the model configured in",
      "model-set": "system settings",
      "system-default": "System default",
      "system-default-desc":
        "Use the system LLM preference for this workspace.",
      "no-selection": "No LLM selected",
      "select-provider": "Select an LLM provider",
      // Add keys for System Standard Option
      "system-standard-name": "System Standard",
      "system-standard-desc": "Use the primary LLM defined in system settings.",
    },
    "speak-prompt": "Speak your prompt",
    "view-agents": "View all available agents you can use for chatting",
    "ability-tag": "Ability",
    "change-text-size": "Change text size",
    "aria-text-size": "Change text size",

    model: {
      title: "Workspace Chat Model",
      description:
        "The specific chat model that will be used for this workspace. If empty, will use the system LLM preference.",
      wait: "-- waiting for models --",
      general: "General models",
      custom: "Custom models",
    },
    mode: {
      title: "Chat mode",
      chat: {
        title: "Chat",
        "desc-start": "will provide answers with the LLM's general knowledge",
        and: "and",
        "desc-end": "document context that is found.",
      },
      query: {
        title: "Query",
        "desc-start": "will provide answers",
        only: "only",
        "desc-end": "if document context is found.",
      },
    },
    history: {
      title: "Chat History",
      "desc-start":
        "The number of previous chats that will be included in the response's short-term memory.",
      recommend: "Recommend 20. ",
      "desc-end":
        "Anything more than 45 is likely to lead to continuous chat failures depending on message size.",
    },
    prompt: {
      title: "Prompt",
      description:
        "The prompt that will be used on this workspace. Define the context and instructions for the AI to generate a response. You should to provide a carefully crafted prompt so the AI can generate a relevant and accurate response.",
    },
    refusal: {
      title: "Query mode refusal response",
      "desc-start": "When in",
      query: "query",
      "desc-end":
        "mode, you may want to return a custom refusal response when no context is found.",
    },
    temperature: {
      title: "LLM Temperature",
      "desc-start":
        'This setting controls how "creative" your LLM responses will be.',
      "desc-end":
        "The higher the number the more creative. For some models this can lead to incoherent responses when set too high.",
      hint: "Most LLMs have various acceptable ranges of valid values. Consult your LLM provider for that information.",
    },
    "dynamic-pdr": {
      title: "Dynamic PDR for Workspace",
      description: "Enable or disable Dynamic PDR for this workspace.",
      "global-enabled":
        "Dynamic PDR is globally enabled and cannot be disabled for individual workspaces.",
    },
    // Chat log display section
    display_prompt_output_description:
      "Display the prompt output logging, Open & Download the file",
    display_prompt_output: "Display prompt output",
    loading_prompt_output: "Loading prompt output...",
    prompt_output_not_available:
      "*** Prompt output is not available for this chat.",
    open_in_new_tab: "Open in new tab",
  },

  // =========================
  // VECTOR DATABASE (WORKSPACE)
  // =========================
  "vector-workspace": {
    identifier: "Vector database identifier",
    snippets: {
      title: "Max Context Snippets",
      description:
        "This setting controls the maximum amount of context snippets the will be sent to the LLM for per chat or query.",
      recommend:
        "Recommended value is at least 30. Setting much higher numbers will increase processing time without necessarily improving precision depending on the capacity of the LLM used.",
    },
    doc: {
      title: "Document similarity threshold",
      description:
        "The minimum similarity score required for a source to be considered related to the chat. The higher the number, the more similar the source must be to the chat.",
      zero: "No restriction",
      low: "Low (similarity score ≥ .25)",
      medium: "Medium (similarity score ≥ .50)",
      high: "High (similarity score ≥ .75)",
    },
    reset: {
      reset: "Reset Vector Database",
      resetting: "Clearing vectors...",
      confirm:
        "You are about to reset this workspace's vector database. This will remove all vector embeddings currently embedded.\n\nThe original source files will remain untouched. This action is irreversible.",
      error: "Workspace vector database could not be reset!",
      success: "Workspace vector database was reset!",
    },
    prompt: { placeholder: "Ask your question here..." },
    refusal: { placeholder: "Sorry, I cannot answer that question" },
  },

  // =========================
  // AGENT CONFIGURATION
  // =========================
  agent: {
    "performance-warning":
      "Performance of LLMs that do not explicitly support tool-calling is highly dependent on the model's capabilities and accuracy. Some abilities may be limited or non-functional.",
    provider: {
      title: "Workspace Agent LLM Provider",
      description:
        "The specific LLM provider & model that will be used for this workspace's @agent agent.",
      "need-setup":
        "To use {{name}} as this workspace's agent LLM you need to set it up first.",
    },
    mode: {
      chat: {
        title: "Workspace Agent Chat model",
        description:
          "The specific chat model that will be used for this workspace's @agent agent.",
      },
      title: "Workspace Agent model",
      description:
        "The specific LLM model that will be used for this workspace's @agent agent.",
      wait: "-- waiting for models --",
    },
    skill: {
      title: "Default agent skills",
      description:
        "Improve the natural abilities of the default agent with these pre-built skills. This set up applies to all workspaces.",
      rag: {
        title: "RAG & long-term memory",
        description:
          'Allow the agent to leverage your local documents to answer a query or ask the agent to "remember" pieces of content for long-term memory retrieval.',
      },
      configure: {
        title: "Configure Agent Skills",
        description:
          "Customize and enhance the default agent's capabilities by enabling or disabling specific skills. These settings will be applied across all workspaces.",
      },
      view: {
        title: "View & summarize documents",
        description:
          "Allow the agent to list and summarize the content of workspace files currently embedded.",
      },
      scrape: {
        title: "Scrape websites",
        description:
          "Allow the agent to visit and scrape the content of websites.",
      },
      generate: {
        title: "Generate charts",
        description:
          "Enable the default agent to generate various types of charts from data provided or given in chat.",
      },
      save: {
        title: "Generate & save files to browser",
        description:
          "Enable the default agent to generate and write to files that save and can be downloaded in your browser.",
      },
      web: {
        title: "Live web search and browsing",
        "desc-start":
          "Enable your agent to search the web to answer your questions by connecting to a web-search (SERP) provider.",
        "desc-end":
          "Web search during agent sessions will not work until this is set up.",
      },
    },
  },

  // =========================
  // RECORDED WORKSPACE CHATS
  // =========================
  recorded: {
    title: "Workspace Chats",
    description:
      "These are all the recorded chats and messages that have been sent by users ordered by their creation date.",
    export: "Export",
    table: {
      id: "Id",
      by: "Sent By",
      workspace: "Workspace",
      prompt: "Prompt",
      response: "Response",
      at: "Sent At",
      invoice: "Invoice ref",
      "completion-token": "Completion Token",
      "prompt-token": "Prompt Token",
    },
    "clear-chats": "Delete All Current Logs",
    "confirm-clear-chats":
      "Are you sure you want to clear all logs?\n\nThis action is irreversible.",
    "fine-tune-modal": "Order Fine-Tune Model",
    "confirm-delete.chat":
      "Are you sure you want to delete this chat?\n\nThis action is irreversible.",
    next: "Next Page",
    previous: "Previous Page",
    filters: {
      "by-name": "Filter by username",
      "by-reference": "Reference number",
    },
    bulk_delete_title: "Bulk Delete Old Logs",
    bulk_delete_description:
      "Delete all prompt logs older than the selected timeframe.",
    delete_old_chats: "Delete Old Prompt Logs",
    total_logs: "Total log items",
    filtered_logs: "Filtered log items",
    reset_filters: "Reset filters",
    "no-chats-found": "No prompt logs found",
    "no-chats-description":
      "No prompt logs found matching your filters. Try changing your search criteria or delete older time period.",
    "deleted-old-chats": "Deleted {{count}} old prompt(s)",
    two_days: "2 Days",
    one_week: "1 Week",
    two_weeks: "2 Weeks",
    one_month: "1 Month",
    two_months: "2 Months",
    three_months: "3 Months",
    total_deleted: "Total Deleted Prompt Logs",
  },

  // =========================
  // CDP PROGRESS
  // =========================
  cdbProgress: {
    closeMsg: "Are you sure you want to cancel the process?",
    general: {
      placeholderSubTask: "Processing item {{index}}...",
    },
    main: {
      step1: {
        label: "Generate Section List",
        desc: "Using the main document to create an initial structure.",
      },
      step2: {
        label: "Process Documents",
        desc: "Generating descriptions and checking relevance.",
      },
      step3: {
        label: "Map Documents to Sections",
        desc: "Assigning relevant documents to each section.",
      },
      step4: {
        label: "Identify Legal Issues",
        desc: "Extracting key legal issues for each section.",
      },
      step5: {
        label: "Generate Legal Memos",
        desc: "Creating legal memoranda for the identified issues.",
      },
      step6: {
        label: "Draft Sections",
        desc: "Composing the content for each individual section.",
      },
      step7: {
        label: "Combine & Finalize Document",
        desc: "Assembling sections into the final document.",
      },
    },
    noMain: {
      step1: {
        label: "Processing Documents",
        desc: "Generating descriptions for all uploaded files.",
      },
      step2: {
        label: "Generate Section List",
        desc: "Creating a structured list of sections from document summaries.",
      },
      step3: {
        label: "Finalize Document Mapping",
        desc: "Confirming document relevance for each planned section.",
      },
      step4: {
        label: "Identify Legal Issues",
        desc: "Extracting key legal issues for each section.",
      },
      step5: {
        label: "Generate Legal Memos",
        desc: "Creating legal memoranda for the identified issues.",
      },
      step6: {
        label: "Draft Sections",
        desc: "Composing the content for each individual section.",
      },
      step7: {
        label: "Combine & Finalize Document",
        desc: "Assembling sections into the final legal document.",
      },
    },
  },

  // =========================
  // APPEARANCE & CUSTOMIZATION
  // =========================

  // Moved to systemSettingsPages.js

  // =========================
  // API KEYS
  // =========================
  api: {
    title: "API Keys",
    description:
      "API keys allow the holder to programmatically access and manage this instance.",
    link: "Read the API documentation",
    generate: "Generate New API Key",
    table: { key: "API Key", by: "Created By", created: "Created" },
    new: {
      title: "Create new API key",
      description:
        "Once created the API key can be used to programmatically access and configure this instance.",
      doc: "Read the API documentation",
      cancel: "Cancel",
      "create-api": "Create API key",
    },
  },

  // =========================
  // LLM PREFERENCE
  // =========================

  // Moved to systemSettingsPages.js

  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "The standard option for most non-commercial use.",
    azure: "The enterprise option of OpenAI hosted on Azure services.",
    anthropic: "A friendly AI Assistant hosted by Anthropic.",
    gemini: "Google's largest and most capable AI model",
    huggingface:
      "Access 150,000+ open-source LLMs and the world's AI community",
    ollama: "Run LLMs locally on your own machine.",
    lmstudio:
      "Discover, download, and run thousands of cutting edge LLMs in a few clicks.",
    localai: "Run LLMs locally on your own machine.",
    togetherai: "Run open source models from Together AI.",
    mistral: "Run open source models from Mistral AI.",
    perplexityai:
      "Run powerful and internet-connected models hosted by Perplexity AI.",
    openrouter: "A unified interface for LLMs.",
    groq: "The fastest LLM inferencing available for real-time AI applications.",
    koboldcpp: "Run local LLMs using koboldcpp.",
    oobabooga: "Run local LLMs using Oobabooga's Text Generation Web UI.",
    cohere: "Run Cohere's powerful Command models.",
    lite: "Run LiteLLM's OpenAI compatible proxy for various LLMs.",
    "generic-openai":
      "Connect to any OpenAI-compatible service via a custom configuration",
    native:
      "Use a downloaded custom Llama model for chatting on this instance.",
    xai: "Run xAI's powerful LLMs like Grok-2 and more.",
    "aws-bedrock": "Run powerful foundation models privately with AWS Bedrock.",
    deepseek: "Run DeepSeek's powerful LLMs.",
    fireworksai:
      "The fastest and most efficient inference engine to build production-ready, compound AI systems.",
    bedrock: "Run powerful foundation models privately with AWS Bedrock.",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Speech-to-text Preference",
    provider: "Provider",
    "system-native": "System Native",
    "desc-speech":
      "Here you can specify what kind of text-to-speech and speech-to-text providers you would want to use in your platform experience. By default, we use the browser's built in support for these services, but you may want to use others.",
    "title-text": "Text-to-speech Preference",
    "desc-text":
      "Here you can specify what kind of text-to-speech providers you would want to use in your platform experience. By default, we use the browser's built in support for these services, but you may want to use others.",
    "desc-config": "No configuration needed for browser native text to speech.",
    "placeholder-stt": "Search speech to text providers",
    "placeholder-tts": "Search text to speech providers",
    "native-stt": "Uses your browser's built in STT service if supported.",
    "native-tts": "Uses your browser's built in TTS service if supported.",
    "piper-tts": "Run TTS models locally in your browser privately.",
    "openai-description": "Use OpenAI's text to speech voices and technology.",
    openai: {
      "api-key": "API Key",
      "api-key-placeholder": "OpenAI API Key",
      "voice-model": "Voice Model",
    },
    elevenlabs: "Use ElevenLabs's text to speech voices and technology.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Transcription Model Preference",
    description:
      "These are the credentials and settings for your preferred transcription model provider. Its important these keys are current and correct or else media files and audio will not transcribe.",
    provider: "Transcription Provider",
    "warn-start":
      "Using the local whisper model on machines with limited RAM or CPU can stall the platform when processing media files.",
    "warn-recommend":
      "We recommend at least 2GB of RAM and upload files <10Mb.",
    "warn-end":
      "The built-in model will automatically download on the first use.",
    "search-audio": "Search audio transcription providers",
    "api-key": "API Key",
    "api-key-placeholder": "OpenAI API Key",
    "whisper-model": "Whisper Model",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Default Built-In",
    "default-built-in-desc":
      "Run a built-in whisper model on this instance privately.",
    "openai-name": "OpenAI",
    "openai-desc":
      "Leverage the OpenAI Whisper-large model using your API key.",
    "model-turbo": "openai/whisper-large-v3-turbo", // New model name
    "model-size-turbo": "(~810mb)", // New model size
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Embedding Preference",
    "desc-start":
      "When using an LLM that does not natively support an embedding engine - you may need to additionally specify credentials to for embedding text.",
    "desc-end":
      "Embedding is the process of turning text into vectors. These credentials are required to turn your files and prompts into a format which the platform can use to process.",
    provider: {
      title: "Embedding Provider",
      description:
        "There is no set up required when using the platform's native embedding engine.",
      "search-embed": "Search all embedding providers",
      search: "Search all embedding providers",
      select: "Select an embedding provider",
    },
    workspace: {
      title: "Workspace Embedding preference",
      description:
        "The specific embedding provider & model that will be used for this workspace. By default, it uses the system embedding provider and settings.",
      "multi-model":
        "Multi-model support is not supported for this provider yet.",
      "workspace-use": "This workspace will use",
      "model-set": "the model set for the system.",
      embedding: "Workspace Embedding model",
      model:
        "The specific embedding model that will be used for this workspace. If empty, will use the system embedding preference.",
      wait: "-- waiting for models --",
      setup: "Setup",
      use: "To use",
      "need-setup": "as this workspace embedder you need to set it up first.",
      cancel: "Cancel",
      save: "save",
      settings: "settings",
      search: "Search all embedding providers",
      "need-llm": "as this workspace LLM you need to set it up first.",
      "save-error": "Failed to save {{provider}} settings: {{error}}",
      "system-default": "System Default",
      "system-default-desc":
        "Use the system embedding preference for this workspace.",
    },
    warning: {
      "switch-model":
        "Switching the embedding model will break previously embedded documents from working during chat. They will need to un-embed from every workspace and fully removed and re-uploaded so they can be embed by the new embedding model.",
    },
  },

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Text Splitting & Chunking Settings",
    "desc-start":
      "Configure how your documents are split into chunks for processing.",
    "desc-end":
      "These settings affect how documents are processed and embedded.",
    "warn-start": "Warning:",
    "warn-center":
      "Changing these settings will only affect newly processed documents.",
    "warn-end":
      "Existing documents will need to be reprocessed to use the new settings.",
    method: {
      title: "Text Splitter Method",
      "native-explain": "Use local chunk size & overlap for splitting.",
      "jina-explain": "Delegate chunking/segmenting to Jina's built-in method.",
      "jina-info": "Jina chunking active.",
      jina: {
        max_tokens: "Jina: Max Tokens per Chunk",
        max_tokens_desc:
          "Defines the max tokens in each chunk for Jina's segmenter (maximum 2000 tokens).",
        return_tokens: "Return token information",
        return_tokens_desc:
          "Include token count and tokenizer information in the response.",
        return_chunks: "Return chunk information",
        return_chunks_desc:
          "Include chunk positions and metadata in the response.",
      },
    },
    size: {
      title: "Text Chunk Size",
      description:
        "This is the maximum length of characters that can be present in a single vector.",
      recommend: "Embed model maximum length is",
    },
    overlap: {
      title: "Text Chunk Overlap",
      description:
        "This is the maximum overlap of characters that occurs during chunking between two adjacent text chunks.",
      error: "Chunk overlap cannot be larger or equal to chunk size.",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Vector Database",
    description:
      "These are the credentials and settings for how your platform instance will function. It's important these keys are current and correct.",
    provider: {
      title: "Vector Database Provider",
      description: "There is no configuration needed for LanceDB.",
      "search-db": "Search all vector database providers",
      search: "Search all vector databases",
      select: "Select Vector Database Provider",
    },
    warning: {
      "switch-db":
        "Switching the vector database will require you to re-embed all documents across all relevant workspaces. This may take some time.",
    },
    search: {
      title: "Vector Search Mode",
      mode: {
        "globally-enabled":
          "This setting is controlled globally in system settings. Visit system settings to change the re-rank behavior.",
        default: "Default Search",
        "default-desc": "Standard vector similarity search without re-ranking.",
        "accuracy-optimized": "Accuracy Optimized",
        "accuracy-desc":
          "Re-ranks results to improve accuracy using cross-attention.",
      },
    },
  },

  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Embeddable Chat Widgets",
    description:
      "Embeddable chat widgets are public facing chat interfaces that are tied to a single workspace. These allow you to build workspaces that then you can publish to the world.",
    create: "Create embed",
    table: {
      workspace: "Workspace",
      chats: "Sent Chats",
      Active: "Active Domains",
    },
  },

  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Embed Chats",
    export: "Export",
    description:
      "These are all the recorded chats and messages from any embed that you have published.",
    table: {
      embed: "Embed",
      sender: "Sender",
      message: "Message",
      response: "Response",
      at: "Sent At",
    },
    delete: {
      title: "Delete Chat",
      message:
        "Are you sure you want to delete this chat?\n\nThis action is irreversible.",
    },
    config: {
      "delete-title": "Delete embed",
      "delete-message":
        "Are you sure you want to delete this embed? Once deleted this embed will no longer respond to chats or be active. This action is irreversible.",
      "disable-title": "Disable embed",
      "disable-message":
        "Are you sure you want to disable this embed?\n\nOnce disabled the embed will no longer respond to any chat requests.",
      "enable-title": "Enable embed",
      "enable-message":
        "Are you sure you want to enable this embed? Once enabled the embed will respond to chat requests again.",
    },
  },

  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Multi-User Mode",
    description:
      "Set up your instance to support your team by activating Multi-User Mode.",
    enable: {
      "is-enable": "Multi-User Mode is Enabled",
      enable: "Enable Multi-User Mode",
      description:
        "By default, you will be the only admin. As an admin you will need to create accounts for all new users or admins. Do not lose your password as only an Admin user can reset passwords.",
      username: "Admin account email",
      password: "Admin account password",
      "username-placeholder": "Your admin username",
      "password-placeholder": "Your admin password",
    },
    password: {
      title: "Password Protection",
      description:
        "Protect your instance with a password. If you forget this there is no recovery method so ensure you save this password.",
    },
    instance: {
      title: "Password Protect Instance",
      description:
        "By default, you will be the only admin. As an admin you will need to create accounts for all new users or admins. Do not lose your password as only an Admin user can reset passwords.",
      password: "Instance password",
    },
  },

  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Event Logs",
    description:
      "View all actions and events happening on this instance for monitoring.",
    clear: "Clear Event Logs",
    table: { type: "Event Type", user: "User", occurred: "Occurred At" },
  },

  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Privacy & Data-Handling",
    description:
      "This is your configuration for how connected third party providers and our platform handle your data.",
    llm: "LLM Selection",
    embedding: "Embedding Preference",
    vector: "Vector Database",
    anonymous: "Anonymous Telemetry Enabled",
    "desc-event": "All events do not record IP-address and contain",
    "desc-id": "no identifying",
    "desc-cont":
      "content, settings, chats, or other non-usage based information. To see the list of event tags collected you can look on",
    "desc-git": "Github here",
    "desc-end":
      "As an open-source project we respect your right to privacy. We are dedicated to building the best solution for integrating AI and documents privately and securely. If you do decide to turn off telemetry all we ask is to consider sending us feedback and thoughts so that we can continue to improve the platform for you",
  },

  // =========================
  // DEFAULT CHAT
  // =========================
  "default-chat": {
    welcome: "Welcome to IST Legal.",
    "choose-legal": "Choose a legal area to the left.",
  },

  // =========================
  // INVITES
  // =========================
  invites: {
    title: "Invitations",
    description:
      "Create invitation links for people in your organization to accept and sign up with. Invitations can only be used by a single user.",
    link: "Create Invite Link",
    accept: "Accepted By",
    usage: "Usage",
    "created-by": "Created By",
    created: "Created",
    new: {
      title: "Create new invite",
      "desc-start":
        "After creation you will be able to copy the invite and send it to a new user where they can create an account as the",
      "desc-mid": "default",
      "desc-end": "role and automatically be added to workspaces selected.",
      "auto-add": "Auto-add invitee to workspaces",
      "desc-add":
        "You can optionally automatically assign the user to the workspaces below by selecting them. By default, the user will not have any workspaces visible. You can assign workspaces later post-invite acceptance.",
      cancel: "Cancel",
      "create-invite": "Create Invite",
      error: "Error: ",
    },
    "link-copied": "Invite link copied",
    "copy-link": "Copy Invite Link",
    "delete-invite-title": "Deactivate Invite",
    "delete-invite-confirmation":
      "Are you sure you want to deactivate this invite?\nAfter you do this it will not longer be useable.\n\nThis action is irreversible.",
    status: {
      label: "Status",
      pending: "Pending",
      disabled: "Disabled",
      claimed: "Accepted",
    },
  },

  // =========================
  // USER MENU
  // =========================
  "user-menu": {
    edit: "Edit Account",
    profile: "Profile Picture",
    size: "800 x 800",
    "remove-profile": "Remove Profile Picture",
    username: "Email Address",
    "username-placeholder": "Enter email address",
    "new-password": "New Password",
    "new-password-placeholder": "New password",
    cancel: "Cancel",
    update: "Update Account",
    language: "Preferred language",
    email: "Email Address",
    "email-placeholder": "Enter email address",
  },

  // =========================
  // SIDEBAR (THREADS)
  // =========================
  sidebar: {
    thread: {
      "load-thread": "Loading threads...",
      delete: "Delete selected threads",
      deleted: "deleted",
      "empty-thread": "Thread",
      default: "Standard",
      "starting-thread": "Starting thread...",
      thread: "New thread",
      "rename-message": "Enter new name for thread:",
      "delete-message":
        "Are you sure you want to delete this thread? This action cannot be undone.",
      rename: "Rename",
      "delete-thread": "Delete thread",
      "rename-thread-title": "Rename Thread",
      "new-name-placeholder": "Enter new thread name",
      "delete-thread-title": "Delete Thread?",
      "delete-confirmation-message":
        'Are you sure you want to delete the thread "{{name}}"? This action cannot be undone.',
    },
  },

  // =========================
  // THREAD NAME ERROR
  // =========================
  thread_name_error:
    "Thread name must be between 3 and 255 characters and contain only letters, numbers, spaces, or hyphens.",

  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Use the built-in embedding provider. Zero setup!",
    openai: "The standard option for most non-commercial use.",
    azure: "The enterprise option of OpenAI hosted on Azure services.",
    localai: "Run embedding models locally on your own machine.",
    ollama: "Run embedding models locally on your own machine.",
    lmstudio:
      "Discover, download, and run thousands of cutting edge LLMs in a few clicks.",
    cohere: "Run powerful embedding models from Cohere.",
    voyageai: "Run powerful embedding models from Voyage AI.",
    "generic-openai": "Use a generic OpenAI embedding model.",
    "default.embedder": "Default Embedder",
    jina: "Jina AI's text-embedding models for multilingual and high-performance embeddings.",
    litellm: "Run powerful embedding models from LiteLLM.",
  },

  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb:
      "100% local vector DB that runs on the same instance as the platform.",
    chroma:
      "Open source vector database you can host yourself or on the cloud.",
    pinecone: "100% cloud-based vector database for enterprise use cases.",
    zilliz:
      "Cloud hosted vector database built for enterprise with SOC 2 compliance.",
    qdrant: "Open source local and distributed cloud vector database.",
    weaviate: "Open source local and cloud hosted multi-modal vector database.",
    milvus: "Open-source, highly scalable, and blazing fast.",
    astra: "Vector Search for Real-world GenAI.",
  },

  // =========================
  // SYSTEM PREFERENCES
  // =========================
  system: {
    title: "System Preferences",
    "desc-start":
      "These are the overall settings and configurations of your instance.",
    context_window: {
      title: "Dynamic Context Window",
      desc: "Control how much of the LLM's context window is used for additional sources.",
      label: "Context Window Percentage",
      help: "Percentage of the context window that can be used for enrichment (10-100%).",
      "toast-success": "Context window percentage updated successfully.",
      "toast-error": "Failed to update context window percentage.",
    },
    "change-login-ui": {
      title: "Select the Login Default UI",
      status: "Current",
      subtitle:
        "The UI will be applied as the default login UI for the application",
    },
    attachment_context: {
      title: "Attachment Context Window",
      desc: "Control how much of the Dynamic Context Window can be used for attachments.",
      label: "Attachment Context Percentage",
      help: "Percentage of the Dynamic Context Window that can be used for attachments (10-80%).",
      "toast-success": "Attachment context percentage updated successfully.",
      "toast-error": "Failed to update attachment context percentage.",
      "validation-error":
        "Attachment context percentage must be between 10 and 80.",
    },
    user: "Users can delete workspaces",
    "desc-delete":
      "Allow non-admin users to delete workspaces that they are a part of. This would delete the workspace for everyone.",
    limit: {
      title: "Message Limit",
      "desc-limit": "Limit the number of messages a user can send per day.",
      "per-day": "Messages per day",
      label: "Message limit is currently ",
    },
    max_tokens: {
      title: "Maximum Login Tokens per User",
      desc: "Set the maximum number of active authentication tokens each user can have at once. When exceeded, older tokens will be automatically removed.",
      label: "Maximum tokens",
      help: "Value must be greater than 0",
    },
    state: { enabled: "Enabled", disabled: "Disabled" },
    "source-highlighting": {
      title: "Enable / Disable Source highlighting",
      description: "Hide or display the source highlighting to users.",
      label: "Citation: ",
      "toast-success": "Source highlighting setting has been updated",
      "toast-error": "Failed to update source highlighting setting",
    },
    "usage-registration": {
      title: "Usage registration for invoicing",
      description: "Enable or disable invoice logging for system monitoring.",
      label: "Invoicing logging is ",
    },
    "forced-invoice-logging": {
      title: "Forced Invoice Logging",
      description:
        "Enable to require an invoicing reference before using the platform.",
      label: "Force invoicing logging is ",
    },
    "rexor-linkage": {
      title: "Rexor linkage",
      description:
        "Enable rexor linkage to get active cases references from rexor service.",
      label: "Rexor linkage is ",
      "activity-id": "Activity ID",
      "activity-id-description": "Enter the activity ID for Rexor integration",
    },
    save: "Save changes",
    rerank: {
      title: "Re-ranking Settings",
      description:
        "Configure re-ranking settings to improve search result relevance with LanceDB.",
      "enable-title": "Enable Re-ranking",
      "enable-description":
        "Enable re-ranking to improve search result relevance by considering more context.",
      status: "Re-ranking Status",
      "vector-count-title": "Additional Vectors for Re-ranking",
      "vector-count-description":
        "Number of additional vectors to retrieve beyond the workspace's vector count setting. For example, if workspace is set to retrieve 30 vectors and this is set to 50, a total of 80 vectors will be considered for re-ranking. A higher number may improve accuracy but will increase processing time.",
      "lancedb-only": "LanceDB Only",
      "lancedb-notice":
        "This feature is only available when using LanceDB as your vector database.",
    },
  },

  feedback: {
    fullName: "Full Name",
    fullNamePlaceholder: "Enter your full name",
    message: "Message",
    messagePlaceholder: "Enter your feedback message",
    attachment: "Attachment",
    submit: "Submit Feedback",
    submitting: "Submitting...",
    submitSuccess: "Feedback submitted successfully",
    submitError: "Failed to submit feedback",
    imageLoadError: "Failed to load image",
    unsupportedFile: "Unsupported file type",
    filePreview: "File Preview",
    thankYou: "Thank you! Your feedback has been submitted successfully.",
    emailSendError: "Failed to send email: ",
    submitFeedbackError: "Failed to submit feedback: ",
    attachFile: "Attach a file",
    improvePlatform: "Help us improve the platform!",
    suggestionOrQuestion: "Any suggestions? Or questions?",
    clickToWrite: "Please click to write to us",
    noFeedback: "No feedback found",
    previewImage: "Preview image",
    noFile: "No file attached",
    validation: {
      fullNameMinLength: "Full name must be at least 2 characters",
      fullNameMaxLength: "Full name cannot be longer than 100 characters",
      fullNameFormat:
        "Full name can only include letters, numbers, spaces, underscores (_), dots (.), @ symbols, and hyphens (-)",
      messageMinLength: "Message must be at least 12 characters",
      messageMaxLength: "Message cannot be longer than 1000 characters",
      messageMinWords: "Message must contain at least 4 words",
      fileType: "File must be a JPEG, PNG, or PDF",
      fileSize: "File size must be less than 5MB",
    },
  },

  "feedback-settings": {
    "delete-feedback": "Feedback deleted successfully!",
    "delete-error": "Delete feedback was not deleted",
    "header-title": "Feedback list",
    "header-description":
      "This is the complete list of feedback for this instance. Please note that deleting feedback is permanent and cannot be undone.",
    title: "User Feedback Button",
    description: "Enable or disable the feedback submission button for users.",
    successMessage: "User feedback button has been updated.",
    failureUpdateMessage: "Failed to update user feedback button status.",
    errorSubmitting: "Error submitting feedback settings.",
    errorFetching: "Error fetching feedback settings.",
    "new-feedback-available": "New feedback received in the last 24 hours",
  },

  // =========================
  // USER SETTINGS (INSTANCE USERS)
  // =========================
  "user-setting": {
    description:
      "These are all the accounts which have an account on this instance. Removing an account will instantly remove their access to this instance.",
    "add-user": "Add User",
    username: "Email Address",
    role: "Role",
    "economy-id": "Economy ID",
    "economy-id-ph": "Enter economy system identifier",
    "economy-id-hint":
      "ID used for integrations with external economy systems (e.g., Rexor)",
    default: "Default",
    manager: "Manager",
    admin: "Admin",
    superuser: "Superuser",
    "date-added": "Date Added",
    "all-domains": "All Domains",
    "other-users": "Other Users (No Domain)",
    // Sorting options for user list
    "sort-username": "Sort by Username",
    "sort-organization": "Sort by Organization",
    edit: "Edit: ",
    "new-password": "New Password",
    "password-rule": "Password must be at least 8 characters long.",
    "update-user": "Update User",
    placeholder: "Enter email address",
    cancel: "Cancel",
    "remove-user": "Remove User",
    "remove-user-title": "Remove User",
    "remove-user-confirmation": "Are you sure you want to remove this user?",
    error: "Error: ",
  },

  "login-ui": {
    "show-toast": {
      "update-failed": "Failed to update login UI",
      "updated-login-ui": "Login interface has been updated",
    },
    "visit-website": "Visit the website",
    loading: "Loading ...",
    "rw-login-description":
      "Maximize legal productivity with our AI-powered platform!",
  },

  // =========================
  // SUPPORT EMAIL
  // =========================
  support: {
    title: "Support Email",
    description:
      "Set the support email address that shows up in the user menu while logged into this instance.",
    clear: "Clear",
    save: "Save",
  },

  // =========================
  // PUBLIC MODE
  // =========================
  "public-mode": {
    enable: "Enable Public-User Mode",
    enabled: "Public-User Mode is Enabled",
  },

  // =========================
  // BUTTON LABELS
  // =========================
  button: {
    delete: "Delete",
    edit: "Edit",
    suspend: "Suspend",
    unsuspend: "Unsuspend",
    save: "Save",
    accept: "Accept",
    decline: "Decline",
    ok: "OK",
    "flush-vector-caches": "Flush Vector Caches",
    cancel: "Cancel",
    saving: "Saving",
    save_llm: "Save LLM Selection",
    save_template: "Save Template",
    "reset-to-default": "Reset to Default",
    create: "Create",
    enable: "Enable",
    disable: "Disable",
    reset: "Reset",
    revoke: "Revoke",
  },

  // =========================
  // NEW USER (INSTANCE)
  // =========================
  "new-user": {
    title: "Add user to instance",
    username: "Email Address",
    "username-ph": "Enter email address",
    password: "Password",
    "password-ph": "User's initial password",
    role: "Role",
    default: "Default",
    manager: "Manager",
    admin: "Administrator",
    superuser: "Superuser",
    description:
      "After creating a user they will need to login with their initial login to get access.",
    cancel: "Cancel",
    "add-User": "Add User",
    error: "Error: ",
    "invalid-email": "Please enter a valid email address.",
    permissions: {
      title: "Permissions",
      default: [
        "Can only send chats with workspaces they are added to by admin or managers.",
        "Cannot modify any settings at all.",
      ],
      manager: [
        "Can view, create, and delete any workspaces and modify workspace-specific settings.",
        "Can create, update and invite new users to the instance.",
        "Cannot modify LLM, vectorDB, embedding, or other connections.",
      ],
      admin: [
        "Highest user level privilege.",
        "Can see and do everything across the system.",
      ],
      superuser: [
        "Can access specific settings pages like the Document Builder and Prompt Upgrade.",
        "Cannot modify system-wide settings like LLM, vectorDB configurations.",
        "Can send chats with workspaces they are added to by admin or managers.",
      ],
    },
  },

  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Create new embed for workspace",
    error: "Error: ",
    "desc-start":
      "After creating an embed you will be provided a link that you can publish on your website with a simple",
    script: "script",
    tag: "tag.",
    cancel: "Cancel",
    "create-embed": "Create embed",
    workspace: "Workspace",
    "desc-workspace":
      "This is the workspace your chat window will be based on. All defaults will be inherited from the workspace unless overridden by this config.",
    "allowed-chat": "Allowed chat method",
    "desc-query":
      "Set how your chatbot should operate. Query means it will only respond if a document helps answer the query.",
    "desc-chat":
      "Chat opens the chat to even general questions and can answer totally unrelated queries to your workspace.",
    "desc-response": "Chat: Respond to all questions regardless of context",
    "query-response":
      "Query: Only respond to chats related to documents in workspace",
    restrict: "Restrict requests from domains",
    filter:
      "This filter will block any requests that come from a domain other than the list below.",
    "use-embed":
      "Leaving this empty means anyone can use your embed on any site.",
    "max-chats": "Max chats per day",
    "limit-chats":
      "Limit the amount of chats this embedded chat can process in a 24 hour period. Zero is unlimited.",
    "chats-session": "Max chats per session",
    "limit-chats-session":
      "Limit the amount of chats a session user can send with this embed in a 24 hour period. Zero is unlimited.",
    "enable-dynamic": "Enable dynamic model use",
    "llm-override":
      "Allow setting of the preferred LLM model to override the workspace default.",
    "llm-temp": "Enable dynamic LLM temperature",
    "desc-temp":
      "Allow setting of the LLM temperature to override the workspace default.",
    "prompt-override": "Enable Prompt Override",
    "desc-override":
      "Allow setting of the system prompt to override the workspace default.",
  },

  // =========================
  // SHOW TOAST MESSAGES
  // =========================

  // moved to showToast.js

  // =========================
  // TOAST MESSAGES
  // =========================
  toast: {
    export: {
      success: "{{name}} exported successfully",
      failed: "Failed to export",
      "embed-chats-success": "Embed chats exported successfully as {{name}}",
      "embed-chats-failed": "Failed to export embed chats",
    },
    document: {
      "move-success": "Successfully moved {{count}} documents",
      "pdr-failed": "Failed to PDR document: {{message}}",
      "watch-failed": "Failed to watch document: {{message}}",
      "pdr-added": "Document added to Parent Document Retrieval",
      "pdr-removed": "Document removed from Parent Document Retrieval",
      "pin-success":
        "DEPRECATED - Use document.pin-success-added or document.pin-success-removed instead",
      "pin-success-added": "Document pinned to workspace",
      "pin-success-removed": "Document unpinned from workspace",
    },
    profile: {
      "upload-failed": "Failed to upload profile picture: {{error}}",
      "remove-failed": "Failed to remove profile picture: {{error}}",
    },
    settings: {
      "preferences-failed": "Failed to save preferences: {{error}}",
      "multi-user-failed": "Failed to enable Multi-User mode: {{error}}",
      "public-user-failed": "Failed to enable Public-User mode: {{error}}",
      "password-failed": "Failed to update password: {{error}}",
      "vector-db-failed": "Failed to save vector database settings: {{error}}",
      "llm-failed": "Failed to save LLM settings: {{error}}",
      "llm-save-failed": "Failed to save {{name}} settings: {{error}}",
      "preferences-save-failed": "Failed to save preferences: {{error}}",
      "transcription-save-failed":
        "Failed to save transcription settings: {{error}}",
      "embedding-save-failed": "Failed to save embedding settings: {{error}}",
      "welcome-messages-failed": "Failed to save welcome messages: {{error}}",
      "welcome-messages-fetch-failed": "Failed to fetch welcome messages",
      "welcome-messages-empty": "Please enter either a heading or text message",
      "welcome-messages-success": "Welcome messages saved successfully",
      "user-update-failed": "Failed to update user: {{error}}",
      "logs-clear-failed": "Failed to clear logs: {{error}}",
      "generic-error": "Error: {{message}}",
      "prompt-examples-failed": "Failed to save prompt examples: {{error}}",
      "prompt-examples-success": "Prompt examples saved successfully",
      "prompt-examples-validation":
        "Example {{number}} is missing required fields: {{fields}}",
      "import-success": "Successfully imported thread: {{threadName}}",
    },
    workspace: {
      "create-failed": "Failed to create workspace: {{error}}",
      "update-failed": "Failed to update workspace: {{error}}",
      "thread-create-failed": "Could not create thread: {{error}}",
      "thread-update-failed": "Thread could not be updated: {{message}}",
    },
    errors: {
      "file-move": "Error moving files: {{message}}",
      "thread-create": "Could not create thread - {{error}}",
      "connector-error": "{{error}}",
      "generic-error": "{{error}}",
      "failed-update-user": "Failed to update user: {{error}}",
      "failed-save-llm": "Failed to save LLM settings: {{error}}",
      "failed-save-preferences": "Failed to save preferences: {{error}}",
      "failed-clear-logs": "Failed to clear logs: {{error}}",
      "failed-create-thread": "Could not create thread - {{error}}",
      "failed-move-files": "Error moving files: {{message}}",
      streaming: {
        failed:
          "An error occurred while streaming response, such as AI engine being offline or over capacity.",
        code: "Code",
        unknown: "Unknown Error.",
      },
    },
    success: {
      "workspace-created":
        "Workspace created successfully! Taking you to home...",
      "generic-success": "{{message}}",
    },
    "prompt-output-logging": {
      enabled: "Prompt output logging has been enabled",
      disabled: "Prompt output logging has been disabled",
      error: "Error while submitting prompt output logging settings.",
    },
    "welcome-messages": {
      "update-failed": "Failed to update welcome messages: {{error}}",
      "drafting-update-failed":
        "Failed to update document drafting messages: {{error}}",
    },
    info: { "file-moved": "{{message}}" },
    experimental: {
      "feature-enabled": "Experimental features enabled",
      "feature-disabled": "Experimental features disabled",
      "update-failed": "Failed to update experimental feature status",
      "features-enabled":
        "Experimental Feature set enabled. Reloading the page.",
      "live-sync": {
        enabled: "Live document sync enabled",
        disabled: "Live document sync disabled",
      },
    },
  },

  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    openai: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to OpenAI",
      ],
    },
    azure: {
      description: [
        "Your chats will not be used for training",
        "Your text and embedding text are not visible to OpenAI or Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to Anthropic",
      ],
    },
    gemini: {
      description: [
        "Your chats are de-identified and used in training",
        "Your prompts and document text used in response creation are visible to Google",
      ],
    },
    lmstudio: {
      description: [
        "Your model and chats are only accessible on the server running LMStudio",
      ],
    },
    localai: {
      description: [
        "Your model and chats are only accessible on the server running LocalAI",
      ],
    },
    ollama: {
      description: [
        "Your model and chats are only accessible on the machine running Ollama models",
      ],
    },
    native: {
      description: [
        "Your model and chats are only accessible on this instance",
      ],
    },
    togetherai: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to TogetherAI",
      ],
    },
    mistral: {
      description: [
        "Your prompts and document text used in response creation are visible to Mistral",
      ],
    },
    huggingface: {
      description: [
        "Your prompts and document text used in response are sent to your HuggingFace managed endpoint",
      ],
    },
    perplexity: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to OpenRouter",
      ],
    },
    groq: {
      description: [
        "Your chats will not be used for training",
        "Your prompts and document text used in response creation are visible to Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Your model and chats are only accessible on the server running KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Your model and chats are only accessible on the server running the Oobabooga Text Generation Web UI",
      ],
    },
    "generic-openai": {
      description: [
        "Data is shared according to the terms of service applicable with your generic endpoint provider.",
      ],
    },
    cohere: {
      description: [
        "Data is shared according to the terms of service of cohere.com and your localities privacy laws.",
      ],
    },
    litellm: {
      description: [
        "Your model and chats are only accessible on the server running LiteLLM",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Your vectors and document text are stored on your Chroma instance",
        "Access to your instance is managed by you",
      ],
    },
    pinecone: {
      description: [
        "Your vectors and document text are stored on Pinecone's servers",
        "Access to your data is managed by Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Your vectors and document text are stored on your Qdrant instance (cloud or self-hosted)",
      ],
    },
    weaviate: {
      description: [
        "Your vectors and document text are stored on your Weaviate instance (cloud or self-hosted)",
      ],
    },
    milvus: {
      description: [
        "Your vectors and document text are stored on your Milvus instance (cloud or self-hosted)",
      ],
    },
    zilliz: {
      description: [
        "Your vectors and document text are stored on your Zilliz cloud cluster.",
      ],
    },
    astra: {
      description: [
        "Your vectors and document text are stored on your cloud AstraDB database.",
      ],
    },
    lancedb: {
      description: [
        "Your vectors and document text are stored privately on this instance of the platform",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: [
        "Your document text is embedded privately on this instance of the platform",
      ],
    },
    openai: {
      description: [
        "Your document text is sent to OpenAI servers",
        "Your documents are not used for training",
      ],
    },
    azure: {
      description: [
        "Your document text is sent to your Microsoft Azure service",
        "Your documents are not used for training",
      ],
    },
    localai: {
      description: [
        "Your document text is embedded privately on the server running LocalAI",
      ],
    },
    ollama: {
      description: [
        "Your document text is embedded privately on the server running Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Your document text is embedded privately on the server running LMStudio",
      ],
    },
    cohere: {
      description: [
        "Data is shared according to the terms of service of cohere.com and your localities privacy laws.",
      ],
    },
    voyageai: {
      description: [
        "Data sent to Voyage AI's servers is shared according to the terms of service of voyageai.com.",
      ],
    },
  },

  // =========================
  // PROMPT VALIDATION
  // =========================
  "prompt-validate": {
    edit: "Edit",
    response: "Response",
    prompt: "Prompt",
    regenerate: "Regenerate response",
    good: "Good response",
    bad: "Bad response",
    copy: "Copy",
    more: "More actions",
    fork: "Fork",
    delete: "Delete",
    cancel: "Cancel",
    save: "Save & Submit",
    "export-word": "Export to Word",
    exporting: "Exporting...",
  },

  // =========================
  // CITATIONS
  // =========================
  citations: {
    show: "Show Citations",
    hide: "Hide Citations",
    chunk: "Citation Chunks",
    pdr: "Parent Document",
    "pdr-h": "Document Highlighting",
    referenced: "Referenced",
    times: "times.",
    citation: "Citation",
    match: "match",
    download:
      "This browser does not support PDFs. Please download the PDF to view it:",
    "download-btn": "Download PDF",
    view: "View citations",
    sources: "View sources and citations",
    "pdf-collapse-tip":
      "Tip: You can collapse this PDF tab using the button in the upper left corner",
    "open-in-browser": "Open in browser",
    "loading-pdf": "-- loading PDF --",
    "error-loading": "Error loading PDF",
    "no-valid-path": "No valid PDF path found",
    "web-search": "Web Search",
    "web-search-summary": "Web Search Summary",
    "web-search-results": "Web Search Results",
    "no-web-search-results": "No web search results found",
    "previous-highlight": "Previous highlight",
    "next-highlight": "Next highlight",
    "try-alternative-view": "Try alternative view",
  },

  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Document Drafting",
    description: "Control your document drafting settings.",
    configuration: "Configuration",
    "drafting-model": "Drafting LLM",
    enabled: "Document Drafting is enabled",
    disabled: "Document Drafting is disabled",
    "enabled-toast": "Document Drafting enabled",
    "disabled-toast": "Document Drafting disabled",
    "desc-settings":
      "The Admin can change the document drafting settings for all users.",
    "drafting-llm": "Drafting LLM Preference",
    saving: "Saving...",
    save: "Save changes",
    "chat-settings": "Chat Settings",
    "drafting-chat-settings": "Document Drafting Chat Settings",
    "chat-settings-desc":
      "Control the behavior of the chat feature for document drafting.",
    "drafting-prompt": "Document Drafting system Prompt",
    "drafting-prompt-desc":
      "The system prompt that will be used in document drafting is different from the legal Q&A systemprompt. Document drafting system prompt defines the context and instructions for the AI to generate a response. You should to provide a carefully crafted prompt so the AI can generate a relevant and accurate response",
    linking: "Document Linking",
    "legal-issues-prompt": "Legal Issues Prompt",
    "legal-issues-prompt-desc": "Enter the prompt for legal issues.",
    "memo-prompt": "Memo Prompt",
    "memo-prompt-desc": "Enter the prompt for memo.",
    "desc-linkage":
      "Enable adding further legal context by doing Vector/PDR searches on top of memo fetching",
    message: {
      title: "Suggested Document Drafting Messages",
      description:
        "Add suggested messages that users can quickly select when drafting documents.",
      heading: "Default Message Heading",
      body: "Default Message Body",
      "new-heading": "Message Heading",
      message: "Message Content",
      add: "Add Message",
      save: "Save Messages",
    },
    "combine-prompt": "Combine Prompt",
    "combine-prompt-desc":
      "Provide the system prompt for combining multiple responses into a single response. This prompt is used for both combining response and DD Linkage memos, and for combining the different responses from Infinity Context processing.",
    "page-description":
      "This page is for adjusting the different prompts used in different functionalities in the document drafting module. In each input field, the default prompt is shown, which will be used unless a custom prompt is applied on this page.",
    "dd-linkage-steps": "Prompts applied for DD Linkage steps",
    "general-combination-prompt": "General Combination Prompt",
    "import-memo": {
      title: "Import from Legal QA",
      "button-text": "Import Memo",
      "search-placeholder": "Search threads...",
      import: "Import",
      importing: "Importing...",
      "no-threads": "No Legal QA threads found",
      "no-matching-threads": "No threads match your search",
      "thread-not-found": "Selected thread not found",
      "empty-thread": "The selected thread has no content to import",
      "import-success": "Thread content imported successfully",
      "import-error": "Failed to import thread content",
      "import-error-details": "Error during import: {{details}}",
      "fetch-error": "Failed to fetch threads. Please try again later.",
      "imported-from": "Imported from Legal QA thread",
      "unnamed-thread": "Unnamed Thread",
      "unknown-workspace": "Unknown Workspace",
      "no-threads-available": "No threads available to import",
      "create-conversations-first":
        "Create conversations in a Legal QA workspace first, then you can import them here.",
      "no-legal-qa-workspaces":
        "No Legal QA workspaces with active threads were found. Create conversations in a Legal QA workspace first to import them.",
      "empty-workspaces-with-names":
        "Found Legal QA workspaces ({{workspaceNames}}) but they don't contain any active threads yet. Create conversations in these workspaces first to import them.",
      "import-success-with-name":
        "Successfully imported thread: {{threadName}}",
    },
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Legal task user prompt generator",
    description: "Automatic proposal of the customized prompt for a legal task",
    "task-description": "Legal task description",
    "task-description-placeholder":
      "Describe the legal task you want to accomplish...",
    "specific-instructions": "Specific instructions or know-how",
    "specific-instructions-description":
      "Include any special instructions or expertise specific to this legal task",
    "specific-instructions-placeholder":
      "Add specific instructions, expertise, or know-how for handling this legal task...",
    "suggested-prompt": "Suggested user prompt",
    "generation-prompt": "Prompt for generation",
    "create-task": "Create legal task based on this suggestion",
    generating: "Generating...",
    generate: "Generate proposal",
    "toast-success": "Successfully generated prompt",
    "toast-fail": "Failed to generate prompt",
    button: "Generate Prompt",
    success: "Prompt generated successfully",
    error: "Please enter a name or subcategory first",
    failed: "Failed to generate prompt",
  },

  // =========================
  // DD SETTINGS (WORKSPACE LINKING SETTINGS)
  // =========================
  "dd-settings": {
    title: "Workspace Linking Settings",
    description:
      "Control token limits and behavior for workspace linking features",
    "vector-search": {
      title: "Vector Search",
      description:
        "When enabled, this feature performs semantic vector searches across all linked workspaces to find relevant legal documents. The system will convert user queries into vector embeddings and match them against document vectors in each linked workspace's database. This feature serves as a fallback when Memo Generation is enabled but fails to produce results. When Memo Generation is disabled, Vector Search becomes the primary method for retrieving information from linked workspaces. The search depth is controlled by the Vector Token Limit setting.",
    },
    "memo-generation": {
      title: "Memo Generation",
      description:
        "This feature automatically generates concise legal issue memos from documents found in linked workspaces. When enabled, the system analyzes retrieved documents to create structured summaries of key legal points, precedents, and relevant context. These memos serve as the primary method for incorporating knowledge from linked workspaces. If memo generation fails or returns no results, the system will automatically fall back to Vector Search (if enabled) to ensure relevant information is still retrieved. The length and detail of these memos are controlled by the Memo Token Limit setting.",
    },
    "base-generation": {
      title: "Base Legal Analysis",
      description:
        "Enables preliminary legal analysis generation based on the user's initial query, before incorporating information from linked workspaces. When active, the system creates a foundational analysis framework that helps guide the subsequent document search and memo generation processes. This base analysis helps ensure that responses remain focused on the core legal issues while incorporating supporting information from linked workspaces. The scope of this analysis is controlled by the Base Token Limit setting.",
    },
    "linked-workspace-impact": {
      title: "Linked Workspace Token Impact",
      description:
        "Controls how the system manages its token budget across multiple linked workspaces. When enabled, the system will dynamically adjust the available tokens for each workspace based on the total number of linked workspaces, ensuring fair distribution of computational resources. This prevents any single workspace from dominating the context window while maintaining comprehensive coverage across all relevant legal areas. This setting reserves token capacity specifically for Memo Generation and/or Vector Search results from each linked workspace, which can reduce the total tokens available for the primary workspace when many workspaces are linked.",
    },
    "vector-token-limit": {
      title: "Vector Token Limit",
      description:
        "Specifies the maximum number of tokens allocated for vector search results from each linked workspace. This limit applies when Vector Search is used, either as the primary method (when Memo Generation is disabled) or as a fallback (when Memo Generation fails). Higher limits allow more comprehensive document retrieval but reduce the tokens available for other operations.",
    },
    "memo-token-limit": {
      title: "Memo Token Limit",
      description:
        "Controls the maximum length of generated legal issue memos from each linked workspace. As the primary method for knowledge incorporation, these memos summarize key legal points from the linked workspace's documents. If a memo exceeds this token limit, it will be rejected and the system will fall back to Vector Search (if enabled). Higher limits enable more detailed legal analysis but may reduce the number of linked workspaces that can be incorporated.",
    },
    "base-token-limit": {
      title: "Base Token Limit",
      description:
        "Determines the maximum token length for the initial legal analysis framework. This limit affects how comprehensive the base analysis can be before incorporating information from linked workspaces. A higher limit allows for more detailed initial analysis but leaves less room for incorporating content from linked workspaces.",
    },
    "toast-success": "Settings updated successfully",
    "toast-fail": "Failed to update settings",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Workspace Linking Settings",
    description:
      "Control token limits and behavior for workspace linking features",
    "vector-search": {
      title: "Vector Search",
      description:
        "Fallback method for finding relevant documents when memo generation fails or is disabled",
    },
    "memo-generation": {
      title: "Memo Generation",
      description:
        "Primary method for incorporating knowledge from linked workspaces",
    },
    "base-generation": {
      title: "Base Legal Analysis",
      description: "Generate initial legal issue analysis from user prompts",
    },
    "linked-workspace-impact": {
      title: "Linked Workspace Token Impact",
      description:
        "Reserve tokens for each linked workspace in proportion to their number",
    },
    "vector-token-limit": {
      title: "Vector Token Limit",
      description: "Maximum tokens per linked workspace for vector search",
    },
    "memo-token-limit": {
      title: "Memo Token Limit",
      description: "Maximum tokens for legal issue memo generation",
    },
    "base-token-limit": {
      title: "Base Token Limit",
      description: "Maximum tokens for base content fetching",
    },
    "toast-success": "Settings updated successfully",
    "toast-fail": "Failed to update settings",
  },

  // =========================
  // MODALE (DOCUMENT & CONNECTORS)
  // =========================
  modale: {
    document: {
      title: "My Documents",
      document: "Documents",
      search: "Search for document",
      folder: "New Folder",
      name: "Name",
      empty: "No Documents",
      "move-workspace": "Move to Workspace",
      "doc-processor": "Document Processor",
      "processor-offline":
        "The document processor is currently offline. Please try again later.",
      "drag-drop": "Click to upload or drag and drop files here",
      "supported-files": "Supported files: PDF",
      "submit-link": "Or submit a link to process",
      fetch: "Fetch",
      fetching: "Fetching...",
      "file-desc":
        "Files will be processed and made available for chat context.",
      cost: "*One time cost for embeddings",
      "save-embed": "Save and Embed",
      "failed-uploads": "Failed Uploads",
      "loading-message": "This may take a while for large documents",
      "uploading-file": "Uploading file(s)...",
      "scraping-link": "Scraping link...",
      "moving-documents": "Moving {{count}} documents. Please wait.",
      "exceeds-prompt-limit":
        "Note: The uploaded content exceeds what can fit with one prompt. The system will process any requests by multiple prompts which will increase the time for generating the answer, and precision may be affected.",
    },
    connectors: {
      title: "Data Connectors",
      search: "Search data connectors",
      empty: "No data connectors found.",
    },
    "justify-betweening": "Processing...",
  },

  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "GitHub Repo",
      description:
        "Import an entire public or private Github repository in a single click.",
      url: "GitHub Repo URL",
      "collect-url": "Url of the GitHub repo you wish to collect.",
      "access-token": "Github Access Token",
      optional: "optional",
      "rate-limiting": "Access Token to prevent rate limiting.",
      "desc-picker":
        "Once complete, all files will be available for embedding into workspaces in the document picker.",
      branch: "Branch",
      "branch-desc": "Branch you wish to collect files from.",
      "branch-loading": "-- loading available branches --",
      "desc-start": "Without filling out the",
      "desc-token": "Github Access Token",
      "desc-connector": "this data connector will only be able to collect the",
      "desc-level": "top-level",
      "desc-end": "files of the repo due to GitHub's public API rate-limits.",
      "personal-token":
        "Get a free Personal Access Token with a GitHub account here.",
      without: "Without a",
      "personal-token-access": "Personal Access Token",
      "desc-api":
        ", the GitHub API may limit the number of files that can be collected due to rate limits. You can",
      "temp-token": "create a temporary Access Token",
      "avoid-issue": "to avoid this issue.",
      submit: "Submit",
      "collecting-files": "Collecting files...",
    },
    "youtube-transcript": {
      name: "YouTube Transcript",
      description:
        "Import the transcription of an entire YouTube video from a link.",
      url: "YouTube Video URL",
      "url-video": "URL of the YouTube video you wish to transcribe.",
      collect: "Collect transcript",
      collecting: "Collecting transcript...",
      "desc-end":
        "once complete, the transcription will be available for embedding into workspaces in the document picker.",
    },
    "website-depth": {
      name: "Bulk Link Scraper",
      description: "Scrape a website and its sub-links up to a certain depth.",
      url: "Website URL",
      "url-scrape": "URL of the website you want to scrape.",
      depth: "Depth",
      "child-links":
        "This is the number of child-links that the worker should follow from the origin URL.",
      "max-links": "Max Links",
      "links-scrape": "Maximum number of links to scrape.",
      scraping: "Scraping website...",
      submit: "Submit",
      "desc-scrap":
        "Once complete, all scraped pages will be available for embedding into workspaces in the document picker.",
    },
    confluence: {
      name: "Confluence",
      description: "Import an entire Confluence page in a single click.",
      url: "Confluence Page URL",
      "url-page": "URL of a page in the Confluence space.",
      username: "Confluence Username",
      "own-username": "Your Confluence username.",
      token: "Confluence Access Token",
      "desc-start":
        "You need to provide an access token for authentication. You can generate an access token",
      here: "here",
      access: "Access token for authentication.",
      collecting: "Collecting pages...",
      submit: "Submit",
      "desc-end":
        "Once complete, all pages will be available for embedding into workspaces.",
    },
  },

  // =========================
  // MODULE DEFINITIONS
  // =========================
  module: {
    "legal-qa": "Legal Q&A",
    "document-drafting": "Document Drafting",
    "active-case": "Active Case",
  },

  // =========================
  // FINE-TUNE NOTIFICATION
  // =========================
  "fine-tune": {
    title: "You have enough data for a fine-tune!",
    link: "click to learn more",
    dismiss: "dismiss",
  },

  // =========================
  // MOBILE DISCLAIMER
  // =========================
  mobile: {
    disclaimer:
      "DISCLAIMER: For the best experience and full access to all features, please use a computer to access the app.",
  },

  // =========================
  // SHARE MODAL
  // =========================
  shareModal: {
    title: "Share {type}",
    titleWorkspace: "Share Workspace",
    titleThread: "Share Thread",
    shareWithUsers: "Share with users",
    shareWithOrg: "Share with entire organization",
    searchUsers: "Search users...",
    noUsersFound: "No users found",
    loadingUsers: "Loading users...",
    errorLoadingUsers: "Error loading users",
    errorLoadingStatus: "Error loading share status",
    userAccessGranted: "User access granted successfully",
    userAccessRevoked: "User access revoked successfully",
    orgAccessGranted: "Organization access granted successfully",
    orgAccessRevoked: "Organization access revoked successfully",
    errorUpdateUser: "Error updating user access",
    errorUpdateOrg: "Error updating organization access",
    errorNoOrg: "Unable to share: account not linked to an organization",
    close: "Close",
    grantAccess: "Grant Access",
    revokeAccess: "Revoke Access",
  },

  // =========================
  // ONBOARDING
  // =========================
  onboarding: {
    welcome: "Welcome to",
    "get-started": "Get started",
    "llm-preference": {
      title: "LLM Preference",
      description:
        "ISTLLM can work with many LLM providers. This will be the service which handles chatting.",
      "LLM-search": "Search LLM providers",
    },
    "user-setup": {
      title: "User Setup",
      description: "Configure your user settings.",
      "sub-title": "How many people will be using your instance?",
      "single-user": "Just me",
      "multiple-user": "My team",
      "setup-password": "Would you like to set up a password?",
      "password-requirment": "Passwords must be at least 8 characters.",
      "save-password":
        "It's important to save this password because there is no recovery method.",
      "password-label": "Instance Password",
      username: "Admin account email",
      password: "Admin account password",
      "account-requirment":
        "Email must be valid and only contain lowercase letters, numbers, underscores, and hyphens with no spaces. Password must be at least 8 characters long.",
      "password-note":
        "By default, you will be the only admin. Once onboarding is completed you can create and invite others to be users or admins. Do not lose your password as only admins can reset passwords.",
    },
    "data-handling": {
      title: "Data Handling & Privacy",
      description:
        "We are committed to transparency and control when it comes to your personal data.",
      "llm-label": "LLM Selection",
      "embedding-label": "Embedding Preference",
      "database-lablel": "Vector Database",
      "reconfigure-option":
        "These settings can be reconfigured at any time in the settings.",
    },
    survey: {
      title: "Welcome to IST Legal LLM",
      description: "Help us make IST Legal LLM built for your needs. Optional.",
      email: "What's your email?",
      usage: "What will you use the platform for?",
      work: "For work",
      "personal-use": "For my personal use",
      other: "Other",
      comment: "Any comments for the team?",
      optional: "(Optional)",
      feedback: "Thank you for your feedback!",
    },
    button: { yes: "Yes", no: "No", "skip-survey": "Skip Survey" },
    placeholder: {
      "admin-password": "Your admin password",
      "admin-username": "Your admin email",
      "email-example": "<EMAIL>",
      comment:
        "If you have any questions or comments right now, you can leave them here and we will get back to you. You can <NAME_EMAIL>",
    },
  },

  // =========================
  // DEFAULT SETTINGS FOR LEGAL Q&A
  // =========================
  "default-settings": {
    "canvas-prompt": "Canvas System Prompt",
    "canvas-prompt-desc":
      "Prompt for the canvas chat system. Used as the system prompt for canvas chat interactions.",
    title: "Default Settings for Legal Q&A",
    "default-desc": "Control the default behavior of workspaces for Legal Q&A",
    prompt: "Legal Q&A system Prompt",
    "prompt-desc":
      "The default prompt that will be used for new Legal Q&A workspaces. Define the context and instructions for the AI to generate a response. You should provide a carefully crafted prompt so the AI can generate a relevant and accurate response. To apply this setting to all existing workspaces, overriding their custom settings, use the apply button below.",
    "prompt-placeholder": "Enter your prompt here",
    "toast-success": "Default system prompt updated",
    "toast-fail": "Failed to updated system prompt",
    "apply-all-confirm":
      "Are you sure you want to apply this prompt to all existing Legal Q&A workspaces? This action cannot be undone and will override any custom settings.",
    "apply-to-all": "Apply to all existing Legal Q&A workspaces",
    applying: "Applying...",
    "toast-apply-success": "Default prompt applied to {{count}} workspaces",
    "toast-apply-fail": "Failed to apply default prompt to workspaces",
    snippets: {
      title: "Default Max Context Snippets",
      description:
        "The number of relevant document snippets to include in the context for new workspaces. To apply this setting to all existing workspaces, overriding their custom settings, use the apply button below.",
      recommend:
        "Recommended value is at least 30. Setting much higher numbers will increase processing time without necessarily improving precision depending on the capacity of the LLM used.",
    },
    "rerank-limit": {
      title: "Maximum Rerank Limit",
      description: "The maximum number of snippets to rerank",
      recommend: "Recommended value is between 20-50",
    },
    "validation-prompt": {
      title: "Validation Prompt",
      description:
        "This setting controls the validation prompt that will be sent to the LLM to validate the given answer.",
      placeholder:
        "Please validate the following response, checking all legal references and citations for accuracy against the provided context. List any inaccuracies or misrepresentations found.",
    },
    "apply-vector-search-to-all": "Apply to all existing Legal Q&A workspaces",
    "apply-vector-search-all-confirm":
      "Are you sure you want to apply this vector search setting to all existing Legal Q&A workspaces? This action cannot be undone.",
    "toast-vector-search-apply-success":
      "Vector search setting applied to {{count}} workspaces",
    "toast-vector-search-apply-fail":
      "Failed to apply vector search setting to workspaces",
    "canvas-upload-prompt": "Canvas Upload System Prompt",
    "canvas-upload-prompt-desc":
      "The system prompt used when processing uploads via the canvas. This prompt guides the LLM's behavior for uploaded content.",
    saving: "Saving...",
    save: "Save Changes",
    "toast-fail-load-prompts": "Failed to load prompt configurations.", // New key added here
  },

  // =========================
  // CONFIRM MESSAGE
  // =========================
  "confirm-message": {
    "delete-doc":
      "Are you sure you want to delete these files and folders?\nThis will remove the files from the system and remove them from any existing workspaces automatically.\nThis action is not reversible.",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Perform Legal Task",
    noTaskfund: "No legal tasks available.",
    noSubtskfund: "No subcategories available.",
    "loading-subcategory": "Loading subcategories...",
    "select-category": "Select category",
    "choose-task": "Choose legal task to perform",
    "duration-info":
      "The time for performing a legal task depends on the number of documents in the workspace. With many documents and a complex task, this can take a very long time.",
    description:
      "Enable or disable perform legal task button in the Document Drafting.",
    successMessage: "Perform legal task has been {{status}}",
    failureUpdateMessage: "Failed to update perform legal task setting.",
    errorSubmitting: "Error while submitting perform legal task settings.",
    "additional-instructions-label": "Additional Instructions:",
    "custom-instructions-placeholder":
      "Enter additional instructions for the legal task (optional)...",
    "warning-title": "Warning",
    "no-files-title": "No Files Available",
    "no-files-description":
      "There are no files in this workspace. Please upload at least one file before performing a legal task.",
    "settings-button": "Add or edit available legal tasks",
    settings: "Legal Tasks Settings",
    subStep: "Ongoing or queued step",
  },

  // =========================
  // CANVAS CHAT
  // =========================
  canvasChat: {
    title: "Canvas",
    "input-placeholder": "Ask for legal information",
    chatboxinstruction: "Instruct any adjustment of the answer",
    explanation:
      "This tool is for AI-editing of the answer in various ways. The sources for the underlying answer are applied, which means that you can ask for additional clarifications using the same source material as for the underlying answer.",
    editAnswer: "Edit answer",
  },

  // =========================
  // STATUSES
  // =========================
  statuses: { enabled: "enabled", disabled: "disabled" },

  // =========================
  // ANSWER UPGRADE
  // =========================

  // Moved to answerUpgrade.js

  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "PDR Settings",
    description:
      "Configure Parent Document Retrieval settings for your workspaces.",
    "desc-end":
      "These settings affect how PDR documents are processed and used in chat responses.",
    "global-override": {
      title: "Global Dynamic PDR Override",
      description:
        "When enabled, this will handle all workspace documents as PDR-enabled for context in responses. When disabled, only documents explicitly marked as PDR will be used, which may reduce available context and rely result in much lower quality answers since only vector chunks from search will be used as sources in those cases.",
    },
    "toast-success": "PDR settings updated",
    "toast-fail": "Failed to update PDR settings",
    "adjacent-vector-limit": "Adjacent Vector Limit",
    "adjacent-vector-limit-desc": "Limit for adjacent vectors.",
    "adjacent-vector-limit-placeholder": "Enter adjacent vector limit",
    "keep-pdr-vectors": "Keep PDR Vectors",
    "keep-pdr-vectors-desc": "Option to keep PDR vectors.",
  },

  // =========================
  // VALIDATE RESPONSE
  // =========================
  "validate-response": {
    title: "Validation Result",
    "toast-fail": "Could not validate response",
    validating: "Validating response",
    button: "Validate response",
    "adjust-prefix":
      "Make all indicated changes to the response based on this feedback: ",
    "adjust-button": "Apply suggested changes",
  },

  // =========================
  // WORKSPACE NAMES (LEGAL AREAS)
  // =========================
  "workspace-names": {
    "Administrative Law": "Administrative Law",
    "Business Law": "Business Law",
    "Civil Law": "Civil Law",
    "Criminal Law": "Criminal Law",
    "Diplomatic Law": "Diplomatic Law",
    "Fundamental Law": "Fundamental Law",
    "Human Rights Law": "Human Rights Law",
    "Judicial Laws": "Judicial Laws",
    "Security Laws": "Security Laws",
    "Taxation Laws": "Taxation Laws",
  },

  // =========================
  // VALIDATE ANSWER
  // =========================
  "validate-answer": {
    setting: "Validation LLM",
    title: "Validation LLM Preference",
    description:
      "These are the credentials and settings for your preferred validation LLM chat & embedding provider. Its important these keys are current and correct or else the system will not function properly.",
    "toast-success": "Validation LLM settings updated",
    "toast-fail": "Failed to update Validation LLM settings",
    saving: "Saving...",
    "save-changes": "Save changes",
  },

  // =========================
  // ACTIVE CASE
  // =========================
  "active-case": {
    title: "Active Case Reference",
    placeholder: "Enter case reference number",
    "select-reference": "Select Reference",
    "warning-title": "Missing Reference Number",
    "warning-message":
      "No reference number has been set. Do you want to proceed without a reference number?",
  },

  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Multi-user mode is permanently enabled for security reasons",
    "password-validation": {
      "restricted-chars":
        "Your password has restricted characters in it. Allowed symbols are _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "When enabled, any user can access the public workspaces without logging in.",
    },
    button: { saving: "Saving...", "save-changes": "Save changes" },
  },

  // =========================
  // ERRORS
  // =========================
  errors: {
    "fetch-models": "Failed to fetch custom models",
    "fetch-models-error": "Error fetching models",
    "upgrade-error": "Error during upgrade",
    "failed-process-file": "Failed to process file: {{text}}",
    "failed-process-attachment": "Failed to process attachment",
    "failed-extract-content": "Failed to extract content from {{fileName}}",
    "failed-process-content": "Failed to process file content",
    common: { error: "Error" },
    workspace: {
      "already-exists": "A workspace with this name already exists",
    },
    auth: {
      "invalid-credentials": "Invalid login credentials.",
      "account-suspended": "Account suspended by admin.",
      "invalid-password": "Invalid password provided",
    },
    env: {
      "anthropic-key-format":
        "Invalid Anthropic API key format. The key must start with 'sk-ant-'",
      "openai-key-format": "OpenAI API key must start with 'sk-'",
      "jina-key-format": "Jina API key must start with 'jina_'",
    },
  },

  // =========================
  // LOADING STATES
  // =========================
  loading: {
    models: "-- loading available models --",
    "waiting-url": "-- waiting for URL --",
    "waiting-api-key": "-- waiting for API key --",
    "waiting-models": "-- waiting for models --",
  },

  // =========================
  // CHARTS
  // =========================
  charts: {
    downloading: "Downloading image...",
    download: "Download graph image",
  },

  // =========================
  // MODALS
  // =========================
  modals: {
    warning: {
      title: "Warning",
      proceed: "Are you sure you want to proceed?",
      cancel: "Cancel",
      confirm: "Confirm",
      "got-it": "Okay, got it",
    },
  },

  // =========================
  // DOCUMENTS & PINNING
  // =========================
  documents: {
    "pin-info-button": "About pinning",
    "pin-title": "What is document pinning?",
    "pin-desc-1":
      "When you pin a document the platform we will inject the entire content of the document into your prompt window for your LLM to fully comprehend.",
    "pin-desc-2":
      "This works best with large-context models or small files that are critical to its knowledge-base.",
    "pin-desc-3":
      "If you are not getting the answers you desire by default then pinning is a great way to get higher quality answers in a click.",
    "pin-add": "Pin to workspace",
    "pin-unpin": "Un-Pin from workspace",
    "watch-title": "What does watching a document do?",
    "watch-desc-1":
      "When you watch a document we will automatically sync your document content from its original source on regular intervals. This will automatically update the content in every workspace where this file is managed.",
    "watch-desc-2":
      "This feature currently supports online-based content and will not be available for manually uploaded documents.",
    "watch-desc-3": "You can manage what documents are watched from the",
    "file-manager": "File manager",
    "admin-view": "admin view",
    "pdr-add": "Added all documents to Parent Document Retrieval",
    "pdr-remove": "Removed all documents from Parent Document Retrieval",
    empty: "No documents found",
    tooltip: { date: "Date: ", type: "Type: ", cached: "Cached" },
    actions: { removing: "Removing file from workspace" },
    costs: { estimate: "Estimated Cost: $", minimum: "< $0.01" },
    "new-folder": {
      title: "Create New Folder",
      "name-label": "Folder Name",
      "name-placeholder": "Enter folder name",
      create: "Create Folder",
    },
    error: { "create-folder": "Failed to create folder" },
  },

  // =========================
  // LEGAL QUESTION
  // =========================
  "legal-question": {
    "category-one": "Category One",
    "category-two": "Category Two",
    "category-three": "Category Three",
    "category-four": "Category Four",
    "category-five": "Category Five",
    "item-one": "Items One",
    "item-two": "Items Two",
    "item-three": "Items Three",
    "item-four": "Items Four",
    "item-five": "Items Five",
    "item-six": "Items Six",
    "item-seven": "Items Seven",
    "example-title": "Eat Well: A Guide to Enjoying Food",
    example: {
      breakfast: {
        title: "1. Healthy Breakfast Options",
        items: [
          "Oatmeal with fresh fruits and honey",
          "Greek yogurt parfait with granola",
          "Avocado toast with poached eggs",
          "Green smoothie packed with spinach, banana, and almond milk",
          "Whole-grain pancakes with maple syrup",
        ],
      },
      lunch: {
        title: "2. Quick and Easy Lunch Ideas",
        items: [
          "Grilled chicken wrap with mixed greens",
          "Quinoa salad with roasted vegetables",
          "Turkey sandwich on whole-grain bread",
          "Vegetable stir-fry with brown rice",
          "Soup and side salad combo",
        ],
      },
      dinner: {
        title: "3. Delicious Dinner Recipes",
        items: [
          "Baked salmon with lemon and asparagus",
          "Spaghetti with marinara sauce and meatballs",
          "Vegetable curry served with basmati rice",
          "Grilled steak with roasted potatoes",
          "Stuffed bell peppers with quinoa and cheese",
        ],
      },
    },
  },

  // =========================
  // PRESETS
  // =========================
  presets: {
    "edit-title": "Edit Standard Prompt",
    description: "Description of the prompt",
    "description-placeholder": "Makes a summary of the attached files.",
    deleting: "Deleting...",
    "delete-preset": "Delete Standard Prompt",
    cancel: "Cancel",
    save: "Save",
    "add-title": "Add Standard Prompt",
    "command-label": "Name of the prompt, one single word",
    "command-placeholder": "Summary",
    "command-desc":
      "The name is also the chatbox shortcut, starting with /, to use this prompt without pressing buttons.",
    "prompt-label": "Prompt",
    "prompt-placeholder": "Produce a summary of the files I have attached.",
    "prompt-desc":
      "The prompt that will be sent when this prompt preset is used.",
    "tooltip-add": "Add New Standard Prompt",
    "tooltip-hover": "View your own standard prompts.",
    "confirm-delete": "Confirm deleting standard prompt preset.",
  },

  // =========================
  // LEGAL CATEGORIES
  // =========================
  "legal-categories": {
    process: "Process",
    "process-stamning": "Statement of Claim",
    "process-svaromal": "Statement of Defence",
    "process-yrkanden": "Claims and Presentation",
    avtal: "Agreements",
    "avtal-anstallning": "Employment Agreements",
    "avtal-finansiering": "Financing and Security Agreements",
    "avtal-licens": "License Agreements",
    "due-diligence": "Due Diligence",
    "due-diligence-avtal": "Contract Review",
    "due-diligence-checklista": "Due Diligence Checklist",
    "due-diligence-compliance": "Compliance Check",
  },

  // =========================
  // VALIDATION
  // =========================
  validation: {
    responseHeader: "Here is the Response that was generated",
    contextHeader: "Original Context and Sources",
  },

  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================
  "document-builder": {
    title: "Document Builder Prompts",
    description:
      "Customize the default prompts used by the Document Builder feature.",
    "override-prompt-placeholder":
      "Enter prompt to override the default system prompt",
    saving: "Saving...",
    save: "Save Prompt Settings",
    "toast-success": "Document builder prompts saved successfully.",
    "toast-fail": "Failed to save document builder prompts.",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Document Summary Prompts",
          description:
            "Configure system and user prompts for Document Summary.",
        },
        document_relevance: {
          title: "Document Relevance Prompts",
          description:
            "Configure system and user prompts for Document Relevance.",
        },
        section_drafting: {
          title: "Section Drafting Prompts",
          description:
            "Configure system and user prompts for Section Drafting.",
        },
        section_legal_issues: {
          title: "Section Legal Issues Prompts",
          description:
            "Configure system and user prompts for Section Legal Issues.",
        },
        memo_creation: {
          title: "Memo Creation Prompts",
          description: "Configure prompts for Memo Creation.",
        },
        section_index: {
          title: "Section Index Prompts",
          description: "Configure prompts for Section Index.",
        },
        select_main_document: {
          title: "Select Main Document Prompts",
          description:
            "Configure system and user prompts for Select Main Document.",
        },
        section_list_from_main: {
          title: "Section List From Main Prompts",
          description:
            "Configure system and user prompts for Section List From Main.",
        },
        section_list_from_summaries: {
          title: "Section List From Summaries Prompts",
          description:
            "Configure system and user prompts for Section List From Summaries.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Document Summary (System)",
      "document-summary-system-description":
        "System prompt for instructing the AI on how to summarize a document's content and relevance to a legal task.",
      "document-summary-user-label": "Document Summary (User)",
      "document-summary-user-description":
        "User prompt template for generating a detailed summary of document content in relation to a specific legal task.",

      // Document Relevance
      "document-relevance-system-label": "Document Relevance (System)",
      "document-relevance-system-description":
        "System prompt for evaluating if a document is relevant to a legal task, expecting a true/false answer.",
      "document-relevance-user-label": "Document Relevance (User)",
      "document-relevance-user-description":
        "User prompt template to check if document content is relevant for a given legal task.",

      // Section Drafting
      "section-drafting-system-label": "Section Drafting (System)",
      "section-drafting-system-description":
        "System prompt for generating a single document section in a professional legal style, using specified documents and context.",
      "section-drafting-user-label": "Section Drafting (User)",
      "section-drafting-user-description":
        "User prompt template to generate a specific section of a legal document, considering title, task, source documents, and neighboring sections.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Section Legal Issues Identification (System)",
      "section-legal-issues-system-description":
        "System prompt for identifying specific legal topics for which factual information should be retrieved to support drafting a document section.",
      "section-legal-issues-user-label":
        "Section Legal Issues Identification (User)",
      "section-legal-issues-user-description":
        "User prompt template to list legal topics or data points for fetching background information relevant to a specific document section and legal task.",

      // Memo Creation
      "memo-creation-template-label": "Default Memo Creation Template",
      "memo-creation-template-description":
        "Prompt template for creating a legal memorandum addressing a specific legal issue, considering provided documents and task context.",

      // Section Index
      "section-index-system-label": "Section Index (System)",
      "section-index-system-description":
        "System prompt for generating a structured index of sections for a legal document.",

      // Select Main Document
      "select-main-document-system-label": "Select Main Document (System)",
      "select-main-document-system-description":
        "System prompt for identifying the most relevant main document for a legal task from multiple document summaries.",
      "select-main-document-user-label": "Select Main Document (User)",
      "select-main-document-user-description":
        "User prompt template for identifying the main document for a legal task given summaries of multiple documents.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Section List from Main Document (System)",
      "section-list-from-main-system-description":
        "System prompt for drafting a JSON-structured list of sections for a legal document based on main document content and the legal task.",
      "section-list-from-main-user-label":
        "Section List from Main Document (User)",
      "section-list-from-main-user-description":
        "User prompt template for providing the legal task and main document content to generate a section list.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Section List from Summaries (System)",
      "section-list-from-summaries-system-description":
        "System prompt for drafting a JSON-structured list of sections based on document summaries and the legal task when no main document exists.",
      "section-list-from-summaries-user-label":
        "Section List from Summaries (User)",
      "section-list-from-summaries-user-description":
        "User prompt template for providing the legal task and document summaries to generate a section list when no main document exists.",
    },

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================

    "view-categories": "View all categories",
    "hide-categories": "Hide list",
    "add-task": "Add legal task",
    loading: "Loading...",
    table: {
      title: "Legal Tasks",
      name: "Name",
      "sub-category": "Sub-Category",
      description: "Description",
      prompt: "Legal Task Prompt",
      actions: "Actions",
      delete: "Delete",
      "delete-confirm": "Are you sure you want to delete this category?",
      "delete-success": "Category deleted",
      "delete-error": "Failed to delete category",
    },
    "create-task-title": "Create a legal task",
    "category-name": "Category Name",
    "category-name-desc": "Enter the name of the main category.",
    "category-name-placeholder": "Enter category name",
    "subcategory-name": "Sub-Category Name",
    "subcategory-name-desc": "Enter the name of the sub-category.",
    "subcategory-name-placeholder": "Enter sub-category name",
    "description-desc": "Enter a description of the category and sub-category.",
    "description-placeholder": "Enter a short description",
    submitting: "Submitting...",
    submit: "Submit",
    validation: {
      "category-required": "Category Name is required.",
      "subcategory-required": "Sub-Category Name is required.",
      "description-required": "Description is required.",
      "prompt-required": "Legal Task Prompt is required.",
    },
    "create-task": {
      title: "Create a legal task",
      category: {
        name: "Category Name",
        desc: "Enter the name of the category.",
        placeholder: "Enter category name",
        type: "Category Type",
        new: "Create new category",
        existing: "Use existing category",
        select: "Select category",
        "select-placeholder": "Select an existing category",
      },
      subcategory: {
        name: "Sub-Category Name",
        desc: "Enter the name of the sub-category.",
        placeholder: "Enter sub-category name",
      },
      description: {
        name: "Description and user instructions",
        desc: "Information and instructions that the user will see.",
        placeholder:
          "Describe the type of documents that need to be uploaded to the workspace to achieve the best possible result",
      },
      prompt: {
        name: "Legal Task Prompt",
        desc: "Enter the prompt that will be used for this legal task. You can also upload example documents with the buttons to add content examples to your prompt.",
        placeholder:
          "Enter legal task prompt or upload example documents to improve your prompt...",
      },
      submitting: "Submitting...",
      submit: "Submit",
      validation: {
        "category-required": "Category Name is required.",
        "subcategory-required": "Sub-Category Name is required.",
        "description-required": "Description is required.",
        "prompt-required": "Legal Task Prompt is required.",
      },
    },
    "edit-task": {
      title: "Edit legal task",
      submitting: "Updating...",
      submit: "Update task",
      subcategory: {
        name: "Sub-Category Name",
        desc: "Enter a new name for this legal task",
        placeholder: "Enter legal task...",
      },
      description: {
        name: "Description and user instructions",
        desc: "Enter a description and user instructions for this legal task",
        placeholder: "Enter description and user instructions...",
      },
      prompt: {
        name: "Legal Task Prompt",
        desc: "Enter the prompt that will be used for this legal task. You can also upload example documents with the buttons to add content examples to your prompt.",
        placeholder:
          "Enter legal task prompt or upload example documents to improve your prompt...",
      },
      validation: {
        "subcategory-required": "Legal Task Name is required",
        "description-required": "Description is required",
        "prompt-required": "Legal Task Prompt is required",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Main Document Selection Required",
      "requires-main-doc-description":
        "If marked, the user must select the main document from the uploaded files when this task is performed. This is strongly recommended for legal tasks that involve responding to a letter or a court order or similar, as it structures the result based on the document being answered.",
      "requires-main-doc-placeholder": "Yes or No",
      "requires-main-doc-explanation-default":
        "A selection is required because this determines how the document will be built.",
      "requires-main-doc-explanation-yes":
        "If 'Yes' the user will need to select a main document when this legal task is started. This document will be central to the task's workflow.",
      "requires-main-doc-explanation-no":
        "If 'No' the legal task will continue without requiring a default main document. The task will create a result more dynamically based on all uploaded documents and the legal task.",
    },

    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Review Generator Prompt",
    reviewGeneratorPromptButtonTooltip:
      "View the exact prompt template used to generate the legal task suggestion. (Admin only)",
    reviewGeneratorPromptTitle: "Generator Prompt Review",
    reviewPromptLabel: "The following prompt was used for generation:",
    reviewPromptTextareaLabel: "Generator Prompt Content",
  },

  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Register Rexor Project",
    "project-id": "Project ID",
    "resource-id": "Resource ID",
    "activity-id": "Activity ID",
    register: "Register Project",
    "invoice-text": "Foynet number of lookups",
    registering: "registering ...",
    "not-active": "This case is not active to register",
    account: {
      title: "Login to Rexor",
      username: "Username",
      password: "Password",
      "no-token": "No token received in handleLoginSuccess",
      logout: "Logout",
      "no-user": "Please loggin first",
      connected: "Connected to Rexor",
      "not-connected": "Not connected",
      "change-account": "Logout/Change Rexor account",
      "session-expired": "Session expired. Please log in again.",
    },
    "hide-article-transaction": "Hide Article Transaction Form",
    "show-article-transaction": "Show Article Transaction Form",
    "article-transaction-title": "Add Article Transaction",
    "registration-date": "Registration Date",
    description: "Description",
    "description-internal": "Internal Description",
    "hours-worked": "Hours Worked",
    "invoiced-hours": "Invoiced Hours",
    invoiceable: "Invoiceable",
    "sending-article-transaction": "Sending Article Transaction...",
    "save-article-transaction": "Save Article Transaction",
    "project-not-register": "Project must be registered first.",
    "article-transaction-error": "Failed to write article transaction",
    "not-exist": "This case could not be found",
  },

  // =========================
  // OPTIONS
  // =========================
  options: { yes: "Yes", no: "No" },

  // =========================
  // LEGAL TEMPLATES MODAL
  // =========================

  //moved to legalTemplates

  // =========================
  // CUSTOM LEGAL TEMPLATES MODAL
  // =========================

  // moved to customLegalTemplates.js

  // =========================
  // TEXTGENWEBUI COMPONENT
  // =========================
  textgenwebui: {
    "base-url": "Base URL",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "token-window": "Token context window",
    "token-window-placeholder": "Content window limit (eg: 4096)",
    "api-key": "API Key (Optional)",
    "api-key-placeholder": "TextGen Web UI API Key",
    "max-tokens": "Max Tokens",
    "max-tokens-placeholder": "Max tokens per request (eg: 1024)",
  },

  // =========================
  // TOGETHERAI LLM SELECTION
  // =========================
  togetherai: {
    "api-key": "TogetherAI API Key",
    "api-key-placeholder": "Enter your TogetherAI API Key",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
  },

  // =========================
  // XAI LLM OPTIONS
  // =========================

  // =========================
  // JINA EMBEDDING
  // =========================
  jina: {
    "api-key": "Jina API Key",
    "api-key-placeholder": "Enter your Jina API Key",
    "api-key-format": "Jina API key must start with 'jina_'",
    "model-preference": "Model Preference",
  },

  // =========================
  // OLLAMA EMBEDDING
  // =========================
  ollama: { "max-embedding-chunk-length": "Max Embedding Chunk Length" },

  // =========================
  // VOYAGEAI EMBEDDING
  // =========================
  voyageai: {
    "api-key": "VoyageAI API Key",
    "api-key-placeholder": "Enter your VoyageAI API Key",
    "model-preference": "Model Preference",
  },

  // =========================
  // METRICS VISIBILITY
  // =========================
  "metrics.visibility.hover": "Metrics are visible.",
  "metrics.visibility.available": "Metrics are available.",

  // =========================
  // PROMPT ERRORS
  // =========================
  prompt: {
    error: {
      empty: "Prompt cannot be empty",
      upgrade: "Error upgrading prompt",
    },
    decline: "Decline",
  },

  // =========================
  // AGENT MENU
  // =========================
  "agent-menu.default-agent": "Default Agent",
  "agent-menu.ability.rag-search": "RAG Search",
  "agent-menu.ability.web-scraping": "Web Scraping",
  "agent-menu.ability.web-browsing": "Web Browsing",
  "agent-menu.ability.save-file-to-browser": "Save File to Browser",
  "agent-menu.ability.list-documents": "List Documents",
  "agent-menu.ability.summarize-document": "Summarize Document",
  "agent-menu.ability.chart-generation": "Chart Generation",

  // =========================
  // PIPER TTS OPTIONS
  // =========================
  piperTTS: {
    description:
      "All PiperTTS models will run in your browser locally. This can be resource intensive on lower-end devices.",
    "voice-model": "Voice Model Selection",
    "loading-models": "-- loading available models --",
    "stored-indicator":
      'The "✔" indicates this model is already stored locally and does not need to be downloaded when run.',
    "flush-cache": "Flush voice cache",
    "flush-success": "All voices flushed from browser storage",
    demo: {
      stop: "Stop demo",
      loading: "Loading voice",
      play: "Play sample",
      text: "Hello, welcome to IST Legal!",
    },
  },

  // =========================
  // ADMIN AGENTS
  // =========================
  agents: {
    title: "Agent Skills",
    "agent-skills": "Configure and manage agent capabilities",
    "custom-skills": "Custom Skills",
    back: "Back",
    "select-skill": "Select a skill to configure",
    "preferences-saved": "Agent preferences saved successfully.",
    "preferences-failed": "Agent preferences failed to save.",
    "skill-status": { on: "On", off: "Off" },
    "skill-config-updated": "Skill config updated successfully",
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chatSettings: {
    placeholder: {
      drafting:
        "Given the following conversation, relevant context, and a follow up question, reply with an answer to the current question the user is asking. Return only your response to the question given the above information following the users instructions as needed.",
      "legal-questions":
        "Which legal questions arise from the given context with the prompt",
      "legal-memo": "Provide a memo about each of these legal issues",
    },
  },

  // =========================
  // LOGGING
  // =========================
  logging: { show: "show", hide: "hide", "event-metadata": "Event Metadata" },

  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Show Code",
    enable: "Enable",
    disable: "Disable",
    "all-domains": "all",
    "disable-confirm":
      "Are you sure you want to disabled this embed?\nOnce disabled the embed will no longer respond to any chat requests.",
    "delete-confirm":
      "Are you sure you want to delete this embed?\nOnce deleted this embed will no longer respond to chats or be active.\n\nThis action is irreversible.",
    "disabled-toast": "Embed has been disabled",
    "enabled-toast": "Embed is active",
  },

  badges: {
    default: {
      text: "Default",
      tooltip: "This skill is enabled by default and cannot be turned off.",
    },
  },

  // =========================
  // DOCX EDITOR
  // =========================
  "docx-edit": {
    "edit-instructions":
      "Enter instructions for how you want to edit the document. Be specific about what changes you want to make.",
    "instructions-placeholder":
      "e.g., Fix grammatical errors, make the tone more formal, add a conclusion paragraph...",
    "process-button": "Process Document",
    "upload-docx": "Upload DOCX",
    "processing-upload": "Processing...",
    "content-extracted": "Content extracted from DOCX file",
    "file-type-note": "Only .docx files are supported",
    "upload-error": "Error uploading file: ",
    "no-instructions": "Please enter editing instructions",
    "process-error": "Error processing document: ",
    "changes-highlighted": "Document with highlighted changes",
    "download-button": "Download Document",
    "start-over-button": "Start Over",
    "no-document": "No document available for download",
    "download-error": "Error downloading document: ",
    "download-success": "Document downloaded successfully",
    processing: "Processing document...",
    "instructions-used": "Instructions Used",
    "import-success": "DOCX content imported successfully",
    "edit-success": "DOCX content updated successfully",
    "canvas-document-title": "Canvas Document",
    "upload-button": "Upload DOCX",
    "download-as-docx": "Download as DOCX",
    "output-example": "Output example",
    "output-example-desc":
      "Upload a DOCX file to add example output content to your prompt",
    "content-examples-tag-open": "<CONTENT_EXAMPLE>",
    "content-examples-tag-close": "</CONTENT_EXAMPLE>",
    "content-examples-info":
      "<INFO>This is an example of the content to be produced, from a similar legal task. Note that this example content can be much shorter or longer than the content that shall now be produced.</INFO>",
    "contains-example-content": "[Contains example content]",
  },

  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "{{provider}} logo",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "LM Studio Embedding Model",
      "max-chunk-length": "Maximum Chunk Length",
      "max-chunk-length-help": "Maximum length of text chunks for embedding.",
      "hide-endpoint": "Hide Manual Endpoint Input",
      "show-endpoint": "Show Manual Endpoint Input",
      "base-url": "LM Studio Base URL",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Enter the URL where LM Studio is running.",
      "auto-detect": "Auto-Detect",
      "loading-models": "--loading available models--",
      "enter-url-first": "Enter LM Studio URL first",
      "model-help":
        "Select the LM Studio model for embeddings. Models will load after entering a valid LM Studio URL.",
      "loaded-models": "Your loaded models",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Ollama Embedding Model",
      "max-chunk-length": "Maximum Chunk Length",
      "max-chunk-length-help": "Maximum length of text chunks for embedding.",
      "hide-endpoint": "Hide Manual Endpoint Input",
      "show-endpoint": "Show Manual Endpoint Input",
      "base-url": "Ollama Base URL",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Enter the URL where Ollama is running.",
      "auto-detect": "Auto-Detect",
      "loading-models": "--loading available models--",
      "enter-url-first": "Enter Ollama URL first",
      "model-help":
        "Select the Ollama model for embeddings. Models will load after entering a valid Ollama URL.",
      "loaded-models": "Your loaded models",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Embedding Model Selection",
      "max-chunk-length": "Maximum Chunk Length",
      "max-chunk-length-help": "Maximum length of text chunks for embedding.",
      "api-key": "API Key",
      optional: "optional",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- loading available models --",
      "waiting-url": "-- waiting for URL --",
      "loaded-models": "Your loaded models",
      "model-tooltip": "View supported embedding models at",
      "model-tooltip-link": "LiteLLM's documentation",
      "model-tooltip-more": "for more information about available models.",
    },
    // Cohere Embedding Options
    cohere: {
      "api-key": "Cohere API Key",
      "api-key-placeholder": "Enter your Cohere API Key",
      "model-label": "Model Selection",
      "available-models": "Available embedding models",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Jina API Key",
      "api-key-placeholder": "Enter your Jina API Key",
      "api-key-error": "API key must start with 'jina_'",
      "model-label": "Model Selection",
      "available-models": "Available Embedding Models",
      "embedding-type": "Embedding Type",
      "available-types": "Available Embedding Types",
      dimensions: "Dimensions",
      "available-dimensions": "Available Dimensions",
      task: "Task",
      "available-tasks": "Available Tasks",
      "late-chunking": "Late Chunking",
      "late-chunking-help": "Enable late chunking for document processing",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Embedding Model Name",
      "hide-endpoint": "Hide advanced settings",
      "show-endpoint": "Show advanced settings",
      "base-url": "LocalAI Base URL",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Enter the URL where LocalAI is running.",
      "auto-detect": "Auto-Detect",
      "loading-models": "-- loading available models --",
      "waiting-url": "-- waiting for URL --",
      "loaded-models": "Your loaded models",
    },
    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "Base URL",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Enter the base URL for your OpenAI-compatible API endpoint.",
      "model-label": "Embedding Model",
      "model-placeholder": "Enter the model name (e.g. text-embedding-ada-002)",
      "model-help": "Specify the model identifier for generating embeddings.",
      "api-key": "API Key",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Enter your API key for authentication.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "OpenAI API Key",
      "api-key-placeholder": "Enter your OpenAI API Key",
      "model-label": "Model Selection",
      "available-models": "Available embedding models",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "VoyageAI API Key",
      "api-key-placeholder": "Enter your VoyageAI API Key",
      "model-label": "Model Selection",
      "available-models": "Available embedding models",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Azure OpenAI Service Endpoint",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help": "Enter your Azure OpenAI service endpoint URL",
      "api-key": "Azure OpenAI API Key",
      "api-key-placeholder": "Enter your Azure OpenAI API Key",
      "api-key-help": "Enter your Azure OpenAI API key for authentication",
      "deployment-name": "Embedding Model Deployment Name",
      "deployment-name-placeholder":
        "Enter your Azure OpenAI embedding model deployment name",
      "deployment-name-help":
        "The deployment name for your Azure OpenAI embedding model",
    },
    // Native Embedding Options
    native: {
      description: "Using native embedding provider for text processing",
    },
  },

  "appearance.siteSettings.tabIcon": "Tab Icon",
  "appearance.siteSettings.fabIconUrl": "Favicon URL",
  "appearance.siteSettings.placeholder": "Enter favicon URL",
  "appearance.siteSettings.title-placeholder": "Enter site title",
  "appearance.welcome.heading": "Welcome to IST Legal",
  "appearance.welcome.text": "Select legal area on the left",

  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "API Keys",
    description: "Manage API keys for connecting to this instance.",
    "generate-key": "Generate New API Key",
    "table-headers": {
      "connection-string": "Connection String",
      "created-by": "Created By",
      "created-at": "Created At",
      actions: "Actions",
    },
    "no-keys": "No API keys found",
    modal: {
      title: "New Browser Extension API Key",
      "multi-user-warning":
        "Warning: You are in multi-user mode, this API key will allow access to all workspaces associated with your account. Please share it cautiously.",
      "create-description":
        'After clicking "Create API Key", this instance will attempt to create a new API key for you to use with the browser extension.',
      "connection-help":
        'If you see "Connected to IST Legal" in the extension, the connection was successful. If not, please copy the connection string and paste it into the extension manually.',
      cancel: "Cancel",
      "create-key": "Create API Key",
      "copy-key": "Copy API Key",
      "key-copied": "API Key Copied!",
    },
    tooltips: {
      "copy-connection": "Copy connection string",
      "auto-connect": "Automatically connect to extension",
    },
    confirm: {
      revoke:
        "Are you sure you want to revoke this browser extension API key?\nAfter you do this it will no longer be useable.\n\nThis action is irreversible.",
    },
    toasts: {
      "key-revoked": "Browser Extension API Key permanently revoked",
      "revoke-failed": "Failed to revoke API Key",
      copied: "Connection string copied to clipboard",
      connecting: "Attempting to connect to browser extension...",
    },
    "revoke-title": "Revoke Browser Extension API Key",
    "revoke-message":
      "Are you sure you want to revoke this browser extension API key?\nAfter you do this it will no longer be useable.\n\nThis action is irreversible.",
  },

  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Experimental Features",
    description: "Features that are currently in beta testing phase",
    "live-sync": {
      title: "Live Document Sync",
      description:
        "Enable automatic content synchronization from external sources",
      "manage-title": "Watched documents",
      "manage-description":
        "These are all the documents that are currently being watched in your instance. The content of these documents will be periodically synced.",
      "document-name": "Document Name",
      "last-synced": "Last Synced",
      "next-refresh": "Time until next refresh",
      "created-on": "Created On",
      "auto-sync": "Automatic Content Sync",
      "sync-description":
        'Enable the ability to specify a content source to be "watched". Watched content will be regularly fetched and updated in this instance.',
      "sync-workspace-note":
        "Watched content will automatically update in all workspaces they are referenced in at the same time of update.",
      "sync-limitation":
        "This feature only applies to web-based content, such as websites, Confluence, YouTube, and GitHub files.",
      documentation: "Feature Documentation and Warnings",
      "manage-content": "Manage Watched Content",
    },
    tos: {
      title: "Terms of use for experimental features",
      description:
        "Experimental features of this platform are features that we are piloting and are opt-in. We proactively will condition or warn you on any potential concerns should any exist prior to approval of any feature.",
      "possibilities-title":
        "Use of any feature on this page can result in, but not limited to, the following possibilities.",
      possibilities: {
        "data-loss": "Loss of data.",
        "quality-change": "Change in quality of results.",
        "storage-increase": "Increased storage.",
        "resource-consumption": "Increased resource consumption.",
        "cost-increase":
          "Increased cost or use of any connected LLM or embedding provider.",
        "potential-bugs": "Potential bugs or issues using this application.",
      },
      "conditions-title":
        "Use of an experimental feature also comes with the following list of non-exhaustive conditions.",
      conditions: {
        "future-updates": "Feature may not exist in future updates.",
        stability: "The feature being used is not currently stable.",
        availability:
          "The feature may not be available in future versions, configurations, or subscriptions of this instance.",
        privacy:
          "Your privacy settings will be honored with use of any beta feature.",
        changes: "These conditions may change in future updates.",
      },
      "read-more": "If you would like to read more you can refer to",
      contact: "or email",
      reject: "Reject & Close",
      accept: "I understand",
    },
    "update-failed": "Failed to update status of feature",
  },
  "cdb-llm-preference": {
    title: "CDB LLM Preference",
    settings: "CDB LLM",
    description: "Configure the LLM provider for CDB",
  },
  "template-llm-preference": {
    title: "Template LLM Preference",
    settings: "Template LLM",
    description:
      "Select the LLM provider used when generating document templates. Defaults to the system provider if unset.",
    "toast-success": "Template LLM settings updated",
    "toast-fail": "Failed to update Template LLM settings",
    saving: "Saving...",
    "save-changes": "Save changes",
  },

  // =========================
  // PROMPT LOGGING
  // =========================
  promptLogging: {
    title: "Prompt output logging",
    description:
      "Enable or disable logging of prompt outputs for system monitoring.",
    label: "Prompt output logging: ",
    state: { enabled: "Enabled", disabled: "Disabled" },
  },

  // =========================
  // USER ACCESS
  // =========================
  userAccess: {
    title: "Allow User Access",
    description:
      "Enable to allow regular users to access legal tasks. By default, only superusers, managers and admins have access.",
    label: "User Access: ",
    state: { enabled: "Enabled", disabled: "Disabled" },
  },
  // =========================
  // CUSTOM USER AI
  // =========================
  "custom-user-ai": {
    title: "Custom User AI",
    settings: "Custom User AI",
    description: "Configure the Custom User AI Provider",
    "custom-model-reference": "Custom Model Name & Description",
    "custom-model-reference-description":
      "Add a custom reference for this model. This will be visible when using the Custom User AI engine selector in the prompt panel.",
    "custom-model-reference-name": "Custom Model Name",
    "custom-model-reference-description-label": "Model Description (Optional)",
    "custom-model-reference-description-placeholder":
      "Enter an optional description for this model",
    "custom-model-reference-name-placeholder":
      "Enter a custom name for this model",
    "model-ref-placeholder":
      "Enter a custom name or description for this model setup",
    "enter-custom-model-reference": "Enter a custom name for this model",
    "standard-engine": "Standard AI Engine",
    "standard-engine-description": "Our default engine useful for most tasks",
    "dynamic-context-window-percentage": "Dynamic Context Window Percentage",
    "dynamic-context-window-percentage-desc":
      "Controls how much of this LLM's context window can be used for additional sources (10-100%)",
    "no-alternative-title": "No Alternative Model Selected",
    "no-alternative-desc":
      "When this option is selected, users do not have the ability to select an alternative model.",
    "select-option": "Select Custom AI Profile",
    tab: {
      "custom-1": "Custom Engine 1",
      "custom-2": "Custom Engine 2",
      "custom-3": "Custom Engine 3",
      "custom-4": "Custom Engine 4",
      "custom-5": "Custom Engine 5",
      "custom-6": "Custom Engine 6",
    },
    engine: {
      "custom-1": "Custom Engine 1",
      "custom-2": "Custom Engine 2",
      "custom-3": "Custom Engine 3",
      "custom-4": "Custom Engine 4",
      "custom-5": "Custom Engine 5",
      "custom-6": "Custom Engine 6",
      "custom-1-title": "Custom Engine 1",
      "custom-2-title": "Custom Engine 2",
      "custom-3-title": "Custom Engine 3",
      "custom-4-title": "Custom Engine 4",
      "custom-5-title": "Custom Engine 5",
      "custom-6-title": "Custom Engine 6",
      "custom-1-description": "Configure settings for Custom Engine 1",
      "custom-2-description": "Configure settings for Custom Engine 2",
      "custom-3-description": "Configure settings for Custom Engine 3",
      "custom-4-description": "Configure settings for Custom Engine 4",
      "custom-5-description": "Configure settings for Custom Engine 5",
      "custom-6-description": "Configure settings for Custom Engine 6",
    },
    "option-number": "Option {{number}}",
    "llm-provider-selection": "LLM Provider Selection",
    "llm-provider-selection-desc":
      "Choose the LLM provider for this custom AI configuration",
    "custom-option": "Custom Option",
    saving: "Saving...",
    "save-changes": "Save Changes",
    "model-ref-saved": "Custom model settings saved successfully",
    "model-ref-save-failed": "Failed to save custom model settings: {{error}}",
    "llm-settings-save-failed": "Failed to save LLM settings: {{error}}",
    "settings-fetch-failed": "Failed to fetch settings",
    "llm-saved": "LLM settings saved successfully",
    "select-provider-first":
      "Please select an LLM provider to configure model settings. Once configured, this option will be selectable as a custom AI engine in the user interface.",
  },

  // =========================
  // CHAT LOGS & PREVIEW
  // =========================
  chat_logs: {
    display_description:
      "Display the raw data logging, open or download the file",
    display_prompt_output: "Display raw data",
    loading_prompt_output: "Loading raw data...",
    not_available: "*** Raw data is not available for this chat.",
    token_count: "Tokens (in all raw data): {{count}}",
    token_count_detailed:
      "Tokens to LLM: {{promptTokens}} | Tokens in LLM response: {{completionTokens}} | Total tokens: {{totalTokens}}",
  },

  // LLM Provider specific translations
  "llm-provider.textgenwebui": "Connect to a Text Generation WebUI instance.",
  "llm-provider.litellm": "Connect to any LLM via LiteLLM.",
  "llm-provider.openai-generic":
    "Connect to any OpenAI-compatible API endpoint.",
  "llm-provider.system-default": "Use the built-in Native model.",
  // =========================
  // INVITATION RELATED KEYS
  // =========================
  invite: {
    "accept-button": "Accept Invitation",
    newUser: {
      title: "Create a new account",
      usernameLabel: "Username",
      passwordLabel: "Password",
      description:
        "After creating your account you will be able to login with these credentials and start using workspaces.",
    },
  },

  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Context Window",
    "max-output-tokens": "Max Output Tokens",
    "output-limit": "Output Limit",
    tokens: "tokens",
    "fallback-value": "Fallback value used",
  },

  // =========================
  // VARIOUS MODALS AND NEW FEATURES
  // =========================

  // Organization & Organizations translations for user management
  organization: {
    label: "Organization",
    select: "-- Select Organization --",
    none: "None",
    "create-new": "+ Create New Organization",
    "new-name": "New Organization Name",
    "new-name-ph": "Enter new organization name",
  },
  organizations: {
    "fetch-error": "Failed to fetch organizations",
  },
  "request-legal-assistance": {
    title: "Request legal assistance",
    description:
      "Configure visibility of button for requesting legal assistance.",
    enable: "Enable Request Legal Assistance",
    "law-firm-name": "Law Firm Name",
    "law-firm-placeholder": "Enter law firm name",
    "law-firm-help":
      "Name of the law firm that will handle legal assistance requests",
    email: "Legal Assistance Email",
    "email-placeholder": "Enter legal assistance email address",
    "email-help": "Email address where legal assistance requests will be sent",
    "settings-saved": "Legal assistance settings saved successfully",
    "save-error": "Failed to save legal assistance settings",
    status: "Legal Assistance Button: ",
    "load-error": "Failed to load legal assistance settings",
    "save-button": "Save changes",
    request: {
      title: "Request legal assistance",
      description:
        "Send a request to {{lawFirmName}} for legal assistance, finishing research or other consultations. You will be notified by email when the request is processed.",
      button: "Request legal assistance",
      message: "Message",
      "message-placeholder":
        "Enter any specific instructions or information for the legal assistance team",
      send: "Send request",
      cancel: "Cancel",
      error: "Failed to submit legal assistance request",
      success: "Legal assistance request submitted successfully",
      submitting: "Submitting request...",
      submit: "Submit request",
      partyName: "Party name",
      partyOrgId: "Organization ID",
      partyNameRequired: "Party name is required",
      partyOrgIdRequired: "Organization ID is required",
      partyNamePlaceholder: "Enter the name of your organization",
      partyOrgIdPlaceholder: "Enter your organization ID",
      opposingPartyName: "Opposing party name (if applicable)",
      opposingPartyOrgId: "Opposing party organization ID (if known)",
      opposingPartyNamePlaceholder: "Enter the name of the opposing party",
      opposingPartyOrgIdPlaceholder:
        "Enter the opposing party's organization ID",
    },
  },

  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Response Generation Progress",
    description:
      "Displays the real-time progress of tasks for finishing prompt, depending on linking with other workspaces and size of files. The modal will close automatically once all steps are complete.",
    step_fetching_memos: "Fetching legal data on current topics",
    step_processing_chunks: "Processing uploaded documents",
    step_combining_responses: "Finalize response",
    sub_step_chunk_label: "Processing document group {{index}}",
    sub_step_memo_label: "Fetched legal data from {{workspaceSlug}}",
    placeholder_sub_task: "Queued sub-task",
    desc_fetching_memos:
      "Retrieving relevant legal information from linked workspaces",
    desc_processing_chunks:
      "Analyzing and extracting information from document groups",
    desc_combining_responses:
      "Synthesizing information into a comprehensive response",
  },

  // =========================
  // MCP SERVER PAGE
  // =========================

  mcp: {
    title: "MCP Server Management",
    description:
      "Manage Multi-Component Processing (MCP) server configurations.",
    currentServers: "Current Servers",
    noServers: "No MCP servers configured.",
    fetchError: "Failed to fetch servers: {{error}}",
    addServerButton: "Add New Server",
    addServerModalTitle: "Add New MCP Server",
    addServerModalDesc:
      "Define the configuration for the new MCP server process.",
    serverName: "Server Name (Unique ID)",
    configJson: "Configuration (JSON)",
    addButton: "Add Server",
    addSuccess: "Server added successfully.",
    addError: "Failed to add server: {{error}}",
  },

  // =========================
  // ADMIN SYSTEM SETTINGS (UNIVERSITY MODE)
  // =========================
  admin: {
    system: {
      universityMode: {
        title: "University Mode",
        description:
          "When enabled, hides validation, prompt upgrade, template and web search tools for all users.",
        enable: "Enable University Mode",
        saved: "University Mode settings saved.",
        error: "Failed to save University Mode settings.",
        saveChanges: "Save University Mode Settings",
      },
    },
  },

  // Months
  "month.1": "Jan",
  "month.2": "Feb",
  "month.3": "Mar",
  "month.4": "Apr",
  "month.5": "May",
  "month.6": "Jun",
  "month.7": "Jul",
  "month.8": "Aug",
  "month.9": "Sep",
  "month.10": "Oct",
  "month.11": "Nov",
  "month.12": "Dec",

  // =========================
  // FEATURE CARDS
  // =========================
  featureCards: {
    "draft-from-template-title": "Create document draft from template",
    "draft-from-template-description":
      "Use the function to, for example, create an AML policy, minutes for a general meeting, or a standardized arbitration agreement.",
    "complex-document-builder-title": "Perform complex legal task",
    "complex-document-builder-description":
      "Perfect when you, for example, need to review hundreds of documents before a company acquisition or draft a detailed statement of claim.",
  },

  // =========================
  // WORKSPACE SELECTOR MODAL
  // =========================
  workspaceSelector: {
    chooseWorkspace: "Start a new chat",
    selectAiType:
      "Selection of module and workspace for initiating the feature",
    cloudAiDescription:
      "Uses a cloud-based AI model for chat and question answering. Your documents will be processed and stored securely in the cloud.",
    localAiDescription:
      "Uses a local AI model for chat and document drafting. Your documents will be processed and stored on your local machine.",
    cloudAiDescriptionTemplateFeature:
      "Create template in existing workspace with legal data, template may draw legal data depending on the query.",
    localAiDescriptionTemplateFeature:
      "Create template in own workspace, using entirely local AI if that is enabled on server.",
    cloudAiDescriptionComplexFeature:
      "Drafting of complex documents are not available for these workspaces, since the user must upload documents to the workspace before initiating",
    localAiDescriptionComplexFeature:
      "Choose one of your workspaces for initiating a legal task, and ensure necessary documents are uploaded in the workspace before initiating.",
    newWorkspaceComplexTaskInfo:
      "If you are creating a new workspace, you will be entering the upload view to upload all necessary documents, which is necessary for performing a legal task document generation.",
    selectExistingWorkspace: "Select an existing workspace",
    selectExistingDocumentDraftingWorkspace:
      "Select an existing Document Drafting workspace",
    orCreateNewBelow: "Or, create a new Document Drafting workspace below.",
    newWorkspaceName: "Enter a name for your new workspace",
    newWorkspaceNameOptional:
      "Enter a name for your new workspace (if you are not using an existing workspace)",
    workspaceNamePlaceholder: "eg: My new workspace",
    next: "Next",
    pleaseSelectWorkspace: "Please select a workspace to continue.",
    workspaceNameRequired: "Workspace name is required.",
    workspaceNameOrExistingWorkspaceRequired:
      "Please enter a workspace name or select an existing workspace.",
    workspaceNameMustBeMoreThanOneCharacter:
      "Workspace name must be more than one character.",
    noWorkspacesAvailable: "No workspaces available for this type.",
    selectWorkspacePlaceholder: "Select a workspace",
    featureUnavailable: {
      title: "Feature Not Available",
      description:
        "This feature is not enabled for your account or is disabled in this system. Please contact an administrator to enable this feature if needed.",
      close: "Close",
    },
    createNewWorkspace: {
      title: "Create New Document Drafting Workspace",
      description:
        "This will create a new workspace specifically for complex document drafting using the selected template.",
      workspaceName: "Workspace Name",
      create: "Create Workspace",
    },
    selectExisting: {
      title: "Select Workspace for Legal Q&A",
      description:
        "Choose an existing workspace to start a Legal Q&A chat session.",
      selectWorkspace: "Select Workspace",
    },
  },
};

export default TRANSLATIONS;
