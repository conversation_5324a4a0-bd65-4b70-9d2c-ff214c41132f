export default {
  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================
  "document-builder": {
    title: "Document Builder Prompts",
    description:
      "Customize the default prompts used by the Document Builder feature.",
    "override-prompt-placeholder":
      "Enter prompt to override the default system prompt",
    saving: "Saving...",
    save: "Save Prompt Settings",
    "toast-success": "Document builder prompts saved successfully.",
    "toast-fail": "Failed to save document builder prompts.",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Document Summary Prompts",
          description:
            "Configure system and user prompts for Document Summary.",
        },
        document_relevance: {
          title: "Document Relevance Prompts",
          description:
            "Configure system and user prompts for Document Relevance.",
        },
        section_drafting: {
          title: "Section Drafting Prompts",
          description:
            "Configure system and user prompts for Section Drafting.",
        },
        section_legal_issues: {
          title: "Section Legal Issues Prompts",
          description:
            "Configure system and user prompts for Section Legal Issues.",
        },
        memo_creation: {
          title: "Memo Creation Prompts",
          description: "Configure prompts for Memo Creation.",
        },
        section_index: {
          title: "Section Index Prompts",
          description: "Configure prompts for Section Index.",
        },
        select_main_document: {
          title: "Select Main Document Prompts",
          description:
            "Configure system and user prompts for Select Main Document.",
        },
        section_list_from_main: {
          title: "Section List From Main Prompts",
          description:
            "Configure system and user prompts for Section List From Main.",
        },
        section_list_from_summaries: {
          title: "Section List From Summaries Prompts",
          description:
            "Configure system and user prompts for Section List From Summaries.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Document Summary (System)",
      "document-summary-system-description":
        "System prompt for instructing the AI on how to summarize a document's content and relevance to a legal task.",
      "document-summary-user-label": "Document Summary (User)",
      "document-summary-user-description":
        "User prompt template for generating a detailed summary of document content in relation to a specific legal task.",

      // Document Relevance
      "document-relevance-system-label": "Document Relevance (System)",
      "document-relevance-system-description":
        "System prompt for evaluating if a document is relevant to a legal task, expecting a true/false answer.",
      "document-relevance-user-label": "Document Relevance (User)",
      "document-relevance-user-description":
        "User prompt template to check if document content is relevant for a given legal task.",

      // Section Drafting
      "section-drafting-system-label": "Section Drafting (System)",
      "section-drafting-system-description":
        "System prompt for generating a single document section in a professional legal style, using specified documents and context.",
      "section-drafting-user-label": "Section Drafting (User)",
      "section-drafting-user-description":
        "User prompt template to generate a specific section of a legal document, considering title, task, source documents, and neighboring sections.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Section Legal Issues Identification (System)",
      "section-legal-issues-system-description":
        "System prompt for identifying specific legal topics for which factual information should be retrieved to support drafting a document section.",
      "section-legal-issues-user-label":
        "Section Legal Issues Identification (User)",
      "section-legal-issues-user-description":
        "User prompt template to list legal topics or data points for fetching background information relevant to a specific document section and legal task.",

      // Memo Creation
      "memo-creation-template-label": "Default Memo Creation Template",
      "memo-creation-template-description":
        "Prompt template for creating a legal memorandum addressing a specific legal issue, considering provided documents and task context.",

      // Section Index
      "section-index-system-label": "Section Index (System)",
      "section-index-system-description":
        "System prompt for generating a structured index of sections for a legal document.",

      // Select Main Document
      "select-main-document-system-label": "Select Main Document (System)",
      "select-main-document-system-description":
        "System prompt for identifying the most relevant main document for a legal task from multiple document summaries.",
      "select-main-document-user-label": "Select Main Document (User)",
      "select-main-document-user-description":
        "User prompt template for identifying the main document for a legal task given summaries of multiple documents.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Section List from Main Document (System)",
      "section-list-from-main-system-description":
        "System prompt for drafting a JSON-structured list of sections for a legal document based on main document content and the legal task.",
      "section-list-from-main-user-label":
        "Section List from Main Document (User)",
      "section-list-from-main-user-description":
        "User prompt template for providing the legal task and main document content to generate a section list.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Section List from Summaries (System)",
      "section-list-from-summaries-system-description":
        "System prompt for drafting a JSON-structured list of sections based on document summaries and the legal task when no main document exists.",
      "section-list-from-summaries-user-label":
        "Section List from Summaries (User)",
      "section-list-from-summaries-user-description":
        "User prompt template for providing the legal task and document summaries to generate a section list when no main document exists.",
    },

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================

    "view-categories": "View all categories",
    "hide-categories": "Hide list",
    "add-task": "Add legal task",
    loading: "Loading...",
    table: {
      title: "Legal Tasks",
      name: "Name",
      "sub-category": "Sub-Category",
      description: "Description",
      prompt: "Legal Task Prompt",
      actions: "Actions",
      delete: "Delete",
      "delete-confirm": "Are you sure you want to delete this category?",
      "delete-success": "Category deleted",
      "delete-error": "Failed to delete category",
    },
    "create-task-title": "Create a legal task",
    "category-name": "Category Name",
    "category-name-desc": "Enter the name of the main category.",
    "category-name-placeholder": "Enter category name",
    "subcategory-name": "Sub-Category Name",
    "subcategory-name-desc": "Enter the name of the sub-category.",
    "subcategory-name-placeholder": "Enter sub-category name",
    "description-desc": "Enter a description of the category and sub-category.",
    "description-placeholder": "Enter a short description",
    submitting: "Submitting...",
    submit: "Submit",
    validation: {
      "category-required": "Category Name is required.",
      "subcategory-required": "Sub-Category Name is required.",
      "description-required": "Description is required.",
      "prompt-required": "Legal Task Prompt is required.",
    },
    "create-task": {
      title: "Create a legal task",
      category: {
        name: "Category Name",
        desc: "Enter the name of the category.",
        placeholder: "Enter category name",
        type: "Category Type",
        new: "Create new category",
        existing: "Use existing category",
        select: "Select category",
        "select-placeholder": "Select an existing category",
      },
      subcategory: {
        name: "Sub-Category Name",
        desc: "Enter the name of the sub-category.",
        placeholder: "Enter sub-category name",
      },
      description: {
        name: "Description and user instructions",
        desc: "Information and instructions that the user will see.",
        placeholder:
          "Describe the type of documents that need to be uploaded to the workspace to achieve the best possible result",
      },
      prompt: {
        name: "Legal Task Prompt",
        desc: "Enter the prompt that will be used for this legal task. You can also upload example documents with the buttons to add content examples to your prompt.",
        placeholder:
          "Enter legal task prompt or upload example documents to improve your prompt...",
      },
      submitting: "Submitting...",
      submit: "Submit",
      validation: {
        "category-required": "Category Name is required.",
        "subcategory-required": "Sub-Category Name is required.",
        "description-required": "Description is required.",
        "prompt-required": "Legal Task Prompt is required.",
      },
    },
    "edit-task": {
      title: "Edit legal task",
      submitting: "Updating...",
      submit: "Update task",
      subcategory: {
        name: "Sub-Category Name",
        desc: "Enter a new name for this legal task",
        placeholder: "Enter legal task...",
      },
      description: {
        name: "Description and user instructions",
        desc: "Enter a description and user instructions for this legal task",
        placeholder: "Enter description and user instructions...",
      },
      prompt: {
        name: "Legal Task Prompt",
        desc: "Enter the prompt that will be used for this legal task. You can also upload example documents with the buttons to add content examples to your prompt.",
        placeholder:
          "Enter legal task prompt or upload example documents to improve your prompt...",
      },
      validation: {
        "subcategory-required": "Legal Task Name is required",
        "description-required": "Description is required",
        "prompt-required": "Legal Task Prompt is required",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Main Document Selection Required",
      "requires-main-doc-description":
        "If marked, the user must select the main document from the uploaded files when this task is performed. This is strongly recommended for legal tasks that involve responding to a letter or a court order or similar, as it structures the result based on the document being answered.",
      "requires-main-doc-placeholder": "Yes or No",
      "requires-main-doc-explanation-default":
        "A selection is required because this determines how the document will be built.",
      "requires-main-doc-explanation-yes":
        "If 'Yes' the user will need to select a main document when this legal task is started. This document will be central to the task's workflow.",
      "requires-main-doc-explanation-no":
        "If 'No' the legal task will continue without requiring a default main document. The task will create a result more dynamically based on all uploaded documents and the legal task.",
    },

    // New keys for Review Generator Prompt feature
    reviewGeneratorPromptButton: "Review Generator Prompt",
    reviewGeneratorPromptButtonTooltip:
      "View the exact prompt template used to generate the legal task suggestion. (Admin only)",
    reviewGeneratorPromptTitle: "Generator Prompt Review",
    reviewPromptLabel: "The following prompt was used for generation:",
    reviewPromptTextareaLabel: "Generator Prompt Content",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Perform Legal Task",
    noTaskfund: "No legal tasks available.",
    noSubtskfund: "No subcategories available.",
    "loading-subcategory": "Loading subcategories...",
    "select-category": "Select category",
    "choose-task": "Choose legal task to perform",
    "duration-info":
      "The time for performing a legal task depends on the number of documents in the workspace. With many documents and a complex task, this can take a very long time.",
    description:
      "Enable or disable perform legal task button in the Document Drafting.",
    successMessage: "Perform legal task has been {{status}}",
    failureUpdateMessage: "Failed to update perform legal task setting.",
    errorSubmitting: "Error while submitting perform legal task settings.",
    "additional-instructions-label": "Additional Instructions:",
    "custom-instructions-placeholder":
      "Enter additional instructions for the legal task (optional)...",
    "warning-title": "Warning",
    "no-files-title": "No Files Available",
    "no-files-description":
      "There are no files in this workspace. Please upload at least one file before performing a legal task.",
    "settings-button": "Add or edit available legal tasks",
    settings: "Legal Tasks Settings",
    subStep: "Ongoing or queued step",
  },
  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Legal task user prompt generator",
    description: "Automatic proposal of the customized prompt for a legal task",
    "task-description": "Legal task description",
    "task-description-placeholder":
      "Describe the legal task you want to accomplish...",
    "specific-instructions": "Specific instructions or know-how",
    "specific-instructions-description":
      "Include any special instructions or expertise specific to this legal task",
    "specific-instructions-placeholder":
      "Add specific instructions, expertise, or know-how for handling this legal task...",
    "suggested-prompt": "Suggested user prompt",
    "generation-prompt": "Prompt for generation",
    "create-task": "Create legal task based on this suggestion",
    generating: "Generating...",
    generate: "Generate proposal",
    "toast-success": "Successfully generated prompt",
    "toast-fail": "Failed to generate prompt",
    button: "Generate Prompt",
    success: "Prompt generated successfully",
    error: "Please enter a name or subcategory first",
    failed: "Failed to generate prompt",
  },
};
