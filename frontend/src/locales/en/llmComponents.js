export default {
  // =========================
  // GENERIC PROVIDER SELECTION SETTINGS
  // =========================
  generic: {
    "base-url": "Base URL",
    "api-key": "API Key",
    "api-key-placeholder": "Enter your API Key",
    "chat-model": "Chat Model",
    "chat-model-placeholder": "Enter the chat model",
    "token-window": "Token context window",
    "token-window-placeholder": "Content window limit (eg: 4096)",
    "max-tokens": "Max Tokens",
    "max-tokens-placeholder": "Max tokens per request (eg: 1024)",
    "embedding-deployment": "Embedding Deployment Name",
    "embedding-deployment-placeholder":
      "Azure OpenAI embedding model deployment name",
    "embedding-model": "Embedding Model",
    "embedding-model-placeholder": "Enter the embedding model",
    "max-embedding-chunk-length": "Max Embedding Chunk Length",
    saving: "Saving...",
    "save-changes": "Save changes",
    "workspace-update-error": "Error: {{error}}",
    "base-url-placeholder": "eg: https://proxy.openai.com",
    "password-mask-length": "12",
  },

  // =========================
  // GENERIC MODEL SELECTION SETTINGS
  // =========================
  model: {
    selection: "Chat Model Selection",
    "embedding-selection": "Embedding Model Selection",
    "enter-api-key":
      "Enter a valid API key to view all available models for your account.",
    "enter-url": "Enter URL first",
    "your-models": "Your loaded models",
    "available-models": "Available Models",
  },

  // =========================
  // ANTHROPIC
  // =========================
  anthropic: {
    "api-key": "Anthropic API Key",
    "api-key-placeholder": "Enter your Anthropic API Key",
    "model-selection": "Chat Model Selection",
  },

  // =========================
  // AZURE SETTINGS
  // =========================
  azure: {
    "service-endpoint": "Azure Service Endpoint",
    "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
    "api-key": "API Key",
    "api-key-placeholder": "Azure OpenAI API Key",
    "chat-deployment": "Chat Deployment Name",
    "chat-deployment-placeholder": "Azure OpenAI chat model deployment name",
    "token-limit": "Chat Model Token Limit",
  },
  //AZURE-AI FOR EMBEDDING //TODO SORT LLM AND EMBEDDING
  azureai: {
    "service-endpoint": "Azure Service Endpoint",
    "api-key": "API Key",
    "api-key-placeholder": "Azure OpenAI API Key",
    "embedding-deployment-name": "Embedding Deployment Name",
    "embedding-deployment-name-placeholder":
      "Enter the embedding deployment name",
  },

  // =========================
  // AWS BEDROCK SETTINGS
  // =========================
  bedrock: {
    "iam-warning":
      "You should use a properly defined IAM user for inferencing.",
    "read-more": "Read more on how to use AWS Bedrock in this instance",
    "access-id": "AWS Bedrock IAM Access ID",
    "access-id-placeholder": "AWS Bedrock IAM User Access ID",
    "access-key": "AWS Bedrock IAM Access Key",
    "access-key-placeholder": "AWS Bedrock IAM User Access Key",
    region: "AWS region",
    "model-id": "Model ID",
    "model-id-placeholder": "Model id from AWS eg: meta.llama3.1-v0.1",
    "context-window": "Model context window",
    "context-window-placeholder": "Content window limit (eg: 4096)",
  },

  // =========================
  // COHERE
  // =========================
  cohere: {
    "api-key": "Cohere API Key",
    "api-key-placeholder": "Enter your Cohere API Key",
    "model-preference": "Model Preference",
    "model-selection": "Model Selection",
  },

  // =========================
  // DEEPSEEK LLM SETTINGS
  // =========================
  deepseek: {
    "api-key": "DeepSeek API Key",
    "api-key-placeholder": "Enter your DeepSeek API key",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
  },

  // =========================
  // FIREWORKSAI LLM SELECTION
  // =========================
  fireworksai: {
    "api-key": "FireworksAI API Key",
    "api-key-placeholder": "Enter your FireworksAI API Key",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
  },
  // =========================
  // GEMINI LLM OPTIONS
  // =========================
  gemini: {
    "api-key": "Google AI API Key",
    "api-key-placeholder": "Google Gemini API Key",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
    "manual-options": "Manual Options",
    "safety-setting": "Safety Setting",
    experimental: "Experimental",
    stable: "Stable",
    "safety-options": {
      none: "None (default)",
      "block-few": "Block Only High Risk",
      "block-some": "Block Medium & High Risk",
      "block-most": "Block Most Content",
    },
  },

  // =========================
  // GROQ LLM SELECTION
  // =========================
  groq: {
    "api-key": "Groq API Key",
    "api-key-placeholder": "Enter your Groq API key",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
    "enter-api-key":
      "Enter a valid API key to view all available models for your account.",
    "available-models": "Available models",
    "model-description":
      "Select the GroqAI model you want to use for your conversations.",
  },

  // =========================
  // HUGGINGFACE LLM SETTINGS
  // =========================
  huggingface: {
    "inference-endpoint": "HuggingFace Inference Endpoint",
    "endpoint-placeholder": "https://example.endpoints.huggingface.cloud",
    "access-token": "HuggingFace Access Token",
    "token-placeholder": "Enter your HuggingFace Access Token",
    "token-limit": "Model Token Limit",
    "token-limit-placeholder": "4096",
  },

  // =========================
  // KOBOLDCPP SETTINGS
  // =========================
  koboldcpp: {
    "show-advanced": "Show Manual Endpoint Input",
    "hide-advanced": "Hide Manual Endpoint Input",
    "base-url": "KoboldCPP Base URL",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "base-url-desc": "Enter the URL where KoboldCPP is running.",
    "auto-detect": "Auto-Detect",
    "token-context-window": "Token Context Window",
    "token-window-placeholder": "4096",
    "token-window-desc": "Maximum number of tokens for context and response.",
    model: "KoboldCPP Model",
    "loading-models": "--loading available models--",
    "enter-url": "Enter KoboldCPP URL first",
    "model-desc":
      "Select the KoboldCPP model you want to use. Models will load after entering a valid KoboldCPP URL.",
    "model-choose":
      "Choose the KoboldCPP model you want to use for your conversations.",
  },

  // =========================
  // LITELLM
  // =========================
  litellm: {
    "model-tooltip":
      "Be sure to select a valid embedding model. Chat models are not embedding models. See",
    "model-tooltip-link": "this page",
    "model-tooltip-more": "for more information.",
    "base-url": "Base URL",
    "base-url-placeholder": "eg: https://proxy.openai.com",
    "max-embedding-chunk-length": "Max Embedding Chunk Length",
    "token-window": "Token Context Window",
    "token-window-placeholder": "Context window limit (eg: 4096)",
    "api-key": "API Key",
    optional: "optional",
    "api-key-placeholder": "sk-mysecretkey",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
    "waiting-url": "-- waiting for URL --",
    "loaded-models": "Your loaded models",
    "manage-embedding": "Manage embedding",
    "embedding-required":
      "Litellm requires an embedding service to be set to use.",
  },

  // =========================
  // LMSTUDIO LLM SELECTION
  // =========================
  lmstudio: {
    "max-tokens": "Max Tokens",
    "max-tokens-desc": "Maximum number of tokens for context and response.",
    "show-advanced": "Show Manual Endpoint Input",
    "hide-advanced": "Hide Manual Endpoint Input",
    "base-url": "LM Studio Base URL",
    "base-url-placeholder": "http://localhost:1234/v1",
    "base-url-desc": "Enter the URL where LM Studio is running.",
    "auto-detect": "Auto-Detect",
    model: "LM Studio Model",
    "model-loading": "--loading available models--",
    "model-url-first": "Enter LM Studio URL first",
    "model-desc":
      "Select the LM Studio model you want to use. Models will load after entering a valid LM Studio URL.",
    "model-choose":
      "Choose the LM Studio model you want to use for your conversations.",
    "model-loaded": "Your loaded models",
    "embedding-required":
      "LMStudio as your LLM requires you to set an embedding service to use.",
    "manage-embedding": "Manage embedding",
    "max-embedding-chunk-length": "Max Embedding Chunk Length",
  },

  // =========================
  // LOCALAI LLM SELECTION
  // =========================
  localai: {
    "token-window": "Token context window",
    "token-window-placeholder": "4096",
    "api-key": "Local AI API Key",
    "api-key-optional": "optional",
    "api-key-placeholder": "sk-mysecretkey",
    "show-advanced": "Show advanced settings",
    "hide-advanced": "Hide advanced settings",
    "base-url": "Local AI Base URL",
    "base-url-placeholder": "http://localhost:8080/v1",
    "base-url-help": "Enter the URL where LocalAI is running.",
    "auto-detect": "Auto-Detect",
    "max-embedding-chunk-length": "Max Embedding Chunk Length",
    "embedding-required": "Embedding is required for LocalAI.",
    "manage-embedding": "Manage Embedding",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
    "waiting-url": "-- waiting for URL --",
    "loaded-models": "Your loaded models",
  },

  // =========================
  // MISTRAL LLM OPTIONS
  // =========================
  mistral: {
    "api-key": "Mistral API Key",
    "api-key-placeholder": "Mistral API Key",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
    "waiting-key": "-- waiting for API key --",
    "available-models": "Available Mistral Models",
  },

  // =========================
  // NATIVE LLM SETTINGS
  // =========================

  native: {
    "experimental-warning":
      "Using a locally hosted LLM is experimental and may not work as expected.",
    "model-desc": "Select a model from your locally hosted models.",
    "token-desc": "Maximum number of tokens for context and response.",
  },

  // =========================
  // OLLAMA LLM SELECTION
  // =========================

  ollamallmselection: {
    "max-tokens": "Max Tokens",
    "max-tokens-desc": "Maximum number of tokens for context and response.",
    "show-advanced": "Show Manual Endpoint Input",
    "hide-advanced": "Hide Manual Endpoint Input",
    "base-url": "Ollama Base URL",
    "base-url-placeholder": "http://127.0.0.1:11434",
    "base-url-desc": "Enter the URL where Ollama is running.",
    "auto-detect": "Auto-Detect",
    "keep-alive": "Ollama Keep Alive",
    "no-cache": "No cache",
    "five-minutes": "5 minutes",
    "one-hour": "1 hour",
    forever: "Forever",
    "keep-alive-desc": "Keep the model loaded in memory.",
    "learn-more": "Learn more",
    "performance-mode": "Performance Mode",
    "base-default": "Base (Default)",
    maximum: "Maximum",
    "performance-mode-desc": "Select the performance mode.",
    note: "Note:",
    "maximum-warning": "Maximum mode uses more resources.",
    base: "Base",
    "base-desc": "Base mode is the default setting.",
    "maximum-desc":
      "Maximum mode provides higher performance, using full context window up to max tokens.",
    model: "Ollama Model",
    "loading-models": "--loading available models--",
    "enter-url": "Enter Ollama URL first",
    "model-desc":
      "Select the Ollama model you want to use. Models will load after entering a valid Ollama URL.",
    "model-choose":
      "Choose the Ollama model you want to use for your conversations.",
  },

  // =========================
  // OPENAI LLM SELECTION
  // =========================
  openai: {
    "api-key": "OpenAI API Key",
    "api-key-placeholder": "Enter your OpenAI API Key",
    "model-preference": "Model Preference",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
    "model-divider": "──────────",
  },

  // =========================
  // OPENROUTER LLM SELECTION
  // =========================
  openrouter: {
    "api-key": "OpenRouter API Key",
    "api-key-placeholder": "Enter your OpenRouter API key",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
  },

  // =========================
  // PERPLEXITY LLM SELECTION
  // =========================
  perplexity: {
    "api-key": "Perplexity API Key",
    "api-key-placeholder": "Enter your Perplexity API Key",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
    "available-models": "Available Perplexity Models",
  },

  // =========================
  // TOGETHERAI
  // =========================
  togetherai: {
    "api-key": "TogetherAI API Key",
    "api-key-placeholder": "Enter your TogetherAI API Key",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
  },

  // =========================
  // XAI
  // =========================
  xai: {
    "api-key": "xAI API Key",
    "api-key-placeholder": "xAI API Key",
    "model-selection": "Chat Model Selection",
    "loading-models": "-- loading available models --",
    "enter-api-key":
      "Enter a valid API key to view all available models for your account.",
    "available-models": "Available models",
    "model-description":
      "Select the xAI model you want to use for your conversations.",
  },
};
