export default {
  // =========================
  // UPLOAD MODAL LEGALQ&A
  // =========================
  documents: {
    "pin-info-button": "About pinning",
    "pin-title": "What is document pinning?",
    "pin-desc-1":
      "When you pin a document the platform we will inject the entire content of the document into your prompt window for your LLM to fully comprehend.",
    "pin-desc-2":
      "This works best with large-context models or small files that are critical to its knowledge-base.",
    "pin-desc-3":
      "If you are not getting the answers you desire by default then pinning is a great way to get higher quality answers in a click.",
    "pin-add": "Pin to workspace",
    "pin-unpin": "Un-Pin from workspace",
    "watch-title": "What does watching a document do?",
    "watch-desc-1":
      "When you watch a document we will automatically sync your document content from its original source on regular intervals. This will automatically update the content in every workspace where this file is managed.",
    "watch-desc-2":
      "This feature currently supports online-based content and will not be available for manually uploaded documents.",
    "watch-desc-3": "You can manage what documents are watched from the",
    "file-manager": "File manager",
    "admin-view": "admin view",
    "pdr-add": "Added all documents to Parent Document Retrieval",
    "pdr-remove": "Removed all documents from Parent Document Retrieval",
    empty: "No documents found",
    tooltip: { date: "Date: ", type: "Type: ", cached: "Cached" },
    actions: { removing: "Removing file from workspace" },
    costs: { estimate: "Estimated Cost: $", minimum: "< $0.01" },
    "new-folder": {
      title: "Create New Folder",
      "name-label": "Folder Name",
      "name-placeholder": "Enter folder name",
      create: "Create Folder",
    },
    error: { "create-folder": "Failed to create folder" },
  },
};
