export default {
  appearance: {
    title: "Appearance",
    description: "Customize the appearance settings of your platform.",
    logo: {
      title: "Customize logo",
      description: "Upload your custom logo for light mode.",
      add: "Add a custom logo",
      recommended: "Recommended size: 800 x 200",
      remove: "Remove",
    },
    logoDark: {
      title: "Customize dark mode logo",
      description: "Upload your custom logo for dark mode.",
    },
    "welcome-message": {
      title: "Customize welcome messages",
      description:
        "Customize the heading and text that's displayed on the home page.",
      heading: "Heading",
      text: "Text",
      save: "Save messages",
    },
    icons: {
      title: "Custom Footer Icons",
      description:
        "Customize the footer icons displayed on the bottom of the sidebar.",
      icon: "Icon",
      link: "Link",
    },
    display: {
      title: "Display Language",
      description: "Select the preferred language.",
    },
    color: {
      title: "Custom colors",
      reset: "Reset",
      "desc-start": "Customize the",
      "desc-mid": "*background-color, *primary-color",
      "desc-and": "and",
      "desc-end": "*text-color",
      red: "Red",
      gray: "Gray",
      foynet: "Foynet",
      brown: "Brown",
      green: "Green",
      yellow: "Yellow",
      cyan: "Cyan",
      magenta: "Magenta",
      orange: "TenderFlow",
      purple: "Purple",
      navy: "Navy",
      black: "Black",
    },
    login: {
      title: "Custom Login Text",
      description: "Customize the paragraph text displayed on the login page.",
      placeholder: "Please contact the admin of the system",
      website: {
        title: "Customize the Application Website",
        description: "Customize the URL of your application's website.",
        toggle: "Display Website Link",
      },
      validation: { invalidURL: "Please enter a valid URL." },
    },
    siteSettings: {
      title: "Custom Site Settings",
      description:
        "Change the content of the browser tab for customization and branding.",
      tabTitle: "Tab Title",
      tabDescription:
        "Set a custom tab title when the app is open in a browser.",
      tabIcon: "Tab Favicon",
      fabIconUrl: "Define a url to an image to use for your favicon",
      placeholder: "url to your image",
      "invalid-file-type":
        "Invalid file type. Please use PNG, JPEG, GIF, WebP, SVG, or ICO files.",
      "file-too-large": "File too large. Maximum size is 5MB.",
      "default-title": "IST Legal",
    },
    appName: {
      title: "Custom App Name",
      description: "Set a custom app name that is displayed on the login page.",
    },
    customTab: {
      title: "Customize Tab Name",
      tab1: "Tab 1",
      tab2: "Tab 2",
      tab3: "Tab 3",
    },
    template: {
      title: "System DOCX Template",
      description:
        "Upload a default Word template (.docx) that will be used for Canvas exports.",
      tags: "Required merge tags inside the document: {{title}} for the document title and {{body}} for the main content. Optional tags: {{author}}, {{date}}, {{createdAt}}, {{updatedAt}}, {{header}}, {{footer}}, {{pageNumber}}, {{totalPages}}, {{company}}, {{reference}}.",
      remove: "Remove",
      upload: "Upload template",
      invalid: "Only .docx files are allowed",
    },
    "prompt-examples": {
      title: "Prompt examples",
      description: "Manage the example prompts shown on the main page.",
      example: "Example {{number}}",
      remove: "Remove",
      "title-field": "Title",
      area: "Legal area",
      prompt: "Prompt",
      icon: "Select icon",
      workspace: "Workspace",
      add: "Add example",
      save: "Save the UI",
    },
  },

  llm: {
    title: "LLM Preference",
    description:
      "These are the credentials and settings for your preferred LLM chat & embedding provider. Its important these keys are current and correct or else the system will not function properly.",
    provider: "LLM Provider",
    "secondary-provider": "Secondary LLM Provider",
    "none-selected": "None selected",
    "select-llm": "Agents will not work until a valid selection is made.",
    "search-llm": "Search all LLM providers",
    "context-window-warning":
      "Warning: Could not fetch context window for the selected model.",
    "context-window-waiting": " -- waiting for context window information -- ",
    "validation-prompt": {
      disable: {
        label: "Disable Validation Prompt",
        description:
          "When enabled, the validation button will not appear in the UI.",
      },
    },
    "prompt-upgrade": {
      title: "Prompt Upgrade LLM Provider",
      description:
        "The specific LLM provider & model that will be used for upgrading user prompt. By default, it uses the system LLM provider and settings.",
      search: "Search available LLM providers for the feature",
      template: "Prompt Upgrade Template",
      "template-description":
        "This template will be used when upgrading prompts. Use {{prompt}} to refer to the text that should be upgraded.",
      "template-placeholder":
        "Enter the template that will be used for upgrading prompts...",
      "template-hint":
        "Example: Please upgrade the following text while maintaining its meaning: {{prompt}}",
    },
    "logo-alt": "{{name}} logo",
    "context-window": "Context Window",
    "default-context-window": "(default size for this provider)",
    tokens: "tokens",
    "save-error": "Failed to save LLM settings",
  },
};
