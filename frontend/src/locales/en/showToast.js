export default {
  "show-toast": {
    "recovery-codes": "Recovery codes copied to clipboard",
    "token-minimum-error": "File content below minimum token requirement",
    "file-process-error": "Failed to process file",
    "scraping-website": "Scraping website - this may take a while.",
    "fetching-transcript": "Fetching transcript for YouTube video.",
    "request-legal-assistance-sent": "Legal assistance request has been sent.",
    "request-legal-assistance-error":
      "Failed to send legal assistance request.",
    "updating-workspace": "Updating workspace...",
    "flashing-started": "Flashing started...",
    "flashing-success": "Flashing completed successfully",
    "flashing-error": "Error during flashing: {{error}}",
    "pin-success-added": "Document pinned to workspace successfully",
    "pin-success-removed": "Document unpinned from workspace successfully",
    "workspace-updated": "Workspace updated successfully.",
    "link-uploaded": "Link uploaded successfully",
    "password-reset": "Password reset successful",
    "invalid-reset": "Invalid reset token",
    "delete-option": "Thread could not be deleted!",
    "thread-deleted": "Thread deleted successfully!",
    "threads-deleted": "Threads deleted successfully!",
    "chat-deleted": "Chat deleted successfully!",
    "failed-delete-chat": "Failed to delete the chat. Please try again.",
    "error-deleting-chat": "An error occurred while deleting the chat.",
    "chat-memory-reset": "Workspace chat memory has been reset!",
    "picture-uploaded": "Profile picture uploaded.",
    "profile-updated": "Profile updated.",
    "logs-cleared": "Event logs cleared successfully.",
    "preferences-updated": "System preferences updated successfully.",
    "user-created": "User created successfully.",
    "user-creation-error": "Failed to create user: {{error}}",
    "user-exists-error": "User with this email already exists",
    "user-deleted": "User deleted from system.",
    "workspaces-saved": "Workspaces saved successfully!",
    "failed-workspaces": "Failed to save workspaces. Please try again.",
    "api-deleted": "API Key permanently deleted",
    "api-copied": "API Key copied to clipboard",
    "appname-updated": "Successfully updated custom app name.",
    "appname-update-error": "Failed to update custom app name: ",
    "language-updated": "Successfully updated language.",
    "palette-updated": "Successfully updated palette.",
    "image-uploaded": "Image uploaded successfully.",
    "logo-remove-error": "Error removing logo: ",
    "logo-removed": "Logo successfully removed.",
    "logo-uploaded": "Logo uploaded successfully.",
    "logo-upload-error": "Failed to upload logo: ",
    "updated-welcome": "Successfully updated welcome messages.",
    "update-welcome-error": "Failed to update welcome messages:",
    "updated-footer": "Successfully updated footer icons.",
    "update-footer-error": "Failed to update footer icons: ",
    "updated-paragraph": "Successfully updated custom paragraph text.",
    "update-paragraph-error": "Failed to update custom paragraph text: ",
    "updated-supportemail": "Successfully updated support email.",
    "update-supportemail-error": "Failed to update support email: ",
    "stt-success": "Speech-to-text preferences saved successfully.",
    "tts-success": "Text-to-speech preferences saved successfully.",
    "failed-chats-export": "Failed to export chats.",
    "chats-exported": "Chats exported successfully as {{name}}.",
    "cleared-chats": "Cleared all chats.",
    "embed-deleted": "Embed deleted from system.",
    "snippet-copied": "Snippet copied to clipboard!",
    "embed-updated": "Embed updated successfully.",
    "embedding-saved": "Embedding preferences saved successfully.",
    "chunking-settings": "Text chunking strategy settings saved.",
    "llm-saved": "LLM preferences saved successfully.",
    "llm-saving-error": "Failed to save LLM settings: ",
    "multiuser-enabled": "Multi-User mode enabled successfully.",
    "publicuser-enabled": "Public-User mode enabled successfully.",
    "publicuser-disabled": "Public-User mode disabled successfully.",
    "page-refresh": "Your page will refresh in a few seconds.",
    "transcription-saved": "Transcription preferences saved successfully.",
    "vector-saved": "Vector database preferences saved successfully.",
    "workspace-not-deleted": "Workspace could not be deleted!",
    "maximum-messages": "Maximum of 4 messages allowed.",
    "users-updated": "Users updated successfully.",
    "vectordb-not-reset": "Workspace vector database could not be reset!",
    "vectordb-reset": "Workspace vector database was reset!",
    "meta-data-update": "Site preferences updated!",
    "linked-workspaces-updated": "Linked workspaces updated successfully.",
    "upgrade-answer-error": "Failed to upgrade answer: ",
    "upgrade-text-error": "Failed to upgrade text: ",
    "reset-tab-name-error": "Failed to reset to default tab name.",
    "update-tab-name-error": "Failed to update tab names: ",
    "updated-website": "Website settings updated successfully.",
    "update-website-error": "Failed to update website link: ",
    "reset-website-error": "Failed to reset to default website link.",
    "palette-update-error": "Failed to update color palette: ",
    "citation-state-updated":
      "Citation state updated successfully. {{citationState}}",
    "citation-state-update-error": "Failed to update Citation setting",
    "citation-update-error": "Error while submitting Citation setting",
    "message-limit-updated": "Message limit preferences updated successfully.",
    "validate-response-error": "Validation failed with ",
    "invoice-logging-state-updated":
      "Invoice logging preferences updated successfully.",
    "invoice-logging-state-update-error":
      "Error while updating invoice logging state: ",
    "error-fetching-tab-names": "Error fetching tab names",
    "active-case": {
      "reference-updated": "Active case reference updated successfully",
      "reference-cleared": "Active case reference cleared successfully",
    },
    "export-word": "Export to Word",
    "export-error": "Error exporting to Word document",
    "export-success": "Document exported successfully",
    "file-upload-success": "File successfully attached",
    "files-upload-success": "{{count}} files successfully attached",
    "file-upload-error": "Error uploading file(s)",
    "file-removed": "File removed successfully",
    "file-remove-error": "Failed to remove file completely. Please try again.",
    "link-upload-success": "Link uploaded successfully",
    "link-upload-error": "Error uploading link: {{error}}",
    "qura-login-success": "Qura Log in successfully",
    "error-linking-rexor": "Error fetching rexor linkage status",
    "article-transaction-registered":
      "Article transaction registered successfully",
    "changes-saved": "Changes saved successfully",
    "missing-data": "Missing required data",
    "save-error": "Error saving changes",
    "invalid-response": "Invalid response received",
    "streaming-error": "Error during streaming",
    "qura-auth.error.fill-fields": "Please fill in all fields.",
    "show-toast.qura-login-success": "Login successful.",
    "qura-auth.error.invalid-credentials": "Invalid username or password.",
    "qura-auth.connect": "Connect to Qura",
    "speech-to-text.microphone-access-error": "Microphone access is required.",
    "show-toast.meta-data-update": "Metadata updated successfully.",
    "appearance.siteSettings.tabIcon": "Tab Icon",
    "appearance.siteSettings.fabIconUrl": "Favicon URL",
    "appearance.siteSettings.placeholder": "Enter favicon URL",
    "appearance.siteSettings.title-placeholder": "Enter site title",
    "show-toast.workspace-updated": "Workspace updated successfully.",
    "pdr-settings.toast-success": "PDR settings updated successfully.",
    "pdr-settings.toast-fail": "Failed to update PDR settings.",
    "pdr-settings.title": "PDR Settings",
    "pdr-settings.description": "Manage your PDR settings here.",
    "pdr-settings.desc-end": "Ensure all values are correct.",
    "pdr-settings.pdr-token-limit": "PDR Token Limit",
    "pdr-settings.pdr-token-limit-desc": "Maximum number of tokens for PDR.",
    "pdr-settings.pdr-token-limit-placeholder": "Enter PDR token limit",
    "pdr-settings.input-prompt-token-limit": "Input Prompt Token Limit",
    "pdr-settings.input-prompt-token-limit-desc":
      "Maximum number of tokens for input prompts.",
    "pdr-settings.input-prompt-token-limit-placeholder":
      "Enter input prompt token limit",
    "pdr-settings.response-token-limit": "Response Token Limit",
    "pdr-settings.response-token-limit-desc":
      "Maximum number of tokens for responses.",
    "pdr-settings.response-token-limit-placeholder":
      "Enter response token limit",
    "pdr-settings.adjacent-vector-limit": "Adjacent Vector Limit",
    "pdr-settings.adjacent-vector-limit-desc": "Limit for adjacent vectors.",
    "pdr-settings.adjacent-vector-limit-placeholder":
      "Enter adjacent vector limit",
    "pdr-settings.keep-pdr-vectors": "Keep PDR Vectors",
    "pdr-settings.keep-pdr-vectors-desc": "Option to keep PDR vectors.",
    "workspace-update-error": "Error: {{error}}",
    "workspace-update-failed": "Workspace update failed: {{error}}",
    "token-window-exceeded":
      "File was not added because it exceeds the available token limit of current AI engine",
    "token-limit-exceeded":
      "File too large for available context window. Available tokens: {{available}}, Required tokens: {{required}}",
    "rexor-activity-id-update-success":
      "Rexor Activity ID updated successfully",
    "rexor-activity-id-update-error": "Failed to update Rexor Activity ID",
    "pin-error": "Failed to {{action}} document.",
    "pin-error-detail": "Failed to pin document. {{message}}",
    "error-fetching-settings": "An error occurred while fetching settings.",
    "experimental-features-unlocked": "Experimental feature previews unlocked!",
    "workspace-updated-success": "Workspace updated successfully.",
    "legal-task-created": "Legal task created!",
    "form-submission-failed": "Failed to submit form!",
    "provider-endpoint-discovered":
      "Provider endpoint discovered automatically.",
    "failed-save-embedding": "Failed to save embedding settings: {{error}}",
    "provider-endpoint-discovery-failed":
      "Couldn't automatically discover the provider endpoint. Please enter it manually.",
    "qura-setting-update-failed": "Failed to update Qura setting.",
    "qura-settings-submission-error": "Error while submitting Qura settings.",
    "preferences-save-error": "An error occurred while saving preferences.",
    "skill-config-updated": "Skill config updated successfully.",
    "category-deleted-success": "Category deleted successfully!",
    "category-deletion-failed": "Failed to delete category!",
    "embed-chats-export-failed": "Failed to export embed chats.",
    "api-key-revoked": "Browser Extension API Key permanently revoked",
    "api-key-revoke-failed": "Failed to revoke API Key",
    "connection-string-copied": "Connection string copied to clipboard",
    "connecting-to-extension": "Attempting to connect to browser extension...",
    "error-loading-settings":
      "Error loading settings. Please refresh the page.",
    "error-fetching-prompt-logging":
      "Error fetching prompt output logging setting.",
    "error-updating-prompt-logging":
      "Failed to update prompt output logging setting.",
    "error-updating-feature": "Failed to update status of feature.",
    "rexor-linkage-state-updated": "Rexor linkage state updated to {{state}}",
    "rexor-linkage-state-update-error":
      "Failed to update Rexor linkage state: {{error}}",
    "language-update-failed": "Failed to update language: {{error}}",
    "llm-settings-save-failed": "Failed to save LLM settings: {{error}}",
    "setup-error": "Error: {{error}}",
    "template-saved": "Prompt template saved successfully",
    "template-saving-error": "Failed to save template: {{error}}",
    "template-reset": "Template reset to default",
    "legal-task-updated": "Legal task updated successfully",
    "legal-task-update-failed": "Failed to update legal task",
    "model-ref-saved":
      "Changes in Custom Model Name and/or Custom Dynamic PDR saved",
    "model-ref-save-failed": "Failed to save custom model settings: {{error}}",
    "settings-fetch-failed": "Failed to fetch settings",
    "api-key-saved": "Successfully saved API key",
    "auto-environment-update":
      "Auto environment update started. This will take a few seconds.",
    "example-prompt-error": "Could not submit example prompt: {{error}}",
    "files-processed": "Successfully processed {{count}} file(s)",
    "token-validation-failed":
      "File validation failed - token count or size limit exceeded",
    "deleted-old-chats": "Deleted {{count}} old prompt(s)",
    "pdr-failed": "Failed to PDR document: {{message}}",
    "watch-failed": "Failed to watch document: {{message}}",
    "pdr-added": "Document added to Parent Document Retrieval",
    "pdr-removed": "Document removed from Parent Document Retrieval",
    "unsaved-changes":
      "You have unsaved changes. Are you sure you want to cancel?",
  },
  // =========================
  // TOAST MESSAGES
  // =========================
  toast: {
    export: {
      success: "{{name}} exported successfully",
      failed: "Failed to export",
      "embed-chats-success": "Embed chats exported successfully as {{name}}",
      "embed-chats-failed": "Failed to export embed chats",
    },
    profile: {
      "upload-failed": "Failed to upload profile picture: {{error}}",
      "remove-failed": "Failed to remove profile picture: {{error}}",
    },
    workspace: {
      "create-failed": "Failed to create workspace: {{error}}",
      "update-failed": "Failed to update workspace: {{error}}",
      "thread-create-failed": "Could not create thread: {{error}}",
      "thread-update-failed": "Thread could not be updated: {{message}}",
    },
    errors: {
      "file-move": "Error moving files: {{message}}",
      "thread-create": "Could not create thread - {{error}}",
      "connector-error": "{{error}}",
      "generic-error": "{{error}}",
      "failed-update-user": "Failed to update user: {{error}}",
      "failed-save-llm": "Failed to save LLM settings: {{error}}",
      "failed-save-preferences": "Failed to save preferences: {{error}}",
      "failed-clear-logs": "Failed to clear logs: {{error}}",
      "failed-create-thread": "Could not create thread - {{error}}",
      "failed-move-files": "Error moving files: {{message}}",
      streaming: {
        failed:
          "An error occurred while streaming response, such as AI engine being offline or over capacity.",
        code: "Code",
        unknown: "Unknown Error.",
      },
    },
    success: {
      "workspace-created":
        "Workspace created successfully! Taking you to home...",
      "generic-success": "{{message}}",
    },
    "prompt-output-logging": {
      enabled: "Prompt output logging has been enabled",
      disabled: "Prompt output logging has been disabled",
      error: "Error while submitting prompt output logging settings.",
    },
    "welcome-messages": {
      "update-failed": "Failed to update welcome messages: {{error}}",
      "drafting-update-failed":
        "Failed to update document drafting messages: {{error}}",
    },
    info: { "file-moved": "{{message}}" },
    document: {
      "move-success": "Successfully moved {{count}} documents",
      "pdr-failed": "Failed to PDR document: {{message}}",
      "watch-failed": "Failed to watch document: {{message}}",
      "pdr-added": "Document added to Parent Document Retrieval",
      "pdr-removed": "Document removed from Parent Document Retrieval",
      "pin-success":
        "DEPRECATED - Use document.pin-success-added or document.pin-success-removed instead",
      "pin-success-added": "Document pinned to workspace",
      "pin-success-removed": "Document unpinned from workspace",
    },
    settings: {
      "preferences-failed": "Failed to save preferences: {{error}}",
      "multi-user-failed": "Failed to enable Multi-User mode: {{error}}",
      "public-user-failed": "Failed to enable Public-User mode: {{error}}",
      "password-failed": "Failed to update password: {{error}}",
      "vector-db-failed": "Failed to save vector database settings: {{error}}",
      "llm-failed": "Failed to save LLM settings: {{error}}",
      "llm-save-failed": "Failed to save {{name}} settings: {{error}}",
      "preferences-save-failed": "Failed to save preferences: {{error}}",
      "transcription-save-failed":
        "Failed to save transcription settings: {{error}}",
      "embedding-save-failed": "Failed to save embedding settings: {{error}}",
      "welcome-messages-failed": "Failed to save welcome messages: {{error}}",
      "welcome-messages-fetch-failed": "Failed to fetch welcome messages",
      "welcome-messages-empty": "Please enter either a heading or text message",
      "welcome-messages-success": "Welcome messages saved successfully",
      "user-update-failed": "Failed to update user: {{error}}",
      "logs-clear-failed": "Failed to clear logs: {{error}}",
      "generic-error": "Error: {{message}}",
      "prompt-examples-failed": "Failed to save prompt examples: {{error}}",
      "prompt-examples-success": "Prompt examples saved successfully",
      "prompt-examples-validation":
        "Example {{number}} is missing required fields: {{fields}}",
      "import-success": "Successfully imported thread: {{threadName}}",
    },
    experimental: {
      "feature-enabled": "Experimental features enabled",
      "feature-disabled": "Experimental features disabled",
      "update-failed": "Failed to update experimental feature status",
      "features-enabled":
        "Experimental Feature set enabled. Reloading the page.",
      "live-sync": {
        enabled: "Live document sync enabled",
        disabled: "Live document sync disabled",
      },
    },
  },
};
