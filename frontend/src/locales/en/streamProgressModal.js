export default {
  // =========================
  // CDB PROGRESS
  // =========================
  cdbProgress: {
    "close-msg": "Are you sure you want to cancel the process?",
    general: {
      placeholderSubTask: "Processing item {{index}}...",
    },
    main: {
      step1: {
        label: "Generate Section List",
        desc: "Using the main document to create an initial structure.",
      },
      step2: {
        label: "Process Documents",
        desc: "Generating descriptions and checking relevance.",
      },
      step3: {
        label: "Map Documents to Sections",
        desc: "Assigning relevant documents to each section.",
      },
      step4: {
        label: "Identify Legal Issues",
        desc: "Extracting key legal issues for each section.",
      },
      step5: {
        label: "Generate Legal Memos",
        desc: "Creating legal memoranda for the identified issues.",
      },
      step6: {
        label: "Draft Sections",
        desc: "Composing the content for each individual section.",
      },
      step7: {
        label: "Combine & Finalize Document",
        desc: "Assembling sections into the final document.",
      },
    },
    noMain: {
      step1: {
        label: "Processing Documents",
        desc: "Generating descriptions for all uploaded files.",
      },
      step2: {
        label: "Generate Section List",
        desc: "Creating a structured list of sections from document summaries.",
      },
      step3: {
        label: "Finalize Document Mapping",
        desc: "Confirming document relevance for each planned section.",
      },
      step4: {
        label: "Identify Legal Issues",
        desc: "Extracting key legal issues for each section.",
      },
      step5: {
        label: "Generate Legal Memos",
        desc: "Creating legal memoranda for the identified issues.",
      },
      step6: {
        label: "Draft Sections",
        desc: "Composing the content for each individual section.",
      },
      step7: {
        label: "Combine & Finalize Document",
        desc: "Assembling sections into the final legal document.",
      },
    },
  },
  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Response Generation Progress",
    description:
      "Displays the real-time progress of tasks for finishing prompt, depending on linking with other workspaces and size of files. The modal will close automatically once all steps are complete.",
    step_fetching_memos: "Fetching legal data on current topics",
    step_processing_chunks: "Processing uploaded documents",
    step_combining_responses: "Finalize response",
    sub_step_chunk_label: "Processing document group {{index}}",
    sub_step_memo_label: "Fetched legal data from {{workspaceSlug}}",
    placeholder_sub_task: "Queued sub-task",
    desc_fetching_memos:
      "Retrieving relevant legal information from linked workspaces",
    desc_processing_chunks:
      "Analyzing and extracting information from document groups",
    desc_combining_responses:
      "Synthesizing information into a comprehensive response",
  },
};
