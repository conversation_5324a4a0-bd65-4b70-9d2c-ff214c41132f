// Looking for a language to translate IST Legal to?
// Create a `common.js` file in the language's ISO code https://www.w3.org/International/O-charset-lang.html
// eg: Spanish => es/common.js
// eg: French => fr/common.js
// You should copy the en/common.js file as your template and just translate every string in there.
// By default, we try to see what the browsers native language is set to and use that. If a string
// is not defined or is null in the translation file, it will fallback to the value in the en/common.js file
// RULES:
// The EN translation file is the ground-truth for what keys and options are available. DO NOT add a special key
// to a specific language file as this will break the other languages. Any new keys should be added to english
// and the language file you are working on.

// Contributor Notice: If you are adding a translation you MUST locally run `npm run verify:translations` from the root prior to PR.
// please do not submit PR's without first verifying this test passes as it will tell you about missing keys or values
// from the primary dictionary.

// Import all English locale files
import EnglishAnswerUpgrade from "./en/answerUpgrade.js";
import EnglishCdbRelatedKeys from "./en/cdbRelatedKeys.js";
import EnglishCommon from "./en/common.js";
import EnglishCustomLegalTemplates from "./en/customLegalTemplates.js";
import EnglishDataConnectors from "./en/dataConnectors.js";
import EnglishDdModuleKeys from "./en/ddModuleKeys.js";
import EnglishDocxEditor from "./en/docxEditor.js";
import EnglishEmbeddingModelKeys from "./en/embeddingModelKeys.js";
import EnglishFeedbackFunction from "./en/feedbackFunction.js";
import EnglishHomePage from "./en/homePage.js";
import EnglishLegalTemplates from "./en/legalTemplates.js";
import EnglishLlmComponents from "./en/llmComponents.js";
import EnglishLoginPage from "./en/loginPage.js";
import EnglishModalRelatedKeys from "./en/modalRelatedKeys.js";
import EnglishOnboarding from "./en/onboarding.js";
import EnglishPrivacyKeys from "./en/privacyKeys.js";
import EnglishRecentUploads from "./en/recentUploads.js";
import EnglishRexor from "./en/rexor.js";
import EnglishSettings from "./en/settings.js";
import EnglishShowToast from "./en/showToast.js";
import EnglishStreamProgressModal from "./en/streamProgressModal.js";
import EnglishSystemAdminSettingsPages from "./en/systemAdminSettingsPages.js";
import EnglishSystemOtherSettingsPages from "./en/systemOtherSettingsPages.js";
import EnglishUploadModal from "./en/uploadModal.js";
import EnglishWorkspaceSettings from "./en/workspaceSettings.js";
import EnglishWorkspaceViewAndButtons from "./en/workspaceViewAndButtons.js";
import EnglishWorkspaceSidebar from "./en/workspaceSidebar.js";

// Import all French locale files
import FrAnswerUpgrade from "./fr/answerUpgrade.js";
import FrCdbRelatedKeys from "./fr/cdbRelatedKeys.js";
import FrCommon from "./fr/common.js";
import FrCustomLegalTemplates from "./fr/customLegalTemplates.js";
import FrDataConnectors from "./fr/dataConnectors.js";
import FrDdModuleKeys from "./fr/ddModuleKeys.js";
import FrDocxEditor from "./fr/docxEditor.js";
import FrEmbeddingModelKeys from "./fr/embeddingModelKeys.js";
import FrFeedbackFunction from "./fr/feedbackFunction.js";
import FrHomePage from "./fr/homePage.js";
import FrLegalTemplates from "./fr/legalTemplates.js";
import FrLlmComponents from "./fr/llmComponents.js";
import FrLoginPage from "./fr/loginPage.js";
import FrModalRelatedKeys from "./fr/modalRelatedKeys.js";
import FrOnboarding from "./fr/onboarding.js";
import FrPrivacyKeys from "./fr/privacyKeys.js";
import FrRecentUploads from "./fr/recentUploads.js";
import FrRexor from "./fr/rexor.js";
import FrSettings from "./fr/settings.js";
import FrShowToast from "./fr/showToast.js";
import FrStreamProgressModal from "./fr/streamProgressModal.js";
import FrSystemAdminSettingsPages from "./fr/systemAdminSettingsPages.js";
import FrSystemOtherSettingsPages from "./fr/systemOtherSettingsPages.js";
import FrUploadModal from "./fr/uploadModal.js";
import FrWorkspaceSettings from "./fr/workspaceSettings.js";
import FrWorkspaceViewAndButtons from "./fr/workspaceViewAndButtons.js";
import FrWorkspaceSidebar from "./fr/workspaceSidebar.js";

// Import all German locale files
import DeAnswerUpgrade from "./de/answerUpgrade.js";
import DeCdbRelatedKeys from "./de/cdbRelatedKeys.js";
import DeCommon from "./de/common.js";
import DeCustomLegalTemplates from "./de/customLegalTemplates.js";
import DeDataConnectors from "./de/dataConnectors.js";
import DeDdModuleKeys from "./de/ddModuleKeys.js";
import DeDocxEditor from "./de/docxEditor.js";
import DeEmbeddingModelKeys from "./de/embeddingModelKeys.js";
import DeFeedbackFunction from "./de/feedbackFunction.js";
import DeHomePage from "./de/homePage.js";
import DeLegalTemplates from "./de/legalTemplates.js";
import DeLlmComponents from "./de/llmComponents.js";
import DeLoginPage from "./de/loginPage.js";
import DeModalRelatedKeys from "./de/modalRelatedKeys.js";
import DeOnboarding from "./de/onboarding.js";
import DePrivacyKeys from "./de/privacyKeys.js";
import DeRecentUploads from "./de/recentUploads.js";
import DeRexor from "./de/rexor.js";
import DeSettings from "./de/settings.js";
import DeShowToast from "./de/showToast.js";
import DeStreamProgressModal from "./de/streamProgressModal.js";
import DeSystemAdminSettingsPages from "./de/systemAdminSettingsPages.js";
import DeSystemOtherSettingsPages from "./de/systemOtherSettingsPages.js";
import DeUploadModal from "./de/uploadModal.js";
import DeWorkspaceSettings from "./de/workspaceSettings.js";
import DeWorkspaceViewAndButtons from "./de/workspaceViewAndButtons.js";
import DeWorkspaceSidebar from "./de/workspaceSidebar.js";

// Import all Swedish locale files
import SvAnswerUpgrade from "./sv/answerUpgrade.js";
import SvCdbRelatedKeys from "./sv/cdbRelatedKeys.js";
import SvCommon from "./sv/common.js";
import SvCustomLegalTemplates from "./sv/customLegalTemplates.js";
import SvDataConnectors from "./sv/dataConnectors.js";
import SvDdModuleKeys from "./sv/ddModuleKeys.js";
import SvDocxEditor from "./sv/docxEditor.js";
import SvEmbeddingModelKeys from "./sv/embeddingModelKeys.js";
import SvFeedbackFunction from "./sv/feedbackFunction.js";
import SvHomePage from "./sv/homePage.js";
import SvLegalTemplates from "./sv/legalTemplates.js";
import SvLlmComponents from "./sv/llmComponents.js";
import SvLoginPage from "./sv/loginPage.js";
import SvModalRelatedKeys from "./sv/modalRelatedKeys.js";
import SvOnboarding from "./sv/onboarding.js";
import SvPrivacyKeys from "./sv/privacyKeys.js";
import SvRecentUploads from "./sv/recentUploads.js";
import SvRexor from "./sv/rexor.js";
import SvSettings from "./sv/settings.js";
import SvShowToast from "./sv/showToast.js";
import SvStreamProgressModal from "./sv/streamProgressModal.js";
import SvSystemAdminSettingsPages from "./sv/systemAdminSettingsPages.js";
import SvSystemOtherSettingsPages from "./sv/systemOtherSettingsPages.js";
import SvUploadModal from "./sv/uploadModal.js";
import SvWorkspaceSettings from "./sv/workspaceSettings.js";
import SvWorkspaceViewAndButtons from "./sv/workspaceViewAndButtons.js";
import SvWorkspaceSidebar from "./sv/workspaceSidebar.js";

// Import all Kinyarwanda locale files
import RwAnswerUpgrade from "./rw/answerUpgrade.js";
import RwCdbRelatedKeys from "./rw/cdbRelatedKeys.js";
import RwCommon from "./rw/common.js";
import RwCustomLegalTemplates from "./rw/customLegalTemplates.js";
import RwDataConnectors from "./rw/dataConnectors.js";
import RwDdModuleKeys from "./rw/ddModuleKeys.js";
import RwDocxEditor from "./rw/docxEditor.js";
import RwEmbeddingModelKeys from "./rw/embeddingModelKeys.js";
import RwFeedbackFunction from "./rw/feedbackFunction.js";
import RwHomePage from "./rw/homePage.js";
import RwLegalTemplates from "./rw/legalTemplates.js";
import RwLlmComponents from "./rw/llmComponents.js";
import RwLoginPage from "./rw/loginPage.js";
import RwModalRelatedKeys from "./rw/modalRelatedKeys.js";
import RwOnboarding from "./rw/onboarding.js";
import RwPrivacyKeys from "./rw/privacyKeys.js";
import RwRecentUploads from "./rw/recentUploads.js";
import RwRexor from "./rw/rexor.js";
import RwSettings from "./rw/settings.js";
import RwShowToast from "./rw/showToast.js";
import RwStreamProgressModal from "./rw/streamProgressModal.js";
import RwSystemAdminSettingsPages from "./rw/systemAdminSettingsPages.js";
import RwSystemOtherSettingsPages from "./rw/systemOtherSettingsPages.js";
import RwUploadModal from "./rw/uploadModal.js";
import RwWorkspaceSettings from "./rw/workspaceSettings.js";
import RwWorkspaceViewAndButtons from "./rw/workspaceViewAndButtons.js";
import RwWorkspaceSidebar from "./rw/workspaceSidebar.js";

// Import all Norwegian locale files
import NoAnswerUpgrade from "./no/answerUpgrade.js";
import NoCdbRelatedKeys from "./no/cdbRelatedKeys.js";
import NoCommon from "./no/common.js";
import NoCustomLegalTemplates from "./no/customLegalTemplates.js";
import NoDataConnectors from "./no/dataConnectors.js";
import NoDdModuleKeys from "./no/ddModuleKeys.js";
import NoDocxEditor from "./no/docxEditor.js";
import NoEmbeddingModelKeys from "./no/embeddingModelKeys.js";
import NoFeedbackFunction from "./no/feedbackFunction.js";
import NoHomePage from "./no/homePage.js";
import NoLegalTemplates from "./no/legalTemplates.js";
import NoLlmComponents from "./no/llmComponents.js";
import NoLoginPage from "./no/loginPage.js";
import NoModalRelatedKeys from "./no/modalRelatedKeys.js";
import NoOnboarding from "./no/onboarding.js";
import NoPrivacyKeys from "./no/privacyKeys.js";
import NoRecentUploads from "./no/recentUploads.js";
import NoRexor from "./no/rexor.js";
import NoSettings from "./no/settings.js";
import NoShowToast from "./no/showToast.js";
import NoStreamProgressModal from "./no/streamProgressModal.js";
import NoSystemAdminSettingsPages from "./no/systemAdminSettingsPages.js";
import NoSystemOtherSettingsPages from "./no/systemOtherSettingsPages.js";
import NoUploadModal from "./no/uploadModal.js";
import NoWorkspaceSettings from "./no/workspaceSettings.js";
import NoWorkspaceViewAndButtons from "./no/workspaceViewAndButtons.js";
import NoWorkspaceSidebar from "./no/workspaceSidebar.js";

// Import all Polish locale files
import PlAnswerUpgrade from "./pl/answerUpgrade.js";
import PlCdbRelatedKeys from "./pl/cdbRelatedKeys.js";
import PlCommon from "./pl/common.js";
import PlCustomLegalTemplates from "./pl/customLegalTemplates.js";
import PlDataConnectors from "./pl/dataConnectors.js";
import PlDdModuleKeys from "./pl/ddModuleKeys.js";
import PlDocxEditor from "./pl/docxEditor.js";
import PlEmbeddingModelKeys from "./pl/embeddingModelKeys.js";
import PlFeedbackFunction from "./pl/feedbackFunction.js";
import PlHomePage from "./pl/homePage.js";
import PlLegalTemplates from "./pl/legalTemplates.js";
import PlLlmComponents from "./pl/llmComponents.js";
import PlLoginPage from "./pl/loginPage.js";
import PlModalRelatedKeys from "./pl/modalRelatedKeys.js";
import PlOnboarding from "./pl/onboarding.js";
import PlPrivacyKeys from "./pl/privacyKeys.js";
import PlRecentUploads from "./pl/recentUploads.js";
import PlRexor from "./pl/rexor.js";
import PlSettings from "./pl/settings.js";
import PlShowToast from "./pl/showToast.js";
import PlStreamProgressModal from "./pl/streamProgressModal.js";
import PlSystemAdminSettingsPages from "./pl/systemAdminSettingsPages.js";
import PlSystemOtherSettingsPages from "./pl/systemOtherSettingsPages.js";
import PlUploadModal from "./pl/uploadModal.js";
import PlWorkspaceSettings from "./pl/workspaceSettings.js";
import PlWorkspaceViewAndButtons from "./pl/workspaceViewAndButtons.js";
import PlWorkspaceSidebar from "./pl/workspaceSidebar.js";

function mergeDeep(target, source) {
  for (const key of Object.keys(source)) {
    const value = source[key];
    if (value && typeof value === "object" && !Array.isArray(value)) {
      if (!target[key]) target[key] = {};
      mergeDeep(target[key], value);
    } else {
      target[key] = value;
    }
  }
  return target;
}

// Define locale file groups for each language
const localeFiles = {
  en: {
    answerUpgrade: EnglishAnswerUpgrade,
    cdbRelatedKeys: EnglishCdbRelatedKeys,
    common: EnglishCommon,
    customLegalTemplates: EnglishCustomLegalTemplates,
    dataConnectors: EnglishDataConnectors,
    ddModuleKeys: EnglishDdModuleKeys,
    docxEditor: EnglishDocxEditor,
    embeddingModelKeys: EnglishEmbeddingModelKeys,
    feedbackFunction: EnglishFeedbackFunction,
    homePage: EnglishHomePage,
    legalTemplates: EnglishLegalTemplates,
    llmComponents: EnglishLlmComponents,
    loginPage: EnglishLoginPage,
    modalRelatedKeys: EnglishModalRelatedKeys,
    onboarding: EnglishOnboarding,
    privacyKeys: EnglishPrivacyKeys,
    recentUploads: EnglishRecentUploads,
    rexor: EnglishRexor,
    settings: EnglishSettings,
    showToast: EnglishShowToast,
    streamProgressModal: EnglishStreamProgressModal,
    systemAdminSettingsPages: EnglishSystemAdminSettingsPages,
    systemOtherSettingsPages: EnglishSystemOtherSettingsPages,
    uploadModal: EnglishUploadModal,
    workspaceSettings: EnglishWorkspaceSettings,
    workspaceViewAndButtons: EnglishWorkspaceViewAndButtons,
    workspaceSidebar: EnglishWorkspaceSidebar,
  },
  fr: {
    answerUpgrade: FrAnswerUpgrade,
    cdbRelatedKeys: FrCdbRelatedKeys,
    common: FrCommon,
    customLegalTemplates: FrCustomLegalTemplates,
    dataConnectors: FrDataConnectors,
    ddModuleKeys: FrDdModuleKeys,
    docxEditor: FrDocxEditor,
    embeddingModelKeys: FrEmbeddingModelKeys,
    feedbackFunction: FrFeedbackFunction,
    homePage: FrHomePage,
    legalTemplates: FrLegalTemplates,
    llmComponents: FrLlmComponents,
    loginPage: FrLoginPage,
    modalRelatedKeys: FrModalRelatedKeys,
    onboarding: FrOnboarding,
    privacyKeys: FrPrivacyKeys,
    recentUploads: FrRecentUploads,
    rexor: FrRexor,
    settings: FrSettings,
    showToast: FrShowToast,
    streamProgressModal: FrStreamProgressModal,
    systemAdminSettingsPages: FrSystemAdminSettingsPages,
    systemOtherSettingsPages: FrSystemOtherSettingsPages,
    uploadModal: FrUploadModal,
    workspaceSettings: FrWorkspaceSettings,
    workspaceViewAndButtons: FrWorkspaceViewAndButtons,
    workspaceSidebar: FrWorkspaceSidebar,
  },
  de: {
    answerUpgrade: DeAnswerUpgrade,
    cdbRelatedKeys: DeCdbRelatedKeys,
    common: DeCommon,
    customLegalTemplates: DeCustomLegalTemplates,
    dataConnectors: DeDataConnectors,
    ddModuleKeys: DeDdModuleKeys,
    docxEditor: DeDocxEditor,
    embeddingModelKeys: DeEmbeddingModelKeys,
    feedbackFunction: DeFeedbackFunction,
    homePage: DeHomePage,
    legalTemplates: DeLegalTemplates,
    llmComponents: DeLlmComponents,
    loginPage: DeLoginPage,
    modalRelatedKeys: DeModalRelatedKeys,
    onboarding: DeOnboarding,
    privacyKeys: DePrivacyKeys,
    recentUploads: DeRecentUploads,
    rexor: DeRexor,
    settings: DeSettings,
    showToast: DeShowToast,
    streamProgressModal: DeStreamProgressModal,
    systemAdminSettingsPages: DeSystemAdminSettingsPages,
    systemOtherSettingsPages: DeSystemOtherSettingsPages,
    uploadModal: DeUploadModal,
    workspaceSettings: DeWorkspaceSettings,
    workspaceViewAndButtons: DeWorkspaceViewAndButtons,
    workspaceSidebar: DeWorkspaceSidebar,
  },
  sv: {
    answerUpgrade: SvAnswerUpgrade,
    cdbRelatedKeys: SvCdbRelatedKeys,
    common: SvCommon,
    customLegalTemplates: SvCustomLegalTemplates,
    dataConnectors: SvDataConnectors,
    ddModuleKeys: SvDdModuleKeys,
    docxEditor: SvDocxEditor,
    embeddingModelKeys: SvEmbeddingModelKeys,
    feedbackFunction: SvFeedbackFunction,
    homePage: SvHomePage,
    legalTemplates: SvLegalTemplates,
    llmComponents: SvLlmComponents,
    loginPage: SvLoginPage,
    modalRelatedKeys: SvModalRelatedKeys,
    onboarding: SvOnboarding,
    privacyKeys: SvPrivacyKeys,
    recentUploads: SvRecentUploads,
    rexor: SvRexor,
    settings: SvSettings,
    showToast: SvShowToast,
    streamProgressModal: SvStreamProgressModal,
    systemAdminSettingsPages: SvSystemAdminSettingsPages,
    systemOtherSettingsPages: SvSystemOtherSettingsPages,
    uploadModal: SvUploadModal,
    workspaceSettings: SvWorkspaceSettings,
    workspaceViewAndButtons: SvWorkspaceViewAndButtons,
    workspaceSidebar: SvWorkspaceSidebar,
  },
  rw: {
    answerUpgrade: RwAnswerUpgrade,
    cdbRelatedKeys: RwCdbRelatedKeys,
    common: RwCommon,
    customLegalTemplates: RwCustomLegalTemplates,
    dataConnectors: RwDataConnectors,
    ddModuleKeys: RwDdModuleKeys,
    docxEditor: RwDocxEditor,
    embeddingModelKeys: RwEmbeddingModelKeys,
    feedbackFunction: RwFeedbackFunction,
    homePage: RwHomePage,
    legalTemplates: RwLegalTemplates,
    llmComponents: RwLlmComponents,
    loginPage: RwLoginPage,
    modalRelatedKeys: RwModalRelatedKeys,
    onboarding: RwOnboarding,
    privacyKeys: RwPrivacyKeys,
    recentUploads: RwRecentUploads,
    rexor: RwRexor,
    settings: RwSettings,
    showToast: RwShowToast,
    streamProgressModal: RwStreamProgressModal,
    systemAdminSettingsPages: RwSystemAdminSettingsPages,
    systemOtherSettingsPages: RwSystemOtherSettingsPages,
    uploadModal: RwUploadModal,
    workspaceSettings: RwWorkspaceSettings,
    workspaceViewAndButtons: RwWorkspaceViewAndButtons,
    workspaceSidebar: RwWorkspaceSidebar,
  },
  no: {
    answerUpgrade: NoAnswerUpgrade,
    cdbRelatedKeys: NoCdbRelatedKeys,
    common: NoCommon,
    customLegalTemplates: NoCustomLegalTemplates,
    dataConnectors: NoDataConnectors,
    ddModuleKeys: NoDdModuleKeys,
    docxEditor: NoDocxEditor,
    embeddingModelKeys: NoEmbeddingModelKeys,
    feedbackFunction: NoFeedbackFunction,
    homePage: NoHomePage,
    legalTemplates: NoLegalTemplates,
    llmComponents: NoLlmComponents,
    loginPage: NoLoginPage,
    modalRelatedKeys: NoModalRelatedKeys,
    onboarding: NoOnboarding,
    privacyKeys: NoPrivacyKeys,
    recentUploads: NoRecentUploads,
    rexor: NoRexor,
    settings: NoSettings,
    showToast: NoShowToast,
    streamProgressModal: NoStreamProgressModal,
    systemAdminSettingsPages: NoSystemAdminSettingsPages,
    systemOtherSettingsPages: NoSystemOtherSettingsPages,
    uploadModal: NoUploadModal,
    workspaceSettings: NoWorkspaceSettings,
    workspaceViewAndButtons: NoWorkspaceViewAndButtons,
    workspaceSidebar: NoWorkspaceSidebar,
  },
  pl: {
    answerUpgrade: PlAnswerUpgrade,
    cdbRelatedKeys: PlCdbRelatedKeys,
    common: PlCommon,
    customLegalTemplates: PlCustomLegalTemplates,
    dataConnectors: PlDataConnectors,
    ddModuleKeys: PlDdModuleKeys,
    docxEditor: PlDocxEditor,
    embeddingModelKeys: PlEmbeddingModelKeys,
    feedbackFunction: PlFeedbackFunction,
    homePage: PlHomePage,
    legalTemplates: PlLegalTemplates,
    llmComponents: PlLlmComponents,
    loginPage: PlLoginPage,
    modalRelatedKeys: PlModalRelatedKeys,
    onboarding: PlOnboarding,
    privacyKeys: PlPrivacyKeys,
    recentUploads: PlRecentUploads,
    rexor: PlRexor,
    settings: PlSettings,
    showToast: PlShowToast,
    streamProgressModal: PlStreamProgressModal,
    systemAdminSettingsPages: PlSystemAdminSettingsPages,
    systemOtherSettingsPages: PlSystemOtherSettingsPages,
    uploadModal: PlUploadModal,
    workspaceSettings: PlWorkspaceSettings,
    workspaceViewAndButtons: PlWorkspaceViewAndButtons,
    workspaceSidebar: PlWorkspaceSidebar,
  },
};

// Function to merge all locale files for a language
function mergeLocaleFiles(languageFiles) {
  let merged = {};
  for (const [fileName, fileContent] of Object.entries(languageFiles)) {
    console.log(`[i18n] Merging ${fileName}:`, Object.keys(fileContent));
    merged = mergeDeep(merged, fileContent);
  }
  return merged;
}

// Create merged resources for each language
console.log("[i18n] Creating merged locale resources");
export const resources = {};

for (const [langCode, files] of Object.entries(localeFiles)) {
  console.log(`[i18n] Processing ${langCode} locale files`);
  const mergedLocale = mergeLocaleFiles(files);
  resources[langCode] = { common: mergedLocale };
  console.log(`[i18n] ${langCode} merged keys:`, Object.keys(mergedLocale));
}

export const defaultNS = "common";

export const supportedLanguageCodes = [
  "en",
  "fr",
  "sv",
  "rw",
  "de",
  "no",
  "pl",
];

export const loadLanguageAsync = async (language) => {
  // Return static bundle for the language or null if default
  if (language === "en") return null;
  const bundle = resources[language]?.common || null;
  return bundle;
};

export const initializeLanguageAsync = async () => {
  const userLanguage = localStorage.getItem("language");

  if (userLanguage && userLanguage !== "en") {
    const resourceBundle = await loadLanguageAsync(userLanguage);
    if (resourceBundle) return userLanguage;
  } else if (userLanguage === "en") {
    return "en";
  }

  try {
    const response = await fetch(
      `${window.location.origin}/api/setup-complete`
    );

    if (response.ok) {
      const data = await response.json();
      const adminDefaultLang = data?.results?.language;

      if (adminDefaultLang && adminDefaultLang !== "en") {
        const resourceBundle = await loadLanguageAsync(adminDefaultLang);
        if (resourceBundle) {
          localStorage.setItem("language", adminDefaultLang);
          return adminDefaultLang;
        }
      } else if (adminDefaultLang === "en") {
        localStorage.setItem("language", "en");
        return "en";
      }
    }
  } catch (error) {
    console.error("Failed to fetch admin language setting", error);
  }

  localStorage.setItem("language", "en");
  return "en";
};
