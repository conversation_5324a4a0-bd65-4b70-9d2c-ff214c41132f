// Looking for a language to translate IST Legal to?
// Create a `common.js` file in the language's ISO code https://www.w3.org/International/O-charset-lang.html
// eg: Spanish => es/common.js
// eg: French => fr/common.js
// You should copy the en/common.js file as your template and just translate every string in there.
// By default, we try to see what the browsers native language is set to and use that. If a string
// is not defined or is null in the translation file, it will fallback to the value in the en/common.js file
// RULES:
// The EN translation file is the ground-truth for what keys and options are available. DO NOT add a special key
// to a specific language file as this will break the other languages. Any new keys should be added to english
// and the language file you are working on.

// Contributor Notice: If you are adding a translation you MUST locally run `npm run verify:translations` from the root prior to PR.
// please do not submit PR's without first verifying this test passes as it will tell you about missing keys or values
// from the primary dictionary.

// Import all English locale files
import EnglishAnswerUpgrade from "./en/answerUpgrade.js";
import EnglishCommon from "./en/common.js";
import EnglishCustomLegalTemplates from "./en/customLegalTemplates.js";
import EnglishLegalTemplates from "./en/legalTemplates.js";
import EnglishLlmComponents from "./en/llmComponents.js";
import EnglishRecentUploads from "./en/recentUploads.js";
import EnglishSettings from "./en/settings.js";
import EnglishShowToast from "./en/showToast.js";
import EnglishSystemSettingsPages from "./en/systemSettingsPages.js";

// Import all French locale files
import FrAnswerUpgrade from "./fr/answerUpgrade.js";
import FrCommon from "./fr/common.js";
import FrCustomLegalTemplates from "./fr/customLegalTemplates.js";
import FrLegalTemplates from "./fr/legalTemplates.js";
import FrLlmComponents from "./fr/llmComponents.js";
import FrRecentUploads from "./fr/recentUploads.js";
import FrSettings from "./fr/settings.js";
import FrShowToast from "./fr/showToast.js";
import FrSystemSettingsPages from "./fr/systemSettingsPages.js";

// Import all German locale files
import DeAnswerUpgrade from "./de/answerUpgrade.js";
import DeCommon from "./de/common.js";
import DeCustomLegalTemplates from "./de/customLegalTemplates.js";
import DeLegalTemplates from "./de/legalTemplates.js";
import DeLlmComponents from "./de/llmComponents.js";
import DeRecentUploads from "./de/recentUploads.js";
import DeSettings from "./de/settings.js";
import DeShowToast from "./de/showToast.js";
import DeSystemSettingsPages from "./de/systemSettingsPages.js";

// Import all Swedish locale files
import SvAnswerUpgrade from "./sv/answerUpgrade.js";
import SvCommon from "./sv/common.js";
import SvCustomLegalTemplates from "./sv/customLegalTemplates.js";
import SvLegalTemplates from "./sv/legalTemplates.js";
import SvLlmComponents from "./sv/llmComponents.js";
import SvRecentUploads from "./sv/recentUploads.js";
import SvSettings from "./sv/settings.js";
import SvShowToast from "./sv/showToast.js";
import SvSystemSettingsPages from "./sv/systemSettingsPages.js";

// Import all Kinyarwanda locale files
import RwAnswerUpgrade from "./rw/answerUpgrade.js";
import RwCommon from "./rw/common.js";
import RwCustomLegalTemplates from "./rw/customLegalTemplates.js";
import RwLegalTemplates from "./rw/legalTemplates.js";
import RwLlmComponents from "./rw/llmComponents.js";
import RwRecentUploads from "./rw/recentUploads.js";
import RwSettings from "./rw/settings.js";
import RwShowToast from "./rw/showToast.js";
import RwSystemSettingsPages from "./rw/systemSettingsPages.js";

// Import all Norwegian locale files
import NoAnswerUpgrade from "./no/answerUpgrade.js";
import NoCommon from "./no/common.js";
import NoCustomLegalTemplates from "./no/customLegalTemplates.js";
import NoLegalTemplates from "./no/legalTemplates.js";
import NoLlmComponents from "./no/llmComponents.js";
import NoRecentUploads from "./no/recentUploads.js";
import NoSettings from "./no/settings.js";
import NoShowToast from "./no/showToast.js";
import NoSystemSettingsPages from "./no/systemSettingsPages.js";

// Import all Polish locale files
import PlAnswerUpgrade from "./pl/answerUpgrade.js";
import PlCommon from "./pl/common.js";
import PlCustomLegalTemplates from "./pl/customLegalTemplates.js";
import PlLegalTemplates from "./pl/legalTemplates.js";
import PlLlmComponents from "./pl/llmComponents.js";
import PlRecentUploads from "./pl/recentUploads.js";
import PlSettings from "./pl/settings.js";
import PlShowToast from "./pl/showToast.js";
import PlSystemSettingsPages from "./pl/systemSettingsPages.js";

function mergeDeep(target, source) {
  for (const key of Object.keys(source)) {
    const value = source[key];
    if (value && typeof value === "object" && !Array.isArray(value)) {
      if (!target[key]) target[key] = {};
      mergeDeep(target[key], value);
    } else {
      target[key] = value;
    }
  }
  return target;
}

// Define locale file groups for each language
const localeFiles = {
  en: {
    answerUpgrade: EnglishAnswerUpgrade,
    common: EnglishCommon,
    customLegalTemplates: EnglishCustomLegalTemplates,
    legalTemplates: EnglishLegalTemplates,
    llmComponents: EnglishLlmComponents,
    recentUploads: EnglishRecentUploads,
    settings: EnglishSettings,
    showToast: EnglishShowToast,
    systemSettingsPages: EnglishSystemSettingsPages,
  },
  fr: {
    answerUpgrade: FrAnswerUpgrade,
    common: FrCommon,
    customLegalTemplates: FrCustomLegalTemplates,
    legalTemplates: FrLegalTemplates,
    llmComponents: FrLlmComponents,
    recentUploads: FrRecentUploads,
    settings: FrSettings,
    showToast: FrShowToast,
    systemSettingsPages: FrSystemSettingsPages,
  },
  de: {
    answerUpgrade: DeAnswerUpgrade,
    common: DeCommon,
    customLegalTemplates: DeCustomLegalTemplates,
    legalTemplates: DeLegalTemplates,
    llmComponents: DeLlmComponents,
    recentUploads: DeRecentUploads,
    settings: DeSettings,
    showToast: DeShowToast,
    systemSettingsPages: DeSystemSettingsPages,
  },
  sv: {
    answerUpgrade: SvAnswerUpgrade,
    common: SvCommon,
    customLegalTemplates: SvCustomLegalTemplates,
    legalTemplates: SvLegalTemplates,
    llmComponents: SvLlmComponents,
    recentUploads: SvRecentUploads,
    settings: SvSettings,
    showToast: SvShowToast,
    systemSettingsPages: SvSystemSettingsPages,
  },
  rw: {
    answerUpgrade: RwAnswerUpgrade,
    common: RwCommon,
    customLegalTemplates: RwCustomLegalTemplates,
    legalTemplates: RwLegalTemplates,
    llmComponents: RwLlmComponents,
    recentUploads: RwRecentUploads,
    settings: RwSettings,
    showToast: RwShowToast,
    systemSettingsPages: RwSystemSettingsPages,
  },
  no: {
    answerUpgrade: NoAnswerUpgrade,
    common: NoCommon,
    customLegalTemplates: NoCustomLegalTemplates,
    legalTemplates: NoLegalTemplates,
    llmComponents: NoLlmComponents,
    recentUploads: NoRecentUploads,
    settings: NoSettings,
    showToast: NoShowToast,
    systemSettingsPages: NoSystemSettingsPages,
  },
  pl: {
    answerUpgrade: PlAnswerUpgrade,
    common: PlCommon,
    customLegalTemplates: PlCustomLegalTemplates,
    legalTemplates: PlLegalTemplates,
    llmComponents: PlLlmComponents,
    recentUploads: PlRecentUploads,
    settings: PlSettings,
    showToast: PlShowToast,
    systemSettingsPages: PlSystemSettingsPages,
  },
};

// Function to merge all locale files for a language
function mergeLocaleFiles(languageFiles) {
  let merged = {};
  for (const [fileName, fileContent] of Object.entries(languageFiles)) {
    console.log(`[i18n] Merging ${fileName}:`, Object.keys(fileContent));
    merged = mergeDeep(merged, fileContent);
  }
  return merged;
}

// Create merged resources for each language
console.log("[i18n] Creating merged locale resources");
export const resources = {};

for (const [langCode, files] of Object.entries(localeFiles)) {
  console.log(`[i18n] Processing ${langCode} locale files`);
  const mergedLocale = mergeLocaleFiles(files);
  resources[langCode] = { common: mergedLocale };
  console.log(`[i18n] ${langCode} merged keys:`, Object.keys(mergedLocale));
}

export const defaultNS = "common";

export const supportedLanguageCodes = [
  "en",
  "fr",
  "sv",
  "rw",
  "de",
  "no",
  "pl",
];

export const loadLanguageAsync = async (language) => {
  // Return static bundle for the language or null if default
  if (language === "en") return null;
  const bundle = resources[language]?.common || null;
  return bundle;
};

export const initializeLanguageAsync = async () => {
  const userLanguage = localStorage.getItem("language");

  if (userLanguage && userLanguage !== "en") {
    const resourceBundle = await loadLanguageAsync(userLanguage);
    if (resourceBundle) return userLanguage;
  } else if (userLanguage === "en") {
    return "en";
  }

  try {
    const response = await fetch(
      `${window.location.origin}/api/setup-complete`
    );

    if (response.ok) {
      const data = await response.json();
      const adminDefaultLang = data?.results?.language;

      if (adminDefaultLang && adminDefaultLang !== "en") {
        const resourceBundle = await loadLanguageAsync(adminDefaultLang);
        if (resourceBundle) {
          localStorage.setItem("language", adminDefaultLang);
          return adminDefaultLang;
        }
      } else if (adminDefaultLang === "en") {
        localStorage.setItem("language", "en");
        return "en";
      }
    }
  } catch (error) {
    console.error("Failed to fetch admin language setting", error);
  }

  localStorage.setItem("language", "en");
  return "en";
};
