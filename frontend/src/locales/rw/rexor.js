export default {
  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Iyandikishe umushinga wa Rexor",
    "project-id": "ID y'umushinga",
    "resource-id": "ID y'ibikoresho",
    "activity-id": "ID y'igikorwa",
    register: "Iyandikishe umushinga",
    "invoice-text": "Umubare w'ishakisha rya Foynet",
    registering: "<PERSON><PERSON><PERSON> kwandikwa...",
    "not-active": "<PERSON>yu mwanya nturimo gukora kugirango wandikwe",
    account: {
      title: "Injira muri Rexor",
      username: "<PERSON><PERSON><PERSON> ryo kwinjira",
      password: "Ijambo ry'ibanga",
      "no-token": "Nta token yakiriwe muri handleLoginSuccess",
      logout: "So<PERSON>ka",
      "no-user": "Nyamuneka injira mbere",
      connected: "Wahurijwe na Rexor",
      "not-connected": "Ntabwo wahurijwe",
      "change-account": "Hindura konti",
      "session-expired": "Igihe cyarangiye. Nyamuneka ongera winjire.",
    },
    "hide-article-transaction": "Hisha ifishi y'igihe",
    "show-article-transaction": "Erekana ifishi y'igihe",
    "article-transaction-title": "Ongeraho igikorwa cy'igihe",
    "registration-date": "Itariki yo kwandikwa",
    description: "Ibisobanuro",
    "description-internal": "Ibisobanuro by'imbere",
    "hours-worked": "Amasaha yakoze",
    "invoiced-hours": "Amasaha yishyuwe",
    invoiceable: "Bishobora kwishyurwa",
    "sending-article-transaction": "Kohereza igikorwa cy'igihe...",
    "save-article-transaction": "Bika igikorwa cy'igihe",
    "project-not-register": "Umushinga ugomba kwandikwa mbere.",
    "article-transaction-error": "Ntibishobotse kwandika igikorwa cy'igihe",
    "not-exist": "Iki kibazo nticyashoboye kuboneka",
  },
};
