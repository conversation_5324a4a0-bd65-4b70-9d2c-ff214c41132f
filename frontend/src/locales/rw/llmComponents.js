export default {
  // =========================
  // GENERIC PROVIDER SELECTION SETTINGS
  // =========================
  generic: {
    "base-url": "URL y'ibanze",
    "api-key": "Urufunguzo rwa API",
    "api-key-placeholder": "Injiza urufunguzo rwa API yawe",
    "chat-model": "Modeli y'ibiganiro",
    "chat-model-placeholder": "Injiza izina rya modeli y'ibiganiro",
    "token-window": "Idirishya ry'ibimenyetso",
    "token-window-placeholder": "<PERSON>ubare ntarengwa w'ibimenyetso (nko: 4096)",
    "max-tokens": "Umubare ntarengwa wa tokens",
    "max-tokens-placeholder": "Umubare ntarengwa wa tokens (nko: 1024)",
    "embedding-deployment": "Izina rya Embedding Deployment",
    "embedding-deployment-placeholder":
      "Izina rya deployment ya modeli yo kwinjiza ya Azure OpenAI",
    "embedding-model": "Modeli yo kwinjiza",
    "embedding-model-placeholder": "Injiza modeli yo kwinjiza",
    "max-embedding-chunk-length": "Uburebure ntarengwa bw'igice cyo kwinjiza",
    saving: "Birimo kubika...",
    "save-changes": "Bika impinduka",
    "workspace-update-error": "Ikosa: {{error}}",
    "base-url-placeholder": "nko: https://proxy.openai.com",
    "password-mask-length": "12",
  },

  // =========================
  // GENERIC MODEL SELECTION SETTINGS
  // =========================
  model: {
    selection: "Guhitamo modeli y'ibiganiro",
    "embedding-selection": "Guhitamo modeli yo kwinjiza",
    "enter-api-key":
      "Injiza urufunguzo rwa API rwemewe kugirango urebe modeli zose.",
    "enter-url": "Banza ushyireho URL",
    "your-models": "Modeli zawe zashyizweho",
    "available-models": "Modeli ziboneka",
  },

  // =========================
  // ANTHROPIC
  // =========================
  anthropic: {
    "api-key": "Urufunguzo rwa API rwa Anthropic",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa Anthropic",
    "model-selection": "Guhitamo modeli y'ibiganiro",
  },

  // =========================
  // AZURE SETTINGS
  // =========================
  azure: {
    "service-endpoint": "Umuyoboro wa serivisi ya Azure",
    "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
    "api-key": "Urufunguzo rwa API",
    "api-key-placeholder": "Urufunguzo rwa API ya Azure OpenAI",
    "chat-deployment": "Izina rya Chat Deployment",
    "chat-deployment-placeholder":
      "Izina rya deployment ya modeli y'ibiganiro ya Azure OpenAI",
    "token-limit": "Umubare ntarengwa wa tokens wa modeli y'ibiganiro",
  },
  azureai: {
    "service-endpoint": "Umuyoboro wa serivisi ya Azure AI",
    "api-key": "Urufunguzo rwa API",
    "api-key-placeholder": "Urufunguzo rwa API ya Azure OpenAI",
    "embedding-deployment-name": "Izina rya Embedding Deployment",
    "embedding-deployment-name-placeholder":
      "Injiza izina rya embedding deployment",
  },

  // =========================
  // AWS BEDROCK SETTINGS
  // =========================
  bedrock: {
    "iam-warning":
      "Ugomba gukoresha umukoresha wa IAM uteguye neza kuri inferencing.",
    "read-more":
      "Soma byinshi kuri uko gukoresha AWS Bedrock kuri iyi instance",
    "access-id": "ID yo kwinjira ya AWS Bedrock IAM",
    "access-id-placeholder": "ID yo kwinjira y'umukoresha wa AWS Bedrock IAM",
    "access-key": "Urufunguzo rwo kwinjira rwa AWS Bedrock IAM",
    "access-key-placeholder":
      "Urufunguzo rwo kwinjira rw'umukoresha wa AWS Bedrock IAM",
    region: "Akarere ka AWS",
    "model-id": "ID ya modeli",
    "model-id-placeholder": "ID ya modeli ya AWS nko: meta.llama3.1-v0.1",
    "context-window": "Idirishya ry'imiterere ya modeli",
    "context-window-placeholder": "nko: 4096",
  },

  // =========================
  // COHERE
  // =========================
  cohere: {
    "api-key": "Urufunguzo rwa API rwa Cohere",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa Cohere",
    "model-preference": "Ihitamo rya modeli",
    "model-selection": "Guhitamo modeli",
  },

  // =========================
  // DEEPSEEK LLM SETTINGS
  // =========================
  deepseek: {
    "api-key": "Urufunguzo rwa API rwa DeepSeek",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa DeepSeek",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
  },

  // =========================
  // FIREWORKSAI LLM SELECTION
  // =========================
  fireworksai: {
    "api-key": "Urufunguzo rwa API rwa FireworksAI",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa FireworksAI",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
  },

  // =========================
  // GEMINI LLM OPTIONS
  // =========================
  gemini: {
    "api-key": "Urufunguzo rwa API rwa Google AI",
    "api-key-placeholder": "Urufunguzo rwa API rwa Google Gemini",
    "model-selection": "Guhitamo modeli",
    "loading-models": "Kurimo gutangiza modeli...",
    "manual-options": "Amahitamo y'intoki",
    "safety-setting": "Igenamiterere ry'umutekano",
    experimental: "Igerageza",
    stable: "Yarangiye",
    "safety-options": {
      none: "Nta na kimwe (mburabuzi)",
      "block-few": "Kubuza gusa ibyago byinshi",
      "block-some": "Kubuza ibyago bya hagati n'ibyinshi",
      "block-most": "Kubuza ibintu byinshi",
    },
  },

  // =========================
  // GROQ LLM SELECTION
  // =========================
  groq: {
    "api-key": "Urufunguzo rwa API rwa Groq",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa Groq",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
    "enter-api-key":
      "Injiza urufunguzo rwa API rwemewe kugirango ubone modeli zose.",
    "available-models": "Modeli ziboneka",
    "model-description": "Hitamo modeli ya GroqAI ukoresha.",
  },

  // =========================
  // HUGGINGFACE LLM SETTINGS
  // =========================
  huggingface: {
    "inference-endpoint": "Umuyoboro w'igitekerezo cya HuggingFace",
    "endpoint-placeholder": "https://example.endpoints.huggingface.cloud",
    "access-token": "Ikimenyetso cyo kwinjira cya HuggingFace",
    "token-placeholder": "Injiza ikimenyetso cyo kwinjira cya HuggingFace",
    "token-limit": "Umubare ntarengwa wa tokens wa modeli",
    "token-limit-placeholder": "4096",
  },

  // =========================
  // KOBOLDCPP SETTINGS
  // =========================
  koboldcpp: {
    "show-advanced": "Erekana uburyo bwo kwinjira intoki",
    "hide-advanced": "Hisha uburyo bwo kwinjira intoki",
    "base-url": "URL y'ibanze ya KoboldCPP",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "base-url-desc": "Andika URL aho KoboldCPP ikorera.",
    "auto-detect": "Kubona byikora",
    "token-context-window": "Idirishya ry'imiterere ya tokens",
    "token-window-placeholder": "4096",
    "token-window-desc": "Umubare ntarengwa wa tokens kuri imiterere.",
    model: "Modeli ya KoboldCPP",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
    "enter-url": "Injiza URL ya KoboldCPP",
    "model-desc":
      "Hitamo modeli ya KoboldCPP ukoresha. Izagaragara nyuma yo kwandika URL nziza.",
    "model-choose": "Hitamo modeli ya KoboldCPP ukoresha",
  },

  // =========================
  // LITELLM
  // =========================
  litellm: {
    "model-tooltip": "Hitamo modeli yo kwinjiza iboneye. Reba",
    "model-tooltip-link": "iyi paji",
    "model-tooltip-more": "kugirango umenye byinshi",
    "base-url": "URL y'ibanze",
    "base-url-placeholder": "urugero: https://proxy.openai.com",
    "max-embedding-chunk-length": "Uburebure ntarengwa bw'igice cyo kwinjiza",
    "token-window": "Idirishya ry'imiterere ya tokens",
    "token-window-placeholder": "Umubare ntarengwa w'idirishya (urugero: 4096)",
    "api-key": "Urufunguzo rwa API",
    optional: "bishoboka",
    "api-key-placeholder": "sk-mysecretkey",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
    "waiting-url": "-- tegereza URL --",
    "loaded-models": "Modeli zawe zashyizweho",
    "manage-embedding": "Cunga serivisi yo kwinjiza",
    "embedding-required":
      "Litellm isaba ko serivisi yo kwinjiza ishyirwaho kugirango ikoreshwe.",
  },

  // =========================
  // LMSTUDIO LLM SELECTION
  // =========================
  lmstudio: {
    "max-tokens": "Umubare ntarengwa wa tokens",
    "max-tokens-desc": "Umubare ntarengwa wa tokens ku mwirondoro n'igisubizo.",
    "show-advanced": "Erekana ibikoresho by'inyongera",
    "hide-advanced": "Hisha ibikoresho by'inyongera",
    "base-url": "URL y'ibanze ya LM Studio",
    "base-url-placeholder": "http://localhost:1234/v1",
    "base-url-desc": "Andika URL aho LM Studio ikorera.",
    "auto-detect": "Kubona byikora",
    model: "Modeli ya LM Studio",
    "model-loading": "-- kurimo gutangiza modeli ziboneka --",
    "model-url-first": "Andika URL ya LM Studio mbere",
    "model-desc":
      "Hitamo modeli ya LM Studio ushaka gukoresha. Modeli zizagaragara nyuma yo kwandika URL nyayo ya LM Studio.",
    "model-choose": "Hitamo modeli ya LM Studio ukoresha mu biganiro byawe.",
    "model-loaded": "Modeli zawe zaratangajwe",
    "embedding-required":
      "LMStudio nka LLM yawe isaba ko ushyiraho serivisi yo kwinjiza.",
    "manage-embedding": "Cunga serivisi yo kwinjiza",
    "max-embedding-chunk-length": "Uburebure ntarengwa bw'igice cyo kwinjiza",
  },

  // =========================
  // LOCALAI LLM SELECTION
  // =========================
  localai: {
    "token-window": "Idirishya ry'ibimenyetso",
    "token-window-placeholder": "4096",
    "api-key": "Urufunguzo rwa API ya Local AI",
    "api-key-optional": "bishoboka",
    "api-key-placeholder": "sk-mysecretkey",
    "show-advanced": "Erekana amagenamiterere y'inyongera",
    "hide-advanced": "Hisha amagenamiterere y'inyongera",
    "base-url": "URL y'ibanze ya Local AI",
    "base-url-placeholder": "http://localhost:8080/v1",
    "base-url-help": "Andika URL aho LocalAI ikorera.",
    "auto-detect": "Kubona byikora",
    "max-embedding-chunk-length": "Uburebure ntarengwa bw'igice cyo kwinjiza",
    "embedding-required": "Kwinjiza birakenewe kuri LocalAI.",
    "manage-embedding": "Cunga serivisi yo kwinjiza",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
    "waiting-url": "-- tegereza URL --",
    "loaded-models": "Modeli zawe zishyizweho",
  },

  // =========================
  // MISTRAL LLM OPTIONS
  // =========================
  mistral: {
    "api-key": "Urufunguzo rwa API rwa Mistral",
    "api-key-placeholder": "Urufunguzo rwa API rwa Mistral",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
    "waiting-key": "-- tegereza urufunguzo rwa API --",
    "available-models": "Modeli za Mistral ziboneka",
  },

  // =========================
  // NATIVE LLM SETTINGS
  // =========================

  native: {
    "experimental-warning":
      "Gukoresha LLM iri mu gace ni igerageza. Ishobora kudakora nk'uko wabyiteze.",
    "model-desc": "Hitamo modeli iri mu gace ushaka gukoresha.",
    "token-desc": "Umubare ntarengwa wa tokens kuri imiterere n'igisubizo.",
  },

  // =========================
  // OLLAMA LLM SELECTION
  // =========================

  ollamallmselection: {
    "max-tokens": "Umubare ntarengwa wa tokens",
    "max-tokens-desc":
      "Umubare ntarengwa wa tokens kuri imiterere n'igisubizo.",
    "show-advanced": "Erekana uburyo bwo kwinjira intoki",
    "hide-advanced": "Hisha uburyo bwo kwinjira intoki",
    "base-url": "URL y'ibanze ya Ollama",
    "base-url-placeholder": "http://127.0.0.1:11434",
    "base-url-desc": "Andika URL aho Ollama ikorera.",
    "auto-detect": "Kubona byikora",
    "keep-alive": "Ollama komeza muzima",
    "no-cache": "Nta cache",
    "five-minutes": "Iminota 5",
    "one-hour": "Isaha 1",
    forever: "Iteka",
    "keep-alive-desc": "Bika modeli mu memory.",
    "learn-more": "Menya byinshi",
    "performance-mode": "Uburyo bw'imikorere",
    "base-default": "Ibanze (Mburabuzi)",
    maximum: "Ntarengwa",
    "performance-mode-desc": "Hitamo uburyo bw'imikorere.",
    note: "Icyitonderwa:",
    "maximum-warning": "Uburyo bw'ntarengwa bukoresha umutungo mwinshi.",
    base: "Ibanze",
    "base-desc": "Uburyo bw'ibanze ni mburabuzi.",
    "maximum-desc":
      "Uburyo bw'ntarengwa butanga imikorere myinshi, ukoresha idirishya ryose ry'imiterere kugeza kuri tokens ntarengwa.",
    model: "Modeli ya Ollama",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
    "enter-url": "Andika URL ya Ollama",
    "model-desc":
      "Hitamo modeli ya Ollama ukoresha. Modeli zizaboneka nyuma yo kwandika URL.",
    "model-choose": "Hitamo modeli ya Ollama ukoresha.",
  },

  // =========================
  // OPENAI LLM SELECTION
  // =========================
  openai: {
    "api-key": "Urufunguzo rwa API rwa OpenAI",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa OpenAI",
    "model-preference": "Ihitamo rya Modeli",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
    "model-divider": "──────────",
  },

  // =========================
  // OPENROUTER LLM SELECTION
  // =========================
  openrouter: {
    "api-key": "Urufunguzo rwa API rwa OpenRouter",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa OpenRouter",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
  },

  // =========================
  // PERPLEXITY LLM SELECTION
  // =========================
  perplexity: {
    "api-key": "Urufunguzo rwa API rwa Perplexity",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa Perplexity",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
    "available-models": "Modeli za Perplexity ziboneka",
  },

  // =========================
  // TOGETHERAI
  // =========================
  togetherai: {
    "api-key": "Urufunguzo rwa API rwa TogetherAI",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa TogetherAI",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
  },

  // =========================
  // XAI
  // =========================
  xai: {
    "api-key": "Urufunguzo rwa API rwa xAI",
    "api-key-placeholder": "Urufunguzo rwa API rwa xAI",
    "model-selection": "Guhitamo modeli y'ibiganiro",
    "loading-models": "-- kurimo gutangiza modeli ziboneka --",
    "enter-api-key":
      "Injiza urufunguzo rwa API rwemewe kugirango ubone modeli zose.",
    "available-models": "Modeli ziboneka",
    "model-description": "Hitamo modeli ya xAI ukoresha.",
  },
};
