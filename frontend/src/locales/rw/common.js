const TRANSLATIONS = {
  // =========================
  // COMMON STRINGS & PLACEHOLDERS
  // =========================
  common: {
    examples: "Ingero",
    "workspaces-name": "<PERSON><PERSON>a ry'umwanya w'akazi",
    ok: "OK",
    error: "ikosa",
    confirm: "Emeza",
    confirmstart: "Emeza hanyuma utangire",
    savesuccess: "Igenamiterere ryabitswe neza",
    saveerror: "Kubika igenamiterere byanze",
    success: "ibyatunganye",
    user: "<PERSON>koresh<PERSON>",
    selection: "Ihitamo ry'icyitegererezo",
    saving: "Kubika...",
    save: "Kubika impinduka",
    previous: "Paji ibanza",
    next: "Paji ikurikira",
    cancel: "<PERSON>reka",
    timeframes: "Ibihe",
    other: "<PERSON><PERSON><PERSON> ngingo",
    "search-placeholder": "<PERSON>hak<PERSON><PERSON>...",
    "more-actions": "<PERSON><PERSON><PERSON> bi<PERSON>wa",
    "delete-message": "<PERSON><PERSON> ubutumwa",
    copy: "Ko<PERSON>",
    regenerate: "Ongera ukore",
    "export-word": "Ohereza muri Word",
    "stop-generating": "Hagarika gukora",
    "attach-file": "Shyiraho dosiye kuri iyi chat",
    home: "Ahabanza",
    settings: "Igenamiterere",
    support: "Ubufasha",
    "clear-reference": "Siba referanse",
    "send-message": "Ohereza ubutumwa",
    "ask-legal": "Saba amakuru y'amategeko",
    "stop-response": "Hagarika gukora igisubizo",
    "contact-support": "Vugisha Ubufasha",
    "copy-connection": "Kopi umurongo wo guhuza",
    "auto-connect": "Huza byikora ku mugereka",
    back: "Gusubira inyuma",
    off: "Cye",
    on: "Kuri",
    continue: "Gukomeza",
    rename: "Guhindura izina",
    delete: "Gusiba",
    "default-skill": "Uku gushobora kwa mbere kwemewe ntikugomba gucaho.",
    placeholder: {
      username: "Izina ry'umukoresha",
      password: "Ijambo ry'ibanga",
      email: "Injiza aderesi imeri",
      "support-email": "<EMAIL>",
      website: "https://www.example.com",
      "site-name": "IST Legal",
      "search-llm": "Shakisha utanga serivisi ya LLM wihariye",
      "search-providers": "Shakisha abatanga serivisi",
      "message-heading": "Umutwe w'ubutumwa",
      "message-content": "Ubutumwa",
      "token-limit": "4096",
      "max-tokens": "Umubare ntarengwa wa tokens ku cyifuzo (urugero: 1024)",
      "api-key": "Urufunguzo rwa API",
      "base-url": "URL y'ibanze",
      endpoint: "Icyerekezo cya API",
    },
    tooltip: {
      copy: "Koporora kuri clipboard",
      delete: "Siba iki kintu",
      edit: "Hindura iki kintu",
      save: "Bika impinduka",
      cancel: "Hagarika impinduka",
      search: "Shakisha ibintu",
      add: "Ongeraho ikintu gishya",
      remove: "Kuraho ikintu",
      upload: "Ohereza dosiye",
      download: "Kuramo dosiye",
      refresh: "Vugurura amakuru",
      settings: "Fungura igenamiterere",
      more: "Ibindi bikorwa",
    },
    "default.message": "Injiza ubutumwa bwawe hano",
    preview: "Foto",
    prompt: "Prompt",
    loading: "Kuvuga...",
    download: "Kuramo",
    open_in_new_tab: "Ouvrir dans un nouvel onglet",
    close: "Kuramo",
    note: "Icyitonderwa",
  },

  // =========================
  // RECENT UPLOADS COMPONENT
  // =========================

  // moved to recentUploads.js

  // =========================
  // CHAT BOX DRAG & DROP COMPONENT
  // =========================
  chatboxdnd: {
    title: "Ongeraho dosiye",
    description:
      "Rekura dosiye yawe hano kugira ngo uyongere kuri ubu butumwa. Ntizabikwa mu mwanya w'akazi nk'isoko ihoraho.",
    "file-prefix": "Dosiye:",
    "attachment-tooltip":
      "Iyi dosiye izomekwa ku butumwa bwawe. Ntizabikwa mu mwanya w'akazi nk'isoko ihoraho.",
    "uploaded-file-tag": "DOSIYE Y'UKORESHA YOHEREJWE",
  },

  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Urufunguzo rw'umwanya wa Confluence",
    "space-key-desc":
      "Uru ni urufunguzo rw'umwanya wa Confluence uzakoresha. Akenshi utangira na ~",
    "space-key-placeholder": "urugero: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "urugero: https://example.atlassian.net, http://localhost:8211, n'ibindi...",
    "token-tooltip": "Urashobora gukora urufunguzo rwa API",
    "token-tooltip-here": "hano",
  },

  // =========================
  // CONFIRMATION MESSAGES
  // =========================
  deleteWorkspaceConfirmation:
    "Urabyizeye ko ushaka gusiba {{name}}?\nNyuma y'ibi, ntabwo izaboneka muri iyi porogaramu.\n\nIbi ntibishobora guhindurwa.",
  deleteConfirmation:
    "Urabyizeye ko ushaka gusiba {{username}}?\nNyuma y'ibi, bazasohoka muri konti yabo kandi ntibazashobora kongera gukoresha iyi porogaramu.\n\nIbi ntibishobora guhindurwa.",
  suspendConfirmation:
    "Urabyizeye ko ushaka guhagarika {{username}}?\nNyuma y'ibi, bazasohoka muri konti yabo kandi ntibazashobora kongera kwinjira muri iyi porogaramu kugeza igihe umuyobozi azaba abemerera.\n\nIbi ntibishobora guhindurwa.",
  flushVectorCachesWorkspaceConfirmation:
    "Ese uri wemeza ko ushaka gusiba ububiko bw'igihe gito bwa vekiteri kuri iyi mbuga y'akazi?",
  apiKeys: {
    "deactivate-title": "Deactivate API Key",
    "deactivate-message":
      "Are you sure you want to deactivate this API key?\nAfter you do this it will not longer be useable.\n\nThis action is irreversible.",
  },

  // =========================
  // SETTINGS SIDEBAR MENU ITEMS
  // =========================
  settings: {
    title: "Igenamiterere rya Instance",
    system: "Igenamiterere rusange",
    invites: "Ubutumire",
    users: "Abakoresha",
    workspaces: "Ahantu ho gukorera",
    "workspace-chats": "Ibiganiro by'ahantu ho gukorera",
    customization: "Guhindura",
    "api-keys": "API y'Abakora",
    llm: "LLM",
    transcription: "Gushyira mu nyandiko",
    embedder: "Inkoranyabitabo",
    "text-splitting": "Gusiga no Gukata Ijambo",
    "vector-database": "Dabase y'Urutonde",
    embeds: "Gushyira mu Kiganiro",
    "embed-chats": "Inyandiko z'Ibiganiro",
    security: "Umutekano",
    "event-logs": "Inyandiko z'Ibyabaye",
    "privacy-data": "Ubwiru n'Amakuru",
    "ai-providers": "Abatanga AI",
    "agent-skills": "Ubushobozi bw'Agent",
    admin: "Admin",
    tools: "Ibikoresho",
    audio: "Amahitamo y'Amajwi",
    "link-settings": "Amagenamiterere",
    "default-settings": "Amagenamiterere y'Ibanze",
    "browser-extension": "Inyongera ya Browser",
    "prompt-upgrade-llm": "Kuzamura Prompt LLM",
    "template-llm": "LLM y'Icyitegererezo",
    "voice-speech": "Ijwi & Kuvuga",
    "pdr-settings": "Amagenamiterere ya PDR",
    "document-builder": "Ukora Inyandiko",
    "mcp-servers": "Seriveri za MCP",
    "chat-ui-settings": "Igenamiterere ry'Imiterere ya Chat",
    chat: "DD Prompt Settings", //TODO: translate
    deep_search: {
      title: "Amagenamiterere ya Gushakisha Kure",
      description:
        "Shiraho ubushobozi bwo gushakisha ku mbuga kugira ngo utange ibisubizo byiza. Iyo byemejwe, sisitemu ishobora gushakisha ku mbuga amakuru yo kunoza ibisubizo.",
    },
    "request-legal-assistance": "Kugenzura Ubuziranenge",
  },

  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Igenamiterere ry'Imiterere ya Chat",
    description: "Shiraho igenamiterere ya chat.",
    auto_submit: {
      title: "Kohereza byikora ibyo uvuze",
      description: "Kohereza byikora ibyo uvuze nyuma y'igihe cy'agacecetsi",
    },
    auto_speak: {
      title: "Gusoma byikora ibisubizo",
      description: "Soma byikora ibisubizo bya AI",
    },
  },

  // =========================
  // QURA BUTTONS
  // =========================
  qura: {
    "copy-to-cora": "Ohereza kuri Qura isoko isuzumwa",
    "qura-status": "Qura button iri kuri ",
    "copy-option": "Koporora amahitamo",
    "option-quest": "Ikibazo",
    "option-resp": "Igisubizo",
    "role-description": "Ongeraho buto ya Qura ku gusubiza kuri Qura.law",
  },

  // =========================
  // LOGIN & SIGN-IN PAGES
  // =========================
  login: {
    "multi-user": {
      welcome: "Murakaza neza kuri",
      "placeholder-username": "Aderesi Imeyili",
      "placeholder-password": "Ijambo ryibanga",
      login: "Injira",
      validating: "Gusuzuma...",
      "forgot-pass": "Wibagiwe ijambo ry'ibanga",
      "back-to-login": "Injira kuri konti yawe",
      "reset-password": "Subiramo",
      reset: "Subiramo",
      "reset-password-info":
        "Tanga amakuru akenewe hepfo kugirango usubire mu ijambo ryibanga ryawe.",
    },
    "sign-in": {
      start: "Injira kuri konti yawe",
      end: "konti.",
    },
    button: "injira",
    password: {
      forgot: "Wibagiwe ijambo ry'ibanga?",
      contact: "Nyamuneka hamagara umuyobozi wa sisitemu.",
    },
    publicMode: "Gerageza utagombye konti",
    logging: "Kwinjira...",
  },

  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Hitamo LLM ebyiri",
    "secondary-llm-toggle-description":
      "Emerera abashinzwe gutanga uburenganzira guhitamo hagati ya modeli ebyiri za LLM mu gikorwa cyo gutegura inyandiko.",
    "secondary-llm-toggle-status": "Imiterere: ",
    "secondary-llm-user-level": "Urwego rw'Abakoresha ba LLM ebyiri",
    "secondary-llm-user-level-description":
      "Emerera abakoresha bose guhitamo hagati ya modeli ebyiri za LLM mu buryo bwo gutegura inyandiko.",
  },

  // =========================
  // NEW WORKSPACE
  // =========================
  "new-workspace": {
    title: "Uburyo bushya bwo gukora",
    placeholder: "Uburyo bwanjye bwo gukora",
    "legal-areas": "Inzego z'Amategeko",
    create: {
      title: "Shiraho ahantu hashya ho gukorera",
      description:
        "Nyuma yo gushyiraho aka gahunda, abazoberezi gusa ni bo bazakobona. Ushobora kongeraho abakoresha nyuma yo gushyiraho.",
      error: "Ikosa: ",
      cancel: "Hagarika",
      "create-workspace": "Shiraho ahantu ho gukorera",
    },
  },

  // =========================
  // WORKSPACE CHATS
  // =========================
  "workspace-chats": {
    welcome: "Murakaza neza mu mwanya wawe mushya w'akazi.",
    "desc-start": "Kugira ngo utangire, urashobora",
    "desc-mid": "kohereza inyandiko",
    "desc-or": "cyangwa",
    start: "Kugira ngo utangire",
    "desc-end": "kohereza ubutumwa.",
    "attached-file": "Dosiye yometse",
    "attached-files": "Dosiye zometse",
    "token-count": "Umubare wa tokens",
    "total-tokens": "Igiteranyo cya tokens",
    "context-window": "Idirishya ry'amakuru riboneka",
    "remaining-tokens": "Asigaye",
    "view-files": "Reba dosiye zometse",
    prompt: {
      send: "Ohereza",
      "send-message": "Ohereza ubutumwa",
      placeholder: "Saba amakuru y'ubujyanama bw'amategeko",
      "change-size": "Hindura ubunini bw'inyandiko",
      reset: "Subiramo",
      clear: "Siba amateka yawe y'ibiganiro kandi utangire ikiganiro gishya",
      command: "Itegeko",
      description: "Ibisobanuro",
      save: "Bika",
      small: "Ntoya",
      normal: "Isanzwe",
      large: "Nini",
      larger: "Nini+",
      attach: "Fata dosiye uyishyire muri iki kiganiro",
      upgrade: "Zamura ubutumwa bwawe",
      upgrading: "Kuvugurura ubutumwa...",
      "original-prompt": "Icyifuzo cya mbere:",
      "upgraded-prompt": "Icyifuzo cyavuguruwe:",
      "edit-prompt":
        "Ushobora guhindura icyo ushyizemo gishya mbere yo kugitanga",
      "shortcut-tip":
        "Inama: Kanda Enter kugira ngo wemere impinduka. Koresha Shift+Enter kugira ngo wandike ku murongo mushya.",
      "speak-prompt": "Vuga igitekerezo cyawe",
      "view-agents": "Reba abakozi bose bashobora kuganira",
      "ability-tag": "Ubushobozi",
      "deep-search": "Gushakisha ku mbuga",
      "deep-search-tooltip": "Shakisha ku mbuga amakuru yo kunoza ibisubizo",
    },
  },

  // =========================
  // INVITATION STRINGS
  // =========================
  invite: {
    "accept-button": "Kuramo",
    newUser: {
      title: "Shiraho umukoresha w'umwanya",
      usernameLabel: "Izina rya umukoresha",
      passwordLabel: "Ijambo ryibanga",
      description:
        "Nyamuneka tanga umukoresha w'umwanya, utangira umukoresha w'umwanya w'umwanya w'akazi.",
    },
  },

  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Gushakisha Kure",
    description:
      "Shiraho ubushobozi bwo gushakisha ku mbuga kugira ngo utange ibisubizo byiza. Iyo byemejwe, sisitemu ishobora gushakisha ku mbuga amakuru yo kunoza ibisubizo.",
    enable: "Koresha Gushakisha Kure",
    enable_description:
      "Reka sisitemu ishakishe amakuru ku mbuga igihe isubiza ibibazo.",
    provider_settings: "Igenamiterere ry'Utanga Serivisi",
    provider: "Utanga Serivisi yo Gushakisha",
    model: "Modeli",
    api_key: "Urufunguzo rwa API",
    api_key_placeholder: "Andika urufunguzo rwawe rwa API",
    api_key_placeholder_set:
      "Urufunguzo rwa API rwashyizweho (andika urufunguzo rushya kugira ngo uhindure)",
    api_key_help:
      "Urufunguzo rwawe rwa API rubikwa mu buryo bwizewe kandi rukoreshwa gusa mu gushakisha ku mbuga.",
    context_percentage: "Ijanisha rya Konteksti",
    context_percentage_help:
      "Ijanisha ry'idirishya rya konteksti rigenewe ibisubizo byo gushakisha ku mbuga (5-20%).",
    fetch_error: "Ntibyakunze kuzana igenamiterere rya Gushakisha Kure",
    save_success: "Igenamiterere rya Gushakisha Kure ryabitswe neza",
    save_error:
      "Ntibyakunze kubika igenamiterere rya Gushakisha Kure: {{error}}",
    toast_success: "Igenamiterere rya Gushakisha Kure ryabitswe neza",
    toast_error:
      "Ntibyakunze kubika igenamiterere rya Gushakisha Kure: {{error}}",
    brave_recommended:
      "Brave Search ni uburyo bwiza kandi bwizewe bwo gushakisha.",
  },

  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Guhuza n'ibindi",
      hint: "Emerera guhuza n'ibindi kugira ngo wongere imikorere yo kwinjiza.",
    },
    systemPrompt: {
      label: "Ijambo rya Sisitemu",
      placeholder: "Andika agaciro hano...",
      description:
        "Urugero: Nyamuneka tanga ibisobanuro bigufi kugira ngo ushyire iki gice mu nyandiko yose mu rwego rwo koroshya gushaka igice. Subiza gusa ibisobanuro bigufi ntakindi.",
    },
    userPrompt: {
      label: "Ijambo ry'Umukoresha",
      placeholder: "Andika agaciro hano...",
      description:
        "Urugero: <document>\n{file}\n</document>\nDore igice dushaka gushyira mu nyandiko yose\n<chunk>\n{chunk}\n</chunk>",
    },
  },

  // =========================
  // HEADER
  // =========================
  header: {
    account: "Konti",
    login: "Injira",
    "sign-out": "Sohoka",
  },

  // =========================
  // WORKSPACE OVERVIEW
  // =========================
  workspace: {
    title: "Ahantu ho gukorera hiri kuri Instance",
    description:
      "Aha ni ahantu hose ho gukorera aboneka kuri iyi instance. Gusiba workspace bizakuraho ibiganiro byose bijyanye na yo n'igenamiterere ryayo.",
    "new-workspace": "Umwanya mushya w'akazi",
    name: "Izina",
    link: "Slug",
    users: "Abakoresha",
    type: "Ubwoko",
    "created-on": "Itariki yaremweho",
    save: "Bika impinduka",
    cancel: "Hagarika",
    "sort-by-name": "Tondeka ukurikije izina",
    sort: "Tondeka mu nyuguti",
    unsort: "Garura uko byari biteguwe",
    deleted: {
      title: "Umwanya w'akazi ntuboneka!",
      description: "Bisa nkaho nta mwanya w'akazi uriho ufite iri zina.",
      homepage: "Subira ahabanza",
    },
    "no-workspace": {
      title: "Nta mbuga y'akazi iboneka",
      description: "Ntabwo ufite uburenganzira ku mbuga y'akazi.",
      "contact-admin":
        "Nyamuneka baza umuyobozi wawe kugira ngo usabe uburenganzira.",
      "learn-more": "Menya byinshi ku mbuga y'akazi",
    },
    "no-workspaces":
      "Ntabwo ufite imbuga z'akazi. Hitamo igice cy'amategeko ibumoso kugira ngo utangire.",
    "my-workspaces": "Imbuga zanjye z'akazi",
    "show-my": "Erekana imbuga zanjye z'akazi",
    "show-all": "Erekana imbuga zose z'akazi",
    "cloud-ai": "AI yo mu bicu",
    "local-ai": "AI yo mu gace",
    "welcome-mobile":
      "Kanda buto yo hejuru ibumoso kugira ngo uhitemo igice cy'amategeko.",
    "today-time": "Uyu munsi, {{time}}",
    "date-time": "{{day}} {{month}}, {{time}}",
    "ai-type": "Moduli",
    "latest-activity": "Igikorwa cya nyuma",
    "creator-id": "Yakozwe na ID y'umukoresha: {{id}}",
    "loading-username": "Kuvuga izina rya umukoresha...",
  },

  // =========================
  // WORKSPACES SETTINGS MENU
  // =========================
  "workspaces-settings": {
    general: "Igenamiterere Rusange",
    chat: "Igenamiterere rya Chat",
    vector: "Ibigega bya Vector",
    members: "Abanyamuryango",
    agent: "Igenamiterere rya Agent",
    "general-settings": {
      "workspace-name": "Izina ry'Icyumba Cy'Akazi",
      "desc-name": "Ibi bizahindura gusa izina ryerekanwe ry'icyumba cy'akazi.",
      "assistant-profile": "Ishusho y'umufasha",
      "assistant-image": "Hindura ishusho y'umufasha w'icyumba cy'akazi.",
      "workspace-image": "Ishusho y'Icyumba Cy'akazi",
      "remove-image": "Kuraho ishusho y'icyumba cy'akazi",
      delete: "Siba icyumba cy'akazi",
      deleting: "Gusiba icyumba cy'akazi...",
      update: "Hindura icyumba cy'akazi",
      updating: "Birimo kuvugurura icyumba cy'akazi...",
    },
    "chat-settings": {
      type: "Ubwoko bwa Chat",
      private: "Byihariye",
      standard: "Isanzwe",
      "private-desc-start": "bizaha uburenganzira bwo kwinjira",
      "private-desc-mid": "bonyine",
      "private-desc-end": "abantu batoranijwe gusa.",
      "standard-desc-start": "bizaha uburenganzira bwo kwinjira",
      "standard-desc-mid": "abantu bose",
      "standard-desc-end": "bashya.",
    },
    users: {
      manage: "Cunga Abakoresha",
      "workspace-member": "Nta banyamuryango b'icyumba cy'akazi",
      username: "Izina ry'umukoresha",
      role: "Uruhare",
      date: "Itariki y'inyongera",
      users: "Abakoresha",
      search: "Shakisha umukozi",
      "no-user": "Nta mukoresha wabonetse",
      select: "Hitamo byose",
      unselect: "Kuraho guhitamo",
      save: "Bika",
    },
    "linked-workspaces": {
      title: "Ahantu Habigenewe",
      description:
        "Niba ahantu ho gukorera hafitanye isano, amakuru ajyanye n'amategeko azajya aboneka byikora kuva kuri buri hantu. Ibi bizatuma igihe cyo gutunganya kirekire.",
      "linked-workspace": "Nta hantu habigenewe",
      manage: "Cunga ahantu habigenewe",
      name: "Izina",
      slug: "Izina ry'abigenewe",
      date: "Itariki y'inyongera",
      workspaces: "Ahantu habigenewe",
      search: "Shakisha ahantu habigenewe",
      "no-workspace": "Nta hantu habigenewe habonetse",
      select: "Hitamo byose",
      unselect: "Kuraho",
      save: "Bika",
    },
    "delete-workspace": "Siba ahantu",
    "delete-workspace-message": "Ushaka gusiba ibiganiro?",
    "vector-database": {
      reset: {
        title: "Siba database ya vectors",
        message:
          "Ushaka gusiba database ya vectors kuri ubu buryo bwo gukorera. Ibi bizakuraho vectors zose zashyizweho.\n\nIbintu by'inkomoko bizaguma kandi iki gikorwa ntishobora gusubirwamo.",
      },
    },
  },

  // =========================
  // GENERAL APPEARANCE & CUSTOMIZATION
  // =========================
  general: {
    vector: {
      title: "Umubare wa Vectors",
      description: "Umubare wose wa vectors muri database yawe.",
      vectors: "Umubare wa vectors",
    },
    names: {
      description: "Ibi bizahindura gusa izina ryerekanwe ry'umwanya w'akazi.",
    },
    message: {
      title: "Ubutumwa busabwa mu kiganiro",
      description:
        "Hindura ubutumwa buzajya butangwa ku bakoresha umwanya w'akazi.",
      add: "Ongeraho ubutumwa bushya",
      save: "Bika ubutumwa",
      heading: "Sobanurira",
      body: "ibyiza by'uru rubuga",
      message: "Ubutumwa",
      "new-heading": "Umutwe w'Ubutumwa",
    },
    pfp: {
      title: "Ishusho y'Umufasha",
      description: "Hindura ishusho y'umufasha w'icyumba cy'akazi.",
      image: "Ishusho y'icyumba cy'akazi",
      remove: "Kuraho ishusho y'icyumba cy'akazi",
    },
    delete: {
      delete: "Siba icyumba cy'akazi",
      deleting: "Gusiba icyumba cy'akazi...",
      "confirm-start": "Uri hafi yo gusiba umwanya w'akazi wawe wose",
      "confirm-end":
        "iki gikorwa kizakuraho byose muri database ya vectors.\n\nIbindi bishingiye ku nyandiko bizaguma kandi iki gikorwa ntishobora gusubirwamo.",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chat: {
    llm: {
      title: "Umugabuzi wa LLM w'icyumba cy'akazi",
      description:
        "Umugabuzi na modeli za LLM zizakoreshwa muri ubu buryo. By default, bikoreshwa ibyifuzo bya sisitemu.",
      search: "Shakisha abagabuzi ba LLM bose",
      "save-error": "Ntibyakunze kubika {{provider}} settings: {{error}}",
      setup: "Shiraho",
      use: "Gukoresha",
      "need-setup": "ugomba kubanza kuyishyiraho.",
      cancel: "Hagarika",
      save: "Bika",
      settings: "Igenamiterere",
      "multi-model": "Uyu mugabuzi ntashyigikira modeli nyinshi.",
      "workspace-use": "Uyu workspace uzakoresha modeli yashyizweho muri",
      "model-set": "igenamiterere ya sisitemu",
      "system-default": "System default",
      "system-default-desc": "Koresha igenamiterere rya LLM ryo kuri sisitemu",
      "no-selection": "Nta LLM yatoranijwe",
      "select-provider": "Hitamo utanga LLM",
      "system-standard-name": "LLM standard",
      "system-standard-desc":
        "Urugero: Nyamuneka tanga ibisobanuro bigufi kugira ngo ushyire iki gice mu nyandiko yose mu rwego rwo koroshya gushaka igice. Subiza gusa ibisobanuro bigufi ntakindi.",
    },
    "speak-prompt": "Vuga icyo ushaka",
    "view-agents": "Reba abakozi bose bashobora kuganira",
    "ability-tag": "Ubushobozi",
    "change-text-size": "Hindura ubunini bw'inyandiko",
    "aria-text-size": "Hindura ubunini bw'inyandiko",
    model: {
      title: "Modeli y'ibiganiro by'icyumba cy'akazi",
      description:
        "Modeli izakoreshwa muri ubu buryo. Niba nta kintu, izakoresha ibyifuzo bya sisitemu.",
      wait: "-- tegereza kuri modeli --",
      general: "Modeli rusange",
      custom: "Modeli zabugenewe",
    },
    mode: {
      title: "Imikorere y'ibiganiro",
      chat: {
        title: "Ikiganiro",
        "desc-start": "izatanga ibisubizo ishingiye ku bumenyi bwa LLM",
        and: "kandi",
        "desc-end": "inyandiko z'inyongera zifashishwa.",
      },
      query: {
        title: "Gusaba",
        "desc-start": "izatanga ibisubizo",
        only: "byonyine",
        "desc-end": "niba inyandiko zinyongera zabonetse.",
      },
    },
    history: {
      title: "Amateka y'ibiganiro",
      "desc-start":
        "Umubare w'ibiganiro byabanjirije bizashyirwa mu myibutsa y'igihe gito.",
      recommend: "Teganya 20. ",
      "desc-end":
        "Birenze 45 bishobora guteza ibibazo bitewe n'ubunini bw'ubutumwa.",
    },
    prompt: {
      title: "Ijambo rikoreshwa",
      description:
        "Ijambo rizakoreshwa mu kiganiro. Sobanura context n'amabwiriza, kugira ngo AI itange igisubizo cyiza. Ushobora gutanga prompt iteguye neza.",
    },
    refusal: {
      title: "Igisubizo cya refusal mu buryo bwa query",
      "desc-start": "Muri mode ya",
      query: "query",
      "desc-end":
        "ushobora gusubiza igisubizo cya refusal igihe nta context yabonetse.",
    },
    temperature: {
      title: "Ubushyuhe bwa LLM",
      "desc-start":
        'Iyi igenamiterere igenzura uburyo "kreatif" igisubizo cya LLM kizaba kimeze.',
      "desc-end":
        "Umubare uri hejuru ugaragaza ubwenge bwinshi. Ku bimenyetso bimwe, bishobora guteza igisubizo kitari cyo mugihe byashyizwe hejuru cyane.",
      hint: "LLM nyinshi zifite urwego rutandukanye rw'agaciro keza. Reba amakuru y'umugabuzi wa LLM.",
    },
    "dynamic-pdr": {
      title: "Dynamic PDR ku cyumba cy'akazi",
      description:
        "Emerera cyangwa uhagarike Dynamic PDR kuri ubu buryo bwo gukorera.",
      "global-enabled":
        "Dynamic PDR yashyizweho ku rwego rw'isi kandi ntishobora gucishwa kuri workspace yihariye.",
    },
  },

  // =========================
  // VECTOR DATABASE (WORKSPACE)
  // =========================
  "vector-workspace": {
    identifier: "Indangamuntu ya Vector",
    snippets: {
      title: "Maximum Context Snippets",
      description:
        "Iyi igenamiterere igenzura umubare ntarengwa w'ibice bya context bizoherezwa kuri LLM kuri buri kiganiro cyangwa gusaba.",
      recommend:
        "Agaciro keza ni nibura 30. Gushyiraho imibare myinshi cyane bizongera igihe cyo gutunganya bidakenewe kongerera ubushishozi bitewe n'ubushobozi bwa LLM ikoreshwa.",
    },
    doc: {
      title: "Urwego rw'Isano ry'Inyandiko",
      description:
        "Urwego rw'ibimenyetso bigomba guhura kugira ngo inyandiko ifatwe nk'ijyanye n'ikiganiro. Urwego ruhanitse bisaba ko inyandiko ziba zifite isano rinini n'ikiganiro.",
      zero: "Nta mbonerahamwe",
      low: "Byoroheje (isano ≥ .25)",
      medium: "Hagati (isano ≥ .50)",
      high: "Hejuru (isano ≥ .75)",
    },
    reset: {
      reset: "Siba database ya vectors",
      resetting: "Gusiba vectors...",
      confirm:
        "Uri hafi yo gusiba database ya vectors kuri ubu buryo bwo gukorera. Ibi bizakuraho vectors zose zashyizweho.\n\nIbintu by'inkomoko bizaguma kandi iki gikorwa ntishobora gusubirwamo.",
      error: "Database ya vectors ntiyashoboye gusubirwamo!",
      success: "Database ya vectors yasubiwemo neza!",
    },
  },

  // =========================
  // AGENT CONFIGURATION
  // =========================
  agent: {
    "performance-warning":
      "Imikorere ya LLM idafite ubushobozi bwo guhamagara ibikoresho ishobora kugorana. Ubushobozi bushobora kuba buke cyangwa budakora.",
    provider: {
      title: "Umugabuzi wa LLM w'icyumba cy'akazi cya Agent",
      description:
        "Umugabuzi na modeli za LLM zizakoreshwa kuri agent ya @agent y'icyumba cy'akazi.",
      "need-setup":
        "Uko kugira ngo ushaka kugorana ibyo bya LLM, kugira ngo gusibazo kuva kuri agent @agent.",
    },
    mode: {
      chat: {
        title: "Modeli y'ibiganiro bya Agent",
        description: "Modeli y'ibiganiro izakoreshwa kuri agent ya @agent.",
      },
      title: "Modeli y'Agent",
      description: "Modeli ya LLM izakoreshwa kuri agent ya @agent.",
      wait: "-- tegereza kuri modeli --",
    },
    skill: {
      title: "Ubushobozi bwa Agent busanzwe",
      description:
        "Ongeraho ubushobozi bwa agent busanzwe ukoresheje ubumenyi bwubatswe. Ibi bizakoreshwa mu bice byose by'akazi.",
      rag: {
        title: "RAG & Kwibuka igihe kirekire",
        description:
          "Reka agent akoreshe inyandiko zawe mu gusubiza ikibazo cyangwa usabe agent 'kwibuka' ibintu kugirango bibungabunge igihe kirekire.",
      },
      configure: {
        title: "Hindura ubushobozi bwa Agent",
        description:
          "Hindura ubushobozi bwa agent usanzwe unongeraho ubushobozi bwihariye. Ibi bizakoreshwa mu bice byose by'akazi.",
      },
      view: {
        title: "Reba & Subiramo inyandiko",
        description:
          "Reka agent atange urutonde no gusubiramo inyandiko ziri muri workspace.",
      },
      scrape: {
        title: "Sora urubuga",
        description: "Reka agent asure urubuga asome ibiriho.",
      },
      generate: {
        title: "Kora grafike",
        description:
          "Emerera agent gukora grafike ziturutse ku makuru yatanzwe cyangwa yabonetse mu kiganiro.",
      },
      save: {
        title: "Kora & Bika dosiye mu browser",
        description:
          "Emerera agent gukora dosiye no kuyandika kuri browser kugira ngo zibike kandi ushobore kuzimanura.",
      },
      web: {
        title: "Shakisha kandi usure urubuga",
        "desc-start":
          "Emerera agent gushakisha ku mbuga kugirango asubize ibibazo, akoresheje serivisi ya web-search (SERP).",
        "desc-end":
          "Gushakisha ku mbuga muri session ya agent ntibizakora igihe kitashyizweho.",
      },
    },
  },

  cdbProgress: {
    "close-msg": "Uremeza ko ushaka guhagarika inzira?",
    general: {
      placeholderSubTask: "Gutunganya igice {{index}}...",
    },
    main: {
      step1: {
        label: "Kora Urutonde rw'Ingingo",
        desc: "Gukoresha inyandiko nkuru mu gukora imiterere y'ibanze.",
      },
      step2: {
        label: "Gutunganya Inyandiko",
        desc: "Gukora ibisobanuro no kugenzura iyakwiye.",
      },
      step3: {
        label: "Guhuza Inyandiko n'Ingingo",
        desc: "Guha buri ngingo inyandiko zijyanye.",
      },
      step4: {
        label: "Kumenya Ibibazo by'Amategeko",
        desc: "Gukura ibibazo by'ingenzi by'amategeko kuri buri ngingo.",
      },
      step5: {
        label: "Gukora Raporo z'Amategeko",
        desc: "Gukora raporo z'amategeko ku bibazo byagaragajwe.",
      },
      step6: {
        label: "Gukora Ingingo",
        desc: "Gukora ibikubiye muri buri ngingo.",
      },
      step7: {
        label: "Guhuza & Gusoza Inyandiko",
        desc: "Guhuza ingingo mu nyandiko ya nyuma.",
      },
    },
    noMain: {
      step1: {
        label: "Gutunganya Inyandiko",
        desc: "Gukora ibisobanuro ku madosiye yose yoherejwe.",
      },
      step2: {
        label: "Gukora Urutonde rw'Ingingo",
        desc: "Gukora urutonde rw'ingingo rusobanutse rushingiye ku ncamake z'inyandiko.",
      },
      step3: {
        label: "Gusoza Ihuzwa ry'Inyandiko",
        desc: "Kwemeza ko inyandiko zijyanye na buri ngingo yateganyijwe.",
      },
      step4: {
        label: "Kumenya Ibibazo by'Amategeko",
        desc: "Gukura ibibazo by'ingenzi by'amategeko kuri buri ngingo.",
      },
      step5: {
        label: "Gukora Raporo z'Amategeko",
        desc: "Gukora raporo z'amategeko ku bibazo byagaragajwe.",
      },
      step6: {
        label: "Gukora Ingingo",
        desc: "Gukora ibikubiye muri buri ngingo.",
      },
      step7: {
        label: "Guhuza & Gusoza Inyandiko",
        desc: "Guhuza ingingo zose mu nyandiko ya nyuma y'amategeko.",
      },
    },
  },

  // =========================
  // RECORDED WORKSPACE CHATS
  // =========================
  recorded: {
    title: "Ibiganiro by'Umwanya w'Akazi",
    description:
      "Ibi ni ibiganiro byose byanditswe byoherejwe n'abakoresha, byateguwe hakurikijwe itariki byakoreweho.",
    export: "Ohereza",
    table: {
      id: "Id",
      by: "Byoherejwe na",
      workspace: "Umwanya w'Akazi",
      prompt: "Ijambo rikoreshwa",
      response: "Igisubizo",
      at: "Byoherejwe",
      invoice: "Ref. y'inyemezabuguzi",
      "completion-token": "Ikimenyetso cyo gusoza",
      "prompt-token": "Ikimenyetso cyo gusaba",
    },
    "clear-chats": "Siba ibiganiro byose biriho",
    "confirm-clear-chats":
      "Wizeye neza ko ushaka gusiba ibiganiro byose?\n\nIbi ntibishobora gusubirwamo.",
    "fine-tune-modal": "Saba Fine-Tune Model",
    "confirm-delete.chat":
      "Wizeye neza ko ushaka gusiba iki kiganiro?\n\nIbi ntibishobora gusubirwamo.",
    next: "Paji ikurikira",
    previous: "Paji ibanza",
    filters: {
      "by-name": "Shaka ukurikije izina ry'ukoresha",
      "by-reference": "Nomero ndangamuriro",
    },
    bulk_delete_title: "Gusiba ibiganiro bishaje byinshi",
    bulk_delete_description:
      "Siba amadosiye y'ibiganiro ashaje kurusha igihe cyatoranijwe.",
    delete_old_chats: "Siba ibiganiro bishaje",
    total_logs: "Igiteranyo cy'amadosiye",
    filtered_logs: "Amadosiye ashunguwe",
    reset_filters: "Gusubiza ibisuzuma",
    "no-chats-found": "Nta dosiye y'ibiganiro yabonetse",
    "no-chats-description":
      "Nta nyandiko z'ibiganiro zihura n'amasuzuma yawe zibonetse. Gerageza guhindura ibyo ushakisha cyangwa usibe ibihe byashaje.",
    "deleted-old-chats": "Byasibwe {{count}} ibiganiro bishaje",
    two_days: "Iminsi 2",
    one_week: "Icyumweru 1",
    two_weeks: "Ibyumweru 2",
    one_month: "Ukwezi 1",
    two_months: "Amezi 2",
    three_months: "Amezi 3",
    total_deleted: "Amadosiye y'ibiganiro yose yasibwe",
  },

  // =========================
  // API KEYS
  // =========================
  api: {
    title: "Imfunguzo za API",
    description:
      "Imfunguzo za API zituma ushobora kwinjira no gucunga iyi instance mu buryo bwa program.",
    link: "Soma inyandiko za API",
    generate: "Kora urufunguzo rushya rwa API",
    table: {
      key: "Urufunguzo rwa API",
      by: "Byakozwe na",
      created: "Byakozwe",
    },
    new: {
      title: "Shiraho urufunguzo rushya rwa API",
      description:
        "Nyuma yo gushyiraho, urufunguzo rwa API rushobora gukoreshwa kugira ngo ubone no guhindura iyi instance mu buryo bwa program.",
      doc: "Soma inyandiko za API",
      cancel: "Hagarika",
      "create-api": "Shiraho urufunguzo rwa API",
    },
  },

  // =========================
  // LLM PROVIDER DESCRIPTIONS
  // =========================
  "llm-provider": {
    openai: "Icyitegererezo gisanzwe ku bikorwa bitari iby'ubucuruzi.",
    azure:
      "Icyitegererezo cy'ubucuruzi cya OpenAI gihagarariwe na Azure services.",
    anthropic: "Umufasha w'AI w'inshuti wahagarariwe na Anthropic.",
    gemini: "Modeli nini kandi ifite ubushobozi bwa Google",
    huggingface:
      "Gerageza 150,000+ LLM zifunguye hamwe n'umuryango wa AI ku isi.",
    ollama: "Koresha LLM zigendanwa kuri mashini yawe.",
    lmstudio: "Gerageza, ikura, no gukoresha LLM zigezweho muri click nkeya.",
    localai: "Koresha LLM zigendanwa kuri mashini yawe.",
    togetherai: "Koresha LLM ifunguye yavuye muri Together AI.",
    mistral: "Koresha LLM zigendanwa zavuye muri Mistral AI.",
    perplexityai:
      "Koresha LLM zifite ubushobozi n'izihuza n'interineti zatanzwe na Perplexity AI.",
    openrouter: "Interineti imwe yo guhuza LLMs.",
    groq: "Igisubizo cyihuse cya LLM ku buryo bwa real-time AI applications.",
    koboldcpp: "Koresha LLM zigendanwa ukoresheje koboldcpp.",
    oobabooga:
      "Koresha LLM zigendanwa ukoresheje Oobabooga Text Generation Web UI.",
    cohere: "Koresha LLM za Cohere Command.",
    lite: "Koresha proxy ya LiteLLM ifite OpenAI compatibility.",
    "generic-openai":
      "Huzaho serivisi iyo ari yo yose ya OpenAI ihuza n'igenamiterere ry'ibanze.",
    native: "Koresha modeli ya Llama yakuweho ku instance.",
    xai: "Koresha LLM zifite ubushobozi nka Grok-2 n'izindi.",
    "aws-bedrock": "Koresha LLM zifite ubushobozi mu buryo bwa AWS Bedrock.",
    deepseek: "Koresha DeepSeek's powerful LLMs.",
    fireworksai: "Imikorere ihuse kandi inoze kuri compound AI systems. ",
    bedrock: "Koresha LLM zifite ubushobozi mu buryo bwa AWS Bedrock.",
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Ihitamo rya Kuvuga-ubwanditsi (Speech-to-text)",
    provider: "Umugabuzi",
    "system-native": "Sisitemu kavukire",
    "desc-speech":
      "Hano ushobora gutangaza uburyo bwa serivisi za speech-to-text na text-to-speech ushaka gukoresha muri platform yawe. By default, dukoresha ubufasha bwa browser, ariko ushobora gukoresha izindi.",
    "title-text": "Ihitamo rya Text-to-speech",
    "desc-text":
      "Hano ushobora gutangaza uburyo bwa serivisi za text-to-speech ushaka gukoresha muri platform yawe. By default, dukoresha ubufasha bwa browser, ariko ushobora gukoresha izindi.",
    "desc-config": "Nta igenamiterere rikeneye kuri browser yawe.",
    "placeholder-stt": "Shakisha abagabuzi ba speech-to-text",
    "placeholder-tts": "Shakisha abagabuzi ba text-to-speech",
    "native-stt": "Koresha serivisi ya STT ya browser niba iboneye.",
    "native-tts": "Koresha serivisi ya TTS ya browser niba iboneye.",
    "piper-tts": "Kora modeli za TTS mu buryo bwite muri browser yawe.",
    "openai-description":
      "Koresha amajwi na tekinoloji ya OpenAI mu guhindura umwandiko mo ijwi.",
    openai: {
      "api-key": "Urufunguzo rwa API",
      "api-key-placeholder": "OpenAI API Key",
      "voice-model": "Modeli y'amajwi",
    },
    elevenlabs: "Koresha amajwi ya ElevenLabs mu gukora text-to-speech.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Ihitamo rya Transcription",
    description:
      "Aya ni amakuru n'igenamiterere rya serivisi yo gushyira mu nyandiko. Ni ingenzi ko aya makuru ari ay'ubu kandi nyayo, ubundi amajwi ntashyirwa mu nyandiko.",
    provider: "Umugabuzi wa Transcription",
    "warn-start":
      "Gukoresha icyitegererezo cya whisper kuri mashini ifite RAM cyangwa CPU bike bishobora guhagarika platform igihe cyo gutunganya amajwi.",
    "warn-recommend": "Dusaba nibura 2GB RAM na dosiye ziri munsi ya 10MB.",
    "warn-end":
      "Icyitegererezo cyashyizwemo kizakurwa mu buryo bwikora ku nshuro ya mbere.",
    "search-audio": "Shakisha abatanga serivisi za transcription y'amajwi",
    "api-key": "Urufunguzo rwa API",
    "api-key-placeholder": "Urufunguzo rwa OpenAI API",
    "whisper-model": "Icyitegererezo cya Whisper",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Icyitegererezo Gisanzwe Cyashyizwemo",
    "default-built-in-desc":
      "Koresha icyitegererezo cya whisper cyashyizwemo kuri iyi instance mu buryo bwite.",
    "openai-name": "OpenAI",
    "openai-desc":
      "Koresha icyitegererezo cya OpenAI Whisper-large ukoresheje urufunguzo rwawe rwa API.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Izina rishya rya modeli
    "model-size-turbo": "(~810mb)", // Ingano nshya ya modeli
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Ihitamo rya Embedding",
    "desc-start":
      "Mugihe ukoresheje LLM idafite embedding engine yihariye - ushobora kongera gutangaza uburyo bwo kwinjiza.",
    "desc-end":
      "Embedding ni uburyo bwo guhindura inyandiko mu vectors. Aya makuru akenewe kugirango dosiye zawe na prompts bihindurwe mu buryo bushobora gukoreshwa na platform.",
    provider: {
      title: "Umugabuzi wa Embedding",
      description: "Nta igenamiterere rikeneye kuri LanceDB.",
      "search-embed": "Shakisha abagabuzi ba embedding bose",
      select: "Ugomba guhitamo embedding",
      search: "Shakisha abagabuzi ba embedding bose",
    },
    workspace: {
      title: "Ihitamo rya Embedding ku cyumba cy'akazi",
      description:
        "Umugabuzi na modeli za embedding zizakoreshwa muri workspace. By default, bikoreshwa embedding y'ingenzi ya sisitemu.",
      "multi-model":
        "Inkunga ya multi-model ntabwo irashyigikirwa kuri uyu mugabuzi.",
      "workspace-use": "Uyu workspace uzakoresha",
      "model-set": "imiterere ya modeli yashyizweho kuri sisitemu.",
      embedding: "Modeli ya Embedding ya Workspace",
      model:
        "Modeli ya embedding izakoreshwa muri uyu workspace. Niba ituzuye, izakoresha embedding y'ingenzi ya sisitemu.",
      wait: "-- tegereza kuri modeli --",
      setup: "Shiraho",
      use: "Koresha",
      "need-setup":
        "Nk'umugabuzi wa embedding muri uyu workspace, ugomba kubanza kuyishyiraho.",
      cancel: "Hagarika",
      save: "Bika",
      settings: "Igenamiterere",
      search: "Shakisha abagabuzi ba embedding bose",
      "need-llm":
        "Nk'umugabuzi wa LLM muri uyu workspace, ugomba kubanza kuyishyiraho.",
      "save-error": "Failed to save {{provider}} settings: {{error}}",
      "system-default": "Igenamiterere rya Sisitemu",
      "system-default-desc":
        "Koresha embedding y'ingenzi ya sisitemu kuri uyu workspace.",
    },
    warning: {
      "switch-model":
        "Byakozwe kuri modeli ya embedding brichwa dokumenti zishyiraho zikibazo kugirango gushyiraho.",
    },
  },

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Ihitamo rya Text Splitting & Chunking",
    "desc-start":
      "Hari igihe ushaka guhindura uburyo inyandiko nshya zigabanywa zikanashyirwa mu bice mbere yo gushyirwa muri database ya vectors.",
    "desc-end":
      "Ugomba guhindura iyi igenamiterere gusa niba usobanukiwe uburyo bwo gukata inyandiko n'ingaruka zayo.",
    "warn-start": "Impinduka hano zizahabwa gusa kuri",
    "warn-center": "inyandiko nshya",
    "warn-end": ", ntizikore kuri inyandiko zihari.",
    method: {
      title: "Uburyo bwo gutandukanya inyandiko",
      "native-explain": "Koresha ingano n'ihurirana by'uduce two mu gace.",
      "jina-explain": "Ohereza gutandukanya/gucamo ku buryo bwa Jina.",
      size: {
        title: "Ingano y'uduce",
        description: "Umubare ntarengwa wa tokens ku gice.",
      },
      jina: {
        api_key: "Urufunguzo rwa API ya Jina",
        api_key_desc:
          "Bikenewe kugira ngo ubone serivisi yo gutandukanya ya Jina. Urufunguzo ruzabikwa mu ibidukikije byawe.",
        max_tokens: "Jina: Tokens ntarengwa ku gice",
        max_tokens_desc:
          "Igenamiterere ry'umubare ntarengwa wa tokens mu gice kuri Jina segmenter (ntarengwa 2000 tokens).",
        return_tokens: "Garura amakuru ya tokens",
        return_tokens_desc:
          "Shyiramo umubare wa tokens n'amakuru ya tokenizer mu gisubizo.",
        return_chunks: "Garura amakuru y'uduce",
        return_chunks_desc:
          "Shyiramo amakuru yuzuye ku duce twakoreshejwe mu gisubizo.",
      },
      "jina-info": "Gutandukanya kwa Jina birakora.",
    },
    size: {
      title: "Ingano ya Text Chunk",
      description:
        "Iyi ni uburebure ntarengwa bw'inyuguti zishobora kuboneka muri vector imwe.",
      recommend: "Uburebure ntarengwa bwa modeli yo kwinjiza ni",
    },
    overlap: {
      title: "Kwiyongera kwa Text Chunk",
      description:
        "Iyi ni intera ntarengwa y'inyuguti ziba zihurira hagati ya chunks ebyiri z'inyandiko.",
      error:
        "Chunk overlap ntishobora kuba nini cyangwa kungana na chunk size.",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Ububiko bwa Vector",
    description:
      "Aya ni amakuru n'igenamiterere bigena uburyo platformu yawe ikora. Ni ngombwa ko aya mafunguzo aba ari mashya kandi akwiye.",
    provider: {
      title: "Utanga serivisi y'ububiko bwa Vector",
      description: "Nta konfigirasyo ikenewe kuri LanceDB.",
      "search-db": "Shakisha abatanga serivisi bose b'ububiko bwa vector",
    },
    search: {
      title: "Uburyo bwo gushakisha vector",
      mode: {
        "globally-enabled":
          "Iri genwa rigenwa mu buryo rusange mu migenamiterere ya sisitemu. Sura migenamiterere ya sisitemu kugira ngo uhindure imyitwarire yo kongera gushyira mu byiciro.",
        default: "Gushakisha bisanzwe",
        "default-desc":
          "Gushakisha gusanzwe kw'imisusire ya vector nta kongera gushyira mu byiciro.",
        "accuracy-optimized": "Byatunganyijwe ku bw'ukuri",
        "accuracy-desc":
          "Kongera gushyira mu byiciro ibisubizo kugira ngo hongerwe ukuri hakoreshejwe kwitabwaho guhuza.",
      },
    },
  },

  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Ibikoresho byo kuganira bishobora gushyirwa ahandi",
    description:
      "Ibikoresho byo kuganira bishobora gushyirwa ahandi ni interfaces za chat zifatanyijwe n'icyumba cy'akazi kimwe. Ibi bigufasha gukora ahantu ho gukorera ushobora gusangiza isi.",
    create: "Kora embed",
    table: {
      workspace: "Umwanya w'akazi",
      chats: "Ibiganiro byoherejwe",
      Active: "Domains zemewe",
    },
  },

  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Ibiganiro bya Embed",
    export: "Ohereza",
    description:
      "Aha ni ibiganiro byose byanditswe byaturutse kuri embed washyize hanze.",
    table: {
      embed: "Embed",
      sender: "Wohereje",
      message: "Ubutumwa",
      response: "Igisubizo",
      at: "Byoherejwe",
    },
    delete: {
      title: "Gusiba ibiganiro",
      message: "Ushaka gusiba ibiganiro?",
    },
    config: {
      "delete-title": "Gusiba embed",
      "delete-message": "Ushaka gusiba ibiganiro?",
      "disable-title": "Gusiba embed",
      "disable-message": "Ushaka gusiba ibiganiro?",
      "enable-title": "Gusiba embed",
      "enable-message": "Ushaka gusiba ibiganiro?",
    },
  },

  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Imikorere y'Abakoresha Benshi",
    description:
      "Tegura instance yawe gushyigikira itsinda ryawe ukoresheje Multi-User Mode.",
    enable: {
      "is-enable": "Multi-User Mode yashyizweho",
      enable: "Shyiraho Multi-User Mode",
      description:
        "By default, uzaba umuyobozi wenyine. Nk'umuyobozi, ugomba gukora accounts y'abakoresha bashya cyangwa abashinzwe. Ntugatakaze ijambo ry'ibanga kuko umuyobozi wenyine ashobora gusubiramo ijambo ry'ibanga.",
      username: "Email y'umuyobozi",
      password: "Ijambo ry'ibanga rya admin",
      "username-placeholder": "Izina ryawe rya admin",
      "password-placeholder": "Ijambo ry'ibanga rya admin",
    },
    password: {
      title: "Kurinda Ijambo ry'ibanga",
      description:
        "Kurinda instance yawe n'ijambo ry'ibanga. Niba uyibagiwe, nta buryo bwo kongera kuyibona, rero uyibike neza.",
    },
    instance: {
      title: "Kurinda instance n'ijambo ry'ibanga",
      description:
        "By default, uzaba umuyobozi wenyine. Nk'umuyobozi, ugomba gukora accounts y'abakoresha bashya cyangwa abakozi. Ntugatakaze ijambo ry'ibanga kuko umuyobozi wenyine ashobora gusubiramo ijambo ry'ibanga.",
      password: "Ijambo ry'ibanga rya instance",
    },
  },

  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Ibyanditswe by'Ibikorwa",
    description:
      "Reba ibikorwa byose bibera kuri instance kugirango ubikurikirane.",
    clear: "Siba ibyanditswe by'ibikorwa",
    table: {
      type: "Ubwoko bw'Igikorwa",
      user: "Umukoresha",
      occurred: "Byabaye",
    },
  },

  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Ubwiru & Gukorana n'Amakuru",
    description:
      "Aya ni amagenamiterere yawe ku buryo abatanga serivisi n'iyi platform bakorana n'amakuru yawe.",
    llm: "Guhitamo LLM",
    embedding: "Ihitamo rya Embedding",
    vector: "Database ya Vectors",
    anonymous: "Telemetry y'Ubwiru yashyizweho",
    "desc-event":
      "Ibikorwa byose ntibizandika aderesi ya IP kandi ntibikubiyemo",
    "desc-id": "amakuru atagaragara",
    "desc-cont":
      "ibiri mu biganiro, settings, ibiganiro, cyangwa andi makuru atari ay'ikoreshwa. Reba urutonde rw'ibikorwa bikusanywa kuri",
    "desc-git": "Github hano",
    "desc-end":
      "Nka projet ya open-source, twubaha uburenganzira bwawe bwo kugumana ubwiru. Niba uhagaritse telemetry, turagusaba kuduha ibitekerezo kugirango dukomeze kunoza platform.",
  },

  // =========================
  // DEFAULT CHAT
  // =========================
  "default-chat": {
    welcome: "Murakaza neza kuri IST Legal.",
    "choose-legal": "Hitamo inzego z'amategeko ibumoso.",
  },

  // =========================
  // INVITES
  // =========================
  invites: {
    title: "Ubutumire",
    description:
      "Kora links z'ubutumire ku bantu mu muryango wawe kugirango bemere kandi biyandikishe. Ubutumire burashobora gukoreshwa n'umuntu umwe gusa.",
    link: "Kora link y'ubutumire",
    accept: "Byemejwe na",
    imikoreshereze: "Ikoreshwa",
    "created-by": "Byakozwe na",
    created: "Byakozwe",
    new: {
      title: "Kora ubutumire bushya",
      "desc-start":
        "Nyuma yo gukora, uzashobora gukoporora ubutumire no kubwohereza ku mukoresha mushya aho bazakora konti nka",
      "desc-mid": "defaults",
      "desc-end": "rimwe kandi bazahabwa amasoko y'akazi yatoranijwe.",
      "auto-add": "Ongeraho umukoresha ku madosiye",
      "desc-add":
        "Urashobora gutoranya kongera umukoresha ku madosiye akurikira. By default, umukoresha ntazaba afite amasoko y'akazi agaragara. Uzayashyiraho nyuma yo kwakira ubutumire.",
      cancel: "Hagarika",
      "create-invite": "Kora ubutumire",
      error: "Ikosa: ",
    },
    "link-copied": "Link y'ubutumire yakopowe",
    "copy-link": "Koporora link y'ubutumire",
    "delete-invite-title": "Kurira ubutumire",
    "delete-invite-confirmation":
      "Wizeye ko ushaka gusiba ubu butumire?\nNyuma y'ibi ntibuzongera gukoreshwa.\n\nIbi ntibishobora gusubirwamo.",
    status: {
      label: "Imiterere",
      pending: "Birategerejwe",
      disabled: "Byafunzwe",
      claimed: "Byemejwe",
    },
  },

  // =========================
  // USER MENU
  // =========================
  "user-menu": {
    edit: "Hindura Konti",
    profile: "Ishusho y'umwirondoro",
    size: "800 x 800",
    "remove-profile": "Kuraho ishusho y'umwirondoro",
    username: "Izina ry'umukoresha",
    "username-placeholder": "Injiza izina ry'umukoresha",
    "new-password": "Ijambo ry'ibanga rishya",
    "new-password-placeholder": "Injiza ijambo ry'ibanga rishya",
    cancel: "Hagarika",
    update: "Hindura Konti",
    language: "Ururimi rukunda",
    email: "Aderesi imeri",
    "email-placeholder": "Injiza aderesi imeri",
  },

  // =========================
  // SIDEBAR (THREADS)
  // =========================
  sidebar: {
    thread: {
      "load-thread": "Ikiganiro zibitswe...",
      "starting-thread": "Gutangiza ikiganiro...",
      thread: "Ikiganiro gishya",
      delete: "Siba ikiganiro",
      rename: "Hinura izina",
      "delete-thread": "Gusiba ikiganiro",
      deleted: "sibanye",
      default: "Gisanzwe",
      "empty-thread": "Ikiganiro gishya",
      "rename-message": "Andika izina rishya ry'ikiganiro:",
      "delete-message":
        "Uzi neza ko ushaka gusiba iki kiganiro? Iyi gahunda ntishobora gusubizwa inyuma.",
      "rename-thread-title": "Guhindura izina ry'ikiganiro",
      "new-name-placeholder": "Andika izina rishya ry'ikiganiro",
      "delete-thread-title": "Gusiba ikiganiro?",
      "delete-confirmation-message":
        'Uzi neza ko ushaka gusiba ikiganiro "{{name}}"? Iyi gahunda ntishobora gusubizwa inyuma.',
    },
  },

  // =========================
  // THREAD NAME ERROR
  // =========================
  thread_name_error:
    "Izina ry'ikiganiro rigomba kuba hagati y'inyuguti 3 na 255 kandi rishobora gusa kugira inyuguti, imibare, imyanya cyangwa udukoni.",

  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Koresha embedding engine y'imbere ya platform. Nta igenamiterere rikeneye!",
    openai: "Icyitegererezo gisanzwe ku bikorwa bitari iby'ubucuruzi.",
    azure:
      "Icyitegererezo cy'ubucuruzi cya OpenAI gihagarariwe na Azure services.",
    localai: "Koresha LLM zigendanwa kuri mashini yawe.",
    ollama: "Koresha LLM zigendanwa kuri mashini yawe.",
    lmstudio: "Gerageza, ikura, no gukoresha LLM zigezweho muri click nkeya.",
    cohere: "Koresha LLM za Cohere Command.",
    voyageai: "Koresha LLM za Voyage AI.",
    "generic-openai": "Koresha LLM gisanzwe ku OpenAI.",
    "default.embedder": "Igenamiterere rya Embedding",
    jina: "Koresha LLM za Jina.",
    litellm: "Koresha LLM za LiteLLM.",
  },

  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb: "Database ya vectors 100% ikorera kuri instance ya platform.",
    chroma:
      "Database ya vectors ifunguye ushobora kuyikorera ku giti cyawe cyangwa kuri cloud.",
    pinecone: "Database ya vectors ikorera kuri cloud kubikorwa binini.",
    zilliz:
      "Database ya vectors ikorera kuri cloud yubatswe kubikorwa binini bifite SOC 2 compliance.",
    qdrant:
      "Database ya vectors ifunguye ishobora gukorerwa ku giti cyawe cyangwa kuri cloud.",
    weaviate:
      "Database ya vectors ifunguye ishobora gukorerwa ku giti cyawe cyangwa kuri cloud, ifite ububasha bwinshi.",
    milvus: "Ifunguye, ikora vuba cyane kandi ikomeye.",
    astra: "Ubushakashatsi bwa vector ku GenAI.",
  },

  // =========================
  // SYSTEM PREFERENCES
  // =========================
  system: {
    title: "Igenamiterere rya Sisitemu",
    "desc-start": "Aya ni igenamiterere n'imyanya rusange ya instance yawe.",
    user: "Abakoresha bashobora gusiba workspaces",
    "desc-delete":
      "Emerera abakoresha batari admin gusiba workspace barimo. Ibi bizasiba workspace kuri bose.",
    limit: {
      title: "Umupaka w'Ubutumwa",
      "desc-limit":
        "Shyiraho umubare ntarengwa w'ubutumwa umukoresha ashobora kohereza ku munsi.",
      "per-day": "Ubutumwa ku munsi",
      label: "Umupaka w'ubutumwa uri kuri ",
    },
    max_tokens: {
      title: "Umubare ntarengwa wa Login Tokens kuri buri Mukoresha",
      desc: "Shiraho umubare ntarengwa wa token zishobora kuba ziriho kuri buri mukoresha. Iyo birenze, izishaje ziravanwaho byikora.",
      label: "Token ntarengwa",
      help: "Agaciro kagomba kuba karenze 0",
    },
    state: {
      enabled: "Byemewe",
      disabled: "Byafunzwe",
    },
    "source-highlighting": {
      title: "Kuzamura / Gufunga ibisobanuro by'inkomoko",
      description:
        "Hisha cyangwa werekane ibisobanuro by'inkomoko ku bakoresha.",
      label: "Gusura: ",
      "toast-success": "Amahitamo y'ibisobanuro by'inkomoko yavuguruwe",
      "toast-error": "Kuvugurura amahitamo y'ibisobanuro by'inkomoko byanze",
    },
    "usage-registration": {
      title: "Ibandikiro ry'ikoreshwa kuri fagitire",
      description:
        "Koresha cyangwa uzimye kwandika fagitire y'ikoreshwa rya sisitemu.",
      label: "Ikoreshwa rya fagitire ririmo ",
    },
    "forced-invoice-logging": {
      title: "Guhatira Ikoreshwa rya fagitire",
      description:
        "Shyiraho niba ari ngombwa gutanga reference ya fagitire mbere yo gukoresha platform.",
      label: "Guhatira fagitire ",
    },
    "rexor-linkage": {
      title: "Ihuriro rya Rexor",
      description:
        "Shyiraho guhuza na rexor kugirango ubone inyandiko z'ibirego bikora bya rexor.",
      label: "Ihuza rya Rexor ",
      "activity-id": "ID y'igikorwa",
      "activity-id-description":
        "Andika ID y'igikorwa kugira ngo uhuzwe na Rexor",
    },
    rerank: {
      title: "Igenamiterere ryo Gusubiramo",
      description:
        "Tegura igenamiterere ryo gusubiramo kugirango utunganyirize ibisubizo by'ishakisha na LanceDB.",
      "enable-title": "Koresha Gusubiramo",
      "enable-description":
        "Koresha gusubiramo kugirango utunganyirize ibisubizo by'ishakisha ukoresheje context nyinshi.",
      status: "Imiterere yo Gusubiramo",
      "vector-count-title": "Vectors z'inyongera zo Gusubiramo",
      "vector-count-description":
        "Umubare wa vectors z'inyongera zo gufata hejuru y'umubare wa vectors za workspace. Urugero, niba workspace ishyizweho gufata vectors 30 kandi iyi ishyizweho 50, vectors 80 zizafatwa mu gusubiramo. Umubare munini ushobora gutunganya neza ariko uzongera igihe cyo gutunganya.",
      "lancedb-only": "LanceDB Gusa",
      "lancedb-notice":
        "Iyi feature iboneka gusa iyo ukoresha LanceDB nka vector database.",
    },
    context_window: {
      title: "Idirishya ry'Amakuru Ihinduka",
      desc: "Kugenzura ingano y'idirishya ry'amakuru ya LLM ikoreshwa ku masoko y'inyongera.",
      label: "Ijanisha ry'Idirishya ry'Amakuru",
      help: "Ijanisha ry'idirishya ry'amakuru rishobora gukoreshwa ku gutunganya (10-100%).",
      "toast-success": "Ijanisha ry'idirishya ry'amakuru ryavuguruwe.",
      "toast-error": "Kuvugurura ijanisha ry'idirishya ry'amakuru byanze.",
    },
    "change-login-ui": {
      title: "Hitamo Login Default UI",
      status: "Ubu",
      subtitle: "UI izakoreshwa nk'ui yo kwinjira ya porogaramu",
    },
    attachment_context: {
      title: "Idirishya ry'Amakuru y'Imigereka",
      desc: "Kugenzura ingano y'idirishya ry'amakuru ya LLM ishobora gukoreshwa ku migereka.",
      label: "Ijanisha ry'Amakuru y'Imigereka",
      help: "Ijanisha ry'idirishya ry'amakuru rishobora gukoreshwa ku migereka (10-80%).",
      "toast-success": "Ijanisha ry'amakuru y'imigereka ryavuguruwe.",
      "toast-error": "Kuvugurura ijanisha ry'amakuru y'imigereka byanze.",
      "validation-error":
        "Ijanisha ry'amakuru y'imigereka rigomba kuba hagati ya 10 na 80.",
    },
    save: "Bika impinduka",
  },

  feedback: {
    thankYou: "Murakoze! Ibyo mutekereza byoherejwe neza.",
    emailSendError: "Ntibyashobotse kubika ibyo abakoresha batekereza:",
    submitFeedbackError: "Ntibyashobotse kubika ibyo abakoresha batekereza:",
    attachFile: "Shyiramo dosiye",
    improvePlatform: "Dufashe kuboresha urubuga!",
    suggestionOrQuestion: "Hari icyifuzo? Cyangwa ikibazo?",
    clickToWrite: "Kanda witwandikire",
    noFeedback: "Nta gitekerezo cyabonetse",
    previewImage: "Igaragaza mbere y'ishusho",
    filePreview: "Igaragaza mbere ya dosiye",
    noFile: "Nta dosiye yometse",
    fullName: "Amazina yose",
    fullNamePlaceholder: "Andika amazina yawe yose",
    message: "Ubutumwa",
    messagePlaceholder: "Andika ubutumwa bwawe",
    attachment: "Umugereka",
    submit: "Ohereza igitekerezo",
    submitting: "Kohereza...",
    submitSuccess: "Igitekerezo cyoherejwe neza",
    submitError: "Kohereza igitekerezo byanze",
    imageLoadError: "Ishusho ntishobora gutangira",
    unsupportedFile: "Ubwoko bwa dosiye butemewe",
    validation: {
      fullNameMinLength: "Amazina yose musi kuba 2 tegurwe",
      fullNameMaxLength: "Amazina yose musi kuba 100 tegurwe",
      fullNameFormat:
        "Amazina yose musi kuba tegurwe bya tegurwe, tegurwe, tegurwe, tegurwe, tegurwe, tegurwe",
      messageMinLength: "Ubutumwa musi kuba 12 tegurwe",
      messageMaxLength: "Ubutumwa musi kuba 1000 tegurwe",
      messageMinWords: "Ubutumwa musi kuba 4 tegurwe",
      fileType:
        "Dosiye butemewe musi kuba tegurwe bya tegurwe, tegurwe, tegurwe, tegurwe, tegurwe, tegurwe",
      fileSize: "Dosiye butemewe musi kuba 5MB",
    },
  },

  "feedback-settings": {
    "delete-feedback": "Ibyo batekereza bigarurwa neza!",
    "delete-error": "Ibyo batekereza bishobora gusiba",
    "header-title": "Urutonde rw'ibitekerezo",
    "header-description":
      "Iyi ni urutonde rwuzuye rw'ibitekerezo kuri iyi sisitemu. Nyamuneka wibuke ko gusiba ibitekerezo birambye kandi ntibishobora kugarurwa.",
    title: "Buto yo Gutanga Ibyo Abakoresha Batekereza",
    description:
      "Gukoresha cyangwa guhagarika buto yo gutanga ibyo abakoresha batekereza.",
    successMessage: "Buto yo gutanga ibyo abakoresha batekereza yavuguruwe",
    failureUpdateMessage:
      "Ntibyakunze guhindura imiterere ya buto yo gutanga ibyo abakoresha batekereza.",
    errorSubmitting:
      "Ikosa mu kohereza amagenamiterere y’ibyo abakoresha batekereza.",
    errorFetching:
      "Ikosa mu kugarura amagenamiterere y’ibyo abakoresha batekereza.",
  },

  // =========================
  // USER SETTINGS (INSTANCE USERS)
  // =========================
  "user-setting": {
    description:
      "Aya ni amakuru yose ajyanye na konti ziri kuri instance. Gusiba konti bizahita bibuza uwo mukoresha kugera kuri instance.",
    "add-user": "Ongeraho Umukoresha",
    username: "Izina rya konti",
    role: "Uruhare",
    "economy-id": "ID y'Ubukungu",
    "economy-id-ph": "Injiza ikiranga cy'uburyo bw'ubukungu",
    "economy-id-hint":
      "ID ikoreshwa mu guhuza n'uturyo tw'ubukungu two hanze (urugero: Rexor)",
    default: "Bisanzwe",
    manager: "Umuyobozi",
    admin: "Umucungamakuru",
    superuser: "Superuser",
    "date-added": "Itariki yongeweho",
    "all-domains": "Domains zose",
    "other-users": "Abandi bakoresha (Nta domain)",
    // Amahitamo yo gutondekanya urutonde rw'abakoresha
    "sort-username": "Tondekanya ukurikije izina ry'umukoresha",
    "sort-organization": "Tondekanya ukurikije ikigo",
    edit: "Hindura: ",
    "new-password": "Ijambo ry'ibanga rishya",
    "password-rule": "Ijambo ry'ibanga rigomba kuba rifite inyuguti nibura 8.",
    "update-user": "Hindura Umukoresha",
    placeholder: "Injiza izina rya konti",
    cancel: "Hagarika",
    "remove-user": "Siba Umukoresha",
    "remove-user-title": "Siba Umukoresha",
    "remove-user-confirmation": "Wizeye ko ushaka gusiba uyu mukoresha?",
    error: "Ikosa: ",
  },

  "login-ui": {
    "show-toast": {
      "update-failed": "Ntibyakunze kuvugurura imigaragarire yo kwinjira",
      "updated-login-ui": "Imigaragarire yo kwinjira yaravuguruwe",
    },
    "visit-website": "Sura urubuga",
    loading: "Gutangira ...",
    "rw-login-description":
      "Kongera umusaruro w'amategeko ukoresheje ihuriro ryacu rishingiye kuri AI!",
  },

  // =========================
  // SUPPORT EMAIL
  // =========================
  support: {
    title: "Imeri y'Ubufasha",
    description:
      "Shyiraho aderesi imeri y'ubufasha igaragara kubakoresha bari kuri iyi instance.",
    clear: "Siba",
    save: "Bika",
  },

  // =========================
  // PUBLIC MODE
  // =========================
  "public-mode": {
    enable: "Shyiraho Moderi y'Umukoresha Rusange",
    enabled: "Moderi y'Umukoresha Rusange irahari",
  },

  // =========================
  // BUTTON LABELS
  // =========================
  button: {
    delete: "Siba",
    edit: "Hindura",
    suspend: "Hagarika",
    unsuspend: "Subizaho",
    save: "Bika",
    accept: "Emeza",
    decline: "Oya",
    ok: "OK",
    "flush-vector-caches": "Siba ububiko bwa vekiteri",
    cancel: "Hagarika",
    saving: "Kubika...",
    save_llm: "Bika ihitamo rya LLM",
    save_template: "Bika imbata",
    "reset-to-default": "Subiramo ku miterere y'ibanze",
    create: "Kurema",
    enable: "Subizaho",
    disable: "Hagarika",
    reset: "Subiramo",
    revoke: "Kuvanaho",
  },

  // =========================
  // NEW USER (INSTANCE)
  // =========================
  "new-user": {
    title: "Ongeraho umukoresha kuri instance",
    username: "Aderesi imeri",
    "username-ph": "Injiza aderesi imeri",
    password: "Ijambo ry'ibanga",
    "password-ph": "Ijambo ry'ibanga ry'itangiriro ry'umukoresha",
    role: "Uruhare",
    default: "Bisanzwe",
    manager: "Umuyobozi",
    admin: "Umucungamakuru",
    superuser: "Superuser",
    description:
      "Nyuma yo gukora umukoresha azakenera kwinjira akoresheje ijambo ry'ibanga rye ribanza kugira ngo abone ubwo burenganzira.",
    cancel: "Hagarika",
    "add-User": "Ongeraho Umukoresha",
    error:
      "Ntibyashobotse gukora umukoresha. Ibi bishobora kuba byatewe n'uko umukoresha asanzwe ahari cyangwa hari ikibazo cya sisitemu.",
    "invalid-email": "Nyamuneka injiza aderesi imeri yemewe.",
    permissions: {
      title: "Uburenganzira",
      default: [
        "Arashobora kohereza ibiganiro gusa kuri workspace yongereweho n'abayobozi.",
        "Ntashobora guhindura igenamiterere iriho.",
      ],
      manager: [
        "Arashobora kureba, gukora, no gusiba workspace iyo ari yo yose no guhindura igenamiterere ryayo.",
        "Arashobora gukora, kuvugurura no gutumira abakoresha bashya kuri instance.",
        "Ntashobora guhindura LLM, vectorDB, embedding, cyangwa izindi connections.",
      ],
      admin: [
        "Uruhare rwisumbuye rwose.",
        "Arashobora kubona no gukora byose kuri sisitemu.",
      ],
      superuser: [
        "Arashobora kugera ku mpapuro zimwe z'igenamiterere nka Document Builder na Prompt Upgrade.",
        "Ntashobora guhindura igenamiterere rya sisitemu nka LLM, vectorDB.",
        "Arashobora kohereza ibiganiro kuri workspace yongereweho n'abayobozi.",
      ],
    },
  },

  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Kora embed nshya kuri workspace",
    error: "Ikosa: ",
    "desc-start":
      "Nyuma yo gukora embed uzahabwa ihuriro ushobora gushyira kuri website yawe ukoresheje",
    script: "script",
    tag: "tag.",
    cancel: "Hagarika",
    "create-embed": "Kora embed",
    workspace: "Umwanya w'akazi",
    "desc-workspace":
      "Uyu ni umwanya w'akazi ibiciro bya chat bizashingiraho. By default, bigendera ku igenamiterere ry'uyu mwanya w'akazi keretse ubihindurije aho.",
    "allowed-chat": "Uburyo bwemerewe bwo kuganira",
    "desc-query":
      "Shiraho uko chatbot yawe izitwara. Query isobanura ko izasubiza gusa igihe inyandiko iboneka ifasha gusubiza ikibazo.",
    "desc-chat":
      "Chat ifungura ikiganiro ku bibazo rusange kandi ishobora gusubiza ibibazo bitarebana n'inyandiko za workspace.",
    "desc-response": "Chat: Subiza ibibazo byose uko byaba bimeze",
    "query-response":
      "Query: Subiza gusa ibibazo bifitanye isano n'inyandiko ziri muri workspace",
    restrict: "Zitira requests ziturutse kuri domain",
    filter:
      "Uyu murongo uzabuza requests ziva kuri domain itari iyi iri munsi.",
    "use-embed":
      "Iyo usize iri sanganya ubusa, bivuze ko embed ishobora gukoreshwa kuri site iyo ari yo yose.",
    "max-chats": "Umubare ntarengwa w'ibiganiro ku munsi",
    "limit-chats":
      "Gena umubare w'ibiganiro iyi embed yinjizwemo ishobora kwakira mu masaha 24. Zero bisobanura ko bitagira imipaka.",
    "chats-session": "Umubare ntarengwa w'ibiganiro kuri session",
    "limit-chats-session":
      "Gena umubare w'ibiganiro umukoresha ashobora kohereza kuri embed mu masaha 24. Zero bisobanura ko ntampaka.",
    "enable-dynamic": "Shyiraho modeli ihinduka",
    "llm-override":
      "Emera guhindura modeli ya LLM bityo ikaruta iya workspace isanzwe.",
    "llm-temp": "Emera guhindura ubukonje n'ubushyuhe bwa LLM",
    "desc-temp":
      "Emera guhindura ubushyuhe bwa LLM bityo ikaruta iherutse ya workspace.",
    "prompt-override": "Emera guhindura Prompt",
    "desc-override":
      "Emera guhindura system prompt bigasimbura prompt y'ibanze ya workspace.",
  },

  // =========================
  // SHOW TOAST MESSAGES
  // =========================

  // Moved to showToast.js

  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    // We can add short translations or keep them minimal if previously missing.
    openai: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe zikoreshwa mu gusubiza bikagaragara kuri OpenAI",
      ],
    },
    azure: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Inyandiko n'ibyo winjiza ntibigaragara kuri OpenAI cyangwa Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe zikoreshwa mu gusubiza bikagaragara kuri Anthropic",
      ],
    },
    gemini: {
      description: [
        "Ibiganiro byawe byavanwemo imyirondoro kandi bikaba bikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe bikagaragara kuri Google",
      ],
    },
    lmstudio: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri server iriho LMStudio",
      ],
    },
    localai: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri server ikoresha LocalAI",
      ],
    },
    ollama: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri mashini iriho Ollama",
      ],
    },
    native: {
      description: ["Modeli yawe n'ibiganiro byawe biboneka kuri iyi instance"],
    },
    togetherai: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo n'inyandiko zawe biragaragara kuri TogetherAI",
      ],
    },
    mistral: {
      description: ["Ibyifuzo byawe n'inyandiko bikagaragara kuri Mistral"],
    },
    huggingface: {
      description: [
        "Ibyifuzo byawe n'inyandiko zawe zo gusubiza bizajya kuri endpoint ya HuggingFace wakoresheje",
      ],
    },
    perplexity: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe bikagaragara kuri Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe zikoreshwa bikagaragara kuri OpenRouter",
      ],
    },
    groq: {
      description: [
        "Ibiganiro byawe ntibikoreshwa mu mahugurwa",
        "Ibyifuzo byawe n'inyandiko zawe bikagaragara kuri Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri server iriho KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri server iriho Oobabooga Text Generation Web UI",
      ],
    },
    "generic-openai": {
      description: [
        "Amakuru asangirwa hakurikijwe amabwiriza ya serivisi waba uhuje nayo.",
      ],
    },
    cohere: {
      description: [
        "Amakuru asangirwa hakurikijwe amabwiriza ya cohere.com n'amategeko agenga aho utuye.",
      ],
    },
    litellm: {
      description: [
        "Modeli yawe n'ibiganiro byawe biboneka kuri server iriho LiteLLM",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Vectors zawe n'inyandiko ziba zibitswe kuri Chroma yawe",
        "Kwinjira kuri instance yawe kugenwa nawe ubwawe",
      ],
    },
    pinecone: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri server za Pinecone",
        "Kwinjira kuri data yawe bikagenwa na Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri Qdrant (cloud cyangwa self-hosted)",
      ],
    },
    weaviate: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri Weaviate (cloud cyangwa self-hosted)",
      ],
    },
    milvus: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri Milvus (cloud cyangwa self-hosted)",
      ],
    },
    zilliz: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri Zilliz cloud cluster.",
      ],
    },
    astra: {
      description: [
        "Vectors zawe n'inyandiko zibitswe kuri AstraDB yawe iri kuri cloud.",
      ],
    },
    lancedb: {
      description: [
        "Vectors zawe n'inyandiko zibitswe hano kuri iyi instance ya platform",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: [
        "Inyandiko zawe zishyirwa muri embedding kuri iyi platform",
      ],
    },
    openai: {
      description: [
        "Inyandiko zawe zoherezwa kuri server za OpenAI",
        "Inyandiko zawe ntizikoreshwa mu mahugurwa",
      ],
    },
    azure: {
      description: [
        "Inyandiko zawe zoherezwa kuri Microsoft Azure service",
        "Inyandiko zawe ntizikoreshwa mu mahugurwa",
      ],
    },
    localai: {
      description: [
        "Inyandiko zawe zishyirwa muri embedding kuri server ikoresha LocalAI",
      ],
    },
    ollama: {
      description: [
        "Inyandiko zawe zishyirwa muri embedding kuri server iriho Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Inyandiko zawe zishyirwa muri embedding kuri server iriho LMStudio",
      ],
    },
    cohere: {
      description: [
        "Amakuru asangirwa hakurikijwe amabwiriza ya cohere.com n'amategeko agenga aho utuye.",
      ],
    },
    voyageai: {
      description: [
        "Amakuru yoherezwa kuri Voyage AI asangirwa hakurikijwe amabwiriza ya voyageai.com.",
      ],
    },
  },

  // =========================
  // PROMPT VALIDATION
  // =========================
  "prompt-validate": {
    edit: "Hindura",
    response: "Igisubizo",
    prompt: "Prompt",
    regenerate: "Subiramo igisubizo",
    good: "Igisubizo cyiza",
    bad: "Igisubizo kibi",
    copy: "Koporora",
    more: "Ibindi bikorwa",
    fork: "Koramo ishami (fork)",
    delete: "Siba",
    cancel: "Hagarika",
    save: "Bika & Ohereza",
    "export-word": "Ohereza kuri Word",
    exporting: "Birimo koherezwa...",
  },

  // =========================
  // CITATIONS
  // =========================
  citations: {
    show: "Garagaza Citations",
    hide: "Hisha Citations",
    chunk: "Citation Chunks",
    pdr: "Parent Document",
    "pdr-h": "Document Highlighting",
    referenced: "Ikomowe",
    times: "inshuro.",
    citation: "Citation",
    match: "bihuye",
    download: "Iyi browser ntishyigikira PDFs. Nyamuneka urayimure.",
    "download-btn": "Download PDF",
    view: "Reba Citations",
    sources: "Inkomoko ya Citations",
    "pdf-collapse-tip":
      "Inama: Urashobora kugabanya iyi PDF ukoresheje buto iri mu mfuruka yo hejuru ibumoso",
    "open-in-browser": "Fungura muri mushakisha",
    "loading-pdf": "-- PDF irimo irafunguka --",
    "error-loading": "Hari ikibazo mu gufungura PDF",
    "no-valid-path": "Nta nzira yemewe ya PDF yaboneka",
    "web-search": "Gushakisha ku rubuga",
    "web-search-summary": "Incamake y'ishakisha ku rubuga",
    "web-search-results": "Ibisubizo by'ishakisha ku rubuga",
    "no-web-search-results": "Nta bisubizo by'ishakisha ku rubuga byabonetse",
    "previous-highlight": "Ibitsikiye byabanje",
    "next-highlight": "Ibitsikiye bikurikira",
    "try-alternative-view": "Gerageza igaragara rindi",
  },

  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Document Drafting",
    description: "Igenamiterere ry'inyandiko zirimo kwandikwa.",
    configuration: "Igenamiterere",
    "drafting-model": "Drafting LLM",
    enabled: "Document Drafting irahari",
    disabled: "Document Drafting yafunzwe",
    "enabled-toast": "Document Drafting yemejwe",
    "disabled-toast": "Document Drafting yafunzwe",
    "desc-settings":
      "Admin ashobora guhindura igenamiterere ry'inyandiko zirimo kwandikwa kuri bose.",
    "drafting-llm": "Ihitamo rya LLM yo kwandika inyandiko",
    saving: "Birimo kubikwa...",
    save: "Bika impinduka",
    "chat-settings": "Igenamiterere rya Chat",
    "drafting-chat-settings": "Document Drafting Chat Settings",
    "chat-settings-desc": "Gena imikorere ya chat kuri document drafting.",
    "drafting-prompt": "Document Drafting system Prompt",
    "drafting-prompt-desc":
      "System prompt ikoreshwa mu document drafting. Iyi iri hanze ya systemprompt y'amategeko, itanga amabwiriza yihariye yo kwandika.",
    linking: "Document Linking",
    "legal-issues-prompt": "Legal Issues Prompt",
    "legal-issues-prompt-desc": "Shyiramo prompt y'ibibazo by'amategeko.",
    "memo-prompt": "Memo Prompt",
    "memo-prompt-desc": "Shyiramo prompt ya memo.",
    "desc-linkage":
      "Kwemeza kongezwa kwa context yandiya mategeko ukoresheje Vector/PDR search.",
    message: {
      title: "Ubutumwa busabwa ku Document Drafting",
      description:
        "Ongeraho ubutumwa bwifashishwa n'abakoresha mu kwandika inyandiko.",
      heading: "Umutwe w'ubutumwa bw default",
      body: "Umutwe w'ubutumwa bw default",
      "new-heading": "Umutwe w'ubutumwa",
      message: "Ibirimo by'ubutumwa",
      add: "Ongeraho Ubutumwa",
      save: "Bika Ubutumwa",
    },
    "combine-prompt": "Uburyo bwo Kuvanga Ibisubizo",
    "combine-prompt-desc":
      "Tanga ibwiriza ry'ikoranabuhanga ryo guhuza ibisubizo byinshi mu gisubizo kimwe. Iki kibiriza gikoreshwa kandi kose kuri guhuza ibisubizo n'ibisubizo bya DD Linkage, no guhuza ibisubizo bitandukanye biva mu gucunga ibisubizo bya Infinity Context.",
    "combine-prompt-placeholder":
      "Andika prompt y'umukoresha y'intambwe z'ibikorwa...",
    "page-description":
      "Iyi paji igenewe guhindura uburyo butandukanye bukoreshwa mu bice bitandukanye bya module yo kwandika inyandiko. Muri buri kiliziya, uburyo bw'ibanze bw'ikoreshwa bugaragara, buzakoreshwa keretse uburyo bwihariye bushyizwe kuri iyi paji.",
    "dd-linkage-steps": "Uburyo bukoreshwa ku ngingo z'ihuzwa rya DD",
    "general-combination-prompt": "Uburyo rusange bwo guhuza",
    "import-memo": {
      title: "Kuzana ibiva muri Legal QA",
      "button-text": "Kuzana inyandiko",
      "search-placeholder": "Gushakisha imirongo...",
      import: "Kuzana",
      importing: "Kuzana...",
      "no-threads": "Nta mirongo ya Legal QA ibonetse",
      "no-matching-threads": "Nta mirongo ihura n'ibyo ushakisha",
      "thread-not-found": "Umurongo wahisemo ntawubonetse",
      "empty-thread": "Umurongo wahisemo nta birimo ufite byo kuzana",
      "import-success": "Ibirimo by'umurongo byazanywe neza",
      "import-error": "Kuzana ibirimo by'umurongo ntibyakunze",
      "import-error-details": "Ikosa mu kuzana: {{details}}",
      "fetch-error": "Kuzana imirongo ntibyakunze. Ongera ugerageze nyuma.",
      "imported-from": "Byazanywe biva mu murongo wa Legal QA",
      "unnamed-thread": "Umurongo udafite izina",
      "unknown-workspace": "Urubuga rutazwi",
      "no-threads-available": "Nta mirongo iriho yo kuzana",
      "create-conversations-first":
        "Banza ukore ibiganiro mu rubuga rwa Legal QA, nyuma ushobora kuzana hano.",
      "no-legal-qa-workspaces":
        "Nta rubuga rwa Legal QA rufite imirongo ikora rwabonetse. Banza ukore ibiganiro mu rubuga rwa Legal QA kugira ngo uzane.",
      "empty-workspaces-with-names":
        "Urubuga rwa Legal QA rwabonetse ({{workspaceNames}}) ariko ntirufite imirongo ikora. Banza ukore ibiganiro muri urwo rubuga kugira ngo uzane.",
    },
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Ihitamo rya LLM yo kwandika inyandiko",
    description: "Automatic proposal of the customized prompt for a legal task",
    "task-description": "Ihitamo rya LLM yo kwandika inyandiko",
    "task-description-placeholder":
      "Bikora inyandiko by'amategeko by'umukoresha...",
    "suggested-prompt": "Ihitamo rya LLM yo kwandika inyandiko",
    "generation-prompt": "Ihitamo rya LLM yo kwandika inyandiko",
    "create-task": "Bika inyandiko",
    "specific-instructions": "Specific instructions or know-how",
    "specific-instructions-description":
      "Include any special instructions or expertise specific to this legal task",
    "specific-instructions-placeholder":
      "Add specific instructions, expertise, or know-how for handling this legal task...",
    generating: "Birimo kubikwa...",
    generate: "Bika inyandiko",
    "toast-success": "Ihitamo rya LLM yo kwandika inyandiko",
    "toast-fail": "Ntibyakunze kuvugurura inyandiko",
    button: "Tunganya prompt",
    success: "Prompt yakozwe neza",
    error: "Nyamuneka banza ushyiremo izina cyangwa icyiciro",
    failed: "Ntibyashobotse gukora prompt",
  },

  // =========================
  // DD SETTINGS
  // =========================
  "dd-settings": {
    title: "Workspace Linking Settings",
    description:
      "Gena token limits n'imikorere kuri workspace linking features",
    "vector-search": {
      title: "Vector Search",
      description: "Koresha vector search kuboneka inyandiko zijyanye",
    },
    "memo-generation": {
      title: "Memo Generation",
      description: "Kora memos z'ibibazo by'amategeko kubika context",
    },
    "base-generation": {
      title: "Base Legal Analysis",
      description: "Kora inyandiko shingiro ishingiye ku bibazo by'umukoresha",
    },
    "linked-workspace-impact": {
      title: "Linked Workspace Token Impact",
      description:
        "Hindura tokens ziboneka hakurikijwe umubare wa linked workspaces",
    },
    "vector-token-limit": {
      title: "Vector Token Limit",
      description:
        "Umubare ntarengwa wa tokens kuri workspace yahujwe kubijyanye na vector search",
    },
    "memo-token-limit": {
      title: "Memo Token Limit",
      description:
        "Umubare ntarengwa wa tokens yo gukora memo y'ibibazo by'amategeko",
    },
    "base-token-limit": {
      title: "Base Token Limit",
      description: "Umubare ntarengwa wa tokens kubika ibyo ukeneye kugaragaza",
    },
    "toast-success": "Igenamiterere ryavuguruwe",
    "toast-fail": "Ntibyakunze kuvugurura igenamiterere",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Workspace Linking Settings",
    description:
      "Gena token limits n'imikorere kuri workspace linking features",
    "vector-search": {
      title: "Vector Search",
      description: "Koresha vector search kuboneka inyandiko zijyanye",
    },
    "memo-generation": {
      title: "Memo Generation",
      description: "Kora memos z'ibibazo by'amategeko kubika context",
    },
    "base-generation": {
      title: "Base Legal Analysis",
      description: "Kora inyandiko shingiro ishingiye ku bibazo by'umukoresha",
    },
    "linked-workspace-impact": {
      title: "Linked Workspace Token Impact",
      description:
        "Hindura tokens ziboneka hakurikijwe umubare wa linked workspaces",
    },
    "vector-token-limit": {
      title: "Vector Token Limit",
      description:
        "Umubare ntarengwa wa tokens kuri workspace yahujwe kubijyanye na vector search",
    },
    "memo-token-limit": {
      title: "Memo Token Limit",
      description:
        "Umubare ntarengwa wa tokens yo gukora memo y'ibibazo by'amategeko",
    },
    "base-token-limit": {
      title: "Base Token Limit",
      description: "Umubare ntarengwa wa tokens kubika ibyo ukeneye kugaragaza",
    },
    "toast-success": "Igenamiterere ryavuguruwe",
    "toast-fail": "Ntibyakunze kuvugurura igenamiterere",
  },

  // =========================
  // MODALE (DOCUMENT & CONNECTORS)
  // =========================
  modale: {
    // The original rw file includes some references. We can unify if needed.
    document: {
      title: "Amadosiye Yanjye",
      document: "Inyandiko",
      search: "Shakisha inyandiko",
      folder: "Itegure Ububiko Bushya",
      name: "Izina",
      empty: "Nta Nyandiko",
      "move-workspace": "Muversha kuri Workspace",
      "doc-processor": "Document Processor Irabura",
      "processor-offline":
        "Ntitwabasha kwakira dosiye zawe kuko document processor itari online. Gerageza nyuma.",
      "drag-drop": "Kanda kugira ngo wohereze cyangwa sukura dosiye hano",
      "supported-files":
        "ishyigikira text files, csv, spreadsheets, audio, n'izindi!",
      "submit-link": "cyangwa ohereza link",
      fetch: "Tangira gutohoza urubuga",
      fetching: "Birimo gutohoza...",
      "file-desc":
        "Izi dosiye zizasuzumwa na document processor iri kuri iyi instance. Nta kindi cyumba zoherezwamo.",
      cost: "*Amafaranga yo kubika embeddings rimwe",
      "save-embed": "Bika kandi ushyiremo",
      "failed-uploads": "Kohereza byanze",
      "loading-message": "Ibi bishobora gufata igihe ku nyandiko nini",
      "uploading-file": "Kohereza dosiye...",
      "scraping-link": "Gusuzuma link...",
      "moving-documents": "Kwimura inyandiko {{count}}. Tegereza.",
      "exceeds-prompt-limit":
        "Icyitonderwa: Ibikubiyemo byoherejwe birenze ibyo gushyira mu cyifuzo kimwe. Sisitemu izatunganya ibyifuzo hakoreshejwe inyifato nyinshi, bizatinda igihe cyo gutanga igisubizo, kandi inozoza ishobora kugirwaho ingaruka.",
    },
    connectors: {
      title: "Data Connectors",
      search: "Shakisha data connectors",
      empty: "Nta data connectors zabonetse.",
    },
  },

  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "GitHub Repo",
      description:
        "Injiza repository ya GitHub (public cyangwa private) numa guhamagara kumwe.",
      url: "GitHub Repo URL",
      "collect-url": "Url ya GitHub repo ushaka gukusanya.",
      "access-token": "Github Access Token",
      optional: "si ngombwa",
      "rate-limiting": "Access Token yo kwirinda rate limiting.",
      "desc-picker":
        "Nyuma yo kurangiza, dosiye zose zizaboneka mu guhuza na workspace.",
      branch: "Ishami",
      "branch-desc": "Ishami ushaka gukusanyaho dosiye.",
      "branch-loading": "-- kubyaza amashami aboneka --",
      "desc-start": "Utahaye",
      "desc-token": "Github Access Token",
      "desc-connector": "iyi data connector izabasha gukusanya",
      "desc-level": "inzego z'imbere (top-level)",
      "desc-end": "gusa bitewe na rate-limits za GitHub public API.",
      "personal-token":
        "Fata Personal Access Token kuri GitHub account yawe hano.",
      without: "Nta",
      "personal-token-access": "Personal Access Token",
      "desc-api": ", GitHub API ishobora gukumira dosiye nyinshi.",
      "temp-token": "kora temporarily Access Token",
      "avoid-issue": "kugira ngo wirinde iki kibazo.",
      submit: "Ohereza",
      "collecting-files": "Birimo gukusanya dosiye...",
    },
    "youtube-transcript": {
      name: "YouTube Transcript",
      description: "Injiza inyandiko ya videwo ya YouTube aho iri link.",
      url: "YouTube Video URL",
      "url-video": "URL ya YouTube videwo ushaka gutohozaho.",
      collect: "Kusanya transcript",
      collecting: "Birimo gutohoza transcript...",
      "desc-end":
        "nyuma yo kurangiza, transcript izaboneka mu guhuza na workspace.",
    },
    "website-depth": {
      name: "Bulk Link Scraper",
      description:
        "Sora urubuga n'inkuru zako z'imbere kugeza kuri depth runaka.",
      url: "Website URL",
      "url-scrape": "URL y'urubuga ushaka gusora.",
      depth: "Depth",
      "child-links":
        "Umubare w'inkuru zako agent izakurikira kuva kuri URL y'inkomoko.",
      "max-links": "Max Links",
      "links-scrape": "Umubare ntarengwa w'inkuru zo gusora.",
      scraping: "Gusora urubuga...",
      submit: "Ohereza",
      "desc-scrap":
        "Nyuma yo kurangiza, zose zizaboneka mu guhuza na workspace.",
    },
    confluence: {
      name: "Confluence",
      description: "Injiza page ya Confluence numa guhamagara kumwe.",
      url: "Confluence Page URL",
      "url-page": "URL ya page iri mu Confluence space.",
      username: "Confluence Username",
      "own-username": "Izina ryawe kuri Confluence.",
      token: "Confluence Access Token",
      "desc-start":
        "Ukeneye access token kuri authentication. Shobora kugikora",
      here: "hano",
      access: "Access token y'ubwirinzi.",
      collecting: "Birimo gukusanya pages...",
      submit: "Ohereza",
      "desc-end": "Nyuma yo kurangiza, zose zizaboneka mu guhuza na workspace.",
    },
  },

  // =========================
  // MODULE DEFINITIONS
  // =========================
  module: {
    "legal-qa": "Legal Q&A",
    "document-drafting": "Document Drafting",
    "active-case": "Active Case",
  },

  // =========================
  // FINE-TUNE NOTIFICATION
  // =========================
  "fine-tune": {
    title: "Ufite amakuru ahagije yo gukora fine-tune!",
    link: "kanda hano umenye byinshi",
    dismiss: "fungura",
  },

  // =========================
  // MOBILE DISCLAIMER
  // =========================
  mobile: {
    disclaimer:
      "NB: Kugirango ubone imikorere myiza y'izi gahunda, ukoreshe mudasobwa.",
  },

  // =========================
  // ONBOARDING
  // =========================
  onboarding: {
    welcome: "Murakaza neza kuri",
    "get-started": "Tangira",
    "llm-preference": {
      title: "LLM Preference",
      description:
        "ISTLLM ishobora gukorana n'abaguzi benshi ba LLM. Uwo niwo murimo uzifashishwa.",
      "LLM-search": "Shakisha abagabuzi ba LLM",
    },
    "user-setup": {
      title: "Igenamiterere ry'Umukoresha",
      description: "Hindura amakuru ya konti yawe.",
      "sub-title": "Ni bantu bangahe bazakoresha iyi instance?",
      "single-user": "Njye jyenyine",
      "multiple-user": "Itsinda ryanjye",
      "setup-password": "Ese ushaka gushyiraho ijambo ry'ibanga?",
      "password-requirment":
        "Ijambo ry'ibanga rigomba kuba rifite inyuguti nibura 8.",
      "save-password":
        "Ugomba kubika iri jambo ry'ibanga kuko ntaho ryagarurirwa.",
      "password-label": "Ijambo ry'ibanga rya Instance",
      username: "Email ya admin",
      password: "Ijambo ry'ibanga rya admin",
      "account-requirment":
        "Email igomba kuba yemewe. Ijambo ry'ibanga rigomba kuba rifite inyuguti nibura 8.",
      "password-note":
        "By default, uzaba admin wenyine. Nyuma yo kurangiza, ushobora gukora no gutumira abandi. Ntugatakaze ijambo ry'ibanga.",
    },
    "data-handling": {
      title: "Data Handling & Privacy",
      description: "Dufite intego yo kwerekana uburyo amakuru akoreshwa neza.",
      "llm-label": "Guhitamo LLM",
      "embedding-label": "Ihitamo rya Embedding",
      "database-lablel": "Vector Database",
      "reconfigure-option": "Ibi byose birashobora guhindurwa mugihe ushaka.",
    },
    survey: {
      title: "Murakaza neza kuri IST Legal LLM",
      description:
        "Dufashe kumenya ibikenewe. Birahari nk'Ubuntu, ushobora kubyirengagiza.",
      email: "Aderesi yawe imeri ni iyihe?",
      usage: "Uzakoresha iyi platform kuki?",
      work: "Mu kazi",
      "personal-use": "Mu buryo bwange",
      other: "Ibindi",
      comment: "Hari icyo watubwira?",
      optional: "(Ubishaka)",
      feedback: "Urakoze kutubwira!",
    },
    button: {
      yes: "Yego",
      no: "Oya",
      "skip-survey": "Songeraho",
    },
    placeholder: {
      "admin-password": "Ijambo ry'ibanga rya admin",
      "admin-username": "Email yawe ya admin",
      "email-example": "<EMAIL>",
      comment:
        "Niba ufite ikibazo cyangwa igitekerezo, andika hano cyangwa utwandikire.",
    },
  },

  // =========================
  // DEFAULT SETTINGS FOR LEGAL Q&A
  // =========================
  "default-settings": {
    "canvas-prompt": "Prompt ya sisitemu ya Canvas",
    "canvas-prompt-desc":
      "Prompt ikoreshwa kuri sisitemu ya chat ya canvas. Ikoreshwa nka prompt ya sisitemu mu biganiro bya canvas.",
    title: "Igenamiterere Risanzwe rya Legal Q&A",
    "default-desc":
      "Kugenzura imyitwarire isanzwe y'ahantu ho gukora kuri Legal Q&A",
    prompt: "Prompt ya Sisitemu ya Legal Q&A",
    "prompt-desc":
      "Prompt isanzwe izakoreshwa muri Legal Q&A. Sobanura ibikenewe n'amabwiriza kugira ngo AI ibashe gutanga igisubizo. Ugomba gutanga prompt yateguwe neza kugira ngo AI ibashe gutanga igisubizo gifite aho gihuriye n'ikibazo kandi kinyuze. Kugira ngo ukoreshe iki gisabwa ku hantu hose ho gukora hasanzweho, uhindure igenamiterere ryabo ryihariye, koresha buto iri hepfo.",
    "prompt-placeholder": "Andika prompt yawe hano",
    "toast-success": "Prompt rusange y'uburyo yavuguruwe",
    "toast-fail": "Kuvugurura prompt rusange y'uburyo ntibyakunze",
    "apply-all-confirm":
      "Wizeye ko ushaka gushyira iyi prompt ku hantu hose ho gukora Legal Q&A hasanzweho? Iki gikorwa ntikishobora gusubirwaho kandi kizasimbuza igenamiterere ryose ryihariye.",
    "apply-to-all": "Shyira ku hantu hose ho gukora Legal Q&A hasanzweho",
    applying: "Birimo gushyirwaho...",
    "toast-apply-success":
      "Prompt rusange yashyizwe ku hantu {{count}} ho gukora",
    "toast-apply-fail":
      "Gushyira prompt rusange ku hantu ho gukora ntibyakunze",
    snippets: {
      title: "Default Max Context Snippets",
      description:
        "Umubare w'inyandiko zijyanye zishyirwa mu context ku hantu hashya ho gukora. Kugira ngo ukoreshe iki gisabwa ku hantu hose ho gukora hasanzweho, uhindure igenamiterere ryabo ryihariye, koresha buto iri hepfo.",
      recommend:
        "Agaciro keza ni nibura 30. Gushyiraho imibare myinshi cyane bizongera igihe cyo gutunganya bidakenewe kongerera ubushishozi bitewe n'ubushobozi bwa LLM ikoreshwa.",
    },
    "rerank-limit": {
      title: "Maximum Rerank Limit",
      description: "Umubare ntarengwa w'ibice by'inyandiko usubirwamo",
      recommend: "Byiza: hagati ya 20-50",
    },
    "validation-prompt": {
      title: "Validation Prompt",
      description:
        "Iyi igenamiterere irebana na prompt ishinzwe kugenzura igisubizo gitanzwe.",
      placeholder:
        "Nyamuneka genzura igisubizo gikurikira, urebe niba hari amakosa.",
    },
    "apply-vector-search-to-all": "Koresha ku myanya yose isanzwe ya Legal Q&A",
    "apply-vector-search-all-confirm":
      "Wizeye ko ushaka gukoresha iri genwa rya vector search ku myanya yose isanzwe ya Legal Q&A? Iki gikorwa ntikishobora gusubirwaho.",
    "toast-vector-search-apply-success":
      "Igenwa rya vector search ryakoreshejwe ku myanya {{count}}",
    "toast-vector-search-apply-fail":
      "Gukoresha igenwa rya vector search ku myanya byarananiranye",
    "canvas-upload-prompt": "Canvas Upload System Prompt", // TODO: Translate
    "canvas-upload-prompt-desc":
      "The system prompt used when processing uploads via the canvas. This prompt guides the LLM's behavior for uploaded content.", // TODO: Translate
  },

  // =========================
  // CONFIRM MESSAGE
  // =========================
  "confirm-message": {
    "delete-doc-title": "Gusiba dosiye n'ububiko",
    "delete-doc":
      "Urabyizeye ko ushaka gusiba izi dosiye n'ububiko?\nIbi bizazikura kuri sisitemu kandi zivanwe mu makuru zose.\nIbi ntibishobora gusubirwamo.",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Gukora Akazi k'Amategeko",
    noTaskfund: "Nta mirimo yemewe n'amategeko iboneka",
    noSubtskfund: "Nta byiciro bito bibonetse.",
    "loading-subcategory": "Gutegura ibyiciro bito...",
    "action-btn": "Tanga inshingano zemewe n'amategeko",
    description:
      "Shyiraho cyangwa uhagarike buto ya perform legal task mu Document Drafting.",
    successMessage: "Perform legal task yabaye {{status}}",
    failureUpdateMessage: "Ntibyakunze kuvugurura perform legal task.",
    errorSubmitting: "Habaye ikosa mugihe twoherezaga perform legal task.",
    "warning-title": "Umukibazo",
    "no-files-title": "Nta dosiye bito bibonetse.",
    "no-files-description":
      "Nta dosiye bito bibonetse. Tanga co njye dosiye przed kugira ngo gukora inshingano.",
    "settings-button": "Tanga inshingano zemewe n'amategeko",
    settings: "Igenamiterere z'amategeko",
    subStep: "Akazi gato karimo gukorwa cyangwa kari ku murongo",
  },

  // =========================
  // CANVAS CHAT
  // =========================
  canvasChat: {
    title: "Canvas",
    "input-placeholder": "Saba amakuru y'amategeko",
    chatboxinstruction: "Tangira amabwiriza ku gisubizo",
    explanation:
      "Ibi tool ibisubizo bya LLM kugirango kugorana amakuru y'amategeko.",
    editAnswer: "Tangira amakuru y'amategeko",
  },

  // =========================
  // STATUSES
  // =========================
  statuses: {
    enabled: "byemewe",
    disabled: "byafunzwe",
  },

  // =========================
  // SHARE MODAL
  // =========================
  shareModal: {
    title: "Gusangiza {type}",
    titleWorkspace: "Gusangiza umwanya w'akazi",
    titleThread: "Gusangiza ikiganiro",
    shareWithUsers: "Gusangiza abakoresha",
    shareWithOrg: "Gusangiza ikigo cyose",
    searchUsers: "Shakisha abakoresha...",
    noUsersFound: "Nta bakoresha babonetse",
    loadingUsers: "Gutegura abakoresha...",
    errorLoadingUsers: "Ikosa mu gutegura abakoresha",
    errorLoadingStatus: "Ikosa mu gutegura imimerere yo gusangiza",
    userAccessGranted: "Uburenganzira bw'umukoresha bwatanzwe",
    userAccessRevoked: "Uburenganzira bw'umukoresha bwakuweho",
    orgAccessGranted: "Uburenganzira bw'ikigo bwatanzwe",
    orgAccessRevoked: "Uburenganzira bw'ikigo bwakuweho",
    errorUpdateUser: "Ikosa mu kuvugurura uburenganzira bw'umukoresha",
    errorNoOrg: "Ntushobora gusangiza: konti ntabwo ihujwe n'ikigo",
    errorUpdateOrg: "Ikosa mu kuvugurura uburenganzira bw'ikigo",
    close: "Funga",
    grantAccess: "Tanga uburenganzira",
    revokeAccess: "Kuraho uburenganzira",
  },

  // =========================
  // ANSWER UPGRADE
  // =========================

  // Moved to answerUpgrade.js

  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "PDR Settings",
    description: "Gena Parent Document Retrieval settings kuri workspace zawe.",
    "desc-end": "Ibi bigira ingaruka ku buryo inyandiko za PDR zikora.",
    "global-override": {
      title: "Global Dynamic PDR Override",
      description:
        "Iyo yashyizweho, iyi izafata inyandiko zose z'umwanya w'akazi nk'aho zifite PDR-yemejwe mu miterere y'ibisubizo. Iyo idakoreshwa, ni inyandiko zigaragara nk'aho zifite PDR gusa zizakoreshwa, bishobora kugabanya ububiko bw'amakuru kandi bikagira ingaruka yo gutanga ibisubizo bifite ubwiza buke cyane kubera ko ibice bya vektori biva mu ishakisha gusa bizakoreshwa nk'inkomoko muri ibyo bihe.",
    },
    "pdr-token-limit": "PDR Token Limit",
    "pdr-token-limit-desc":
      "Umubare ntarengwa wa tokens ukoreshwa na PDR algorithm.",
    "input-prompt-token-limit": "Input Prompt Token Limit",
    "input-prompt-token-limit-desc":
      "Umubare ntarengwa wa tokens kuri prompt yinjizwamo.",
    "response-token-limit": "Response Token Limit",
    "response-token-limit-desc": "Umubare ntarengwa wa tokens kuri response.",
    "toast-success": "PDR settings yavuguruwe",
    "toast-fail": "Ntibyakunze kuvugurura PDR settings",
    "adjacent-vector-limit": "Adjacent Vector Limit",
    "adjacent-vector-limit-desc": "Umupaka w'inyandiko ziba zikwiranye hafi.",
    "adjacent-vector-limit-placeholder": "Injiza umubare w'inyandiko",
    "keep-pdr-vectors": "Keep PDR Vectors",
    "keep-pdr-vectors-desc": "Option yo kugumana vectors za PDR.",
    "pdr-token-limit-placeholder": "Andika ingano ntarengwa ya tokens ya PDR",
    "input-prompt-token-limit-placeholder":
      "Andika ingano ntarengwa ya tokens y'ubutumwa bw'ibanze",
    "response-token-limit-placeholder":
      "Andika ingano ntarengwa ya tokens y'igisubizo",
  },

  // =========================
  // VALIDATE RESPONSE
  // =========================
  "validate-response": {
    title: "Ibyavuye mu kugenzura",
    "toast-fail": "Ntibyakunze kugenzura igisubizo",
    validating: "Birimo kugenzura igisubizo",
    button: "Genzura igisubizo",
    "adjust-prefix": "Hindura igisubizo hashingiwe kuri iyi feedback: ",
    "adjust-button": "Shyira mu bikorwa impinduka",
  },

  // =========================
  // WORKSPACE NAMES (LEGAL AREAS)
  // =========================
  "workspace-names": {
    "Administrative Law": "Administrative Law",
    "Business Law": "Business Law",
    "Civil Law": "Civil Law",
    "Criminal Law": "Criminal Law",
    "Diplomatic Law": "Diplomatic Law",
    "Fundamental Law": "Fundamental Law",
    "Human Rights Law": "Human Rights Law",
    "Judicial Laws": "Judicial Laws",
    "Security Laws": "Security Laws",
    "Taxation Laws": "Taxation Laws",
  },

  // =========================
  // VALIDATE ANSWER
  // =========================
  "validate-answer": {
    setting: "Validation LLM",
    title: "Validation LLM Preference",
    description:
      "Aya ni amakuru n'igenamiterere rya LLM ishinzwe kugenzura igisubizo. Ni ingenzi ko aya makuru ari ay'ubu kandi nyayo.",
    "toast-success": "Validation LLM settings yavuguruwe",
    "toast-fail": "Ntibyakunze kuvugurura Validation LLM settings",
    saving: "Birimo kubika...",
    "save-changes": "Bika impinduka",
  },

  // =========================
  // ACTIVE CASE
  // =========================
  "active-case": {
    title: "Dosiye ikora",
    placeholder: "Andika numero y'indangamuntu",
    "select-reference": "Hitamo indangamuntu",
    "warning-title": "Nta numero y'indangamuntu",
    "warning-message":
      "Nta numero y'indangamuntu yashyizweho. Urifuza gukomeza nta numero y'indangamuntu?",
  },

  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Multi-user mode yashyizweho burundu ku mpamvu z'umutekano",
    "password-validation": {
      "restricted-chars":
        "Ijambo ry'ibanga ryawe rifite inyuguti zitemewe. Inyuguti zemewe ni _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "Iyo rikoreshwa, umuntu wese ashobora kugera ku mbuga rusange nta konti ikenewe.",
    },
    button: {
      saving: "Kubika...",
      "save-changes": "Bika impinduka",
    },
  },

  // =========================
  // ERRORS
  // =========================
  errors: {
    "fetch-models": "Ntibyakunze kubona modeli zihariye",
    "fetch-models-error": "Ikosa ryo kubona modeli",
    "upgrade-error": "Ikosa mugihe cyo kuzamura",
    "failed-extract-content": "Ntibyashobotse gukura ibiri mu nyandiko",
    "failed-process-attachment": "Ntibyashobotse gutunganya umugereka",
    "failed-process-content": "Ntibyashobotse gutunganya ibiri mu nyandiko",
    "failed-process-file": "Ntibyashobotse gutunganya dosiye",
    common: {
      error: "Ikosa",
    },
    streaming: {
      failed:
        "Habaye ikosa mu gihe cyo kohereza igisubizo, urugero nko muri moteri ya AI yaba itari ku murongo cyangwa ikorewe henshi cyane.",
      code: "Kode",
      unknown: "Ikosa itazwi.",
    },
    workspace: {
      "already-exists": "Umwanya w'akazi ufite iri zina usanzwe uhari",
    },
    env: {
      "anthropic-key-format":
        "Urufunguzo rwa API ya Anthropic rugomba gutangira na 'sk-ant-'",
      "openai-key-format":
        "Urufunguzo rwa API ya OpenAI rugomba gutangira na 'sk-'",
      "jina-key-format":
        "Urufunguzo rwa API ya Jina rugomba gutangira na 'jina_'",
    },
    auth: {
      "invalid-credentials": "Amakuru yo kwinjira atari yo.",
      "account-suspended": "Konti yahagaritswe n'umuyobozi.",
      "invalid-password": "Ijambo ry'ibanga ritari ryo ryatanzwe",
    },
  },

  // =========================
  // LOADING STATES
  // =========================
  loading: {
    models: "-- tegereza modeli --",
    "waiting-url": "-- tegereza URL --",
    "waiting-api-key": "-- tegereza API key --",
    "waiting-models": "-- tegereza modeli --",
  },

  // =========================
  // CHARTS
  // =========================
  charts: {
    downloading: "Birimo gukuramo ishusho...",
    download: "Kuramo ifoto ya grafike",
  },

  // =========================
  // MODALS
  // =========================
  modals: {
    warning: {
      title: "Icyitonderwa",
      proceed: "Urabyizeye ko ushaka gukomeza?",
      cancel: "Hagarika",
      confirm: "Emeza",
      "got-it": "Yego, ndabyumva",
    },
  },

  // =========================
  // DOCUMENTS & PINNING
  // =========================
  documents: {
    "pin-info-button": "Ni iki ari cyo gukingira inyandiko?",
    "pin-title": "Ni iki ari cyo gukingira inyandiko?",
    "pin-desc-1":
      "Iyo ukingiye inyandiko, platform izashyira ibikubiye muri dosiye yose mu kiganiro cyawe.",
    "pin-desc-2":
      "Ibi bikora neza kuri LLM zifite context nini cyangwa dosiye ntoya.",
    "pin-desc-3":
      "Niba udahabwa ibisubizo byiza, gukingira inyandiko ni uburyo bwo kubona ibisubizo byiza.",
    "pin-add": "Kingira mu mwanya w'akazi",
    "pin-unpin": "Kuvanaho mu mwanya w'akazi",
    "watch-title": "Ni iki bivuze gukurikirana inyandiko?",
    "watch-desc-1":
      "Iyo ukurikira inyandiko, platform izajya ivugurura ibikubiye mu nyandiko kuva ku isoko ryayo.",
    "watch-desc-2":
      "Uyu mukorere ukurikirana inyandiko uboneka kuri content iri online.",
    "watch-desc-3": "Urashobora gucunga inyandiko zikurikirana muri",
    "file-manager": "File manager",
    "admin-view": "admin view",
    "pdr-add": "Ongeraho inyandiko zose muri Parent Document Retrieval",
    "pdr-remove": "Kuramo inyandiko zose muri Parent Document Retrieval",
    empty: "Nta nyandiko zabonetse",
    tooltip: {
      date: "Itariki: ",
      type: "Ubwoko: ",
      cached: "Byabitswe",
    },
    actions: {
      removing: "Gukuramo dosiye mu mwanya w'akazi",
    },
    costs: {
      estimate: "Igiciro kiteganijwe: $",
      minimum: "< $0.01",
    },
    "new-folder": {
      title: "Kurema dosiye nshya",
      "name-label": "Izina rya dosiye",
      "name-placeholder": "Andika izina rya dosiye",
      create: "Kurema dosiye",
    },
    error: {
      "create-folder": "Kunanirwa kurema dosiye",
    },
  },

  // =========================
  // LEGAL QUESTION
  // =========================
  "legal-question": {
    "category-one": "Icyiciro cya Mbere",
    "category-two": "Icyiciro cya Kabiri",
    "category-three": "Icyiciro cya Gatatu",
    "category-four": "Icyiciro cya Kane",
    "category-five": "Icyiciro cya Gatanu",
    "item-one": "Igice cya Mbere",
    "item-two": "Igice cya Kabiri",
    "item-three": "Igice cya Gatatu",
    "item-four": "Igice cya Kane",
    "item-five": "Igice cya Gatanu",
    "item-six": "Igice cya Gatandatu",
    "item-seven": "Igice cya Karindwi",
    "example-title": "Kurya Neza: Ubuyobozi bwo Kwishimira Ibiryo",
    example: {
      breakfast: {
        title: "1. Amahitamo meza yo mu gitondo",
        items: [
          "Oatmeal n'imbuto z'ibishyimbo n'ubuki",
          "Greek yogurt parfait n'ubugari",
          "Avocado toast ifite amagi atetse",
          "Smoothie icyatsi ifite spinach, umuneke, n'amata y'ubwoya",
          "Pancakes y'ibinyampeke hamwe na maple syrup",
        ],
      },
      lunch: {
        title: "2. Ibitekerezo byihuse byo ku manywa",
        items: [
          "Wrap ya chicken yokeje hamwe n'imboga zivanzemo",
          "Salade ya quinoa n'imboga zitetse",
          "Sandwich ya turkey ku ifunguro ry'ibinyampeke",
          "Stir-fry y'imboga na rice y'umweru",
          "Isupu n'igice cya salade",
        ],
      },
      dinner: {
        title: "3. Inama zo Gutegura Ibiryo byijimye",
        items: [
          "Salmon yokeje ifite indimu n'asperagus",
          "Spaghetti ifite marinara sauce na meatballs",
          "Curry y'imboga ifite basmati rice",
          "Steak yokeje ifite ibirayi byokeje",
          "Bell peppers zifite quinoa na cheese",
        ],
      },
    },
  },

  // =========================
  // PRESETS
  // =========================
  presets: {
    "edit-title": "Hindura Preset",
    description: "Ibisobanuro",
    "description-placeholder": "Subiza n'indirimbo ku bijyanye na LLM.",
    deleting: "Gusiba...",
    "delete-preset": "Siba Preset",
    cancel: "Hagarika",
    save: "Bika",
    "add-title": "Ongeraho Preset",
    "command-label": "Itegeko",
    "command-placeholder": "/law",
    "command-desc": "Itegeko rizatuma iyi preset itangira.",
    "prompt-label": "Prompt",
    "prompt-placeholder": "Subiza n'indirimbo ku bijyanye na LLM.",
    "prompt-desc":
      "The prompt that will be sent when this prompt preset is used.",
    "tooltip-add": "Ongeraho Preset",
    "tooltip-hover": "Bakurikira iyi preset.",
    "confirm-delete": "Emeza gusiba igipimo cy'ibanze cy'ubusabe.",
  },

  // =========================
  // LEGAL CATEGORIES
  // =========================
  "legal-categories": {
    process: "Urubanza",
    "process-stamning": "Ikirego",
    "process-svaromal": "Isubizo ku kirego",
    "process-yrkanden": "Ibyifuzo n'imyanzuro",
    avtal: "Amasezerano",
    "avtal-anstallning": "Amasezerano y'akazi",
    "avtal-finansiering": "Amasezerano y'inguzanyo n'ubwishingire",
    "avtal-licens": "Amasezerano y'uburenganzira bwo gukoresha",
    "due-diligence": "Iperereza ry'ubucuruzi",
    "due-diligence-avtal": "Kureba amasezerano",
    "due-diligence-checklista": "Urutonde rw'iperereza",
    "due-diligence-compliance": "Gusuzuma kubahiriza amategeko",
  },

  // =========================
  // VALIDATION
  // =========================
  validation: {
    responseHeader: "Dore Igisubizo cyabonywe",
    contextHeader: "Context n'Inkomoko y'ibanze",
  },

  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================

  // Document Builder Page
  "document-builder": {
    title: "Igenamigambi ry'Inyandiko",
    description: "Kugenzura ibikoresho by'igenamigambi ry'inyandiko.",
    "toast-success": "Ibikoresho byavuguruwe",
    "toast-fail": "Kuvugurura ibikoresho byarananiranye",
    save: "Bika",
    saving: "Kubika...",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Prompts z'Incamake y'Inyandiko",
          description:
            "Tegura prompts za sisitemu n'umukoresha ku Ncamake y'Inyandiko.",
        },
        document_relevance: {
          title: "Prompts z'Isano ry'Inyandiko",
          description:
            "Tegura prompts za sisitemu n'umukoresha ku Isano ry'Inyandiko.",
        },
        section_drafting: {
          title: "Prompts zo Gukora Igice",
          description:
            "Tegura prompts za sisitemu n'umukoresha zo Gukora Igice.",
        },
        section_legal_issues: {
          title: "Prompts z'Ibibazo by'Amategeko by'Igice",
          description:
            "Tegura prompts za sisitemu n'umukoresha ku Bibazo by'Amategeko by'Igice.",
        },
        memo_creation: {
          title: "Prompts zo Gukora Inyandiko Ndende",
          description: "Tegura prompts zo Gukora Inyandiko Ndende.",
        },
        section_index: {
          title: "Prompts z'Urutonde rw'Ibice",
          description: "Tegura prompts z'Urutonde rw'Ibice.",
        },
        select_main_document: {
          title: "Prompts zo Guhitamo Inyandiko Nyamukuru",
          description:
            "Tegura prompts za sisitemu n'umukoresha zo Guhitamo Inyandiko Nyamukuru.",
        },
        section_list_from_main: {
          title: "Prompts z'Urutonde rw'Ibice Ruturutse ku Nyandiko Nyamukuru",
          description:
            "Tegura prompts za sisitemu n'umukoresha z'Urutonde rw'Ibice Ruturutse ku Nyandiko Nyamukuru.",
        },
        section_list_from_summaries: {
          title: "Prompts z'Urutonde rw'Ibice Ruturutse ku Ncamake",
          description:
            "Tegura prompts za sisitemu n'umukoresha z'Urutonde rw'Ibice Ruturutse ku Ncamake.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Incamake y'Inyandiko (Sisitemu)",
      "document-summary-system-description":
        "Prompt ya sisitemu yo kuyobora AI ku buryo bwo gukora incamake y'ibikubiye mu nyandiko n'uko bifitanye isano n'akazi k'amategeko.",
      "document-summary-user-label": "Incamake y'Inyandiko (Umukoresha)",
      "document-summary-user-description":
        "Urugero rwa prompt y'umukoresha rwo gukora incamake irambuye y'ibikubiye mu nyandiko bijyanye n'akazi k'amategeko runaka.",

      // Document Relevance
      "document-relevance-system-label": "Isano ry'Inyandiko (Sisitemu)",
      "document-relevance-system-description":
        "Prompt ya sisitemu yo gusuzuma niba inyandiko ifitanye isano n'akazi k'amategeko, itegereje igisubizo cy'ukuri/ikinyoma.",
      "document-relevance-user-label": "Isano ry'Inyandiko (Umukoresha)",
      "document-relevance-user-description":
        "Urugero rwa prompt y'umukoresha rwo kugenzura niba ibikubiye mu nyandiko bifitanye isano n'akazi k'amategeko runaka.",

      // Section Drafting
      "section-drafting-system-label": "Gukora Igice (Sisitemu)",
      "section-drafting-system-description":
        "Prompt ya sisitemu yo gukora igice kimwe cy'inyandiko mu buryo bw'amategeko bwiza, hakoreshejwe inyandiko n'ibitekerezo byihariye.",
      "section-drafting-user-label": "Gukora Igice (Umukoresha)",
      "section-drafting-user-description":
        "Urugero rwa prompt y'umukoresha rwo gukora igice kihariye cy'inyandiko y'amategeko, hitawe ku mutwe, akazi, inyandiko z'isoko, n'ibice bikikije.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Kugaragaza Ibibazo by'Amategeko by'Igice (Sisitemu)",
      "section-legal-issues-system-description":
        "Prompt ya sisitemu yo kugaragaza ingingo z'amategeko zihariye aho amakuru y'ibyabaye yakwegeranyirizwa gushyigikira itegurwa ry'igice cy'inyandiko.",
      "section-legal-issues-user-label":
        "Kugaragaza Ibibazo by'Amategeko by'Igice (Umukoresha)",
      "section-legal-issues-user-description":
        "Urugero rwa prompt y'umukoresha rwo gutanga urutonde rw'ingingo z'amategeko cyangwa amakuru yo gushaka amakuru y'ibanze afitanye isano n'igice kihariye cy'inyandiko n'akazi k'amategeko.",

      // Memo Creation
      "memo-creation-template-label":
        "Urugero Rusanzwe rwo Gukora Inyandiko Ndende",
      "memo-creation-template-description":
        "Urugero rwa prompt rwo gukora inyandiko ndende y'amategeko ivuga ku kibazo cy'amategeko kihariye, hitawe ku nyandiko zatanzwe n'umwirondoro w'akazi.",

      // Section Index
      "section-index-system-label": "Urutonde rw'Ibice (Sisitemu)",
      "section-index-system-description":
        "Prompt ya sisitemu yo gukora urutonde ruteye neza rw'ibice by'inyandiko y'amategeko.",

      // Select Main Document
      "select-main-document-system-label":
        "Guhitamo Inyandiko Nyamukuru (Sisitemu)",
      "select-main-document-system-description":
        "Prompt ya sisitemu yo kugaragaza inyandiko nyamukuru ifitanye isano cyane n'akazi k'amategeko mu ncamake nyinshi z'inyandiko.",
      "select-main-document-user-label":
        "Guhitamo Inyandiko Nyamukuru (Umukoresha)",
      "select-main-document-user-description":
        "Urugero rwa prompt y'umukoresha rwo kugaragaza inyandiko nyamukuru y'akazi k'amategeko hashingiwe ku ncamake z'inyandiko nyinshi.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Urutonde rw'Ibice Ruturutse ku Nyandiko Nyamukuru (Sisitemu)",
      "section-list-from-main-system-description":
        "Prompt ya sisitemu yo gukora urutonde rw'ibice ruteye nka JSON rw'inyandiko y'amategeko hashingiwe ku bikubiye mu nyandiko nyamukuru n'akazi k'amategeko.",
      "section-list-from-main-user-label":
        "Urutonde rw'Ibice Ruturutse ku Nyandiko Nyamukuru (Umukoresha)",
      "section-list-from-main-user-description":
        "Urugero rwa prompt y'umukoresha rwo gutanga akazi k'amategeko n'ibikubiye mu nyandiko nyamukuru kugira ngo hakorwe urutonde rw'ibice.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Urutonde rw'Ibice Ruturutse ku Ncamake (Sisitemu)",
      "section-list-from-summaries-system-description":
        "Prompt ya sisitemu yo gukora urutonde rw'ibice ruteye nka JSON hashingiwe ku ncamake z'inyandiko n'akazi k'amategeko igihe nta nyandiko nyamukuru ihari.",
      "section-list-from-summaries-user-label":
        "Urutonde rw'Ibice Ruturutse ku Ncamake (Umukoresha)",
      "section-list-from-summaries-user-description":
        "Urugero rwa prompt y'umukoresha rwo gutanga akazi k'amategeko n'incamake z'inyandiko kugira ngo hakorwe urutonde rw'ibice igihe nta nyandiko nyamukuru ihari.",
    },

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================

    "view-categories": "Reba ibyiciro byose",
    "hide-categories": "Hisha urutonde",
    "add-task": "Ongeraho ikibazo cy'amategeko",
    loading: "Gutangiza...",
    table: {
      title: "Ibibazo by'Amategeko",
      name: "Izina",
      "sub-category": "Agace",
      description: "Ibisobanuro",
      prompt: "Prompt y'Ikibazo cy'Amategeko",
      actions: "Ibikorwa",
      "no-tasks": "Nta bibazo by'amategeko bihari.",
      delete: "Gusiba",
      edit: "Hindura",
      "delete-confirm": "Uzi neza ko ushaka gusiba iki cyiciro?",
      "delete-success": "Icyiciro cyasibwe neza",
      "delete-error": "Gusiba icyiciro byarananiranye",
    },
    "create-task": {
      title: "Gukora Ikibazo cy'Amategeko",
      category: {
        name: "Izina ry'Icyiciro",
        desc: "Tanga izina ry'icyiciro cy'ibanze.",
        placeholder: "Andika izina ry'icyiciro",
        type: "Ubwoko bw'Icyiciro",
        new: "Gukora icyiciro gishya",
        existing: "Gukoresha icyiciro gisanzwe",
        select: "Hitamo icyiciro",
        "select-placeholder": "Hitamo icyiciro gisanzwe",
      },
      subcategory: {
        name: "Izina ry'Ikibazo cy'Amategeko",
        desc: "Tanga izina ry'ikibazo cy'amategeko.",
        placeholder: "Andika izina ry'ikibazo cy'amategeko",
      },
      description: {
        name: "Ibisobanuro n'Amabwiriza y'Umukoresha",
        desc: "Amakuru n'amabwiriza umukoresha azabona.",
        placeholder:
          "Sobanura urugero nk'ubwoko bw'inyandiko zikenewe koherezwa mu kazi kugira ngo ibisubizo bibe byiza bishoboka",
      },
      prompt: {
        name: "Prompt y'Ikibazo cy'Amategeko",
        desc: "Iyi prompt iyobora LLM mu gukora gahunda y'ibikorwa yihariye ishingiye ku kibazo cyatoranyijwe.",
        placeholder: "Andika izina rya prompt y'amategeko",
      },
      submitting: "Kohereza...",
      submit: "Ohereza",
      validation: {
        "category-required": "Izina ry'icyiciro rirakenewe.",
        "subcategory-required": "Izina ry'ikibazo cy'amategeko rirakenewe.",
        "description-required": "Ibisobanuro birakenewe.",
        "prompt-required": "Izina rya prompt y'amategeko rirakenewe.",
      },
    },
    "edit-task": {
      title: "Hindura Ikibazo cy'Amategeko",
      submitting: "Kubika...",
      submit: "Ohereza",
      subcategory: {
        name: "Izina ry'Icyiciro",
        desc: "Tanga izina ry'icyiciro cy'ibanze.",
        placeholder: "Andika izina ry'icyiciro",
      },
      description: {
        name: "Ibisobanuro n'Amabwiriza y'Umukoresha",
        desc: "Amakuru n'amabwiriza umukoresha azabona.",
        placeholder:
          "Sobanura urugero nk'ubwoko bw'inyandiko zikenewe koherezwa mu kazi kugira ngo ibisubizo bibe byiza bishoboka",
      },
      prompt: {
        name: "Prompt y'Ikibazo cy'Amategeko",
        desc: "Enter the prompt that will be used for this legal task. You can also upload example documents using the buttons above to add content examples to your prompt.",
        placeholder: "Andika prompt y'amategeko...",
      },
      validation: {
        "subcategory-required": "Izina ry'icyiciro rirakenewe.",
        "description-required": "Ibisobanuro birakenewe.",
        "prompt-required": "Izina rya prompt y'amategeko rirakenewe.",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Hitamo inyandiko nkuru birasabwa",
      "requires-main-doc-description":
        "Niba byemejwe, umukoresha agomba guhitamo inyandiko nkuru mu madosiye yoherejwe mugihe akora iki gikorwa. Ibi birasabwa cyane ku bikorwa by'amategeko bijyanye no gusubiza ibaruwa cyangwa inyandiko y'urukiko cyangwa ibisa na byo, kuko bituma igisubizo gishingira ku nyandiko isubizwa.",
      "requires-main-doc-placeholder": "Yego cyangwa Oya",
      "requires-main-doc-explanation-default":
        "Guhitamo birakenewe kuko ibi bigena uburyo inyandiko izubakwa.",
      "requires-main-doc-explanation-yes":
        "Niba 'Yego', umukoresha azasabwa guhitamo inyandiko nkuru igihe atangiza iki gikorwa cy'amategeko. Iyi nyandiko izaba ifite uruhare runini mu mikorere y'iki gikorwa.",
      "requires-main-doc-explanation-no":
        "Niba 'Oya', igikorwa cy'amategeko kizakomeza nta nyandiko nkuru isanzwe ihitamo isabwa. Igikorwa kizakora ibisubizo mu buryo bwimbitse bushingiye ku nyandiko zose zoherejwe n'igikorwa cy'amategeko.",
    },
    reviewGeneratorPromptButton: "Reba Generatorprompt",
    reviewGeneratorPromptButtonTooltip:
      "Reba prompt y'amategeko yatanzwe ku kibazo cyatoranyijwe.",
    reviewGeneratorPromptTitle: "Reba Generatorprompt",
    reviewPromptLabel: "Iyi prompt izabona:",
    reviewPromptTextareaLabel: "Inyandiko nkuru",
  },

  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Iyandikishe umushinga wa Rexor",
    "project-id": "ID y'umushinga",
    "resource-id": "ID y'ibikoresho",
    "activity-id": "ID y'igikorwa",
    register: "Iyandikishe umushinga",
    registering: "Birimo kwandikwa...",
    "not-active": "Uyu mwanya nturimo gukora kugirango wandikwe",
    account: {
      title: "Injira muri Rexor",
      username: "Izina ryo kwinjira",
      password: "Ijambo ry'ibanga",
      "no-token": "Nta token yakiriwe muri handleLoginSuccess",
      logout: "Sohoka",
      "no-user": "Nyamuneka injira mbere",
      connected: "Wahurijwe na Rexor",
      "not-connected": "Ntabwo wahurijwe",
      "change-account": "Hindura konti",
      "session-expired": "Igihe cyarangiye. Nyamuneka ongera winjire.",
    },
    "hide-article-transaction": "Hisha ifishi y'igihe",
    "show-article-transaction": "Erekana ifishi y'igihe",
    "article-transaction-title": "Ongeraho igikorwa cy'igihe",
    "registration-date": "Itariki yo kwandikwa",
    description: "Ibisobanuro",
    "description-internal": "Ibisobanuro by'imbere",
    "hours-worked": "Amasaha yakoze",
    "invoiced-hours": "Amasaha yishyuwe",
    invoiceable: "Bishobora kwishyurwa",
    "sending-article-transaction": "Kohereza igikorwa cy'igihe...",
    "save-article-transaction": "Bika igikorwa cy'igihe",
    "project-not-register": "Umushinga ugomba kwandikwa mbere.",
    "article-transaction-error": "Ntibishobotse kwandika igikorwa cy'igihe",
    "not-exist": "Iki kibazo nticyashoboye kuboneka",
    "invoice-text": "Umubare w'ishakisha rya Foynet",
  },

  // =========================
  // OPTIONS
  // =========================
  options: {
    yes: "Yego",
    no: "Oya",
  },

  // =========================
  // GENERIC PROVIDER SELECTION SETTINGS
  // =========================

  // =========================
  // GENERIC MODEL SELECTION SETTINGS
  // =========================

  // =========================
  // ANTHROPIC
  // =========================

  // =========================
  // AZURE SETTINGS
  // =========================

  // =========================
  // AWS BEDROCK SETTINGS
  // =========================

  // =========================
  // COHERE
  // =========================

  // =========================
  // DEEPSEEK LLM SETTINGS
  // =========================

  // =========================
  // FIREWORKSAI
  // =========================

  // =========================
  // GEMINI LLM
  // =========================

  // =========================
  // GROQ
  // =========================

  // =========================
  // HUGGINGFACE
  // =========================

  // =========================
  // KOBOLDCPP
  // =========================

  // =========================
  // DOCX EDITOR
  // =========================
  "docx-edit": {
    "edit-instructions":
      "Andika amabwiriza y'uburyo ushaka guhindura inyandiko. Garagaza neza impinduka ushaka gukora.",
    "instructions-placeholder":
      "urugero, Kosora amakosa y'ururimi, gira imvugo isobanutse, ongeramo igika cya nyuma...",
    "process-button": "Kuramo inyandiko",
    "upload-docx": "Ohereza DOCX",
    "processing-upload": "Kurimo gukora...",
    "content-extracted": "Ibikubiye muri dosiye ya DOCX byakuwe",
    "file-type-note": "Dosiye za .docx gusa nizo zemewe",
    "upload-error": "Ikosa mu kohereza dosiye: ",
    "no-instructions": "Andika amabwiriza yo guhindura",
    "process-error": "Ikosa mu gukora inyandiko: ",
    "changes-highlighted": "Inyandiko n'impinduka zigaragara",
    "download-button": "Kuramo inyandiko",
    "start-over-button": "Tangira bundi bushya",
    "no-document": "Nta nyandiko iboneka yo gukuramo",
    "download-error": "Ikosa mu gukuramo inyandiko: ",
    "download-success": "Inyandiko yakuwe neza",
    processing: "Gukora inyandiko...",
    "instructions-used": "Amabwiriza yakoreshejwe",
    "import-success": "Ibikubiye muri DOCX byinjijwe neza",
    "edit-success": "Ibikubiye muri DOCX byavuguruwe neza",
    "canvas-document-title": "Inyandiko ya Canvas",
    "upload-button": "Ohereza DOCX",
    "download-as-docx": "Kuramo nka DOCX",
    "output-example": "Urugero rw'igisubizo",
    "output-example-desc":
      "Ohereza dosiye ya DOCX kugira ngo wongeremo urugero rw'ibikubiyemo kuri prompt yawe",
    "content-examples-tag-open": "<INGERO_Y_IBIKUBIYEMO>",
    "content-examples-tag-close": "</INGERO_Y_IBIKUBIYEMO>",
    "content-examples-info":
      "<INFO>Iki ni urugero rw'ibikubiyemo bigomba gukurwa, biturutse ku kibazo cy'amategeko gisa n'iki. Menya ko uru rugero rushobora kuba rugufi cyangwa rurerure kuruta ibikubiyemo bigomba gukurwa ubu.</INFO>",
    "contains-example-content": "[Irimo urugero rw'ibikubiyemo]",
  },

  // =========================
  // LITELLM
  // =========================

  // =========================
  // LMSTUDIO
  // =========================

  // =========================
  // LOCALAI
  // =========================

  // =========================
  // MISTRAL
  // =========================

  // =========================
  // NATIVE LLM
  // =========================

  // =========================
  // OLLAMA LLM
  // =========================

  // =========================
  // OPENAI
  // =========================

  // =========================
  // OPENROUTER
  // =========================

  // =========================
  // PERPLEXITY
  // =========================

  // =========================
  // TEXTGENWEBUI
  // =========================
  textgenwebui: {
    "base-url": "URL y'ibanze",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "token-window": "Idirishya ry'ibimenyetso",
    "token-window-placeholder": "Urugero rw'idirishya (urugero: 4096)",
    "api-key": "Urufunguzo rwa API (Bishoboka)",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa TextGen Web UI",
    "max-tokens": "Umubare ntarengwa wa tokens",
    "max-tokens-placeholder":
      "Umubare ntarengwa wa tokens ku cyifuzo (urugero: 1024)",
  },

  // =========================
  // TOGETHERAI
  // =========================

  // =========================
  // XAI
  // =========================

  // =========================
  // JINA EMBEDDING
  // =========================
  jina: {
    "api-key": "Jina API Key",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa Jina",
    "model-preference": "Ihitamo rya Modeli",
    "api-key-format": "Jina API key must start with 'jina_'",
  },

  // =========================
  // OLLAMA EMBEDDING
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Uburebure ntarengwa bw'igice cyo kwinjiza",
  },

  // =========================
  // VOYAGEAI EMBEDDING
  // =========================
  voyageai: {
    "api-key": "VoyageAI API Key",
    "api-key-placeholder": "Injiza urufunguzo rwa API rwa VoyageAI",
    "model-preference": "Ihitamo rya Modeli",
  },

  // =========================
  // METRICS VISIBILITY
  // =========================
  "metrics.visibility.hover": "Metrics zirahari.",
  "metrics.visibility.available": "Metrics ziboneka.",

  // =========================
  // PROMPT ERRORS
  // =========================
  prompt: {
    error: {
      empty: "Prompt ntishobora kuba ubusa",
      upgrade: "Ikosa mu kongera prompt",
    },
    decline: "Kurandura",
  },

  // =========================
  // AGENT MENU
  // =========================
  "agent-menu.default-agent": "Agent usanzwe",
  "agent-menu.ability.rag-search": "RAG Search",
  "agent-menu.ability.web-scraping": "Gusora urubuga",
  "agent-menu.ability.web-browsing": "Kureba urubuga",
  "agent-menu.ability.save-file-to-browser": "Kubika dosiye muri browser",
  "agent-menu.ability.list-documents": "Urutonde rw'inyandiko",
  "agent-menu.ability.summarize-document": "Gusubiramo inyandiko",
  "agent-menu.ability.chart-generation": "Gukora grafike",

  // =========================
  // PIPER TTS OPTIONS
  // =========================
  piperTTS: {
    description:
      "Modeli za PiperTTS zikorera muri browser yawe kuburyo bwahoze. Ibi bishobora gufata umutungo mwinshi ku mashini zigoramye.",
    "voice-model": "Voice Model Selection",
    "loading-models": "-- loading available models --",
    "stored-indicator":
      '"✔" bivuze ko iyi modeli iri kubikwa locally kandi ntigomba kongera gukururwa.',
    "flush-cache": "Siba voice cache",
    "flush-success": "Amajwi yose yakuweho muri browser storage",
    demo: {
      stop: "Hagarika demo",
      loading: "Birimo gupakirwa ijwi",
      play: "Kanda ushakire urugero",
      text: "Murakaza neza kuri IST Legal!",
    },
  },

  // =========================
  // ADMIN AGENTS
  // =========================
  agents: {
    title: "Ubushobozi bw'Agent",
    "agent-skills": "Tegura kandi ucunge ubushobozi bw'agent",
    "custom-skills": "Ubushobozi Bwihariye",
    back: "Gusubira Inyuma",
    "select-skill": "Hitamo ubushobozi bwo gutegura",
    "preferences-saved": "Amahitamo y'agent yabitswe neza",
    "preferences-failed": "Amahitamo y'agent ntabwo yabitswe",
    "skill-status": {
      on: "Arakora",
      off: "Ntagikora",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chatSettings: {
    placeholder: {
      drafting:
        "Ubona ibi biganiro, context ijyanye, ndetse n'ikibazo kigiyeho. Subiza ikibazo kijyanye, ntucengere ibisubizo bitari byo.",
      "legal-questions":
        "Ni ibihe bibazo by'amategeko biva kuri context hamwe n'iyi prompt",
      "legal-memo": "Tanga memo kuri buri kibazo cy'amategeko",
    },
  },

  // =========================
  // LOGGING
  // =========================
  logging: {
    show: "erekana",
    hide: "hisha",
    "event-metadata": "Event Metadata",
  },

  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Erekana Code",
    enable: "Emeza",
    disable: "Funga",
    "all-domains": "zose",
    "disable-confirm":
      "Urabyizeye ko ushaka gufunga iyi embed?\nNiba ibaye ifunzwe ntishobora kongera gusubiza ibiganiro.",
    "delete-confirm":
      "Urabyizeye ko ushaka gusiba iyi embed?\nNiba isibwe ntishobora kongera gukoreshwa.\n\nIbi ntibishobora gusubirwamo.",
    "disabled-toast": "Embed irafunzwe",
    "enabled-toast": "Embed irakora",
  },

  // =========================
  // BADGES
  // =========================
  badges: {
    default: {
      text: "Default",
      tooltip:
        "Ubu buhanga bukoreshwa ku buryo bw'ibanze kandi ntibushobora guhagarikwa.",
    },
  },

  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Common strings
    "provider-logo": "{{provider}} ikirango",

    // LMStudio Embedding Options
    lmstudio: {
      "model-label": "Icyitegererezo cyo kwinjiza cya LM Studio",
      "max-chunk-length": "Uburebure ntarengwa bw'igice",
      "max-chunk-length-help":
        "Uburebure ntarengwa bw'uduce tw'inyandiko bwo kwinjiza.",
      "hide-endpoint": "Hisha uburyo bwo kwinjiza endpoint y'intoki",
      "show-endpoint": "Erekana uburyo bwo kwinjiza endpoint y'intoki",
      "base-url": "URL shingiro ya LM Studio",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Injiza URL aho LM Studio iri gukorera.",
      "auto-detect": "Kumenya byikora",
      "loading-models": "--iri gupakurura ibyitegererezo biboneka--",
      "enter-url-first": "Injiza URL ya LM Studio mbere",
      "model-help":
        "Hitamo icyitegererezo cyo kwinjiza cya LM Studio. Ibyitegererezo bizapakururwa nyuma yo kwinjiza URL ya LM Studio iboneye.",
      "loaded-models": "Ibyitegererezo byawe byapakuruwe",
    },
    // Ollama Embedding Options
    ollama: {
      "model-label": "Icyitegererezo cyo kwinjiza cya Ollama",
      "max-chunk-length": "Uburebure ntarengwa bw'igice",
      "max-chunk-length-help":
        "Uburebure ntarengwa bw'uduce tw'inyandiko bwo kwinjiza.",
      "hide-endpoint": "Hisha uburyo bwo kwinjiza endpoint y'intoki",
      "show-endpoint": "Erekana uburyo bwo kwinjiza endpoint y'intoki",
      "base-url": "URL shingiro ya Ollama",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Injiza URL aho Ollama iri gukorera.",
      "auto-detect": "Kumenya byikora",
      "loading-models": "--iri gupakurura ibyitegererezo biboneka--",
      "enter-url-first": "Injiza URL ya Ollama mbere",
      "model-help":
        "Hitamo icyitegererezo cyo kwinjiza cya Ollama. Ibyitegererezo bizapakururwa nyuma yo kwinjiza URL ya Ollama iboneye.",
      "loaded-models": "Ibyitegererezo byawe byapakuruwe",
    },
    // LiteLLM Embedding Options
    litellm: {
      "model-label": "Hitamo icyitegererezo cyo kwinjiza",
      "max-chunk-length": "Uburebure ntarengwa bw'igice",
      "max-chunk-length-help":
        "Uburebure ntarengwa bw'uduce tw'inyandiko bwo kwinjiza.",
      "api-key": "Urufunguzo rwa API",
      optional: "by'ubushake",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- iri gupakurura ibyitegererezo biboneka --",
      "waiting-url": "-- turi gutegereza URL --",
      "loaded-models": "Ibyitegererezo byawe byapakuruwe",
      "model-tooltip": "Reba ibyitegererezo byo kwinjiza byashyigikiwe kuri",
      "model-tooltip-link": "inyandiko za LiteLLM",
      "model-tooltip-more": "kubindi bisobanuro ku byitegererezo biboneka.",
    },
    // Cohere Embedding Options
    cohere: {
      "api-key": "Urufunguzo rwa API rwa Cohere",
      "api-key-placeholder": "Injiza urufunguzo rwa API rwawe rwa Cohere",
      "model-label": "Hitamo icyitegererezo",
      "available-models": "Ibyitegererezo byo kwinjiza biboneka",
    },
    // Jina Embedding Options
    jina: {
      "api-key": "Urufunguzo rwa API rwa Jina",
      "api-key-placeholder": "Injiza urufunguzo rwa API rwawe rwa Jina",
      "api-key-error": "Urufunguzo rwa API rugomba gutangira na 'jina_'",
      "model-label": "Hitamo icyitegererezo",
      "available-models": "Ibyitegererezo byo kwinjiza biboneka",
      "embedding-type": "Ubwoko bwo kwinjiza",
      "available-types": "Ubwoko bwo kwinjiza buboneka",
      dimensions: "Ibyipimo",
      "available-dimensions": "Ibyipimo biboneka",
      task: "Umurimo",
      "available-tasks": "Imirimo iboneka",
      "late-chunking": "Gutandukanya ibice nyuma",
      "late-chunking-help":
        "Hemeza gutandukanya ibice nyuma mu gutunganya inyandiko",
    },
    // LocalAI Embedding Options
    localai: {
      "model-label": "Izina ry'icyitegererezo cyo kwinjiza",
      "hide-endpoint": "Hisha amahitamo y'inyongera",
      "show-endpoint": "Erekana amahitamo y'inyongera",
      "base-url": "URL shingiro ya LocalAI",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Injiza URL aho LocalAI iri gukorera.",
      "auto-detect": "Kumenya byikora",
      "loading-models": "-- iri gupakurura ibyitegererezo biboneka --",
      "waiting-url": "-- turi gutegereza URL --",
      "loaded-models": "Ibyitegererezo byawe byapakuruwe",
    },
    // Generic OpenAI-Compatible Embedding Options
    generic: {
      "base-url": "URL shingiro",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help":
        "Injiza URL shingiro y'aho API yawe ihuje na OpenAI iri.",
      "model-label": "Icyitegererezo cyo kwinjiza",
      "model-placeholder":
        "Injiza izina ry'icyitegererezo (urugero: text-embedding-ada-002)",
      "model-help": "Tanga indangamuntu y'icyitegererezo cyo gukora kwinjiza.",
      "api-key": "Urufunguzo rwa API",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Injiza urufunguzo rwa API rwawe kugira ngo wemezwe.",
    },
    // OpenAI Embedding Options
    openai: {
      "api-key": "Urufunguzo rwa API rwa OpenAI",
      "api-key-placeholder": "Injiza urufunguzo rwa API rwawe rwa OpenAI",
      "model-label": "Hitamo icyitegererezo",
      "available-models": "Ibyitegererezo byo kwinjiza biboneka",
    },
    // VoyageAI Embedding Options
    voyageai: {
      "api-key": "Urufunguzo rwa API rwa VoyageAI",
      "api-key-placeholder": "Injiza urufunguzo rwa API rwawe rwa VoyageAI",
      "model-label": "Hitamo icyitegererezo",
      "available-models": "Ibyitegererezo byo kwinjiza biboneka",
    },
    // Azure OpenAI Embedding Options
    azureai: {
      "service-endpoint": "Iherezo rya serivisi ya Azure OpenAI",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help":
        "Injiza URL y'iherezo rya serivisi ya Azure OpenAI",
      "api-key": "Urufunguzo rwa API rwa Azure OpenAI",
      "api-key-placeholder": "Injiza urufunguzo rwa API rwawe rwa Azure OpenAI",
      "api-key-help":
        "Injiza urufunguzo rwa API rwa Azure OpenAI kugira ngo wemezwe",
      "deployment-name":
        "Izina ry'ishyirwa mu bikorwa ry'icyitegererezo cyo kwinjiza",
      "deployment-name-placeholder":
        "Injiza izina ry'ishyirwa mu bikorwa ry'icyitegererezo cyo kwinjiza cya Azure OpenAI",
      "deployment-name-help":
        "Izina ry'ishyirwa mu bikorwa ry'icyitegererezo cyo kwinjiza cya Azure OpenAI",
    },
    // Native Embedding Options
    native: {
      description:
        "Ukoresha umuhawe wa kwinjiza gakondo mu gutunganya inyandiko",
    },
  },

  "appearance.siteSettings.tabIcon": "Ikoni y'umutwe",
  "appearance.siteSettings.fabIconUrl": "URL ya favicon",
  "appearance.siteSettings.placeholder": "Andika URL ya favicon",
  "appearance.siteSettings.title-placeholder": "Andika umutwe w'urubuga",
  "appearance.welcome.heading": "Murakaza neza kuri Foynet test",
  "appearance.welcome.text": "Hitamo igice cy'amategeko mu ruhande rw'ibumoso",

  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "Imfunguzo za API",
    description: "Gucunga imfunguzo za API zo guhuza kuri iyi seriveri.",
    "generate-key": "Kora urufunguzo rushya rwa API",
    "table-headers": {
      "connection-string": "Umurongo wo guhuza",
      "created-by": "Yakozwe na",
      "created-at": "Yaremwe",
      actions: "Ibikorwa",
    },
    "no-keys": "Nta mfunguzo za API zabonetse",
    modal: {
      title: "Urufunguzo rushya rwa API y'inyongera ya mushakisha",
      "multi-user-warning":
        "Iburira: Uri mu buryo bw'abakoresha benshi. Uru rufunguzo rwa API ruzatanga uburenganzira ku mbuga zose z'akazi zifitanye isano na konti yawe. Nyamuneka ugabanye witonze.",
      "create-description":
        'Nyuma yo gukanda "Kora urufunguzo rwa API", iyi seriveri izagerageza gukora urufunguzo rushya rwa API ku nyongera ya mushakisha.',
      "connection-help":
        'Niba ubona "Wahujwe na IST Legal" mu nyongera, guhuza byagenze neza. Niba atari ko biri, nyamuneka kora kopi y\'umurongo wo guhuza maze uwushyire mu nyongera ukoresha intoki.',
      cancel: "Kureka",
      "create-key": "Kora urufunguzo rwa API",
      "copy-key": "Kora kopi y'urufunguzo rwa API",
      "key-copied": "Urufunguzo rwa API rwakopiwe!",
    },
    tooltips: {
      "copy-connection": "Kora kopi y'umurongo wo guhuza",
      "auto-connect": "Huza mu buryo bwikora ku nyongera",
    },
    confirm: {
      revoke:
        "Uzi neza ko ushaka guhagarika uru rufunguzo rwa API y'inyongera ya mushakisha?\nNyuma y'ibi ntiruzongera gukoreshwa.\n\nIyi gahunda ntishobora gusubizwa inyuma.",
    },
    toasts: {
      "key-revoked":
        "Urufunguzo rwa API y'inyongera ya mushakisha rwahagaritswe burundu",
      "revoke-failed": "Ntibyashobotse guhagarika urufunguzo rwa API",
      copied: "Umurongo wo guhuza wakopiwe mu bubiko",
      connecting: "Kuragerageza guhuza ku nyongera ya mushakisha...",
    },
    "revoke-title": "Hagarika urufunguzo rwa API y'inyongera ya mushakisha",
    "revoke-message":
      "Uzi neza ko ushaka guhagarika uru rufunguzo rwa API y'inyongera ya mushakisha?\nNyuma y'ibi ntiruzongera gukoreshwa.\n\nIyi gahunda ntishobora gusubizwa inyuma.",
  },

  // =========================
  // EXPERIMENTAL
  // =========================
  experimental: {
    title: "Ibikorwa by'Igerageza",
    description: "Ibikorwa biri mu igerageza",
    "live-sync": {
      title: "Guhuza Inyandiko ku Muyoboro",
      description: "Gutangiza guhuza ibyanditswe biva mu masoko yo hanze",
      "manage-title": "Inyandiko zikurikiranwa",
      "manage-description":
        "Izi ni inyandiko zose zikurikiranwa kuri ubu muri sisitemu yawe. Ibyanditswe muri izi nyandiko bizajya bihurizwa hamwe buri gihe.",
      "document-name": "Izina ry'Inyandiko",
      "last-synced": "Igihe cya nyuma yahurijwe",
      "next-refresh": "Igihe gisigaye kugira ngo ivugururwe",
      "created-on": "Yaremwe ku wa",
      "auto-sync": "Guhuza Ibyanditswe ku Buryo Bwikora",
      "sync-description":
        "Gutangiza ubushobozi bwo kugena inkomoko y'ibyanditswe 'igomba gukurikiranwa'. Ibyanditswe bikurikiranwa bizajya bifatwa kandi bivugururwa muri iyi sisitemu.",
      "sync-workspace-note":
        "Ibyanditswe bikurikiranwa bizajya bivugururwa ku buryo bwikora mu mbuga zose z'akazi aho byavuzwe.",
      "sync-limitation":
        "Iyi feature ikoreshwa gusa ku byanditswe byo ku rubuga, nk'imbuga, Confluence, YouTube na dosiye za GitHub.",
      documentation: "Inyandiko z'Ibikorwa n'Imburizi",
      "manage-content": "Gucunga Ibyanditswe Bikurikiranwa",
    },
    tos: {
      title: "Amabwiriza yo gukoresha ibikorwa by'igerageza",
      description:
        "Ibikorwa by'igerageza kuri iyi platform ni ibikorwa turimo kugerageza kandi bitorwa ku bushake. Tuzakumenyesha mbere y'igihe ibibazo byose bishobora kubaho mbere yo kwemera igikorwa icyo ari cyo cyose.",
      "possibilities-title":
        "Gukoresha igikorwa kuri iyi paje bishobora gutanga, ariko ntibigomba kugera ku, ibi bikurikira:",
      possibilities: {
        "data-loss": "Gutakaza amakuru.",
        "quality-change": "Impinduka mu ngaruka z'ibisubizo.",
        "storage-increase": "Kwiyongera kw'ubwihugiko.",
        "resource-consumption": "Kwiyongera kw'imikoreshereze y'ibikoresho.",
        "cost-increase":
          "Kwiyongera kw'ikiguzi cyangwa imikoreshereze ya LLM cyangwa abatanga serivisi zo kwinjiza.",
        "potential-bugs":
          "Amakosa ashoboka cyangwa ibibazo mu gukoresha iyi porogaramu.",
      },
      "conditions-title":
        "Gukoresha igikorwa cy'igerageza biza n'urutonde rukurikira rw'amabwiriza atuzuye:",
      conditions: {
        "future-updates":
          "Igikorwa gishobora kutazaboneka mu ivugurura ry'ejo hazaza.",
        stability: "Igikorwa gikoreshwa ubu ntikimeze neza.",
        availability:
          "Igikorwa gishobora kutazaboneka mu bihe bizaza, ibigize cyangwa ubwishingizi bw'iyi sisitemu.",
        privacy:
          "Amabwiriza yawe y'ibanga azubahirizwa mu gukoresha igikorwa cya beta.",
        changes: "Aya mabwiriza ashobora guhinduka mu ivugurura ry'ejo hazaza.",
      },
      "read-more": "Niba ushaka gusoma byinshi ushobora kureba",
      contact: "cyangwa ukandikira",
      reject: "Kwanga & Gufunga",
      accept: "Ndabyumvise",
    },
    "update-failed": "Kuvugurura ibikorwa by'igerageza byarananiwe",
  },

  // =========================
  // LOADING MODELS
  // =========================
  "loading-models": "Gutangiza modeli...",

  // =========================
  // MODEL SELECTION
  // =========================
  "model-selection": "Guhitamo modeli",

  // =========================
  // TOAST
  // =========================
  toast: {
    success: "Byatunganye",
    error: "Ikosa",
    warning: "Iburira",
    info: "Amakuru",
    settings: {
      "welcome-messages-failed":
        "Ntibyashobotse kubika ubutumwa bwo kwakira: {{error}}",
      "welcome-messages-fetch-failed":
        "Ntibyashobotse kubona ubutumwa bwo kwakira",
      "welcome-messages-empty": "Nyamuneka andika umutwe cyangwa inyandiko",
      "welcome-messages-success": "Ubutumwa bwo kwakira bwabitswe neza",
      "prompt-examples-failed": "Ntibyashobotse ku kora ibiganiro byanditswe",
      "prompt-examples-success": "Ibiganiro byanditswe byatunganye",
      "prompt-examples-validation":
        "Ibiganiro {{number}} sakurikira ibyanditswe: {{fields}}",
    },
    document: {
      "move-success": "Ibyanditswe byatunganye {{count}}",
      "pdr-failed": "Ntibyashobotse ku PDR-Dokument: {{message}}",
      "watch-failed": "Ntibyashobotse ku Beobachten des Dokuments: {{message}}",
      "pdr-added": "Dokument hinzugefügt zu Parent Document Retrieval",
      "pdr-removed": "Dokument aus Parent Document Retrieval entfernt",
      "pin-success-added": "Dokument pinniert an workspace",
      "pin-success-removed": "Dokument entfernt von workspace",
    },
    experimental: {
      "feature-enabled": "Ibiranga by'igerageza byakozwe",
      "feature-disabled": "Ibiranga by'igerageza byafunzwe",
      "update-failed":
        "Kuvugurura imiterere y'ikiranga cy'igerageza ntibyakunze",
      "live-sync": {
        enabled: "Guhuza inyandiko ku muyoboro byakozwe",
        disabled: "Guhuza inyandiko ku muyoboro byafunzwe",
      },
      "features-enabled":
        "Ibiranga by'igerageza byakozwe. Urupapuro ruzongera gusubiramo.",
    },
  },

  "cdb-llm-preference": {
    title: "Ibihitamo bya CDB LLM",
    settings: "CDB LLM",
    description: "Gutunganya serivisi ya LLM kuri CDB",
  },
  "template-llm-preference": {
    title: "Ibihitamo bya LLM y'Icyitegererezo",
    settings: "LLM y'Icyitegererezo",
    description:
      "Hitamo serivisi ya LLM ikoreshwa mugutunganya inyandiko z'icyitegererezo. Niba ntacyahiswemo, ikoreshwa iya sisitemu.",
    "toast-success": "Ibihitamo bya LLM y'icyitegererezo byavuguruwe",
    "toast-fail": "Byanze kuvugurura LLM y'icyitegererezo",
    saving: "Birimo Kubika...",
    "save-changes": "Bika Impinduka",
  },
  "custom-user-ai": {
    title: "AI y'Umukoresha Yihariye",
    settings: "AI y'Umukoresha Yihariye",
    description: "Gutunganya serivisi ya AI y'Umukoresha Yihariye",
    "custom-model-reference": "Izina Ry'icyitegererezo Ryihariye & Ibisobanuro",
    "custom-model-reference-description":
      "Ongeraho indangagaciro yihariye kuri iyi modeli. Bizagaragara iyo ukoresha icyiciro cya AI y'Umukoresha Yihariye mu gice cyo guhitamo.",
    "custom-model-reference-name": "Izina Ry'icyitegererezo Ryihariye",
    "custom-model-reference-description-label":
      "Ibisobanuro by'Icyitegererezo (Si ngombwa)",
    "custom-model-reference-description-placeholder":
      "Andika ibisobanuro by'inyongera kuri iyi modeli (si ngombwa)",
    "custom-model-reference-name-placeholder":
      "Andika izina ryihariye kuri iyi modeli",
    "model-ref-placeholder":
      "Andika izina ryihariye cyangwa ibisobanuro kuri iyi setup y'icyitegererezo",
    "enter-custom-model-reference": "Andika izina ryihariye kuri uru rugero",
    "standard-engine": "Moteri ya AI Isanzwe",
    "standard-engine-description":
      "Moteri yacu isanzwe igira akamaro ku mirimo myinshi",
    "dynamic-context-window-percentage":
      "Ijanisha ry'Idirishya ry'Inyandiko Ihinduka",
    "dynamic-context-window-percentage-desc":
      "Igenzura ingano y'idirishya ry'inyandiko ya LLM ishobora gukoreshwa ku masoko y'inyongera (10-100%)",
    "no-alternative-title": "Nta Rugero Rw'Ihitamo Rwatoranyijwe",
    "no-alternative-desc":
      "Iyo iri hitamo ryatoranyijwe, abakoresha ntibashobora guhitamo urugero rw'ihitamo.",
    "select-option": "Hitamo Profili ya AI Yihariye",
    "option-number": "Hitamo {{number}}",
    "llm-provider-selection": "Guhitamo Umuhuza wa LLM",
    "llm-provider-selection-desc":
      "Hitamo umuhuza wa LLM kuri iyi ntunganyabwishyu ya AI yihariye",
    "custom-option": "Hitamo Yihariye",
    tab: {
      "custom-1": "Moteri ya AI Isanzwe 1",
      "custom-2": "Moteri ya AI Isanzwe 2",
      "custom-3": "Moteri ya AI Isanzwe 3",
      "custom-4": "Moteri ya AI Isanzwe 4",
      "custom-5": "Moteri ya AI Isanzwe 5",
      "custom-6": "Moteri ya AI Isanzwe 6",
    },
    engine: {
      "custom-1": "Moteri ya AI Isanzwe 1",
      "custom-2": "Moteri ya AI Isanzwe 2",
      "custom-3": "Moteri ya AI Isanzwe 3",
      "custom-4": "Moteri ya AI Isanzwe 4",
      "custom-5": "Moteri ya AI Isanzwe 5",
      "custom-6": "Moteri ya AI Isanzwe 6",
      "custom-1-title": "Moteri ya AI Isanzwe 1",
      "custom-2-title": "Moteri ya AI Isanzwe 2",
      "custom-3-title": "Moteri ya AI Isanzwe 3",
      "custom-4-title": "Moteri ya AI Isanzwe 4",
      "custom-5-title": "Moteri ya AI Isanzwe 5",
      "custom-6-title": "Moteri ya AI Isanzwe 6",
      "custom-1-description": "Ihitamo rwa Moteri ya AI Isanzwe 1",
      "custom-2-description": "Ihitamo rwa Moteri ya AI Isanzwe 2",
      "custom-3-description": "Ihitamo rwa Moteri ya AI Isanzwe 3",
      "custom-4-description": "Ihitamo rwa Moteri ya AI Isanzwe 4",
      "custom-5-description": "Ihitamo rwa Moteri ya AI Isanzwe 5",
      "custom-6-description": "Ihitamo rwa Moteri ya AI Isanzwe 6",
    },
    saving: "Kuvugurura...",
    "save-changes": "Kuvugurura",
    "model-ref-saved": "Ihitamo rwa Moteri ya AI Isanzwe byakozwe",
    "model-ref-save-failed":
      "Ntibyashobotse ku kuvugurura ihitamo rwa Moteri ya AI Isanzwe: {{error}}",
    "llm-settings-save-failed":
      "Ntibyashobotse ku kuvugurura ihitamo rwa LLM: {{error}}",
    "settings-fetch-failed": "Ntibyashobotse ku kuvugurura ihitamo rwa LLM",
    "llm-saved": "Ihitamo rwa LLM byakozwe",
    "select-provider-first":
      "Nyamuneka guhitamo umuhuza wa LLM kuri iyi ntunganyabwishyu ya AI yihariye. Niba iyi ihitamo byakozwe, iyi ihitamo byatunganye ku mbuga zose z'akazi.",
  },

  promptLogging: {
    title: "Kwandika ibisubizo by'amabwiriza",
    description:
      "Kugenzura cyangwa guhagarika kwandika ibisubizo by'amabwiriza ku igenzura rya sisitemu.",
    label: "Kwandika ibisubizo by'amabwiriza: ",
    state: {
      enabled: "Byemewe",
      disabled: "Byahagaritswe",
    },
  },

  userAccess: {
    title: "Kwemerera abakoresha",
    description:
      "Kwemerera abakoresha basanzwe kugera ku mirimo y'amategeko. Mu buryo busanzwe, superusers, abayobozi n'abagenzuzi nibo bonyine bemerewe.",
    label: "Uburenganzira bw'abakoresha: ",
    state: {
      enabled: "Byemewe",
      disabled: "Byahagaritswe",
    },
  },

  // =========================
  // CHAT LOGS & PREVIEW
  // =========================
  chat_logs: {
    display_description:
      "Erekana inyandiko z'amakuru mabisi, fungura kandi ukurure dosiye",
    display_prompt_output: "Erekana amakuru mabisi",
    loading_prompt_output: "Gutaha amakuru mabisi...",
    not_available: "*** Amakuru mabisi ntababoneka kuri iyi chat.",
    token_count: "Utugero (mu makuru mabisi yose): {{count}}",
    token_count_detailed:
      "Utugero kuri LLM: {{promptTokens}} | Utugero mu gisubizo LLM: {{completionTokens}} | Igiteranyo cy'utugero: {{totalTokens}}",
  },

  // LLM Provider specific translations
  "llm-provider.textgenwebui": "Connect to a Text Generation WebUI instance.",
  "llm-provider.litellm": "Connect to any LLM via LiteLLM.",
  "llm-provider.openai-generic":
    "Connect to any OpenAI-compatible API endpoint.",
  "llm-provider.system-default": "Use the built-in Native model.",

  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Idirishya ry'umwanya",
    "max-output-tokens": "Umubare ntarengwa w'utugero tw'ibisubizo",
    "output-limit": "Ikigero ntarengwa cy'ibisobanuro",
    tokens: "utugero",
    "fallback-value": "Agaciro kariho kakoreshejwe",
  },

  // =========================
  // LEGAL TEMPLATES MODAL
  // =========================

  // moved to legalTemplates

  // =========================
  // CUSTOM LEGAL TEMPLATES MODAL
  // =========================

  // moved to customLegalTemplates.js

  // =========================
  // VARIOUS MODALS AND NEW FEATURES
  // =========================

  // Organization & Organizations translations for user management
  organization: {
    label: "Ishyirahamwe",
    select: "-- Hitamo ishyirahamwe --",
    none: "Nta na rimwe",
    "create-new": "+ Kora ishyirahamwe rishya",
    "new-name": "Izina ry'ishyirahamwe rishya",
    "new-name-ph": "Andika izina ry'ishyirahamwe rishya",
  },
  organizations: {
    "fetch-error": "Ntibyashobotse kuzana amashyirahamwe",
  },

  // =========================
  // REQUEST LEGAL ASSISTANCE
  // =========================
  "request-legal-assistance": {
    title: "Gusaba ubufasha mu by'amategeko",
    description:
      "Gushyiraho iboneka rya buto yo gusaba ubufasha mu by'amategeko.",
    enable: "Gushoboza Gusaba Ubufasha mu by'Amategeko",
    "law-firm-name": "Izina ry'Ikigo cy'Amategeko",
    "law-firm-placeholder": "Andika izina ry'ikigo cy'amategeko",
    "law-firm-help":
      "Izina ry'ikigo cy'amategeko kizakora ibisabwa by'ubufasha mu by'amategeko",
    email: "Imeri y'Ubufasha mu by'Amategeko",
    "email-placeholder": "Andika aderesi imeri y'ubufasha mu by'amategeko",
    "email-help":
      "Aderesi imeri ibisabwa by'ubufasha mu by'amategeko bizoherezwaho",
    "settings-saved":
      "Amagenamiterere y'ubufasha mu by'amategeko yabitswe neza",
    "save-error":
      "Ntabwo byashobotse kubika amagenamiterere y'ubufasha mu by'amategeko",
    status: "Buto y'Ubufasha mu by'Amategeko: ",
    "load-error":
      "Ntabwo byashobotse gufungura amagenamiterere y'ubufasha mu by'amategeko",
    "save-button": "Bika impinduka",
    request: {
      title: "Gusaba ubufasha mu by'amategeko",
      description:
        "Ohereza icyifuzo kuri {{lawFirmName}} kugira ngo ubone ubufasha mu by'amategeko, kurangiza ubushakashatsi cyangwa izindi nama. Uzamenyeshwa kuri imeri igihe icyifuzo cyawe kizaba cyakiriwe.",
      button: "Gusaba ubufasha mu by'amategeko",
      message: "Ubutumwa",
      "message-placeholder":
        "Andika amabwiriza yihariye cyangwa amakuru ku itsinda ry'ubufasha mu by'amategeko",
      send: "Ohereza icyifuzo",
      cancel: "Kureka",
      error: "Ntabwo byashobotse kohereza icyifuzo cy'ubufasha mu by'amategeko",
      success: "Icyifuzo cy'ubufasha mu by'amategeko cyoherejwe neza",
      submitting: "Kohereza icyifuzo...",
      submit: "Ohereza Icyifuzo",
      partyName: "Izina ry'Ubucuruzi",
      partyOrgId: "Numero ya Ubuzima",
      partyNamePlaceholder: "Andika izina ry'Ubucuruzi",
      partyOrgIdPlaceholder: "Andika numero ya Ubuzima",
      partyNameRequired: "Izina ry'Ubucuruzi iri",
      partyOrgIdRequired: "Numero ya Ubuzima iri",
      opposingPartyName: "Izina ry'Ubucuruzi",
      opposingPartyOrgId: "Numero ya Ubuzima",
      opposingPartyNamePlaceholder: "Andika izina ry'Ubucuruzi",
      opposingPartyOrgIdPlaceholder: "Andika numero ya Ubuzima",
    },
  },

  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Uko igisubizo kigenda gitunganywa",
    description:
      "Displays the real-time progress of tasks for finishing prompt, depending on linking with other workspaces and size of files. The modal will close automatically once all steps are complete.",
    step_fetching_memos:
      "Gushaka amakuru y'amategeko ku nsanganyamatsiko ziriho",
    step_processing_chunks: "Gutunganya inyandiko zoherejwe",
    step_combining_responses: "Kurangiza igisubizo",
    sub_step_chunk_label: "Gutunganya itsinda ry'inyandiko {{index}}",
    sub_step_memo_label: "Yakuye amakuru y'amategeko muri {{workspaceSlug}}",
    placeholder_sub_task: "Intambwe iri ku murongo",
    desc_fetching_memos:
      "Gushaka amakuru y'amategeko ajyanye n'ibyo dukeneye mu bice by'akazi bifatanyije",
    desc_processing_chunks:
      "Gusesengura no gukura amakuru mu matsinda y'inyandiko",
    desc_combining_responses: "Guhuza amakuru mu gisubizo kirambuye",
  },

  // =========================
  // MCP SERVER PAGE
  // =========================

  mcp: {
    title: "Gucunga Seriveri ya MCP",
    description:
      "Gucunga iboneza bya seriveri ya Multi-Component Processing (MCP).",
    currentServers: "Seriveri Ziriho",
    noServers: "Nta seriveri za MCP ziteguwe.",
    fetchError: "Ntibyashobotse kuzana seriveri: {{error}}",
    addServerButton: "Ongeramo Seriveri Nshya",
    addServerModalTitle: "Ongeramo Seriveri Nshya ya MCP",
    addServerModalDesc: "Sobanura iboneza ku nzira nshya ya seriveri ya MCP.",
    serverName: "Izina rya Seriveri (ID Idasanzwe)",
    configJson: "Iboneza (JSON)",
    addButton: "Ongeramo Seriveri",
    addSuccess: "Seriveri yongerewe neza.",
    addError: "Ntibyashobotse kongeramo seriveri: {{error}}",
  },

  // =========================
  // ADMIN SYSTEM SETTINGS (UNIVERSITY MODE)
  // =========================
  admin: {
    system: {
      universityMode: {
        title: "Ubuzima bw'Ikigo cy'Amategeko",
        description:
          "Gishobora kugira ngo ukorera ibisabwa by'amategeko ku buto byo kuva kuva.",
        enable: "Gushobora kugira ngo ukorera Ubuzima bw'Ikigo cy'Amategeko",
        saved: "Ubuzima bw'Ikigo cy'Amategeko zabitswe neza.",
        error: "Ntibyashobotse kubika Ubuzima bw'Ikigo cy'Amategeko",
        saveChanges: "Kora Ubuzima bw'Ikigo cy'Amategeko",
      },
    },
  },

  // Months
  "month.1": "Mut.",
  "month.2": "Gas.",
  "month.3": "Wer",
  "month.4": "Mat.",
  "month.5": "Gic.",
  "month.6": "Kam.",
  "month.7": "Nya.",
  "month.8": "Kan.",
  "month.9": "Nze.",
  "month.10": "Ukw.",
  "month.11": "Ugu.",
  "month.12": "Uku.",

  // =========================
  // FEATURE CARDS
  // =========================
  featureCards: {
    "draft-from-template-title": "Kora inyandiko mbisikwa ukoresheje urugero",
    "draft-from-template-description":
      "Koresha iyi serivisi, urugero, mu gukora politiki yo kurwanya itubya ry'amafaranga (AML), inyandikomvugo y'inama rusange, cyangwa amasezerano ahuje uburyo bwo gukemura amakimbirane mu bucamanza butemewe n'amategeko.",
    "complex-document-builder-title": "Kora umurimo w'amategeko ugoye",
    "complex-document-builder-description":
      "Byiza cyane iyo, urugero, ukeneye gusuzuma amadosiye amagana mbere yo kugura ikigo cyangwa gutegura ikirego kirambuye.",
  },

  // =========================
  // WORKSPACE SELECTOR MODAL
  // =========================
  workspaceSelector: {
    chooseWorkspace: "Tangiza ikiganiro gishya",
    selectAiType: "Guhitamo moduli n'ahantu h'akazi ho gutangiza ikiranga",
    cloudAiDescription:
      "Ikoresha modeli ya AI ishingiye ku bicu ku kiganiro no gusubiza ibibazo. Inyandiko zawe zizatunganywa kandi zibikwe mu mutekano mu bicu.",
    localAiDescription:
      "Ikoresha modeli ya AI yo mu gace ku kiganiro no gutegura inyandiko. Inyandiko zawe zizatunganywa kandi zibikwe ku mashini yawe yo mu gace.",
    cloudAiDescriptionTemplateFeature:
      "Kurema urugero mu hantu h'akazi hasanzweho hamwe n'amakuru y'amategeko, urugero rushobora gukurura amakuru y'amategeko bitewe n'ikibazo.",
    localAiDescriptionTemplateFeature:
      "Kurema urugero mu hantu h'akazi bwite, ukoresheje AI yo mu gace niba ikoreshwa kuri seriveri.",
    cloudAiDescriptionComplexFeature:
      "Gutegura inyandiko zikomeye ntibiboneka kuri izi mbuga z'akazi, kuko umukoresha agomba kohereza inyandiko ku mbuga z'akazi mbere yo gutangira",
    localAiDescriptionComplexFeature:
      "Hitamo imwe mu mbuga z'akazi zawe yo gutangiza umurimo w'amategeko, kandi urebe ko inyandiko za ngombwa zoherejwe ku mbuga y'akazi mbere yo gutangira.",
    newWorkspaceComplexTaskInfo:
      "Niba urimo gukora ahantu h'akazi hashya, uzinjira mu mbonerahamwe yo kohereza kugira ngo wohereze inyandiko zose za ngombwa, ibyo bikenewe kugira ngo ushobore gukora inyandiko z'umurimo w'amategeko.",
    selectExistingWorkspace: "Hitamo ahantu h'akazi hasanzwe",
    selectExistingDocumentDraftingWorkspace:
      "Hitamo ahantu h'akazi hasanzwe ho gutegura inyandiko",
    orCreateNewBelow:
      "Cyangwa, kora ahantu h'akazi hashya ho gutegura inyandiko hasi.",
    newWorkspaceName: "Andika izina ry'ahantu h'akazi hawe hashya",
    newWorkspaceNameOptional:
      "Andika izina ry'ahantu h'akazi hawe hashya (niba udakoresha ahantu h'akazi hasanzwe)",
    workspaceNamePlaceholder: "urugero: Ahantu h'akazi hanjye hashya",
    next: "Komeza",
    pleaseSelectWorkspace: "Nyamuneka hitamo ahantu h'akazi.",
    workspaceNameRequired: "Izina ry'ahantu h'akazi rirakenewe.",
    workspaceNameOrExistingWorkspaceRequired:
      "Nyamuneka andika izina ry'ahantu h'akazi cyangwa uhitemo ahantu h'akazi hasanzwe.",
    workspaceNameMustBeMoreThanOneCharacter:
      "Izina ry'ahantu h'akazi rigomba kuba rirera inyuguti imwe.",
    noWorkspacesAvailable: "Nta hantu h'akazi hahari",
    selectWorkspacePlaceholder: "Nyamuneka hitamo ahantu h'akazi",
    featureUnavailable: {
      title: "Iyi serivisi ntiboneka",
      description:
        "Iyi serivisi ntabwo yemerewe kuri konti yawe cyangwa yahagaritswe muri iyi sisitemu. Nyamuneka vugana n'umuyobozi kugira ngo ashobore kuyemerera niba bikenewe.",
      close: "Funga",
    },
    createNewWorkspace: {
      title: "Kora ahantu h'akazi hashya ho gutegura inyandiko",
      description:
        "Ibi bizakora ahantu h'akazi hashya hagenewe gutegura inyandiko zikomeye hakoreshejwe inyandikorugero yatoranijwe.",
      workspaceName: "Izina ry'ahantu h'akazi",
      create: "Kora ahantu h'akazi",
    },
    selectExisting: {
      title: "Hitamo ahantu h'akazi ho kubaza ibibazo by'amategeko",
      description:
        "Hitamo ahantu h'akazi hasanzwe kugira ngo utangire ikiganiro cyo kubaza ibibazo by'amategeko.",
      selectWorkspace: "Hitamo ahantu h'akazi",
    },
  },
};

export default TRANSLATIONS;
