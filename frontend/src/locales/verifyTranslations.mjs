import {
  resources,
  supportedLanguageCodes,
  loadLanguageAsync,
} from "./resources.js";

const languageNames = new Intl.DisplayNames(supportedLanguageCodes, {
  type: "language",
});

function langDisplayName(lang) {
  return languageNames.of(lang);
}

// Helper function to get all keys from an object (including nested ones)
function getAllKeys(obj, parentKey = "") {
  return Object.entries(obj).reduce((keys, [key, value]) => {
    const currentKey = parentKey ? `${parentKey}.${key}` : key;
    if (typeof value === "object" && value !== null && !Array.isArray(value)) {
      return [...keys, ...getAllKeys(value, currentKey)];
    }
    return [...keys, currentKey];
  }, []);
}

function compareStructures(lang, source, target, subdir = null) {
  const missingKeys = [];

  function compare(a, b, path = "") {
    if (a && typeof a === "object") {
      const keysA = Object.keys(a).sort();
      const keysB = Object.keys(b || {}).sort();

      const missing = keysA.filter((key) => !keysB.includes(key));
      missing.forEach((key) => {
        missingKeys.push(path ? `${path}.${key}` : key);
      });

      keysA.forEach((key) => {
        if (b && typeof b === "object" && key in b) {
          compare(a[key], b[key], path ? `${path}.${key}` : key);
        }
      });
    }
  }

  compare(source, target, subdir);

  return {
    passed: missingKeys.length === 0,
    missingKeys,
  };
}

(async () => {
  const TRANSLATIONS = {};
  for (const lang of supportedLanguageCodes) {
    if (lang === "en") {
      TRANSLATIONS[lang] = resources.en.common;
    } else {
      const bundle = await loadLanguageAsync(lang);
      if (bundle) {
        TRANSLATIONS[lang] = bundle;
      }
    }
  }

  const PRIMARY = { ...TRANSLATIONS["en"] };
  delete TRANSLATIONS["en"];

  console.log(
    `\nVerifying translations for: [${Object.keys(TRANSLATIONS).join(", ")}]\n`
  );

  let hasErrors = false;
  const results = {};

  for (const [lang, translations] of Object.entries(TRANSLATIONS)) {
    const result = compareStructures(lang, PRIMARY, translations);
    results[lang] = result;

    console.log(
      `${langDisplayName(lang)} (${lang}): ${result.passed ? "✅" : "❌"}`
    );

    if (!result.passed) {
      hasErrors = true;

      if (result.missingKeys.length > 0) {
        console.log(`\nMissing keys in ${lang}:`);
        result.missingKeys.forEach((key) => console.log(`  - ${key}`));
      }
      console.log(); // Empty line for readability
    }
  }

  if (hasErrors) {
    console.error(
      "\n❌ Some translations need attention. Please fix the issues listed above."
    );
    process.exit(1);
  } else {
    console.log(
      "\n✅ All translation files match the English schema perfectly!"
    );
    process.exit(0);
  }
})();
