export default {
  // =========================
  // GENERIC PROVIDER SELECTION SETTINGS
  // =========================
  generic: {
    "base-url": "Bas-URL",
    "api-key": "API-nyckel",
    "api-key-placeholder": "Ange din API-nyckel",
    "chat-model": "Chattmodell",
    "chat-model-placeholder": "Ange chattmodellen",
    "token-window": "Token-kontextfönster",
    "token-window-placeholder": "Innehållsfönstergräns (t.ex: 4096)",
    "max-tokens": "Max Tokens",
    "max-tokens-placeholder": "Max tokens per förfrågan (t.ex: 1024)",
    "embedding-deployment": "Namn på inbäddningsdistribution",
    "embedding-deployment-placeholder":
      "Azure OpenAI inbäddningsmodell distributionsnamn",
    "embedding-model": "Inbäddningsmodell",
    "embedding-model-placeholder": "<PERSON>e inbäddningsmodell",
    "max-embedding-chunk-length": "Maximal inbäddningschunklängd",
    saving: "Sparar...",
    "save-changes": "Spara ändringar",
    "workspace-update-error": "Fel: {{error}}",
    "base-url-placeholder": "t.ex: https://proxy.openai.com",
    "password-mask-length": "12",
  },

  // =========================
  // GENERIC MODEL SELECTION SETTINGS
  // =========================
  model: {
    selection: "Val av chattmodell",
    "embedding-selection": "Val av inbäddningsmodell",
    "enter-api-key":
      "Ange en giltig API-nyckel för att se alla tillgängliga modeller för ditt konto.",
    "enter-url": "Ange URL först",
    "your-models": "Dina laddade modeller",
    "available-models": "Tillgängliga modeller",
  },

  // =========================
  // ANTHROPIC
  // =========================
  anthropic: {
    "api-key": "Anthropic API-nyckel",
    "api-key-placeholder": "Ange din Anthropic API-nyckel",
    "model-selection": "Val av chattmodell",
  },

  // =========================
  // AZURE SETTINGS
  // =========================
  azure: {
    "service-endpoint": "Azure Service Endpoint",
    "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
    "api-key": "API-nyckel",
    "api-key-placeholder": "Azure OpenAI API-nyckel",
    "chat-deployment": "Chat Deployment Name",
    "chat-deployment-placeholder":
      "Azure OpenAI chattmodellens distributionsnamn",
    "token-limit": "Chattmodellens Token-gräns",
  },
  azureai: {
    "service-endpoint": "Azure Tjänsteendpunkt",
    "api-key": "API-nyckel",
    "api-key-placeholder": "Azure OpenAI API-nyckel",
    "embedding-deployment-name": "Namn på inbäddningsdistribution",
    "embedding-deployment-name-placeholder":
      "Ange inbäddningsmodellens distributionsnamn",
  },

  // =========================
  // AWS BEDROCK SETTINGS
  // =========================
  bedrock: {
    "iam-warning":
      "Du bör använda en korrekt definierad IAM-användare för inferens.",
    "read-more": "Läs mer om hur du använder AWS Bedrock med denna instans",
    "access-id": "AWS Bedrock IAM Access ID",
    "access-id-placeholder": "AWS Bedrock IAM User Access ID",
    "access-key": "AWS Bedrock IAM Access Key",
    "access-key-placeholder": "AWS Bedrock IAM User Access Key",
    region: "AWS region",
    "model-id": "Model ID",
    "model-id-placeholder": "Modell-ID från AWS, t.ex: meta.llama3.1-v0.1",
    "context-window": "Kontextfönster för modellen",
    "context-window-placeholder": "Innehållsfönstergräns (t.ex: 4096)",
  },

  // =========================
  // COHERE
  // =========================
  cohere: {
    "api-key": "Cohere API-nyckel",
    "api-key-placeholder": "Ange din Cohere API-nyckel",
    "model-preference": "Modellpreferens",
    "model-selection": "Val av modell",
  },

  // =========================
  // DEEPSEEK LLM SETTINGS
  // =========================
  deepseek: {
    "api-key": "DeepSeek API-nyckel",
    "api-key-placeholder": "Ange din DeepSeek API-nyckel",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
  },

  // =========================
  // FIREWORKSAI LLM SELECTION
  // =========================
  fireworksai: {
    "api-key": "FireworksAI API-nyckel",
    "api-key-placeholder": "Ange din FireworksAI API-nyckel",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
  },

  // =========================
  // GEMINI LLM OPTIONS
  // =========================
  gemini: {
    "api-key": "Google AI API-nyckel",
    "api-key-placeholder": "Google Gemini API-nyckel",
    "model-selection": "Val av modell",
    "loading-models": "Laddar modeller...",
    "manual-options": "Manuella alternativ",
    "safety-setting": "Säkerhetsinställning",
    experimental: "Experimentell",
    stable: "Stabil",
    "safety-options": {
      none: "Ingen (standard)",
      "block-few": "Blockera endast högrisk",
      "block-some": "Blockera medel- & högrisk",
      "block-most": "Blockera det mesta innehållet",
    },
  },

  // =========================
  // GROQ LLM SELECTION
  // =========================
  groq: {
    "api-key": "Groq API-nyckel",
    "api-key-placeholder": "Ange din Groq API-nyckel",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
    "enter-api-key":
      "Ange en giltig API-nyckel för att se tillgängliga modeller för ditt konto.",
    "available-models": "Tillgängliga modeller",
    "model-description":
      "Välj den GroqAI-modell du vill använda för dina konversationer.",
  },

  // =========================
  // HUGGINGFACE LLM SETTINGS
  // =========================
  huggingface: {
    "inference-endpoint": "HuggingFace Inference Endpoint",
    "endpoint-placeholder": "https://example.endpoints.huggingface.cloud",
    "access-token": "HuggingFace Access Token",
    "token-placeholder": "Ange din HuggingFace Access Token",
    "token-limit": "Modellens token-gräns",
    "token-limit-placeholder": "4096",
  },

  // =========================
  // KOBOLDCPP SETTINGS
  // =========================
  koboldcpp: {
    "show-advanced": "Visa manuell endpoint-inmatning",
    "hide-advanced": "Dölj manuell endpoint-inmatning",
    "base-url": "KoboldCPP Bas-URL",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "base-url-desc": "Ange URL:en där KoboldCPP körs.",
    "auto-detect": "Auto-Upptäck",
    "token-context-window": "Token-kontextfönster",
    "token-window-placeholder": "4096",
    "token-window-desc": "Maximalt antal tokens för kontext och svar.",
    model: "KoboldCPP Modell",
    "loading-models": "--laddar tillgängliga modeller--",
    "enter-url": "Ange KoboldCPP URL först",
    "model-desc":
      "Välj den KoboldCPP-modell du vill använda. Modeller visas efter att en giltig URL angetts.",
    "model-choose":
      "Välj den KoboldCPP-modell du vill använda för dina konversationer.",
  },

  // =========================
  // LITELLM
  // =========================
  litellm: {
    "model-tooltip":
      "Var noga med att välja en giltig inbäddningsmodell. Chattmodeller är inte inbäddningsmodeller. Se",
    "model-tooltip-link": "denna sida",
    "model-tooltip-more": "för mer information.",
    "base-url": "Bas-URL",
    "base-url-placeholder": "t.ex: https://proxy.openai.com",
    "max-embedding-chunk-length": "Maximal inbäddningschunklängd",
    "token-window": "Token-kontextfönster",
    "token-window-placeholder": "Kontextfönstergräns (t.ex: 4096)",
    "api-key": "API-nyckel",
    optional: "valfritt",
    "api-key-placeholder": "sk-mysecretkey",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
    "waiting-url": "-- väntar på URL --",
    "loaded-models": "Dina laddade modeller",
    "manage-embedding": "Hantera inbäddning",
    "embedding-required":
      "Litellm kräver en inbäddningstjänst för att kunna användas.",
  },

  // =========================
  // LMSTUDIO LLM SELECTION
  // =========================
  lmstudio: {
    "max-tokens": "Max Tokens",
    "max-tokens-desc": "Maximalt antal tokens för kontext och svar.",
    "show-advanced": "Visa manuell endpoint-inmatning",
    "hide-advanced": "Dölj manuell endpoint-inmatning",
    "base-url": "LM Studio Bas-URL",
    "base-url-placeholder": "http://localhost:1234/v1",
    "base-url-desc": "Ange URL:en där LM Studio körs.",
    "auto-detect": "Auto-Upptäck",
    model: "LM Studio Modell",
    "model-loading": "--laddar tillgängliga modeller--",
    "model-url-first": "Ange LM Studio URL först",
    "model-desc":
      "Välj den LM Studio-modell du vill använda. Modeller listas efter att en giltig URL har angetts.",
    "model-choose":
      "Välj den LM Studio-modell du vill använda för dina konversationer.",
    "model-loaded": "Dina laddade modeller",
    "embedding-required":
      "LMStudio som LLM kräver att du anger en inbäddningstjänst.",
    "manage-embedding": "Hantera inbäddning",
    "max-embedding-chunk-length": "Maximal inbäddningschunklängd",
  },

  // =========================
  // LOCALAI LLM SELECTION
  // =========================
  localai: {
    "token-window": "Token-kontextfönster",
    "token-window-placeholder": "4096",
    "api-key": "Local AI API-nyckel",
    "api-key-optional": "valfritt",
    "api-key-placeholder": "sk-mysecretkey",
    "show-advanced": "Visa avancerade inställningar",
    "hide-advanced": "Dölj avancerade inställningar",
    "base-url": "Local AI Bas-URL",
    "base-url-placeholder": "http://localhost:8080/v1",
    "base-url-help": "Ange URL:en där LocalAI körs.",
    "auto-detect": "Auto-Upptäck",
    "max-embedding-chunk-length": "Maximal inbäddningschunklängd",
    "embedding-required": "Inbäddning krävs för LocalAI.",
    "manage-embedding": "Hantera inbäddning",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
    "waiting-url": "-- väntar på URL --",
    "loaded-models": "Dina laddade modeller",
  },

  // =========================
  // MISTRAL LLM OPTIONS
  // =========================
  mistral: {
    "api-key": "Mistral API-nyckel",
    "api-key-placeholder": "Mistral API-nyckel",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
    "waiting-key": "-- väntar på API-nyckel --",
    "available-models": "Tillgängliga Mistral-modeller",
  },

  // =========================
  // NATIVE LLM SETTINGS
  // =========================

  native: {
    "experimental-warning":
      "Att använda en lokalt värd LLM är experimentellt och kan fungera oväntat.",
    "model-desc": "Välj en modell från dina lokalt värda modeller.",
    "token-desc": "Maximalt antal tokens för kontext och svar.",
  },

  // =========================
  // OLLAMA LLM SELECTION
  // =========================

  ollamallmselection: {
    "max-tokens": "Max Tokens",
    "max-tokens-desc": "Maximalt antal tokens för kontext och svar.",
    "show-advanced": "Visa manuell slutpunktsinmatning",
    "hide-advanced": "Dölj manuell slutpunktsinmatning",
    "base-url": "Ollama Bas-URL",
    "base-url-placeholder": "http://127.0.0.1:11434",
    "base-url-desc": "Ange URL:en där Ollama körs.",
    "auto-detect": "Auto-Upptäck",
    "keep-alive": "Håll Ollama Aktiv",
    "no-cache": "Ingen cache",
    "five-minutes": "5 minuter",
    "one-hour": "1 timme",
    forever: "För alltid",
    "keep-alive-desc": "Håll modellen laddad i minnet.",
    "learn-more": "Läs mer",
    "performance-mode": "Prestandaläge",
    "base-default": "Bas (Standard)",
    maximum: "Maximum",
    "performance-mode-desc": "Välj prestandaläge.",
    note: "Obs:",
    "maximum-warning": "Maximum-läget använder fler resurser.",
    base: "Bas",
    "base-desc": "Basläget är standardinställningen.",
    "maximum-desc":
      "Maximum-läget ger högre prestanda, använder hela kontextfönstret upp till max tokens.",
    model: "Ollama-modell",
    "loading-models": "--laddar tillgängliga modeller--",
    "enter-url": "Ange Ollama-URL först",
    "model-desc":
      "Välj den Ollama-modell du vill använda. Modeller listas när en giltig URL angetts.",
    "model-choose":
      "Välj den Ollama-modell du vill använda för dina konversationer.",
  },

  // =========================
  // OPENAI LLM SELECTION
  // =========================
  openai: {
    "api-key": "OpenAI API-nyckel",
    "api-key-placeholder": "Ange din OpenAI API-nyckel",
    "model-preference": "Modellpreferens",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
    "model-divider": "──────────",
  },

  // =========================
  // OPENROUTER LLM SELECTION
  // =========================
  openrouter: {
    "api-key": "OpenRouter API-nyckel",
    "api-key-placeholder": "Ange din OpenRouter API-nyckel",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
  },

  // =========================
  // PERPLEXITY LLM SELECTION
  // =========================
  perplexity: {
    "api-key": "Perplexity API-nyckel",
    "api-key-placeholder": "Ange din Perplexity API-nyckel",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
    "available-models": "Tillgängliga Perplexity-modeller",
  },

  // =========================
  // TOGETHERAI
  // =========================
  togetherai: {
    "api-key": "TogetherAI API-nyckel",
    "api-key-placeholder": "Ange din TogetherAI API-nyckel",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
  },

  // =========================
  // XAI
  // =========================
  xai: {
    "api-key": "xAI API-nyckel",
    "api-key-placeholder": "xAI API-nyckel",
    "model-selection": "Val av chattmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
    "enter-api-key":
      "Ange en giltig API-nyckel för att se tillgängliga modeller för ditt konto.",
    "available-models": "Tillgängliga modeller",
    "model-description":
      "Välj den xAI-modell du vill använda för dina konversationer.",
  },
};
