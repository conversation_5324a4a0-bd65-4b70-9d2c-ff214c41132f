const TRANSLATIONS = {
  // =========================
  // COMMON STRINGS & PLACEHOLDERS
  // =========================
  common: {
    examples: "Exempel",
    "workspaces-name": "Arbetsytenamn",
    ok: "OK",
    error: "fel",
    confirm: "Bekräfta",
    confirmstart: "Bekräfta och starta",
    savesuccess: "Inställningar sparades",
    saveerror: "Kunde inte spara inställningar",
    success: "framgång",
    user: "Användare",
    selection: "Modellval",
    saving: "Sparar...",
    save: "Spara ändringar",
    previous: "Föregående sida",
    next: "Nästa sida",
    cancel: "Avbryt",
    "search-placeholder": "Sök...",
    "more-actions": "Fler åtgärder",
    "delete-message": "Ta bort meddelande",
    copy: "<PERSON><PERSON><PERSON>",
    edit: "Redigera",
    regenerate: "Generera igen",
    "export-word": "Exportera till Word",
    "stop-generating": "Stoppa generering",
    "attach-file": "Bifoga en fil till denna chatt",
    home: "Hem",
    settings: "Inställningar",
    support: "Support",
    "clear-reference": "Rensa referens",
    "send-message": "Skicka meddelande",
    "ask-legal": "Fråga om juridisk information",
    "stop-response": "Stoppa generering av svar",
    "contact-support": "Kontakta support",
    "copy-connection": "Kopiera anslutningssträng",
    "auto-connect": "Anslut automatiskt till tillägget",
    back: "Tillbaka",
    "back-to-workspaces": "Tillbaka till arbetsytor",
    off: "Av",
    on: "På",
    continue: "Fortsätt",
    rename: "Byt namn",
    delete: "Ta bort",
    "default-skill":
      "Denna färdighet är aktiverad som standard och kan inte inaktiveras.",
    timeframes: "Tidsperioder",
    other: "Andra alternativ",
    placeholder: {
      username: "Mitt användarnamn",
      password: "Ditt lösenord",
      email: "Ange din e-postadress",
      "support-email": "<EMAIL>",
      website: "https://www.example.com",
      "site-name": "IST Legal",
      "search-llm": "Sök efter en specifik LLM-leverantör",
      "search-providers": "Sök tillgängliga leverantörer",
      "message-heading": "Meddelanderubrik",
      "message-content": "Meddelande",
      "token-limit": "4096",
      "max-tokens": "Maximalt antal tokens per förfrågan (t.ex: 1024)",
      "api-key": "API-nyckel",
      "base-url": "Bas-URL",
      endpoint: "API-endpoint",
    },
    tooltip: {
      copy: "Kopiera till urklipp",
      delete: "Radera detta objekt",
      edit: "Redigera detta objekt",
      save: "Spara ändringar",
      cancel: "Avbryt ändringar",
      search: "Sök objekt",
      add: "Lägg till nytt objekt",
      remove: "Ta bort objekt",
      upload: "Ladda upp fil",
      download: "Ladda ner fil",
      refresh: "Uppdatera data",
      settings: "Öppna inställningar",
      more: "Fler alternativ",
    },
    "default.message": "Ange ditt meddelande här",
    preview: "Förhandsgranska",
    prompt: "Prompt",
    loading: "Laddar...",
    download: "Ladda ner fil i råformat",
    open_in_new_tab: "Öppna i ny flik med formatering",
    close: "Stäng",
    note: "Notera",
  },

  // =========================
  // RECENT UPLOADS COMPONENT
  // =========================

  // moved to recentUploads.js

  // =========================
  // CHAT BOX DRAG & DROP COMPONENT
  // =========================
  chatboxdnd: {
    title: "Lägg till fil",
    description:
      "Släpp din fil här för att lägga till den i detta meddelande. Den kommer inte att sparas i arbetsytan som en permanent källa.",
    "file-prefix": "Fil:",
    "attachment-tooltip":
      "Denna fil kommer att bifogas till ditt meddelande. Den kommer inte att sparas i arbetsytan som en permanent källa.",
    "uploaded-file-tag": "UPPLADDAD ANVÄNDARFIL",
  },

  // =========================
  // CONFLUENCE CONNECTOR COMPONENT
  // =========================
  confluence: {
    "space-key": "Confluence utrymmesnyckel",
    "space-key-desc":
      "Detta är nyckeln för ditt Confluence-utrymme som kommer att användas. Börjar vanligtvis med ~",
    "space-key-placeholder": "t.ex: ~7120208c08555d52224113949698b933a3bb56",
    "url-placeholder":
      "t.ex: https://example.atlassian.net, http://localhost:8211, etc...",
    "token-tooltip": "Du kan skapa en API-token",
    "token-tooltip-here": "här",
  },

  // =========================
  // CONFIRMATION MESSAGES
  // =========================
  deleteWorkspaceConfirmation:
    "Är du säker på att du vill ta bort {{name}}?\nEfter detta kommer det inte att vara tillgängligt i denna instans.\n\nDenna åtgärd går inte att ångra.",
  deleteConfirmation:
    "Är du säker på att du vill ta bort {{username}}?\nEfter detta kommer de att bli utloggade och inte kunna använda denna instans.\n\nDenna åtgärd går inte att ångra.",
  suspendConfirmation:
    "Är du säker på att du vill avaktivera kontot för {{username}}?\nEfter bekräftelse kommer användaren att bli utloggad och kan inte logga in igen innan kontot återaktiverats.",
  flushVectorCachesWorkspaceConfirmation:
    "Är du säker på att du vill tömma vektorcacheminnet för den här arbetsytan?",
  apiKeys: {
    "deactivate-title": "Inaktivera API-nyckel",
    "deactivate-message":
      "Är du säker på att du vill inaktivera denna API-nyckel?\nEfter detta kommer den inte längre att vara tillgänglig.",
  },

  // =========================
  // SETTINGS SIDEBAR MENU ITEMS
  // =========================

  // MOVED TO settings.js - This section has been migrated to settings.js

  // =========================
  // CHAT UI SETTINGS
  // =========================
  "chat-ui-settings": {
    title: "Chattgränssnittsinställningar",
    description: "Konfigurera chattinställningarna.",
    auto_submit: {
      title: "Automatisk inlämning av talad input",
      description: "Skicka automatiskt talad input efter en period av tystnad",
    },
    auto_speak: {
      title: "Automatisk uppläsning av svar",
      description: "Läs automatiskt upp svar från AI:n",
    },
  },

  // =========================
  // QURA BUTTONS
  // =========================
  qura: {
    "copy-to-cora": "Qura källkontroll",
    "qura-status": "Qura-knappen är ",
    "copy-option": "kopieringsalternativ",
    "option-quest": "Fråga",
    "option-resp": "Svar",
    "role-description": "Lägg till en Qura-knapp för att få svar på Qura.law",
  },

  // =========================
  // LOGIN & SIGN-IN PAGES
  // =========================
  login: {
    "multi-user": {
      welcome: "Välkommen till",
      "placeholder-username": "E-postadress",
      "placeholder-password": "Lösenord",
      login: "Logga in",
      validating: "Validerar...",
      "forgot-pass": "Glömt lösenord",
      "back-to-login": "Tillbaka till logga in",
      "reset-password": "Återställ lösenord",
      reset: "Återställ",
      "reset-password-info":
        "Ange nödvändig information nedan för att återställa ditt lösenord.",
    },
    "sign-in": {
      start: "Logga in på ditt konto",
      end: "konto.",
    },
    button: "logga in",
    password: {
      forgot: "Glömt ditt lösenord?",
      contact: "Kontakta systemadministratören.",
    },
    publicMode: "Testa utan konto",
    logging: "Loggar in...",
  },

  // =========================
  // BINARY LLM SELECTION
  // =========================
  binary_llm_selection: {
    "secondary-llm-toggle": "Binärt LLM-val",
    "secondary-llm-toggle-description":
      "Aktivera detta för att ge administratörer möjlighet att välja mellan två LLM-modeller i modul för dokumentutkast.",
    "secondary-llm-toggle-status": "Status: ",
    "secondary-llm-user-level": "Sekundärt LLM-användarnivå",
    "secondary-llm-user-level-description":
      "Aktivera detta för att ge ALLA användare möjlighet att välja mellan två LLM-modeller i arbetsytan för dokumentutkast.",
  },

  // =========================
  // NEW WORKSPACE
  // =========================
  "new-workspace": {
    title: "Ny arbetsyta",
    placeholder: "Min arbetsyta",
    "legal-areas": "Rättsområden",
    create: {
      title: "Skapa ny arbetsyta",
      description:
        "Efter att ha skapat denna arbetsyta kommer endast administratörer att kunna se den. Du kan lägga till användare efter att den har skapats.",
      error: "Fel: ",
      cancel: "Avbryt",
      "create-workspace": "Skapa arbetsyta",
    },
  },

  // =========================
  // WORKSPACE CHATS
  // =========================
  "workspace-chats": {
    welcome: "Välkommen till din nya arbetsyta.",
    "desc-start": "För att komma igång kan du antingen",
    "desc-mid": "ladda upp ett dokument",
    "desc-or": "eller",
    start: "För att komma igång",
    "desc-end": "skicka en chatt.",
    "attached-file": "Bifogad fil",
    "attached-files": "Bifogade filer",
    "token-count": "Antal tokens",
    "total-tokens": "Totalt antal tokens",
    "context-window": "Kontextfönster",
    "remaining-tokens": "Återstående",
    "view-files": "Visa bifogade filer",
    prompt: {
      send: "Skicka",
      "send-message": "Skicka meddelande",
      placeholder: "Be om juridisk information",
      "change-size": "Ändra textstorlek",
      reset: "Återställ din chatt",
      clear: "Rensa din chatthistorik och börja en ny chatt",
      command: "Kommando",
      description: "Beskrivning",
      save: "spara",
      small: "Liten",
      normal: "Normal",
      large: "Stor",
      larger: "Större",
      attach: "Bifoga en fil till denna chatt",
      upgrade: "Uppgradera din prompt",
      upgrading: "Uppgraderar prompt...",
      "original-prompt": "Ursprunglig prompt:",
      "upgraded-prompt": "Uppgraderad prompt:",
      "edit-prompt": "Du kan redigera den nya prompten innan du skickar in den",
      "shortcut-tip":
        "Tips: Tryck på Enter för att acceptera ändringar. Använd Skift+Enter för nya rader.",
      "speak-prompt": "Tala in din prompt",
      "view-agents":
        "Visa alla tillgängliga agenter du kan använda för att chatta",
      "ability-tag": "Förmåga",
      "deep-search": "Websökning",
      "deep-search-tooltip":
        "Sök på webben efter information för att förbättra svar",
      "workspace-chats.prompt.view-agents": "Visa agenter",
      "workspace-chats.prompt.ability-tag": "Förmåga",
      "workspace-chats.prompt.speak-prompt": "Tala in din prompt",
    },
  },

  // =========================
  // DEEP SEARCH SETTINGS
  // =========================
  deep_search: {
    title: "Djupsökning",
    description:
      "Konfigurera webbsökningsfunktioner för chattsvar. När aktiverad kan systemet söka på webben efter information för att förbättra svar.",
    enable: "Aktivera Djupsökning",
    enable_description:
      "Tillåt systemet att söka på webben efter information när det svarar på frågor.",
    provider_settings: "Leverantörsinställningar",
    provider: "Sökleverantör",
    model: "Modell",
    api_key: "API-nyckel",
    api_key_placeholder: "Ange din API-nyckel",
    api_key_placeholder_set:
      "API-nyckel är inställd (ange ny nyckel för att ändra)",
    api_key_help:
      "Din API-nyckel lagras säkert och används endast för webbsökningsförfrågningar.",
    context_percentage: "Kontextprocent",
    context_percentage_help:
      "Procentandel av LLM:s kontextfönster som ska allokeras för webbsökningsresultat (5-20%).",
    fetch_error: "Kunde inte hämta Djupsökningsinställningar",
    save_success: "Djupsökningsinställningar sparades framgångsrikt",
    save_error: "Kunde inte spara Djupsökningsinställningar: {{error}}",
    toast_success: "Djupsökningsinställningar sparades framgångsrikt",
    toast_error: "Kunde inte spara Djupsökningsinställningar: {{error}}",
    brave_recommended:
      "Brave Search är för närvarande det rekommenderade och mest tillförlitliga leverantörsalternativet.",
  },

  // =========================
  // CONTEXTUAL SETTINGS
  // =========================
  contextual: {
    checkbox: {
      label: "Kontextuell inbäddning",
      hint: "Aktivera kontextuell inbäddning för att förbättra inbäddningsprocessen med ytterligare parametrar",
    },
    systemPrompt: {
      label: "Systemmeddelande",
      placeholder: "Ange ett värde...",
      description:
        "Exempel: Ge en kort och koncis kontext för att placera detta stycke inom hela dokumentet, i syfte att förbättra sökningen efter stycket. Svara endast med den koncisa kontexten och inget annat.",
    },
    userPrompt: {
      label: "Användarmeddelande",
      placeholder: "Ange ett värde...",
      description:
        "Exempel: <document>\n{file}\n</document>\nHär är stycket vi vill placera inom hela dokumentet\n<chunk>\n{chunk}\n</chunk>",
    },
  },

  // =========================
  // HEADER
  // =========================
  header: {
    account: "Konto",
    login: "Logga in",
    "sign-out": "Logga ut",
  },

  // =========================
  // WORKSPACE OVERVIEW
  // =========================
  workspace: {
    title: "Instansarbetsytor",
    description:
      "Detta är alla arbetsytor som finns på denna instans. Att ta bort en arbetsyta raderar alla dess associerade chattar och inställningar.",
    "new-workspace": "Ny Arbetsyta",
    name: "Namn",
    link: "Länk",
    users: "Användare",
    type: "Typ",
    "created-on": "Skapad den",
    save: "Spara ändringar",
    cancel: "Avbryt",
    "sort-by-name": "Sortera efter namn",
    sort: "Sortera alfabetiskt",
    unsort: "Återställ ursprunglig ordning",
    deleted: {
      title: "Arbetsyta inte hittad!",
      description:
        "Det verkar som att en arbetsyta med detta namn inte är tillgänglig.",
      homepage: "Gå tillbaka till startsidan",
    },
    "no-workspace": {
      title: "Ingen arbetsyta tillgänglig",
      description: "Du har inte tillgång till några arbetsytor än.",
      "contact-admin": "Kontakta din administratör för att begära tillgång.",
      "learn-more": "Läs mer om arbetsytor",
    },
    "no-workspaces":
      "Du har inga arbetsytor än. Välj ett rättsområde till vänster för att komma igång.",
    "my-workspaces": "Mina arbetsytor",
    "show-my": "Visa mina arbetsytor",
    "show-all": "Visa alla arbetsytor",
    "cloud-ai": "Molnbaserad AI",
    "local-ai": "Lokal AI",
    "welcome-mobile":
      "Tryck på knappen längst upp till vänster för att välja ett rättsområde.",
    "creator-id": "Skapad av användar-ID: {{id}}",
    "loading-username": "Laddar användarnamn...",
    "ai-type": "Modul",
    "latest-activity": "Senaste aktivitet",
    "today-time": "Idag, {{time}}",
    "date-time": "{{day}} {{month}}, {{time}}",
  },

  // =========================
  // WORKSPACES SETTINGS MENU
  // =========================
  "workspaces-settings": {
    general: "Allmänna Inställningar",
    chat: "Chattinställningar",
    vector: "Vektordatabas",
    members: "Medlemmar",
    agent: "Agentkonfiguration",
    "general-settings": {
      "workspace-name": "Arbetsytans Namn",
      "desc-name":
        "Detta kommer bara att ändra visningsnamnet på din arbetsyta.",
      "assistant-profile": "Assistentens Profilbild",
      "assistant-image":
        "Anpassa profilbilden för assistenten för detta arbetsrum.",
      "workspace-image": "Arbetsytans Bild",
      "remove-image": "Ta bort arbetsytans bild",
      delete: "Radera Arbetsyta",
      deleting: "Raderar arbetsyta...",
      update: "Uppdatera arbetsyta",
      updating: "Uppdaterar arbetsyta...",
    },
    "chat-settings": {
      type: "Chatt Typ",
      private: "Privat",
      standard: "Standard",
      "private-desc-start": "kommer manuellt att bevilja tillgång till",
      "private-desc-mid": "endast",
      "private-desc-end": "specifika användare.",
      "standard-desc-start": "kommer automatiskt att bevilja tillgång till",
      "standard-desc-mid": "alla",
      "standard-desc-end": "nya användare.",
    },
    users: {
      manage: "Hantera Användare",
      "workspace-member": "Inga arbetsytmedlemmar",
      username: "E-postadress",
      role: "Roll",
      date: "Tillagd datum",
      users: "Användare",
      search: "Sök efter en användare",
      "no-user": "Inga användare hittades",
      select: "Välj Alla",
      unselect: "Avmarkera Alla",
      save: "Spara",
    },
    "linked-workspaces": {
      title: "Länkade Arbetsytor",
      description:
        "Om arbetsytor är länkade kommer juridisk data som är relevant för prompten automatiskt att hämtas från varje länkat juridiskt område. Observera att länkade arbetsytor kan öka bearbetningstiden.",
      "linked-workspace": "Inga länkade arbetsytor",
      manage: "Hantera Arbetsytor",
      name: "Namn",
      slug: "Slug",
      date: "Datum tillagt",
      workspaces: "Arbetsytor",
      search: "Sök efter en arbetsyta",
      "no-workspace": "Inga arbetsytor hittades",
      select: "Välj Alla",
      unselect: "Avmarkera",
      save: "Spara",
    },
    "delete-workspace": "Radera arbetsyta",
    "delete-workspace-message":
      "Är du säker på att du vill radera denna arbetsyta?\n\nDenna åtgärd är oåterkallelig.",
    "vector-database": {
      reset: {
        title: "Återställ vektordatabas",
        message:
          "Är du säker på att du vill återställa denna arbetsytas vektordatabas? Detta kommer att ta bort alla nuvarande vektorinbäddningar.\n\nOriginalfilerna förblir oförändrade. Denna åtgärd är oåterkallelig.",
      },
    },
  },

  // =========================
  // GENERAL APPEARANCE & CUSTOMIZATION
  // =========================
  general: {
    vector: {
      title: "Antal vektorer",
      description: "Totala antalet vektorer i din vektordatabas.",
      vectors: "Antal vektorer",
    },
    names: {
      description:
        "Detta kommer endast att ändra visningsnamnet på din arbetsyta.",
    },
    message: {
      title: "Föreslagna meddelanden",
      description:
        "Anpassa de meddelanden som kommer att föreslås för dina arbetsytans användare.",
      add: "Lägg till nytt meddelande",
      save: "Spara meddelanden",
      heading: "Förklara för mig",
      body: "fördelarna med plattformen",
      message: "Meddelande",
      "new-heading": "Rubrik",
    },
    pfp: {
      title: "Profilbild för assistent",
      description: "Anpassa profilbilden för assistenten för denna arbetsyta.",
      image: "Arbetsytans bild",
      remove: "Ta bort arbetsytans bild",
    },
    delete: {
      delete: "Radera arbetsyta",
      deleting: "Raderar arbetsyta...",
      "confirm-start": "Du håller på att radera hela din",
      "confirm-end":
        "arbetsyta. Detta kommer att ta bort alla vektorinbäddningar i din vektordatabas.\n\nOriginalfilerna förblir oförändrade. Denna åtgärd är oåterkallelig.",

      confirm:
        "Du håller på att radera hela din\narbetsyta. Detta kommer att ta bort alla vektorinbäddningar i din vektordatabas.\n\nOriginalfilerna förblir oförändrade. Denna åtgärd är oåterkallelig.",
      error: "Arbetsytans vektordatabas kunde inte återställas!",
      success: "Arbetsytans vektordatabas återställdes!",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chat: {
    llm: {
      title: "Arbetsytans LLM-leverantör",
      description:
        "Den specifika LLM-leverantören & modellen som kommer att användas för denna arbetsyta. Som standard används systemets LLM-leverantör och inställningar.",
      search: "Sök bland alla LLM-leverantörer",
      "save-error":
        "Misslyckades att spara {{provider}} inställningar: {{error}}",
      setup: "Installera",
      use: "För att använda",
      "need-setup": "måste du först konfigurera följande referenser.",
      cancel: "Avbryt",
      save: "Spara",
      settings: "inställningar",
      "multi-model": "Denna leverantör stöder inte flera modeller.",
      "workspace-use":
        "Din arbetsyta kommer att använda den modell som är konfigurerad i",
      "model-set": "systeminställningarna",
      "system-default": "Systemstandard",
      "system-default-desc":
        "Använd systemets LLM-inställning som standard för denna arbetsyta.",
      "no-selection": "Ingen LLM vald",
      "select-provider": "Välj en LLM-leverantör",
      "system-standard-name": "LLM standard",
      "system-standard-desc":
        "Använd den primära LLM som är inställd i systeminställningarna.",
    },
    "speak-prompt": "Tala in din prompt",
    "view-agents":
      "Visa alla tillgängliga agenter du kan använda för att chatta",
    "ability-tag": "Förmåga",
    "change-text-size": "Ändra textstorlek",
    "aria-text-size": "Ändra textstorlek",
    model: {
      title: "Arbetsytans chattmodell",
      description:
        "Den specifika chattmodellen som kommer att användas för denna arbetsyta. Om tom, används systemets LLM-preferens.",
      wait: "-- väntar på modeller --",
      general: "Generella modeller",
      custom: "Anpassade modeller",
    },
    mode: {
      title: "Chattläge",
      chat: {
        title: "Chatt",
        "desc-start": "kommer att ge svar med LLM:s allmänna kunskap",
        and: "och",
        "desc-end": "dokumentkontext som hittas.",
      },
      query: {
        title: "Fråga",
        "desc-start": "kommer att ge svar",
        only: "endast",
        "desc-end": "om dokumentkontext hittas.",
      },
    },
    history: {
      title: "Chattens historik",
      "desc-start":
        "Antalet tidigare chattar som kommer att inkluderas i svarets korttidsminne.",
      recommend: "Rekommenderar 20. ",
      "desc-end":
        "Allt över 45 kan orsaka fortlöpande chattfel beroende på meddelandets storlek.",
    },
    prompt: {
      title: "Prompt",
      description:
        "Prompten som används på denna arbetsyta. Definiera kontext och instruktioner för AI:n så att ett relevant och korrekt svar kan genereras.",
    },
    refusal: {
      title: "Vägrande svar i frågeläge",
      "desc-start": "När i",
      query: "fråga",
      "desc-end":
        "läge, kanske du vill returnera ett anpassat vägrande svar när ingen kontext hittas.",
    },
    temperature: {
      title: "LLM:s temperatur",
      "desc-start":
        'Denna inställning styr hur "kreativa" dina LLM-svar kommer att vara.',
      "desc-end":
        "Ju högre värde desto mer kreativt. För vissa modeller kan ett för högt värde leda till osammanhängande svar.",
      hint: "De flesta LLM:er har olika accepterade värden. Konsultera din LLM-leverantör för mer information.",
    },
    "dynamic-pdr": {
      title: "Dynamisk PDR för Arbetsyta",
      description:
        "Aktivera eller inaktivera dynamisk PDR för denna arbetsyta.",
      "global-enabled":
        "Dynamisk PDR är globalt aktiverad och kan inte inaktiveras för enskilda arbetsytor.",
    },
  },

  // =========================
  // VECTOR DATABASE (WORKSPACE)
  // =========================
  "vector-workspace": {
    identifier: "Identifierare för vektordatabas",
    snippets: {
      title: "Maximalt antal kontextutdrag",
      description:
        "Denna inställning styr det maximala antalet kontextsnippets som skickas till LLM per chatt eller fråga.",
      recommend:
        "Rekommenderat värde är minst 30. Att ställa in mycket högre värden kommer att öka bearbetningstiden utan att nödvändigtvis förbättra precisionen beroende på kapaciteten hos den använda LLM:en.",
    },
    doc: {
      title: "Dokumentsimilaritetströskelvärde",
      description:
        "Det minsta likhetspoäng som krävs för att en källa ska anses vara relaterad till chatten. Ju högre siffra, desto mer likhet krävs.",
      zero: "Ingen begränsning",
      low: "Låg (likhetspoäng ≥ .25)",
      medium: "Medel (likhetspoäng ≥ .50)",
      high: "Hög (likhetspoäng ≥ .75)",
    },
    reset: {
      reset: "Återställ vektordatabas",
      resetting: "Rensar vektorer...",
      confirm:
        "Du håller på att återställa denna arbetsytas vektordatabas. Detta tar bort alla nuvarande vektorinbäddningar.\n\nOriginalfilerna förblir oförändrade. Denna åtgärd är oåterkallelig.",
      error: "Arbetsytans vektordatabas kunde inte återställas!",
      success: "Arbetsytans vektordatabas återställdes!",
    },
  },

  // =========================
  // AGENT CONFIGURATION
  // =========================
  agent: {
    "performance-warning":
      "Prestandan hos LLM:er som inte uttryckligen stödjer verktygskallning beror starkt på modellens kapacitet och noggrannhet. Vissa funktioner kan vara begränsade eller inte fungera.",
    provider: {
      title: "Arbetsytans Agent LLM-leverantör",
      description:
        "Den specifika LLM-leverantören & modellen som används för denna arbetsytas @agent agent.",
      "need-setup":
        "För att använda {{name}} som denna arbetsytas @agent agent måste du först konfigurera följande referenser.",
    },
    mode: {
      chat: {
        title: "Arbetsytans agentchattmodell",
        description:
          "Den specifika chattmodellen som används för denna arbetsytas @agent agent.",
      },
      title: "Arbetsytans agentmodell",
      description:
        "Den specifika LLM-modellen som kommer att användas för denna arbetsytas @agent agent.",
      wait: "-- väntar på modeller --",
    },
    skill: {
      title: "Standardagentens färdigheter",
      description:
        "Förbättra standardagentens naturliga förmågor med dessa förinstallerade färdigheter. Denna inställning gäller för alla arbetsytor.",
      rag: {
        title: "RAG & långtidsminne",
        description:
          'Låt agenten använda dina lokala dokument för att besvara en fråga eller "komma ihåg" delar av innehållet för långtidsminnesåtkomst.',
      },
      configure: {
        title: "Konfigurera agentfärdigheter",
        description:
          "Anpassa och förstärk standardagentens möjligheter genom att aktivera eller inaktivera specifika färdigheter. Dessa inställningar gäller för alla arbetsytor.",
      },
      view: {
        title: "Visa & sammanfatta dokument",
        description:
          "Låt agenten lista och sammanfatta innehållet i arbetsytans redan inbäddade filer.",
      },
      scrape: {
        title: "Hämta innehåll från webbplatser",
        description:
          "Låt agenten besöka och hämta innehållet från webbplatser.",
      },
      generate: {
        title: "Generera diagram",
        description:
          "Aktivera standardagenten för att generera olika typer av diagram baserat på tillhandahållen data eller chattinnehåll.",
      },
      save: {
        title: "Generera & spara filer till webbläsaren",
        description:
          "Aktivera standardagenten för att generera och skriva filer som sedan kan laddas ner i din webbläsare.",
      },
      web: {
        title: "Live webbsökning och surfning",
        "desc-start":
          "Aktivera agenten att söka på webben för att besvara dina frågor genom att ansluta till en webbsöktjänst (SERP).",
        "desc-end":
          "Webbsökning under agentsessioner fungerar inte förrän detta är konfigurerat.",
      },
    },
  },

  cdbProgress: {
    "close-msg": "Är du säker på att du vill avbryta processen?",
    general: {
      placeholderSubTask: "Bearbetar objekt {{index}}...",
    },
    main: {
      step1: {
        label: "Generera lista över sektioner",
        desc: "Använder huvuddokumentet för att skapa en initial struktur.",
      },
      step2: {
        label: "Bearbeta dokument",
        desc: "Genererar beskrivningar och kontrollerar relevans.",
      },
      step3: {
        label: "Koppla dokument till sektioner",
        desc: "Tilldelar relevanta dokument till varje sektion.",
      },
      step4: {
        label: "Identifiera juridiska frågor",
        desc: "Extraherar centrala juridiska frågor för varje sektion.",
      },
      step5: {
        label: "Generera juridiska PM",
        desc: "Skapar juridiska promemorior för de identifierade frågorna.",
      },
      step6: {
        label: "Utforma sektioner",
        desc: "Komponerar innehållet för varje enskild sektion.",
      },
      step7: {
        label: "Kombinera & slutför dokument",
        desc: "Sammanställer sektionerna till det slutliga dokumentet.",
      },
    },
    noMain: {
      step1: {
        label: "Bearbetar dokument",
        desc: "Genererar beskrivningar för alla uppladdade filer.",
      },
      step2: {
        label: "Generera lista över sektioner",
        desc: "Skapar en strukturerad lista över sektioner från dokumentsammanfattningar.",
      },
      step3: {
        label: "Slutför dokumentkoppling",
        desc: "Bekräftar dokumentens relevans för varje planerad sektion.",
      },
      step4: {
        label: "Identifiera juridiska frågor",
        desc: "Extraherar centrala juridiska frågor för varje sektion.",
      },
      step5: {
        label: "Generera juridiska PM",
        desc: "Skapar juridiska promemorior för de identifierade frågorna.",
      },
      step6: {
        label: "Utforma sektioner",
        desc: "Komponerar innehållet för varje enskild sektion.",
      },
      step7: {
        label: "Kombinera & slutför dokument",
        desc: "Sammanställer alla sektioner till det slutliga juridiska dokumentet.",
      },
    },
  },

  // =========================
  // RECORDED WORKSPACE CHATS
  // =========================
  recorded: {
    title: "Arbetsytekonversationer",
    description:
      "Här visas alla registrerade chattar och meddelanden som skickats av användare, sorterade efter skapandedatum.",
    export: "Exportera",
    table: {
      id: "Id",
      by: "Skickat av",
      workspace: "Arbetsyta",
      prompt: "Prompt",
      response: "Svar",
      at: "Skickat",
      invoice: "Fakturareferens",
      "completion-token": "Tokens i svar från LLM",
      "prompt-token": "Tokens till LLM",
    },
    "clear-chats": "Radera alla nuvarande loggar",
    "confirm-clear-chats":
      "Är du säker på att du vill rensa alla loggar?\n\nDenna åtgärd kan inte ångras.",
    "fine-tune-modal": "Beställ Fine-Tune modell",
    "confirm-delete.chat":
      "Är du säker på att du vill ta bort denna konversation?\n\nDenna åtgärd går inte att ångra.",
    next: "Nästa sida",
    previous: "Föregående sida",
    filters: {
      "by-name": "Filtrera efter användarnamn",
      "by-reference": "Referensnummer",
    },
    bulk_delete_title: "Radera gamla loggar",
    bulk_delete_description:
      "Radera alla promptloggar äldre än vald tidsperiod.",
    delete_old_chats: "Rensa loggar",
    total_logs: "Totalt antal loggar",
    filtered_logs: "Filtrerade loggar",
    reset_filters: "Återställ filter",
    "no-chats-found": "Inga promptloggar hittades",
    "no-chats-description":
      "Inga promptloggar matchar dina filter. Försök att ändra dina sökkriterier om filtrering används.",
    "deleted-old-chats": "Raderade {{count}} gamla prompt(ar)",
    two_days: "2 dagar",
    one_week: "1 vecka",
    two_weeks: "2 veckor",
    one_month: "1 månad",
    two_months: "2 månader",
    three_months: "3 månader",
    total_deleted: "Totalt raderade promptloggar",
  },

  // =========================
  // API KEYS
  // =========================
  api: {
    title: "API-nycklar",
    description:
      "API-nycklar gör att innehavaren kan komma åt och hantera denna instans via programmering.",
    link: "Läs API-dokumentationen",
    generate: "Generera ny API-nyckel",
    table: {
      key: "API-nyckel",
      by: "Skapad av",
      created: "Skapad",
    },
    new: {
      title: "Skapa ny API-nyckel",
      description:
        "När den har skapats kan API-nyckeln användas för att programmässigt komma åt och konfigurera denna instans.",
      doc: "Läs API-dokumentationen",
      cancel: "Avbryt",
      "create-api": "Skapa API-nyckel",
    },
  },

  // =========================
  // AUDIO PREFERENCE
  // =========================
  audio: {
    title: "Inställningar för Tal-till-text",
    provider: "Leverantör",
    "system-native": "Systemets inbyggda",
    "desc-speech":
      "Här kan du ange vilken typ av tal-till-text och text-till-tal-leverantörer du vill använda. Som standard används webbläsarens inbyggda funktioner, men du kan välja andra.",
    "title-text": "Inställningar för Text-till-tal",
    "desc-text":
      "Här kan du ange vilken typ av text-till-tal-leverantörer du vill använda. Som standard används webbläsarens inbyggda funktioner, men du kan välja andra.",
    "desc-config": "Ingen konfiguration krävs för webbläsarens inbyggda TTS.",
    "placeholder-stt": "Sök tal-till-text-leverantörer",
    "placeholder-tts": "Sök text-till-tal-leverantörer",
    "native-stt": "Använder webbläsarens inbyggda STT om det stöds.",
    "native-tts": "Använder webbläsarens inbyggda TTS om det stöds.",
    "piper-tts": "Kör TTS-modeller lokalt i din webbläsare privat.",
    "openai-description": "Använd OpenAIs text-till-tal-röster och teknik.",
    openai: {
      "api-key": "API-nyckel",
      "api-key-placeholder": "OpenAI API-nyckel",
      "voice-model": "Röstmodell",
    },
    elevenlabs: "Använd ElevenLabs text-till-tal-röster och teknik.",
  },

  // =========================
  // TRANSCRIPTION PREFERENCE
  // =========================
  transcription: {
    title: "Transkriptionsmodellpreferens",
    description:
      "Dessa är referenser och inställningar för din föredragna transkriptionsmodellleverantör. Om dessa inte är aktuella eller korrekta fungerar inte mediefiler och ljud korrekt.",
    provider: "Transkriptionsleverantör",
    "warn-start":
      "Att använda den lokala Whisper-modellen på maskiner med begränsat RAM eller CPU kan göra att plattformen fastnar när den bearbetar mediefiler.",
    "warn-recommend":
      "Vi rekommenderar minst 2 GB RAM och att du laddar upp filer <10Mb.",
    "warn-end":
      "Den inbyggda modellen laddas ner automatiskt vid första användning.",
    "search-audio": "Sök ljudtranskriptionsleverantörer",
    "api-key": "API-nyckel",
    "api-key-placeholder": "OpenAI API-nyckel",
    "whisper-model": "Whisper-modell",
    "whisper-large": "Whisper Large",
    "model-size-small": "(250mb)",
    "model-size-large": "(1.56GB)",
    "model-xenova-small": "Xenova/whisper-small",
    "model-xenova-large": "Xenova/whisper-large",
    "default-built-in": "Inbyggd standardmodell",
    "default-built-in-desc":
      "Kör en inbyggd whisper-modell på denna instans privat.",
    "openai-name": "OpenAI",
    "openai-desc": "Använd OpenAI:s Whisper-large-modell med din API-nyckel.",
    "model-turbo": "openai/whisper-large-v3-turbo", // Ny modellnamn
    "model-size-turbo": "(~810mb)", // Ny modellstorlek
  },

  // =========================
  // EMBEDDING PREFERENCE
  // =========================
  embedding: {
    title: "Inbäddningspreferens",
    "desc-start":
      "När du använder en LLM som inte nativt stödjer en inbäddningsmotor kan du behöva ange referenser för att inbädda text.",
    "desc-end":
      "Inbäddning är processen att omvandla text till vektorer. Dessa referenser är nödvändiga för att göra om dina filer och prompts till ett format plattformen kan hantera.",
    provider: {
      title: "Inbäddningsleverantör",
      description:
        "Ingen konfiguration krävs om du använder plattformens inbyggda inbäddningsmotor.",
      "search-embed": "Sök bland alla inbäddningsleverantörer",
      select: "Välj en inbäddningsleverantör",
      search: "Sök bland alla inbäddningsleverantörer",
    },
    workspace: {
      title: "Arbetsytans inbäddningspreferens",
      description:
        "Den specifika inbäddningsleverantören & modellen som används för denna arbetsyta. Som standard används systemets inbäddningsleverantör och inställningar.",
      "multi-model": "Denna leverantör stöder ännu inte flera modeller.",
      "workspace-use": "Denna arbetsyta kommer att använda",
      "model-set": "den modell som är inställd i systemet.",
      embedding: "Arbetsytans inbäddningsmodell",
      model:
        "Den specifika inbäddningsmodellen som används för denna arbetsyta. Om det är tomt, används systemets inbäddningspreferens.",
      wait: "-- väntar på modeller --",
      setup: "Installera",
      use: "För att använda",
      "need-setup": "som denna arbetsytas inbäddare måste du konfigurera det.",
      cancel: "Avbryt",
      save: "Spara",
      settings: "inställningar",
      search: "Sök bland alla inbäddningsleverantörer",
      "need-llm": "som denna arbetsytas LLM måste du först konfigurera det.",
      "save-error":
        "Misslyckades att spara {{provider}} inställningar: {{error}}",
      "system-default": "Systemstandard",
      "system-default-desc":
        "Använd systemets inbäddningspreferens för denna arbetsyta.",
    },
    warning: {
      "switch-model":
        "Byt inbäddningsmodell kommer att bryta för tidigare inbäddade dokument från att fungera under chatt. De måste un-embeds från alla arbetsytter och tas bort och laddas upp igen så att de kan inbäddas av den nya inbäddningsmodellen.",
    },
  },

  // =========================
  // TEXT SPLITTING & CHUNKING
  // =========================
  text: {
    title: "Textdelning & Chunking-preferenser",
    "desc-start":
      "Ibland kan du behöva ändra standardmetoden för hur nya dokument delas upp och chunkas innan de läggs till i din vektordatabas.",
    "desc-end":
      "Ändra endast detta om du förstår hur textdelning fungerar och vilka följder det kan ha.",
    "warn-start": "Ändringar här gäller endast",
    "warn-center": "nyligen inbäddade dokument",
    "warn-end": ", inte befintliga dokument.",
    method: {
      title: "Text-splitter metod",
      "native-explain":
        "Använd lokal chunkstorlek & överlappning för uppdelning.",
      "jina-explain":
        "Delegera chunking/segmentering till Jinas inbyggda metod.",
      size: {
        title: "Chunkstorlek",
        description: "Det maximala antalet tokens per chunk.",
      },
      jina: {
        api_key: "Jina API-nyckel",
        api_key_desc:
          "Krävs för att använda Jinas segmenteringstjänst. Nyckeln kommer att lagras i din miljö.",
        max_tokens: "Jina: Max tokens per chunk",
        max_tokens_desc:
          "Definierar max tokens i varje chunk för Jinas segmenterare (max 2000 tokens).",
        return_tokens: "Returnera token-information",
        return_tokens_desc:
          "Inkludera token-antal och tokenizer-information i svaret.",
        return_chunks: "Returnera chunk-information",
        return_chunks_desc:
          "Inkludera detaljerad information om genererade chunks i svaret.",
      },
      "jina-info": "Jina-chunking aktiv.",
    },
    size: {
      title: "Textchunkstorlek",
      description:
        "Detta är den maximala teckenlängden som får finnas i en enskild vektor.",
      recommend: "Inbäddningsmodellens maximala längd är",
    },
    overlap: {
      title: "Textchunk överlappning",
      description:
        "Detta är den maximala överlappningen av tecken mellan två intilliggande textchunks.",
      error:
        "Chunk-överlappningen kan inte vara större eller lika med chunkstorleken.",
    },
  },

  // =========================
  // VECTOR DATABASE (SYSTEM)
  // =========================
  vector: {
    title: "Vektordatabas",
    description:
      "Detta är inloggningsuppgifterna och inställningarna för hur din plattformsinstans ska fungera. Det är viktigt att dessa nycklar är aktuella och korrekta.",
    provider: {
      title: "Vektordatabasleverantör",
      description: "Ingen konfiguration krävs för LanceDB.",
      "search-db": "Sök bland alla vektordatabasleverantörer",
    },
    search: {
      title: "Vektorsökläge",
      mode: {
        "globally-enabled":
          "Denna inställning styrs globalt i systeminställningarna. Besök systeminställningar för att ändra omrankning.",
        default: "Standardsökning",
        "default-desc": "Standard vektorlikhetssökning utan omrankning.",
        "accuracy-optimized": "Optimerad för noggrannhet",
        "accuracy-desc":
          "Omrangordnar resultat för att förbättra noggrannheten, men ökar genereringstid.",
      },
    },
    "max-context": "Maximalt antal kontextutdrag",
  },

  // =========================
  // EMBEDDABLE CHAT WIDGETS
  // =========================
  embeddable: {
    title: "Inbäddningsbara chattwidgets",
    description:
      "Inbäddningsbara chattwidgets är publika chattgränssnitt som är knutna till en enskild arbetsyta. Dessa låter dig bygga arbetsytor som du sedan kan publicera för allmänheten.",
    create: "Skapa inbäddning",
    table: {
      workspace: "Arbetsyta",
      chats: "Skickade chattar",
      Active: "Aktiva domäner",
    },
  },

  // =========================
  // EMBED CHATS
  // =========================
  "embed-chats": {
    title: "Inbäddningschattar",
    export: "Exportera",
    description:
      "Här visas alla registrerade chattar och meddelanden från en publicerad inbäddning.",
    table: {
      embed: "Inbäddning",
      sender: "Avsändare",
      message: "Meddelande",
      response: "Svar",
      at: "Skickat vid",
    },
    delete: {
      title: "Ta bort chat",
      message: "Är du säker på att du vill ta bort denna chat?",
    },
    config: {
      "delete-title": "Ta bort chat",
      "delete-message": "Är du säker på att du vill ta bort denna chat?",
      "disable-title": "Ta bort chat",
      "disable-message": "Är du säker på att du vill ta bort denna chat?",
      "enable-title": "Ta bort chat",
      "enable-message": "Är du säker på att du vill ta bort denna chat?",
    },
  },

  // =========================
  // MULTI-USER MODE
  // =========================
  multi: {
    title: "Multi-användarläge",
    description:
      "Konfigurera din instans för att stödja flera användare genom att aktivera multi-användarläge.",
    enable: {
      "is-enable": "Multi-användarläge är aktiverat",
      enable: "Aktivera multi-användarläge",
      description:
        "Som standard är du den enda administratören. Som administratör måste du skapa konton för alla nya användare eller administratörer. Förlora inte ditt lösenord eftersom endast en administratör kan återställa lösenord.",
      username: "Administratörskonto (e-post)",
      password: "Administratörslösenord",
      "username-placeholder": "Ditt admin-användarnamn",
      "password-placeholder": "Ditt admin-lösenord",
    },
    password: {
      title: "Lösenordsskydd",
      description:
        "Skydda din instans med ett lösenord. Om du glömmer detta finns ingen metod för återställning, så säkerställ att du sparar det.",
    },
    instance: {
      title: "Lösenordsskydda instans",
      description:
        "Som standard är du den enda administratören. Du måste skapa konton för alla nya användare eller administratörer. Förlora inte lösenordet eftersom endast administratörer kan återställa det.",
      password: "Instanslösenord",
    },
  },

  // =========================
  // EVENT LOGS
  // =========================
  event: {
    title: "Händelseloggar",
    description:
      "Visa alla åtgärder och händelser som sker på denna instans för övervakning.",
    clear: "Rensa händelseloggar",
    table: {
      type: "Händelsetyp",
      user: "Användare",
      occurred: "Inträffade vid",
    },
  },

  // =========================
  // PRIVACY & DATA-HANDLING
  // =========================
  privacy: {
    title: "Sekretess & Datahantering",
    description:
      "Detta är din konfiguration för hur anslutna tredjepartsleverantörer och vår plattform hanterar dina data.",
    llm: "LLM-val",
    embedding: "Inbäddningspreferens",
    vector: "Vektordatabas",
    anonymous: "Anonym telemetri aktiverad",
    "desc-event": "Alla händelser registreras utan IP-adress och innehåller",
    "desc-id": "ingen identifierande",
    "desc-cont":
      "information, inställningar, chattar eller annan icke-användarinriktad information. Se listan över händelsetaggar på",
    "desc-git": "Github här",
    "desc-end":
      "Som ett open-source-projekt respekterar vi din rätt till integritet. Om du väljer att stänga av telemetri ber vi dig överväga att skicka feedback så att vi kan fortsätta förbättra plattformen.",
  },

  // =========================
  // DEFAULT CHAT
  // =========================
  "default-chat": {
    welcome: "Välkommen till Foynet.",
    "choose-legal": "Välj ett juridiskt område till vänster.",
  },

  // =========================
  // INVITES
  // =========================
  invites: {
    title: "Inbjudningar",
    description:
      "Skapa inbjudningslänkar för personer i din organisation att acceptera och registrera sig med. En inbjudan kan bara användas av en enskild användare.",
    link: "Skapa inbjudningslänk",
    accept: "Accepterad av",
    usage: "Användning",
    "created-by": "Skapad av",
    created: "Skapad",
    new: {
      title: "Skapa ny inbjudan",
      "desc-start":
        "Efter skapandet kan du kopiera inbjudan och skicka den till en ny användare där de kan skapa ett konto som",
      "desc-mid": "standard",
      "desc-end": "roll och automatiskt läggas till i valda arbetsytor nedan.",
      "auto-add": "Lägg till inbjuden automatiskt till arbetsytor",
      "desc-add":
        "Du kan valfritt automatiskt tilldela användaren arbetsytor genom att välja dem nedan. Som standard ges ingen tilldelning. Du kan göra detta senare efter att inbjudan har accepterats.",
      cancel: "Avbryt",
      "create-invite": "Skapa inbjudan",
      error: "Fel: ",
    },
    "link-copied": "Inbjudningslänk kopierad",
    "copy-link": "Kopiera inbjudningslänk",
    "delete-invite-title": "Inaktivera inbjudan",
    "delete-invite-confirmation":
      "Är du säker på att du vill inaktivera denna inbjudan?\nEfter detta kan den inte användas.\n\nÅtgärden är oåterkallelig.",
    status: {
      label: "Status",
      pending: "Väntande",
      disabled: "Inaktiverad",
      claimed: "Accepterad",
    },
  },

  // =========================
  // USER MENU
  // =========================
  "user-menu": {
    edit: "Redigera konto",
    profile: "Profilbild",
    size: "800 x 800",
    "remove-profile": "Ta bort profilbild",
    username: "E-postadress",
    "username-placeholder": "Ange e-postadress",
    "new-password": "Nytt lösenord",
    "new-password-placeholder": "Nytt lösenord",
    cancel: "Avbryt",
    update: "Uppdatera konto",
    language: "Föredraget språk",
    email: "E-postadress",
    "email-placeholder": "Ange e-postadress",
  },

  // =========================
  // HEADER / SIDEBAR (THREADS)
  // =========================
  sidebar: {
    thread: {
      "load-thread": "Laddar trådar....",
      "empty-thread": "Ny tråd",
      default: "Standard",
      "starting-thread": "Startar tråd...",
      thread: "Ny tråd",
      delete: "Ta bort valda",
      deleted: "raderad",
      "rename-message": "Ange ett nytt namn för tråden:",
      "delete-message":
        "Är du säker på att du vill ta bort den här tråden? Denna åtgärd kan inte ångras.",
      rename: "Byt namn",
      "delete-thread": "Ta bort tråd",
      "rename-thread-title": "Byt namn på tråd",
      "new-name-placeholder": "Ange nytt trådnamn",
      "delete-thread-title": "Ta bort tråd?",
      "delete-confirmation-message":
        'Är du säker på att du vill ta bort tråden "{{name}}"? Denna åtgärd kan inte ångras.',
    },
  },

  // =========================
  // THREAD NAME ERROR
  // =========================
  thread_name_error:
    "Trådnamnet måste vara mellan 3 och 255 tecken och får endast innehålla bokstäver, siffror, mellanslag eller bindestreck.",

  // =========================
  // EMBEDDER (EMBEDDING PROVIDER NAMES)
  // =========================
  embeder: {
    allm: "Använd inbyggd inbäddningsleverantör. Ingen inställning krävs!",
    openai: "Standardalternativet för de flesta användningsområden.",
    azure: "Företagsalternativet för OpenAI som hostas på Azure-tjänster.",
    localai: "Kör inbäddningsmodeller lokalt på din egen maskin.",
    ollama: "Kör inbäddningsmodeller lokalt på din egen maskin.",
    lmstudio:
      "Upptäck, ladda ner och kör tusentals banbrytande LLM:er med några klick.",
    cohere: "Kör kraftfulla inbäddningsmodeller från Cohere.",
    voyageai: "Kör kraftfulla inbäddningsmodeller från Voyage AI.",
    "generic-openai": "Använd en generisk OpenAI inbäddningsmodell.",
    "default.embedder": "Standardinbäddning",
    jina: "Kör kraftfulla inbäddningsmodeller från Jina.",
    litellm: "Kör kraftfulla inbäddningsmodeller från LiteLLM.",
  },

  // =========================
  // VECTOR DATABASE PROVIDER DESCRIPTIONS
  // =========================
  vectordb: {
    lancedb:
      "100 % lokal vektordatabas som körs på samma instans som plattformen.",
    chroma:
      "Öppen källkod vektordatabas som du kan drifta själv eller i molnet.",
    pinecone: "100 % molnbaserad vektordatabas för företagsanvändningsfall.",
    zilliz:
      "Molnbaserad vektordatabas byggd för företag med SOC 2-efterlevnad.",
    qdrant: "Öppen källkod, lokal och distribuerad molnvektordatabas.",
    weaviate: "Öppen källkod, lokal och molnhostad multimodal vektordatabas.",
    milvus: "Öppen källkod, mycket skalbar och extremt snabb.",
    astra: "Vektorsök för verklig GenAI.",
  },

  // =========================
  // SYSTEM PREFERENCES
  // =========================
  system: {
    title: "Systeminställningar",
    "desc-start":
      "Detta är de övergripande inställningarna och konfigurationerna för din instans.",
    context_window: {
      title: "Dynamiskt Kontextfönster",
      desc: "Kontrollera hur mycket av LLM:s kontextfönster som används för ytterligare källor.",
      label: "Kontextfönster i procent",
      help: "Procentandel av kontextfönstret som kan användas (10-100%).",
      "toast-success": "Kontextfönsterprocent uppdaterades framgångsrikt.",
      "toast-error": "Misslyckades att uppdatera kontextfönsterprocent.",
    },
    "change-login-ui": {
      title: "Välj standard inloggningsgränssnitt",
      status: "Nuvarande",
      subtitle:
        "Gränssnittet kommer att tillämpas som standard inloggningsgränssnitt för applikationen",
    },
    user: "Användare kan ta bort arbetsytor",
    "desc-delete":
      "Tillåt icke-administratörer att ta bort arbetsytor de är en del av. Detta tar bort arbetsytan för alla.",
    limit: {
      title: "Meddelandegräns",
      "desc-limit":
        "Begränsa antalet meddelanden en användare kan skicka per dag.",
      "per-day": "Meddelanden per dag",
      label: "Meddelandebegränsningen är för närvarande ",
    },
    max_tokens: {
      title: "Maximala inloggningstokens per användare",
      desc: "Ställ in det maximala antalet aktiva autentiseringstokens som varje användare kan ha åt gången. När gränsen överskrids tas äldre tokens bort.",
      label: "Maximalt antal tokens",
      help: "Värdet måste vara större än 0",
    },
    state: {
      enabled: "Aktiverat",
      disabled: "Inaktiverat",
    },
    "source-highlighting": {
      title: "Aktivera / Inaktivera källmarkering",
      description: "Dölj eller visa källmarkering för användare.",
      label: "Citat: ",
      "toast-success": "Källmarkerings-inställningen har uppdaterats",
      "toast-error": "Det gick inte att uppdatera källmarkerings-inställningen",
    },
    "usage-registration": {
      title: "Användningsregistrering för fakturering",
      description:
        "Aktivera eller inaktivera faktureringsloggning för övervakning.",
      label: "Faktureringsloggning är ",
    },
    "forced-invoice-logging": {
      title: "Tvingad fakturaregistrering",
      description:
        "Aktivera för att kräva en fakturareferens innan plattformen används.",
      label: "Tvingad fakturaregistrering är ",
    },
    "rexor-linkage": {
      title: "Rexor-länkning",
      description:
        "Aktivera Rexor-länkning för att få aktiva ärendereferenser till Rexor.",
      label: "Rexor-länkning är ",
      "activity-id": "Aktivitets-ID",
      "activity-id-description": "Ange aktivitets-ID för Rexor-integration",
    },
    rerank: {
      title: "Omrankningsinställningar",
      description:
        "Konfigurera omrankningsinställningar för att förbättra sökresultatens relevans med LanceDB.",
      "enable-title": "Aktivera omrankning",
      "enable-description":
        "Aktivera omrankning för att förbättra sökresultatens relevans genom att ta hänsyn till mer kontext.",
      status: "Omrankningsstatus",
      "vector-count-title": "Ytterligare vektorer för omrankning",
      "vector-count-description":
        "Antal ytterligare vektorer att hämta utöver arbetsytans vektorantal. Till exempel, om arbetsytan är inställd på att hämta 30 vektorer och detta är satt till 50, kommer totalt 80 vektorer att övervägas för omrankning. Ett högre antal kan förbättra noggrannheten men ökar bearbetningstiden.",
      "lancedb-only": "Endast LanceDB",
      "lancedb-notice":
        "Denna funktion är endast tillgänglig när LanceDB används som vektordatabas.",
    },
    attachment_context: {
      title: "Kontextfönster för bilagor",
      desc: "Kontrollera hur mycket av det dynamiska kontextfönstret som kan användas för bilagor.",
      label: "Kontextfönster i procent",
      help: "Procentandel av det dynamiska kontextfönstret som kan användas för bilagor (10-80%).",
      "toast-success": "Kontextfönster för bilagor uppdaterat.",
      "toast-error": "Kunde inte uppdatera kontextfönster för bilagor.",
      "validation-error":
        "Kontextfönster för bilagor måste vara mellan 10 och 80.",
    },
    save: "Spara ändringar",
  },

  feedback: {
    thankYou: "Tack! Din feedback har skickats in framgångsrikt.",
    emailSendError: "Misslyckades att skicka e-post: ",
    submitFeedbackError: "Misslyckades att skicka feedback: ",
    attachFile: "Bifoga en fil",
    improvePlatform: "Hjälp oss förbättra plattformen!",
    suggestionOrQuestion: "Några förslag? Eller frågor?",
    clickToWrite: "Vänligen klicka för att skriva till oss",
    noFeedback: "Ingen feedback hittades",
    previewImage: "Förhandsgranska bild",
    filePreview: "Förhandsgranska fil",
    noFile: "Ingen fil bifogad",
    fullName: "Fullständigt namn",
    fullNamePlaceholder: "Ange ditt fullständiga namn",
    message: "Meddelande",
    messagePlaceholder: "Ange din feedback",
    attachment: "Bilaga",
    submit: "Skicka feedback",
    submitting: "Skickar...",
    submitSuccess: "Feedback skickades",
    submitError: "Det gick inte att skicka feedback",
    imageLoadError: "Det gick inte att ladda bilden",
    unsupportedFile: "Filtypen stöds inte",
    validation: {
      fullNameMinLength: "Fullständigt namn måste vara minst 2 tecken",
      fullNameMaxLength: "Fullständigt namn kan inte vara längre än 100 tecken",
      fullNameFormat:
        "Fullständigt namn kan bara innehålla bokstäver, siffror, mellanslag eller bindestreck",
      messageMinLength: "Meddelande måste vara minst 12 tecken",
      messageMaxLength: "Meddelande kan inte vara längre än 1000 tecken",
      messageMinWords: "Meddelande måste vara minst 4 ord",
      fileType: "Filen måste vara en JPEG, PNG eller PDF",
      fileSize: "Filen måste vara mindre än 5MB",
    },
  },

  "feedback-settings": {
    "header-title": "Feedbacklista",
    "delete-feedback": "Feedback raderad framgångsrikt!",
    "header-description":
      "Detta är den kompletta listan över feedback för denna instans. Observera att borttagning av feedback är permanent och inte kan ångras.",
    title: "Användarfeedback-knapp",
    description: "Aktivera eller inaktivera feedback-knappen för användare.",
    successMessage: "Användarfeedback-knappen har uppdaterats",
    failureUpdateMessage:
      "Misslyckades att uppdatera användarfeedback-knappens status.",
    errorSubmitting: "Fel vid sändning av feedback-inställningar.",
    errorFetching: "Fel vid hämtning av feedback-inställningar.",
  },

  // =========================
  // USER SETTINGS (INSTANCE USERS)
  // =========================
  "user-setting": {
    description:
      "Detta är alla konton som har ett konto på denna instans. Att ta bort ett konto tar omedelbart bort deras åtkomst.",
    "add-user": "Lägg till användare",
    username: "E-postadress",
    role: "Roll",
    "economy-id": "Ekonomi-ID",
    "economy-id-ph": "Ange identifierare för ekonomisystem",
    "economy-id-hint":
      "ID som används för integrationer med externa ekonomisystem (t.ex. Rexor)",
    default: "Standard",
    manager: "Chef",
    admin: "Administratör",
    superuser: "Superuser",
    "date-added": "Datum tillagt",
    "all-domains": "Alla domäner",
    "other-users": "Andra Användare (Ingen Domän)",
    // Sorteringsalternativ för användarlistan
    "sort-username": "Sortera efter användarnamn",
    "sort-organization": "Sortera efter organisation",
    edit: "Redigera: ",
    "new-password": "Nytt lösenord",
    "password-rule": "Lösenordet måste vara minst 8 tecken långt.",
    "update-user": "Uppdatera användare",
    placeholder: "Ange e-postadress",
    cancel: "Avbryt",
    "remove-user": "Ta bort användare",
    "remove-user-title": "Ta bort användare",
    "remove-user-confirmation":
      "Är du säker på att du vill ta bort denna användare?",
    error: "Fel: ",
  },

  "login-ui": {
    "show-toast": {
      "update-failed": "Misslyckades med att uppdatera inloggningsgränssnittet",
      "updated-login-ui": "Inloggningsgränssnittet har uppdaterats",
    },
    "visit-website": "Visit the website",
    loading: "Loading ...",
    "rw-login-description":
      "Maximera juridisk produktivitet med vår AI-drivna plattform!",
  },

  // =========================
  // SUPPORT E-POST
  // =========================
  support: {
    title: "Support E-post",
    description:
      "Ange supportens e-postadress som visas i användarmenyn när du är inloggad på denna instans.",
    clear: "Rensa",
    save: "Spara",
  },

  // =========================
  // PUBLIC MODE
  // =========================
  "public-mode": {
    enable: "Aktivera Offentligt-användarläge",
    enabled: "Offentligt-användarläge är aktiverat",
  },

  // =========================
  // BUTTON LABELS
  // =========================
  button: {
    delete: "Radera",
    edit: "Redigera",
    suspend: "Avstänga",
    unsuspend: "Återaktivera",
    save: "Spara",
    accept: "Godta",
    decline: "Avböj",
    ok: "OK",
    "flush-vector-caches": "Töm vektorcache",
    cancel: "Avbryt",
    saving: "Sparar",
    save_llm: "Spara LLM-val",
    save_template: "Spara mall",
    "reset-to-default": "Återställ till standard",
    create: "Skapa",
    enable: "Aktivera",
    disable: "Inaktivera",
    reset: "Återställ",
    revoke: "Återkalla",
  },

  // =========================
  // NEW USER (INSTANCE)
  // =========================
  "new-user": {
    title: "Lägg till användare i instansen",
    username: "E-postadress",
    "username-ph": "Ange e-postadress",
    password: "Lösenord",
    "password-ph": "Användarens initiala lösenord",
    role: "Roll",
    default: "Standard",
    manager: "Chef",
    admin: "Administratör",
    superuser: "Superuser",
    description:
      "Efter att en användare skapats måste de logga in med det initiala lösenordet för att få åtkomst.",
    cancel: "Avbryt",
    "add-User": "Lägg till användare",
    error:
      "Det gick inte att skapa användaren. Detta kan bero på att användaren redan finns eller att ett systemfel uppstod.",
    "invalid-email": "Ange en giltig e-postadress.",
    permissions: {
      title: "Behörigheter",
      default: [
        "Kan endast skicka chattar i arbetsytor de blivit tillagda i av administratör eller chef.",
        "Kan inte ändra några inställningar alls.",
      ],
      manager: [
        "Kan se, skapa och ta bort alla arbetsytor samt ändra arbetsytespecifika inställningar.",
        "Kan skapa, uppdatera och bjuda in nya användare till instansen.",
        "Kan inte ändra LLM, vektorDB, inbäddningar eller andra anslutningar.",
      ],
      admin: [
        "Högsta nivå av användarbehörighet.",
        "Kan se och göra allt i systemet.",
      ],
      superuser: [
        "Kan komma åt specifika inställningssidor som Dokumentbyggaren och Prompt-förbättring.",
        "Kan inte ändra systemomfattande inställningar som LLM, vektorDB-konfigurationer.",
        "Kan skicka chattar i arbetsytor de blivit tillagda i av administratör eller chef.",
      ],
    },
  },

  // =========================
  // NEW EMBED
  // =========================
  "new-embed": {
    title: "Skapa ny inbäddning för arbetsyta",
    error: "Fel: ",
    "desc-start":
      "Efter att ha skapat en inbäddning får du en länk som du kan publicera på din webbplats med en enkel",
    script: "script",
    tag: "tag.",
    cancel: "Avbryt",
    "create-embed": "Skapa inbäddning",
    workspace: "Arbetsyta",
    "desc-workspace":
      "Detta är arbetsytan som chatten baseras på. Alla standardvärden ärvs från arbetsytan om de inte åsidosätts av denna konfiguration.",
    "allowed-chat": "Tillåten chattmetod",
    "desc-query":
      "Ange hur din chattbot ska bete sig. 'Fråga' innebär att den endast besvarar frågor om det finns dokumentkontext.",
    "desc-chat":
      "Chatt öppnar boten för generella frågor och kan besvara frågor som inte är relaterade till arbetsytan.",
    "desc-response": "Chatt: Svara på alla frågor oavsett kontext",
    "query-response":
      "Fråga: Endast svar om frågan är relaterad till dokument i arbetsytan",
    restrict: "Begränsa förfrågningar från domäner",
    filter:
      "Detta filter blockerar alla förfrågningar från andra domäner än de listade nedan.",
    "use-embed":
      "Om detta lämnas tomt kan vem som helst använda din inbäddning på vilken webbplats som helst.",
    "max-chats": "Max chattar per dag",
    "limit-chats":
      "Begränsa antalet chattar denna inbäddade chatt kan hantera under en 24-timmarsperiod. 0 innebär obegränsat.",
    "chats-session": "Max chattar per session",
    "limit-chats-session":
      "Begränsa antalet chattar en användarsession kan skicka i denna inbäddning under en 24-timmarsperiod. 0 innebär obegränsat.",
    "enable-dynamic": "Aktivera dynamisk modellanvändning",
    "llm-override":
      "Tillåt att man väljer en föredragen LLM-modell som åsidosätter arbetsytans standard.",
    "llm-temp": "Aktivera dynamisk LLM-temperatur",
    "desc-temp":
      "Tillåt att LLM-temperaturen kan ställas in för att åsidosätta arbetsytans standard.",
    "prompt-override": "Aktivera prompt-återställning",
    "desc-override":
      "Tillåt att systemprompten kan åsidosättas och ändra arbetsytans standardprompt.",
  },

  // =========================
  // SHOW TOAST MESSAGES
  // =========================

  // Moved to showToast.js

  // =========================
  // LLM SELECTION PRIVACY
  // =========================
  "llm-selection-privacy": {
    openai: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för svar är synliga för OpenAI",
      ],
    },
    azure: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Din text och inbäddningstext är inte synliga för OpenAI eller Microsoft",
      ],
    },
    anthropic: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för att generera svar är synliga för Anthropic",
      ],
    },
    gemini: {
      description: [
        "Dina chattar är avpersonifierade och kan användas för träning",
        "Dina prompts och dokumenttext som används för att generera svar är synliga för Google",
      ],
    },
    lmstudio: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på servern som kör LMStudio",
      ],
    },
    localai: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på servern som kör LocalAI",
      ],
    },
    ollama: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på maskinen som kör Ollama",
      ],
    },
    native: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på denna instans",
      ],
    },
    togetherai: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för svar är synliga för TogetherAI",
      ],
    },
    mistral: {
      description: [
        "Dina prompts och dokumenttext som används för svar skickas till Mistral",
      ],
    },
    huggingface: {
      description: [
        "Dina prompts och dokumenttext som används för svar skickas till din HuggingFace-hanterade endpoint",
      ],
    },
    perplexity: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för att generera svar är synliga för Perplexity AI",
      ],
    },
    openrouter: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för att generera svar är synliga för OpenRouter",
      ],
    },
    groq: {
      description: [
        "Dina chattar kommer inte att användas för träning",
        "Dina prompts och dokumenttext som används för svar är synliga för Groq",
      ],
    },
    koboldcpp: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på servern som kör KoboldCPP",
      ],
    },
    textgenwebui: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på servern som kör Oobabooga Text Generation Web UI",
      ],
    },
    "generic-openai": {
      description: [
        "Data delas enligt de villkor och avtal som gäller hos din generiska endpoint-leverantör.",
      ],
    },
    cohere: {
      description: [
        "Data delas enligt villkoren för cohere.com och lokala integritetslagar.",
      ],
    },
    litellm: {
      description: [
        "Din modell och dina chattar är endast tillgängliga på servern som kör LiteLLM",
      ],
    },
  },

  // =========================
  // VECTOR DATABASE PRIVACY
  // =========================
  "vector-db-privacy": {
    chroma: {
      description: [
        "Dina vektorer och dokumenttext lagras på din Chroma-instans",
        "Åtkomst till din instans hanteras av dig",
      ],
    },
    pinecone: {
      description: [
        "Dina vektorer och dokumenttext lagras på Pinecones servrar",
        "Åtkomsten till dina data hanteras av Pinecone",
      ],
    },
    qdrant: {
      description: [
        "Dina vektorer och dokumenttext lagras på din Qdrant-instans (moln eller egenvärd)",
      ],
    },
    weaviate: {
      description: [
        "Dina vektorer och dokumenttext lagras på din Weaviate-instans (moln eller egenvärd)",
      ],
    },
    milvus: {
      description: [
        "Dina vektorer och dokumenttext lagras på din Milvus-instans (moln eller egenvärd)",
      ],
    },
    zilliz: {
      description: [
        "Dina vektorer och dokumenttext lagras på ditt Zilliz-molnkluster.",
      ],
    },
    astra: {
      description: [
        "Dina vektorer och dokumenttext lagras på din molndatabas i AstraDB.",
      ],
    },
    lancedb: {
      description: [
        "Dina vektorer och dokumenttext lagras privat på denna instans.",
      ],
    },
  },

  // =========================
  // EMBEDDING ENGINE PRIVACY
  // =========================
  "embedding-engine-privacy": {
    native: {
      description: ["Din dokumenttext inbäddas privat på denna instans."],
    },
    openai: {
      description: [
        "Din dokumenttext skickas till OpenAI:s servrar",
        "Dina dokument används inte för OpenAIs träningsändamål",
      ],
    },
    azure: {
      description: [
        "Din dokumenttext skickas till din Microsoft Azure-tjänst",
        "Dina dokument används inte för träningsändamål",
      ],
    },
    localai: {
      description: [
        "Din dokumenttext inbäddas privat på servern som kör LocalAI",
      ],
    },
    ollama: {
      description: [
        "Din dokumenttext inbäddas privat på servern som kör Ollama",
      ],
    },
    lmstudio: {
      description: [
        "Din dokumenttext inbäddas privat på servern som kör LMStudio",
      ],
    },
    cohere: {
      description: [
        "Data delas enligt villkoren för cohere.com samt lokala integritetslagar.",
      ],
    },
    voyageai: {
      description: [
        "Data som skickas till Voyage AI:s servrar delas enligt VoyageAI:s villkor.",
      ],
    },
  },

  // =========================
  // PROMPT VALIDATION
  // =========================
  "prompt-validate": {
    edit: "Redigera",
    response: "Svar",
    prompt: "Prompt",
    regenerate: "Generera om svar",
    good: "Bra svar",
    bad: "Dåligt svar",
    copy: "Kopiera",
    more: "Fler åtgärder",
    fork: "Forka",
    delete: "Radera",
    cancel: "Avbryt",
    save: "Spara & Skicka",
    "export-word": "Exportera till Word",
    exporting: "Exporterar...",
  },

  // =========================
  // CITATIONS
  // =========================
  citations: {
    show: "Visa citat",
    hide: "Dölj citat",
    chunk: "Citatdelar",
    pdr: "Huvuddokument",
    "pdr-h": "Dokumentmarkering",
    referenced: "Refererad",
    times: "gånger.",
    citation: "Citat",
    match: "träff",
    download:
      "Den här webbläsaren stöder inte PDF. Vänligen ladda ner PDF:en för att visa den:",
    "download-btn": "Ladda ner PDF",
    view: "Visa källor och citat",
    sources: "Källor",
    "pdf-collapse-tip":
      "Tips: Du kan minimera denna PDF-flik med knappen i övre vänstra hörnet",
    "open-in-browser": "Öppna i webbläsaren",
    "loading-pdf": "-- laddar PDF --",
    "error-loading": "Fel vid laddning av PDF",
    "no-valid-path": "Ingen giltig PDF-sökväg hittades",
    "web-search": "Webbsökning",
    "web-search-summary": "Webbsökningssammanfattning",
    "web-search-results": "Webbsökningsresultat",
    "no-web-search-results": "Inga webbsökningsresultat hittades",
    "previous-highlight": "Föregående markering",
    "next-highlight": "Nästa markering",
    "try-alternative-view": "Prova alternativ vy",
  },

  // =========================
  // DOCUMENT DRAFTING
  // =========================
  "document-drafting": {
    title: "Dokumentutkast",
    description: "Kontrollera inställningarna för dokumentutkast.",
    configuration: "Konfiguration",
    "drafting-model": "Dokumentutkast LLM",
    enabled: "Dokumentutkast är aktiverat",
    disabled: "Dokumentutkast är inaktiverat",
    "enabled-toast": "Dokumentutkast aktiverat",
    "disabled-toast": "Dokumentutkast inaktiverat",
    "desc-settings":
      "Administratören kan ändra inställningarna för dokumentutkast för alla användare.",
    "drafting-llm": "LLM-preferens för dokumentutkast",
    saving: "Sparar...",
    save: "Spara ändringar",
    "chat-settings": "Chattinställningar",
    "drafting-chat-settings": "Chattinställningar för dokumentutkast",
    "chat-settings-desc":
      "Kontrollera hur chattfunktionen beter sig för dokumentutkast.",
    "drafting-prompt": "Systemprompt för dokumentutkast",
    "drafting-prompt-desc":
      "Systemprompten som används i dokumentutkastet skiljer sig från systemprompten för juridisk Q&A. Denna prompt definierar kontext och instruktioner så att AI genererar ett relevant och korrekt svar.",
    linking: "Dokumentlänkning",
    "legal-issues-prompt": "Prompt för juridiska frågor",
    "legal-issues-prompt-desc": "Ange prompten för juridiska frågor.",
    "memo-prompt": "Prompt för anteckning",
    "memo-prompt-desc": "Ange prompten för memo/anteckningar.",
    "desc-linkage":
      "Aktivera ytterligare juridisk kontext genom att använda vektor/PDR-sökningar ovanpå memohämtning",
    message: {
      title: "Föreslagna meddelanden för dokumentutkast",
      description:
        "Lägg till förslag på meddelanden som användare snabbt kan välja vid dokumentutkast.",
      heading: "Standardmeddelanderubrik",
      body: "Standardmeddelandetext",
      "new-heading": "Meddelanderubrik",
      message: "Meddelandeinnehåll",
      add: "Lägg till meddelande",
      save: "Spara meddelanden",
    },
    "combine-prompt": "Kombinationsprompt",
    "combine-prompt-desc":
      "Ange systemprompten för att kombinera flera svar till ett enda svar. Denna prompt används både för att kombinera svar och DD Linkage-rapporter, och för att kombinera de olika svaren från Infinity Context-bearbetning.",
    "page-description":
      "Denna sida är för att justera de olika promptar som används i olika funktioner i dokumentutkastmodulen. I varje inmatningsfält visas standardprompten, som kommer att användas om inte en anpassad prompt tillämpas på denna sida.",
    "dd-linkage-steps": "Promptar som tillämpas för DD-länkningssteg",
    "general-combination-prompt": "Allmän kombinationsprompt",
    "import-memo": {
      title: "Importera rättsutredning",
      "button-text": "Importera PM",
      "search-placeholder": "Sök trådar...",
      import: "Importera",
      importing: "Importerar...",
      "no-threads": "Inga rättsutredningstrådar hittades",
      "no-matching-threads": "Inga trådar matchar din sökning",
      "thread-not-found": "Vald tråd hittades inte",
      "empty-thread": "Den valda tråden har inget innehåll att importera",
      "import-success": "Trådinnehåll importerades framgångsrikt",
      "import-error": "Kunde inte importera trådinnehåll",
      "import-error-details": "Fel vid import: {{details}}",
      "fetch-error": "Kunde inte hämta trådar. Försök igen senare.",
      "imported-from": "Importerad från Legal QA-tråd",
      "unnamed-thread": "Namnlös tråd",
      "unknown-workspace": "Okänd arbetsyta",
      "no-threads-available": "Inga trådar tillgängliga att importera",
      "create-conversations-first":
        "Skapa utredning i Extern AI först, sedan kan du importera dem här.",
      "no-legal-qa-workspaces":
        "Inga Extern AI-arbetsytor med aktiva trådar hittades. Skapa utredningar i en Extern AI-arbetsyta först för att importera dem.",
      "empty-workspaces-with-names":
        "Hittade arbetsytor ({{workspaceNames}}) men de innehåller inga aktiva trådar ännu. Skapa konversationer i dessa arbetsytor först för att importera dem.",
      "import-success-with-name": "Tråden har importerats: {{threadName}}",
    },
  },

  // =========================
  // LEGAL TASK PROMPT GENERATOR
  // =========================
  "legal-task-prompt-generator": {
    title: "Generator för användarprompt",
    description:
      "Automatiskt föreslå en anpassad prompt för en juridisk uppgift",
    "task-description": "Beskrivning av den juridiska uppgiften",
    "task-description-placeholder":
      "Beskriv den juridiska uppgiften du vill automatisera...",
    "suggested-prompt": "Föreslagen användarprompt",
    "specific-instructions": "Specifika instruktioner eller kunskap",
    "specific-instructions-description":
      "Inkludera alla specifika instruktioner eller kunskap som är relevant för denna juridiska uppgift",
    "specific-instructions-placeholder":
      "Lägg till specifika instruktioner, kunskap eller erfarenhet för att hantera denna juridiska uppgift...",
    "generation-prompt": "Prompt för generering",
    "create-task": "Skapa en juridisk uppgift baserad på detta förslag",
    generating: "Genererar...",
    generate: "Generera förslag",
    "toast-success": "Prompt genererad framgångsrikt",
    "toast-fail": "Misslyckades att generera prompt",
    button: "Generera prompt",
    success: "Prompt genererad framgångsrikt",
    error: "Ange först ett namn eller en underkategori",
    failed: "Misslyckades att generera prompt",
  },

  // =========================
  // DD SETTINGS (WORKSPACE LINKING SETTINGS)
  // =========================
  "dd-settings": {
    title: "Inställningar för länkning av arbetsytor",
    description:
      "Kontrollera tokenbegränsningar och beteende för länkade arbetsytor",
    "vector-search": {
      title: "Vektorsökning",
      description:
        "När denna funktion är aktiverad utförs semantiska vektorsökningar över alla länkade arbetsytor för att hitta relevanta juridiska dokument. Systemet konverterar användarfrågor till vektorinbäddningar och matchar dem mot dokumentvektorer i varje länkad arbetsytas databas. Denna funktion fungerar som en reservlösning när PM-generering är aktiverad men misslyckas med att producera resultat. När PM-generering är inaktiverad blir Vektorsökning den primära metoden för att hämta information från länkade arbetsytor. Sökdjupet kontrolleras av inställningen för Vektortokenbegränsning.",
    },
    "memo-generation": {
      title: "PM-generering",
      description:
        "Denna funktion genererar automatiskt koncisa juridiska PM från dokument som finns i länkade arbetsytor. När den är aktiverad analyserar systemet hämtade dokument för att skapa strukturerade sammanfattningar av juridiska nyckelpunkter, prejudikat och relevant kontext. Dessa PM fungerar som den primära metoden för att införliva kunskap från länkade arbetsytor. Om PM-generering misslyckas eller inte ger några resultat, kommer systemet automatiskt att återgå till Vektorsökning (om aktiverad) för att säkerställa att relevant information ändå hämtas. Längden och detaljnivån på dessa PM styrs av inställningen för PM-tokenbegränsning.",
    },
    "linked-workspace-impact": {
      title: "Tokenpåverkan för länkade arbetsytor",
      description:
        "Kontrollerar hur systemet hanterar sin tokenbudget över flera länkade arbetsytor. När denna funktion är aktiverad justerar systemet dynamiskt de tillgängliga tokens för varje arbetsyta baserat på det totala antalet länkade arbetsytor, vilket säkerställer en rättvis fördelning av dataresurser. Detta förhindrar att en enskild arbetsyta dominerar kontextfönstret samtidigt som det upprätthåller omfattande täckning över alla relevanta juridiska områden. Denna inställning reserverar tokenkapacitet specifikt för PM-generering och/eller Vektorsökningsresultat från varje länkad arbetsyta, vilket kan minska det totala antalet tokens som är tillgängliga för den primära arbetsytan när många arbetsytor är länkade.",
    },
    "vector-token-limit": {
      title: "Vektortokenbegränsning",
      description:
        "Anger det maximala antalet tokens som tilldelas för vektorsökningsresultat från varje länkad arbetsyta. Denna begränsning gäller när Vektorsökning används, antingen som den primära metoden (när PM-generering är inaktiverad) eller som en reservlösning (när PM-generering misslyckas). Högre gränser möjliggör mer omfattande dokumenthämtning men minskar tokens tillgängliga för andra operationer.",
    },
    "memo-token-limit": {
      title: "PM-tokenbegränsning",
      description:
        "Kontrollerar den maximala längden på genererade juridiska PM från varje länkad arbetsyta. Som den primära metoden för kunskapsintegrering sammanfattar dessa PM juridiska nyckelpunkter från den länkade arbetsytans dokument. Om ett PM överskrider denna tokengräns kommer det att avvisas och systemet återgår till Vektorsökning (om aktiverad). Högre gränser möjliggör mer detaljerad juridisk analys men kan minska antalet länkade arbetsytor som kan införlivas.",
    },
    "toast-success": "Inställningarna uppdaterades framgångsrikt",
    "toast-fail": "Det gick inte att uppdatera inställningarna",
  },

  // =========================
  // WORKSPACE LINKING
  // =========================
  "workspace-linking": {
    title: "Inställningar för länkning av arbetsytor",
    description:
      "Kontrollera tokenbegränsningar och beteende för länkade arbetsytor",
    "vector-search": {
      title: "Vektorsökning",
      description:
        "Reservmetod för att hitta relevanta dokument när PM-generering misslyckas eller är inaktiverad",
    },
    "memo-generation": {
      title: "PM-generering",
      description:
        "Primär metod för att införliva kunskap från länkade arbetsytor",
    },
    "linked-workspace-impact": {
      title: "Tokenpåverkan för länkade arbetsytor",
      description:
        "Reservera tokens för varje länkad arbetsyta i proportion till deras antal",
    },
    "vector-token-limit": {
      title: "Vektortokengräns",
      description:
        "Maximalt antal tokens per länkad arbetsyta för vektorsökning.",
    },
    "memo-token-limit": {
      title: "Memotokengräns",
      description:
        "Maximalt antal tokens för generering av juridiska anteckningar.",
    },
    "base-token-limit": {
      title: "Grundtokengräns",
      description: "Maximalt antal tokens för grundinnehåll.",
    },
    "toast-success": "Inställningar uppdaterade framgångsrikt",
    "toast-fail": "Misslyckades att uppdatera inställningarna",
  },

  // =========================
  // MODALE (DOCUMENT & CONNECTORS)
  // =========================
  modale: {
    document: {
      title: "Mina dokument",
      document: "Dokument",
      search: "Sök efter dokument",
      folder: "Ny mapp",
      name: "Namn",
      empty: "Inga dokument",
      "move-workspace": "Flytta till Arbetsyta",
      "doc-processor": "Dokumentbehandlare",
      "processor-offline":
        "Dokumentprocessorn är för närvarande offline. Försök igen senare.",
      "drag-drop": "Klicka för att ladda upp eller dra och släpp",
      "supported-files": "Stödda filer: PDF",
      "submit-link": "Eller skicka en länk till ett dokument",
      fetch: "Hämta",
      fetching: "Hämtar...",
      "file-desc":
        "Obs: Dokumentet kommer att bearbetas och läggas till i din arbetsyta. Detta kan ta några ögonblick.",
      cost: "*Engångskostnad för inbäddning",
      "save-embed": "Spara och bädda in",
      "failed-uploads": "Misslyckade uppladdningar",
      "loading-message": "Detta kan ta en stund för stora dokument",
      "uploading-file": "Laddar upp fil(er)...",
      "scraping-link": "Bearbetar länk...",
      "moving-documents": "Flyttar {{count}} dokument. Vänligen vänta.",
      "exceeds-prompt-limit":
        "Obs: Det uppladdade innehållet överskrider vad som kan rymmas i en prompt. Systemet kommer att bearbeta förfrågningar genom flera prompts vilket kommer att öka tiden för att generera svaret, och precisionen kan påverkas.",
    },
    connectors: {
      title: "Datakontakter",
      search: "Sök datakontakter",
      empty: "Inga datakontakter hittades.",
    },
    "justify-betweening": "Bearbetar...",
  },

  // =========================
  // DATA CONNECTORS
  // =========================
  dataConnectors: {
    github: {
      name: "GitHub Repo",
      description:
        "Importera ett helt offentligt eller privat GitHub-repository med ett enda klick.",
      url: "GitHub Repo URL",
      "collect-url": "URL till GitHub-repot du vill samla in.",
      "access-token": "GitHub Åtkomsttoken",
      optional: "valfritt",
      "rate-limiting": "Åtkomsttoken för att undvika rate-limiting.",
      "desc-picker":
        "När processen är klar blir alla filer tillgängliga för inbäddning i dokumenthanteraren.",
      branch: "Gren",
      "branch-desc": "Grenen du vill samla filer ifrån.",
      "branch-loading": "-- laddar tillgängliga grenar --",
      "desc-start": "Utan att fylla i",
      "desc-token": "GitHub Åtkomsttoken",
      "desc-connector": "kommer denna datakontakt endast kunna samla in",
      "desc-level": "top-nivåns",
      "desc-end":
        "filer i repot på grund av GitHubs offentliga API-begränsningar.",
      "personal-token":
        "Få en personlig åtkomsttoken gratis med ett GitHub-konto här.",
      without: "Utan en",
      "personal-token-access": "Personlig Åtkomsttoken",
      "desc-api":
        ", kan GitHub-API begränsa antalet filer som kan samlas in p.g.a. rate-limits. Du kan",
      "temp-token": "skapa en tillfällig åtkomsttoken",
      "avoid-issue": "för att undvika detta problem.",
      submit: "Skicka",
      "collecting-files": "Samlar in filer...",
    },
    "youtube-transcript": {
      name: "YouTube Transkript",
      description:
        "Importera transkriptet av en hel YouTube-video från en länk.",
      url: "YouTube Video URL",
      "url-video": "URL till den YouTube-video du vill transkribera.",
      collect: "Hämta transkript",
      collecting: "Hämtar transkript...",
      "desc-end":
        "När processen är klar blir transkriptet tillgängligt för inbäddning i dokumenthanteraren.",
    },
    "website-depth": {
      name: "Bulk Link Scraper",
      description:
        "Skrapa en webbplats och dess underlänkar ner till en viss nivå.",
      url: "Webbplats URL",
      "url-scrape": "URL till webbplatsen du vill skrapa.",
      depth: "Djup",
      "child-links":
        "Antalet underlänkar som ska följas från ursprungs-URL:en.",
      "max-links": "Max antal länkar",
      "links-scrape": "Max antal länkar att skrapa.",
      scraping: "Skrapar webbplats...",
      submit: "Skicka",
      "desc-scrap":
        "När klart blir alla skrapade sidor tillgängliga för inbäddning i dokumenthanteraren.",
    },
    confluence: {
      name: "Confluence",
      description: "Importera en hel Confluence-sida med ett klick.",
      url: "Confluence Sid URL",
      "url-page": "URL till en sida i Confluence-utrymmet.",
      username: "Confluence Användarnamn",
      "own-username": "Ditt Confluence-användarnamn.",
      token: "Confluence Åtkomsttoken",
      "desc-start":
        "Du måste ange en åtkomsttoken för autentisering. Du kan generera en API-token",
      here: "här",
      access: "Åtkomsttoken för autentisering.",
      collecting: "Samlar in sidor...",
      submit: "Skicka",
      "desc-end":
        "När klart blir alla sidor tillgängliga för inbäddning i dokumenthanteraren.",
    },
  },

  // =========================
  // MODULE DEFINITIONS
  // =========================
  module: {
    "legal-qa": "Rättsutredning",
    "document-drafting": "Dokumentutkast",
    "active-case": "Aktivt ärende",
  },

  // =========================
  // FINE-TUNE NOTIFICATION
  // =========================
  "fine-tune": {
    title: "Du har tillräckligt med data för en fine-tune!",
    link: "Klicka för att lära dig mer",
    dismiss: "Avvisa",
  },

  // =========================
  // MOBILE DISCLAIMER
  // =========================
  mobile: {
    disclaimer:
      "VARNING: För bästa upplevelse och full tillgång till alla funktioner, använd en dator för att komma åt appen.",
  },
  // =========================
  // SHARE MODAL
  // =========================
  shareModal: {
    title: "Dela {type}",
    titleWorkspace: "Dela arbetsyta",
    titleThread: "Dela tråd",
    shareWithUsers: "Dela med användare",
    shareWithOrg: "Dela med hela organisationen",
    searchUsers: "Sök användare...",
    noUsersFound: "Inga användare hittades",
    loadingUsers: "Laddar användare...",
    errorLoadingUsers: "Fel vid laddning av användare",
    errorLoadingStatus: "Fel vid laddning av delningsstatus",
    userAccessGranted: "Användaråtkomst beviljad",
    userAccessRevoked: "Användaråtkomst återkallad",
    orgAccessGranted: "Organisationsåtkomst beviljad",
    orgAccessRevoked: "Organisationsåtkomst återkallad",
    errorUpdateUser: "Fel vid uppdatering av användaråtkomst",
    errorNoOrg: "Kan inte dela: kontot är inte kopplat till en organisation",
    errorUpdateOrg: "Fel vid uppdatering av organisationsåtkomst",
    close: "Stäng",
    grantAccess: "Bevilja åtkomst",
    revokeAccess: "Återkalla åtkomst",
  },

  // =========================
  // ONBOARDING
  // =========================
  onboarding: {
    welcome: "Välkommen till",
    "get-started": "Kom igång",
    "llm-preference": {
      title: "LLM-preferens",
      description:
        "ISTLLM kan arbeta med många olika LLM-leverantörer. Detta kommer att vara tjänsten som hanterar chattandet.",
      "LLM-search": "Sök LLM-leverantörer",
    },
    "user-setup": {
      title: "Användarinställningar",
      description: "Konfigurera dina användarinställningar.",
      "sub-title": "Hur många personer kommer att använda din instans?",
      "single-user": "Bara jag",
      "multiple-user": "Mitt team",
      "setup-password": "Vill du ställa in ett lösenord?",
      "password-requirment": "Lösenordet måste vara minst 8 tecken långt.",
      "save-password":
        "Det är viktigt att spara detta lösenord eftersom det inte finns någon återställningsmetod.",
      "password-label": "Instanslösenord",
      username: "Administratörskonto (e-post)",
      password: "Administratörslösenord",
      "account-requirment":
        "E-post måste vara giltig. Lösenordet måste vara minst 8 tecken.",
      "password-note":
        "Som standard är du den enda administratören. När onboarding är klar kan du skapa och bjuda in andra användare eller administratörer. Förlora inte lösenordet eftersom endast administratörer kan återställa det.",
    },
    "data-handling": {
      title: "Datahantering & Integritet",
      description:
        "Vi är engagerade i transparens och kontroll när det gäller dina personuppgifter.",
      "llm-label": "LLM-val",
      "embedding-label": "Inbäddningspreferens",
      "database-lablel": "Vektordatabas",
      "reconfigure-option":
        "Dessa inställningar kan omkonfigureras när som helst i inställningsmenyn.",
    },
    survey: {
      title: "Välkommen till IST Legal LLM",
      description:
        "Hjälp oss göra IST Legal LLM anpassat för dina behov. (Frivilligt)",
      email: "Vilken är din e-postadress?",
      usage: "Vad ska du använda plattformen till?",
      work: "För arbete",
      "personal-use": "För personligt bruk",
      other: "Annat",
      comment: "Några kommentarer till teamet?",
      optional: "(Frivilligt)",
      feedback: "Tack för din feedback!",
    },
    button: {
      yes: "Ja",
      no: "Nej",
      "skip-survey": "Hoppa över undersökningen",
    },
    placeholder: {
      "admin-password": "Ditt administratörslösenord",
      "admin-username": "Din admin-e-post",
      "email-example": "<EMAIL>",
      comment:
        "Om du har några frågor eller kommentarer nu, kan du skriva dem här, <NAME_EMAIL>",
    },
  },

  // =========================
  // DEFAULT SETTINGS FOR LEGAL Q&A
  // =========================
  "default-settings": {
    "canvas-prompt": "Canvas-systemprompt",
    "canvas-prompt-desc":
      "Prompt för canvas-chatt. Används som systemprompt för interaktioner i canvas.",
    title: "Standardinställningar för Legal Q&A",
    "default-desc":
      "Kontrollera standardbeteendet för arbetsytor för Legal Q&A",
    prompt: "Legal Q&A systemprompt",
    "prompt-desc":
      "Standardprompten som kommer att användas för nya Legal Q&A arbetsytor. Definiera kontext och instruktioner för AI att generera ett svar. Du bör tillhandahålla en noggrant utformad prompt så att AI kan generera ett relevant och korrekt svar. För att tillämpa denna inställning på alla befintliga arbetsytor och åsidosätta deras anpassade inställningar, använd knappen nedan.",
    "prompt-placeholder": "Ange din prompt här",
    "toast-success": "Standardsystemprompt uppdaterad",
    "toast-fail": "Misslyckades att uppdatera standardsystemprompten.",
    "apply-all-confirm":
      "Är du säker på att du vill använda denna systemprompt i alla befintliga Legal Q&A arbetsytor? Denna åtgärd kan inte ångras och kommer att åsidosätta eventuella anpassade inställningar.",
    "apply-to-all": "Använd på alla befintliga Legal Q&A arbetsytor",
    applying: "Använder...",
    "toast-apply-success": "Standardsystemprompt införd i {{count}} arbetsytor",
    "toast-apply-fail": "Kunde inte införa standardsystemprompt i arbetsytor",
    snippets: {
      title: "Standardinställning för max antal kontextutdrag",
      description:
        "Detta styr det maximala antalet kontextutdrag som skickas till LLM för nya arbetsytor. För att tillämpa denna inställning på alla befintliga arbetsytor och åsidosätta deras anpassade inställningar, använd knappen nedan.",
      recommend:
        "Rekommenderat värde är minst 30. Att ställa in mycket högre värden kommer att öka bearbetningstiden utan att nödvändigtvis förbättra precisionen beroende på kapaciteten hos den använda LLM:en.",
    },
    "rerank-limit": {
      title: "Maximal omrankningsgräns",
      description:
        "Det maximala antalet utdrag som kommer att omrankas. Ett högre värde kan ge bättre resultat men ökar bearbetningstiden.",
      recommend: "Rekommenderat värde är mellan 20-50",
    },
    "validation-prompt": {
      title: "Valideringsprompt",
      description:
        "Denna inställning styr den prompt som skickas till LLM för att validera det givna svaret.",
      placeholder:
        "Validera följande svar genom att kontrollera alla juridiska referenser och citat mot den tillhandahållna kontexten. Lista eventuella felaktigheter.",
    },
    "apply-vector-search-to-all":
      "Använd på alla befintliga Legal Q&A arbetsytor",
    "apply-vector-search-all-confirm":
      "Är du säker på att du vill använda denna vektorsökningsinställning i alla befintliga Legal Q&A arbetsytor? Denna åtgärd kan inte ångras.",
    "toast-vector-search-apply-success":
      "Vektorsökningsinställning införd i {{count}} arbetsytor",
    "toast-vector-search-apply-fail":
      "Kunde inte införa vektorsökningsinställning i arbetsytor",
    "canvas-upload-prompt": "Systemprompt för Canvas-uppladdning",
    "canvas-upload-prompt-desc":
      "Systemprompten som används vid uppladdning via canvas. Denna prompt styr LLM:s beteende för uppladdat innehåll.",
  },

  // =========================
  // CONFIRM MESSAGE
  // =========================
  "confirm-message": {
    "delete-doc-title": "Ta bort filer och mappar",
    "delete-doc":
      "Är du säker på att du vill ta bort dessa filer och mappar?\nDetta tar bort filerna från systemet och raderar dem från eventuella befintliga arbetsytor.\nDenna åtgärd kan inte ångras.",
  },

  // =========================
  // PERFORM LEGAL TASK
  // =========================
  performLegalTask: {
    title: "Utför juridisk uppgift",
    noTaskfund: "Inga juridiska uppgifter hittades.",
    noSubtskfund: "Inga underuppgifter hittades.",
    "loading-subcategory": "Laddar underuppgifter...",
    "select-category": "Välj kategori",
    "choose-task": "Välj juridisk uppgift att utföra",
    "duration-info":
      "Tiden för att utföra en juridisk uppgift beror på antalet dokument i arbetsytan. Med många dokument och en komplex uppgift kan detta ta mycket lång tid.",
    description:
      "Aktivera eller inaktivera knappen för att utföra juridisk uppgift i dokumentutkast.",
    successMessage: "Funktionen för juridisk uppgift har {{status}}",
    subStep: "Pågående eller köat steg",
    failureUpdateMessage:
      "Det gick inte att uppdatera inställningen för juridisk uppgift.",
    errorSubmitting:
      "Ett fel uppstod när inställningarna för juridisk uppgift skulle skickas.",
    "custom-instructions-placeholder":
      "Ange ärendespecifika instruktioner, exempelvis uppgift om vilken part vi företräder, särskilda önskemål, bakgrundsinformation etc (valfritt)...",
    "additional-instructions-label": "Ytterligare instruktioner:",
    "warning-title": "Varning",
    "no-files-title": "Inga filer tillgängliga",
    "no-files-description":
      "Det finns inga filer i detta arbetsyteområde. Ladda upp minst en fil före att utföra en juridisk uppgift.",
    "settings-button": "Ändra eller lägg till juridiska uppgifter",
    settings: "Juridiska uppgifter",
  },

  // =========================
  // CANVAS CHAT
  // =========================
  canvasChat: {
    title: "Canvas",
    "input-placeholder": "Be om juridisk information",
    chatboxinstruction: "Instruera alla slags ändringar av svaret",
    explanation:
      "Detta verktyg är för AI-redigering av svaret på olika sätt. Källorna för det underliggande svaret tillämpas, vilket innebär att du kan be om ytterligare förklaringar utifrån samma källmaterial.",
    editAnswer: "Redigera svaret",
  },

  // =========================
  // STATUSES
  // =========================
  statuses: {
    enabled: "aktiverad",
    disabled: "inaktiverad",
  },

  // =========================
  // ANSWER UPGRADE
  // =========================

  // Moved to answerUpgrade.js

  // =========================
  // PDR SETTINGS
  // =========================
  "pdr-settings": {
    title: "PDR-inställningar",
    description:
      "Hantera inställningar för Parent Document Retrieval för dina arbetsytor.",
    "desc-end":
      "Dessa inställningar påverkar hur PDR-dokument hanteras och används i chattsvar.",
    "global-override": {
      title: "Global dynamisk PDR-åsidosättning",
      description:
        "När detta är aktiverat kommer alla arbetsytors dokument att hanteras som PDR-aktiverade i källgenereringen. När det är inaktiverat kommer endast dokument som uttryckligen markerats som PDR att hanteras som sådana, vilket kan minska källsammanhang och leda till svar av lägre kvalitet eftersom endast vektordelar från sökningar kommer att användas som källor i dessa fall.",
    },
    "toast-success": "PDR-inställningar uppdaterade",
    "toast-fail": "Misslyckades att uppdatera PDR-inställningarna.",
    "adjacent-vector-limit": "Angränsande vektorgräns",
    "adjacent-vector-limit-desc": "Gräns för hur många angränsande vektorer.",
    "adjacent-vector-limit-placeholder": "Ange angränsande vektorgräns",
    "keep-pdr-vectors": "Behåll PDR-vektorer",
    "keep-pdr-vectors-desc": "Inställning för att behålla PDR-vektorer.",
    "pdr-token-limit-placeholder": "Ange token-gräns för PDR",
    "input-prompt-token-limit-placeholder":
      "Ange token-gräns för inmatningsprompt",
    "response-token-limit-placeholder": "Ange token-gräns för svar",
  },

  // =========================
  // VALIDATE RESPONSE
  // =========================
  "validate-response": {
    title: "Valideringsresultat",
    "toast-fail": "Kunde inte validera svaret",
    validating: "Validerar svar",
    button: "Validera svar",
    "adjust-prefix":
      "Gör alla nämnda ändringar i svaret baserat på återkopplingen: ",
    "adjust-button": "Tillämpa föreslagna ändringar",
  },

  // =========================
  // WORKSPACE NAMES (LEGAL AREAS)
  // =========================
  "workspace-names": {
    "Administrative Law": "Förvaltningsrätt",
    "Business Law": "Affärsrätt",
    "Civil Law": "Civilrätt",
    "Criminal Law": "Straffrätt",
    "Diplomatic Law": "Diplomaträtt",
    "Fundamental Law": "Grundlag",
    "Human Rights Law": "Mänskliga rättigheter",
    "Judicial Laws": "Rättslagar",
    "Security Laws": "Säkerhetslagar",
    "Taxation Laws": "Skattelagar",
  },

  // =========================
  // VALIDATE ANSWER
  // =========================
  "validate-answer": {
    setting: "Validerings-LLM",
    title: "Validerings-LLM-preferens",
    description:
      "Dessa är referenser och inställningar för din föredragna validerings-LLM för chatt & inbäddning. Det är viktigt att dessa nycklar är aktuella och korrekta för att systemet ska fungera korrekt.",
    "toast-success": "Valideringsinställningar för LLM uppdaterade!",
    "toast-fail": "Misslyckades att uppdatera LLM-valideringsinställningar",
    saving: "Sparar...",
    "save-changes": "Spara ändringar",
  },

  // =========================
  // ACTIVE CASE
  // =========================
  "active-case": {
    title: "Aktivt ärende",
    placeholder: "Ange referensnummer",
    "select-reference": "Välj referens",
    "warning-title": "Saknar referensnummer",
    "warning-message":
      "Inget referensnummer har angetts. Vill du fortsätta utan referensnummer?",
  },

  // =========================
  // SECURITY
  // =========================
  security: {
    "multi-user-mode-permanent":
      "Fleranvändarläge är permanent aktiverat av säkerhetsskäl",
    "password-validation": {
      "restricted-chars":
        "Ditt lösenord innehåller otillåtna tecken. Tillåtna symboler är _,-,!,@,$,%,^,&,*,(,),;",
    },
    "public-workspace": {
      "access-description":
        "När aktiverat kan alla användare komma åt de offentliga arbetsytorna utan att logga in.",
    },
    button: {
      saving: "Sparar...",
      "save-changes": "Spara ändringar",
    },
  },

  // =========================
  // ERRORS
  // =========================
  errors: {
    "fetch-models": "Misslyckades att hämta anpassade modeller",
    "fetch-models-error": "Fel vid hämtning av modeller",
    "upgrade-error": "Fel under uppgradering",
    "thread-creation-failed":
      "Det gick inte att skapa en ny tråd. Försök igen.",
    "failed-extract-content": "Kunde inte extrahera innehåll",
    "failed-process-attachment": "Kunde inte bearbeta bilaga",
    "failed-process-content": "Kunde inte bearbeta innehåll",
    "failed-process-file": "Kunde inte bearbeta fil",
    "ollama-endpoint-error":
      "Kunde inte automatiskt upptäcka Ollama-slutpunkten. Vänligen ange den manuellt.",
    common: {
      error: "Fel",
    },
    streaming: {
      failed:
        "Ett fel uppstod vid strömning av svaret, till exempel att AI-motorn är offline eller överbelastad.",
      code: "Kod",
      unknown: "Okänt fel.",
    },
    workspace: {
      "already-exists": "En arbetsyta med detta namn finns redan",
    },
    env: {
      "anthropic-key-format":
        "Ogiltigt Anthropic API-nyckelformat. Nyckeln måste börja med 'sk-ant-'",
      "openai-key-format": "OpenAI API-nyckeln måste börja med 'sk-'",
      "jina-key-format": "Jina API-nyckel måste börja med 'jina_'",
    },
    auth: {
      "invalid-credentials": "Ogiltiga inloggningsuppgifter.",
      "account-suspended": "Konto avstängt av administratör.",
      "invalid-password": "Ogiltigt lösenord angivet",
    },
    "invalid-token-count": "Ogiltigt antal tokens",
  },

  // =========================
  // LOADING STATES
  // =========================
  loading: {
    models: "-- laddar tillgängliga modeller --",
    "waiting-url": "-- väntar på URL --",
    "waiting-api-key": "-- väntar på API-nyckel --",
    "waiting-models": "-- väntar på modeller --",
  },

  // =========================
  // CHARTS
  // =========================
  charts: {
    downloading: "Laddar ner bild...",
    download: "Ladda ner diagrambild",
  },

  // =========================
  // MODALS
  // =========================
  modals: {
    warning: {
      title: "Varning",
      proceed: "Är du säker på att du vill fortsätta?",
      cancel: "Avbryt",
      confirm: "Bekräfta",
      "got-it": "Okej, jag förstår",
    },
  },

  // =========================
  // DOCUMENTS & PINNING
  // =========================
  documents: {
    "pin-info-button": "Om fästning",
    "pin-title": "Vad är att fästa (pin) ett dokument?",
    "pin-desc-1":
      "När du fäster ett dokument kommer plattformen att injicera hela dokumentets innehåll i din prompt, så att LLM fullt ut förstår det.",
    "pin-desc-2":
      "Detta fungerar bäst med modeller som har stort kontextfönster eller mindre filer som är kritiska för dess kunskapsbas.",
    "pin-desc-3":
      "Om du inte får de svar du önskar från standardinställningen kan fästning av dokument vara ett effektivt sätt att förbättra svarskvaliteten.",
    "pin-add": "Fäst till arbetsyta",
    "pin-unpin": "Ta bort från arbetsyta",
    "watch-title": "Vad innebär att bevaka (watch) ett dokument?",
    "watch-desc-1":
      "När du bevakar ett dokument kommer plattformen automatiskt att synka dokumentets innehåll från dess ursprungskälla med jämna mellanrum. Detta uppdaterar automatiskt innehållet i varje arbetsyta där filen används.",
    "watch-desc-2":
      "Den här funktionen stöder för närvarande endast online-baserat innehåll och är inte tillgänglig för dokument som laddas upp manuellt.",
    "watch-desc-3": "Du kan hantera vilka dokument som bevakas från",
    "file-manager": "fildelaren",
    "admin-view": "adminvyn",
    "pdr-add": "Alla dokument har lagts till Parent Document Retrieval",
    "pdr-remove":
      "Alla dokument har tagits bort från Parent Document Retrieval",
    empty: "Inga dokument hittades",
    tooltip: {
      date: "Datum: ",
      type: "Typ: ",
      cached: "Cachad",
    },
    actions: {
      removing: "Tar bort fil från arbetsytan",
    },
    costs: {
      estimate: "Uppskattad kostnad: $",
      minimum: "< $0.01",
    },
    "new-folder": {
      title: "Skapa ny mapp",
      "name-label": "Mappnamn",
      "name-placeholder": "Ange mappnamn",
      create: "Skapa mapp",
    },
    error: {
      "create-folder": "Misslyckades med att skapa mapp",
    },
  },

  // =========================
  // LEGAL QUESTION (EXAMPLE)
  // =========================
  "legal-question": {
    "category-one": "Kategori Ett",
    "category-two": "Kategori Två",
    "category-three": "Kategori Tre",
    "category-four": "Kategori Fyra",
    "category-five": "Kategori Fem",
    "item-one": "Artikel Ett",
    "item-two": "Artikel Två",
    "item-three": "Artikel Tre",
    "item-four": "Artikel Fyra",
    "item-five": "Artikel Fem",
    "item-six": "Artikel Sex",
    "item-seven": "Artikel Sju",
    "example-title": "Äta bra: En guide till att njuta av mat",
    example: {
      breakfast: {
        title: "1. Hälsosamma frukostalternativ",
        items: [
          "Havregrynsgröt med färska frukter och honung",
          "Grekisk yoghurtparfait med granola",
          "Avokadotoast med pocherade ägg",
          "Grön smoothie med spenat, banan och mandelmjölk",
          "Fullkornspannkakor med lönnsirap",
        ],
      },
      lunch: {
        title: "2. Snabba och enkla lunchidéer",
        items: [
          "Grillad kycklingwrap med blandad sallad",
          "Quinoasallad med rostade grönsaker",
          " Kalkonsmörgås på fullkornsbröd",
          "Grönsakswok med brunt ris",
          "Soppa och sidosallad",
        ],
      },
      dinner: {
        title: "3. Utsökta middagsrecept",
        items: [
          "Ugnsbakad lax med citron och sparris",
          "Spaghetti med tomatsås och köttbullar",
          "Grönsakscurry serverad med basmatiris",
          "Grillad biff med rostade potatisar",
          "Fyllda paprikor med quinoa och ost",
        ],
      },
    },
  },

  // =========================
  // PRESETS
  // =========================
  presets: {
    "edit-title": "Redigera standardprompt",
    description: "Beskrivning",
    "description-placeholder": "Sammanfattar bifogade filer.",
    deleting: "Raderar...",
    "delete-preset": "Radera standardprompt",
    cancel: "Avbryt",
    save: "Spara",
    "add-title": "Lägg till standardprompt",
    "command-label": "Namn på prompten, ett ord",
    "command-placeholder": "Sammanfattning",
    "command-desc":
      "Namnet är också chattboxens genväg, startar med /, för att använda denna prompt utan att trycka på knappar.",
    "prompt-label": "Prompt",
    "prompt-placeholder": "Sammanfattar bifogade filer.",
    "prompt-desc":
      "Prompten som skickas när denna prompt förinställning används.",
    "tooltip-add": "Lägg till ny standardprompt",
    "tooltip-hover": "Visa egna inställda standardpromptar.",
    "confirm-delete": "Bekräfta borttagning av standardpromptförinställning.",
  },

  // =========================
  // LEGAL CATEGORIES
  // =========================
  "legal-categories": {
    process: "Process",
    "process-stamning": "Stämning",
    "process-svaromal": "Svaromål",
    "process-yrkanden": "Yrkanden och Framställning",
    avtal: "Avtal",
    "avtal-anstallning": "Anställningsavtal",
    "avtal-finansiering": "Finansierings- och säkerhetsavtal",
    "avtal-licens": "Licensavtal",
    "due-diligence": "Due Diligence",
    "due-diligence-avtal": "Avtalsgranskning",
    "due-diligence-checklista": "Due Diligence-checklista",
    "due-diligence-compliance": "Compliance-kontroll",
  },

  // =========================
  // VALIDATION
  // =========================
  validation: {
    responseHeader: "Här är det genererade svaret",
    contextHeader: "Ursprunglig kontext och källor",
  },

  // =========================
  // DOCX EDITOR
  // =========================
  "docx-edit": {
    "edit-instructions":
      "Ange instruktioner för hur du vill redigera dokumentet. Var specifik med vilka ändringar du vill göra.",
    "instructions-placeholder":
      "t.ex. Åtgärda grammatiska fel, gör tonen mer formell, lägg till ett avslutande stycke...",
    "process-button": "Bearbeta dokument",
    "no-instructions": "Ange redigeringsinstruktioner",
    "process-error": "Fel vid bearbetning av dokument: ",
    "changes-highlighted": "Dokument med markerade ändringar",
    "download-button": "Ladda ner dokument",
    "start-over-button": "Börja om",
    "no-document": "Inget dokument tillgängligt för nedladdning",
    "download-error": "Fel vid nedladdning av dokument: ",
    "download-success": "Dokument nedladdat",
    processing: "Bearbetar dokument...",
    "instructions-used": "Använda instruktioner",
    "import-success": "DOCX-innehåll importerat",
    "edit-success": "DOCX-innehåll uppdaterat",
    "canvas-document-title": "Canvas-dokument",
    "upload-button": "Ladda upp DOCX",
    "download-as-docx": "Ladda ner som DOCX",
    "output-example": "Exempel på utdata",
    "output-example-desc":
      "Ladda upp en DOCX-fil för att lägga till exempelinnehåll till din prompt",
    "content-examples-tag-open": "<INNEHÅLL_EXEMPEL>",
    "content-examples-tag-close": "</INNEHÅLL_EXEMPEL>",
    "content-examples-info":
      "<INFO>Detta är ett exempel på innehåll som ska produceras, från en liknande juridisk uppgift. Observera att detta exempelinnehåll kan vara mycket kortare eller längre än det innehåll som nu ska produceras.</INFO>",
    "contains-example-content": "[Innehåller exempelinnehåll]",
  },

  // =========================
  // DOCUMENT BUILDER SETTINGS
  // =========================

  // Document Builder Page
  "document-builder": {
    title: "Inställningar för dokumentbyggare",
    description: "Kontrollera inställningarna för dokumentbyggaren.",
    "toast-success": "Inställningarna uppdaterade",
    "toast-fail": "Misslyckades att uppdatera inställningarna",
    save: "Spara",
    saving: "Sparar...",

    // =========================
    // ADD/EDIT LEGAL TASK KEYS
    // =========================

    "view-categories": "Visa alla kategorier",
    "hide-categories": "Dölj listan",
    "add-task": "Lägg till juridisk uppgift",
    loading: "Laddar...",
    table: {
      title: "Juridiska uppgifter",
      name: "Namn",
      "sub-category": "Underkategori",
      description: "Beskrivning",
      prompt: "Juridisk uppgiftsprompt",
      actions: "Åtgärder",
      delete: "Radera",
      "delete-confirm": "Är du säker på att du vill radera denna kategori?",
      "delete-success": "Kategorin har raderats",
      "delete-error": "Kunde inte radera kategorin",
    },
    "create-task-title": "Skapa en juridisk uppgift",
    "category-name": "Kategorinamn",
    "category-name-desc": "Ange huvudkategorins namn.",
    "category-name-placeholder": "Ange kategorinamn",
    "subcategory-name": "Underkategorinamn",
    "subcategory-name-desc": "Ange namnet på underkategorin.",
    "subcategory-name-placeholder": "Ange underkategorinamn",
    "description-desc": "Ge en beskrivning av kategorin och underkategorin.",
    "description-placeholder": "Ange en kort beskrivning",
    submitting: "Skickar...",
    submit: "Skicka",
    validation: {
      "category-required": "Kategorinamn krävs.",
      "subcategory-required": "Underkategorinamn krävs.",
      "description-required": "Beskrivning krävs.",
      "prompt-required": "Prompt för juridisk uppgift krävs.",
    },
    "create-task": {
      title: "Skapa juridisk uppgift",
      category: {
        name: "Kategorinamn",
        desc: "Ange kategorins namn.",
        placeholder: "Ange kategorinamn",
        type: "Kategorityp",
        new: "Skapa ny kategori",
        existing: "Använd befintlig kategori",
        select: "Välj kategori",
        "select-placeholder": "Välj en befintlig kategori",
      },
      subcategory: {
        name: "Namn på juridisk uppgift",
        desc: "Ange namnet på den juridiska uppgiften.",
        placeholder: "Ange namn på juridisk uppgift",
      },
      description: {
        name: "Beskrivning och användarinstruktioner",
        desc: "Informationen och instruktionerna som användaren kommer att se.",
        placeholder:
          "Beskriv t.ex. vilka slags dokument som behöver laddas upp i arbetsytan för att utfallet ska bli så bra som möjligt",
      },
      prompt: {
        name: "Juridisk uppgiftsprompt",
        desc: "Ange prompten som ska användas för denna juridiska uppgift. Du kan också ladda upp exempeldokument med knapparna för att lägga till innehållsexempel i din prompt.",
        placeholder:
          "Ange juridisk uppgiftsprompt eller ladda upp exempel dokument för att förbättra din prompt...",
      },
      submitting: "Skickar...",
      submit: "Skicka",
      validation: {
        "category-required": "Kategorinamn krävs.",
        "subcategory-required": "Namn på juridisk uppgift krävs.",
        "description-required": "Beskrivning krävs.",
        "prompt-required": "Juridisk prompt krävs.",
      },
    },
    "edit-task": {
      title: "Redigera juridisk uppgift",
      submitting: "Uppdaterar...",
      submit: "Uppdatera uppgift",
      subcategory: {
        name: "Namn på juridisk uppgift",
        desc: "Ange ett nytt namn för denna juridiska uppgift",
        placeholder: "Ange juridisk uppgift...",
      },
      description: {
        name: "Beskrivning och användarinstruktioner",
        desc: "Ange beskrivning och användarinstruktioner för denna juridiska uppgift",
        placeholder: "Ange beskrivning och användarinstruktioner...",
      },
      prompt: {
        name: "Juridisk uppgiftsprompt",
        desc: "Ange prompten som ska användas för denna juridiska uppgift. Du kan också ladda upp exempeldokument med knapparna för att lägga till innehållsexempel i din prompt.",
        placeholder:
          "Ange juridisk uppgiftsprompt eller ladda upp exempel dokument för att förbättra din prompt...",
      },
      validation: {
        "subcategory-required": "Namn på juridisk uppgift krävs",
        "description-required": "Beskrivning krävs",
        "prompt-required": "Juridisk uppgiftsprompt krävs",
      },
    },
    "task-form": {
      "requires-main-doc-label": "Val av huvuddokument krävs",
      "requires-main-doc-description":
        "Om markerad måste användaren välja huvuddokumentet från de uppladdade filerna när denna uppgift utförs. Detta rekommenderas starkt för juridiska uppgifter som involverar svar på ett brev eller en domstolsinlaga eller liknande, eftersom det strukturerar resultatet baserat på det dokument som besvaras.",
      "requires-main-doc-placeholder": "Ja eller Nej",
      "requires-main-doc-explanation-default":
        "Ett val behövs eftersom detta avgör hur dokumentet kommer att byggas.",
      "requires-main-doc-explanation-yes":
        "Om 'Ja' kommer användaren att behöva välja ett huvuddokument när denna juridiska uppgift påbörjas. Detta dokument kommer att vara centralt för uppgiftens arbetsflöde.",
      "requires-main-doc-explanation-no":
        "Om 'Nej' kommer den juridiska uppgiften att fortsätta utan att kräva ett förvalt huvuddokument. Uppgiften kommer att skapa resultat mer dynamiskt baserat på alla uppladdade dokument och den juridiska uppgiften.",
    },
    // Generatorprompt
    reviewGeneratorPromptButton: "Granska Generatorprompt",
    reviewGeneratorPromptButtonTooltip:
      "Visa exakt konstruerad prompt som användes för att generera juridisk uppgiftsförslag. (Endast administratörer)",
    reviewGeneratorPromptTitle: "Granska Generatorprompt",
    reviewPromptLabel: "Följande prompt användes för att generera:",
    reviewPromptTextareaLabel: "Innehåll i Generatorprompt",

    // =========================
    // LEGAL DRAFTING PROMPTS
    // =========================
    prompts: {
      group: {
        document_summary: {
          title: "Prompter för dokumentsammanfattning",
          description:
            "Konfigurera system- och användarprompter för dokumentsammanfattning.",
        },
        document_relevance: {
          title: "Prompter för dokumentrelevans",
          description:
            "Konfigurera system- och användarprompter för dokumentrelevans.",
        },
        section_drafting: {
          title: "Prompter för sektionsutkast",
          description:
            "Konfigurera system- och användarprompter för sektionsutkast.",
        },
        section_legal_issues: {
          title: "Prompter för juridiska frågor i sektioner",
          description:
            "Konfigurera system- och användarprompter för juridiska frågor i sektioner.",
        },
        memo_creation: {
          title: "Prompter för PM-skapande",
          description: "Konfigurera prompter för PM-skapande.",
        },
        section_index: {
          title: "Prompter för sektionsindex",
          description: "Konfigurera prompter för sektionsindex.",
        },
        select_main_document: {
          title: "Prompter för val av huvuddokument",
          description:
            "Konfigurera system- och användarprompter för val av huvuddokument.",
        },
        section_list_from_main: {
          title: "Prompter för sektionslista från huvuddokument",
          description:
            "Konfigurera system- och användarprompter för sektionslista från huvuddokument.",
        },
        section_list_from_summaries: {
          title: "Prompter för sektionslista från sammanfattningar",
          description:
            "Konfigurera system- och användarprompter för sektionslista från sammanfattningar.",
        },
      },
      // Document Summary
      "document-summary-system-label": "Dokumentsammanfattning (System)",
      "document-summary-system-description":
        "Systemprompt för att instruera AI om hur man sammanfattar ett dokuments innehåll och relevans för en juridisk uppgift.",
      "document-summary-user-label": "Dokumentsammanfattning (Användare)",
      "document-summary-user-description":
        "Användarpromptmall för att generera en detaljerad sammanfattning av dokumentinnehåll i relation till en specifik juridisk uppgift.",

      // Document Relevance
      "document-relevance-system-label": "Dokumentrelevans (System)",
      "document-relevance-system-description":
        "Systemprompt för att utvärdera om ett dokument är relevant för en juridisk uppgift, förväntar sig ett sant/falskt svar.",
      "document-relevance-user-label": "Dokumentrelevans (Användare)",
      "document-relevance-user-description":
        "Användarpromptmall för att kontrollera om dokumentinnehåll är relevant för en given juridisk uppgift.",

      // Section Drafting
      "section-drafting-system-label": "Sektionsutkast (System)",
      "section-drafting-system-description":
        "Systemprompt för att generera en enskild dokumentsektion i professionell juridisk stil, med användning av specificerade dokument och kontext.",
      "section-drafting-user-label": "Sektionsutkast (Användare)",
      "section-drafting-user-description":
        "Användarpromptmall för att generera en specifik sektion av ett juridiskt dokument, med hänsyn till titel, uppgift, källdokument och angränsande sektioner.",

      // Section Legal Issues
      "section-legal-issues-system-label":
        "Identifiering av juridiska frågor för sektion (System)",
      "section-legal-issues-system-description":
        "Systemprompt för att identifiera specifika juridiska ämnen för vilka faktisk information bör hämtas för att stödja utformningen av en dokumentsektion.",
      "section-legal-issues-user-label":
        "Identifiering av juridiska frågor för sektion (Användare)",
      "section-legal-issues-user-description":
        "Användarpromptmall för att lista juridiska ämnen eller datapunkter för att hämta bakgrundsinformation relevant för en specifik dokumentsektion och juridisk uppgift.",

      // Memo Creation
      "memo-creation-template-label": "Standardmall för skapande av PM",
      "memo-creation-template-description":
        "Promptmall för att skapa ett juridiskt PM som behandlar en specifik juridisk fråga, med hänsyn till tillhandahållna dokument och uppgiftskontext.",

      // Section Index
      "section-index-system-label": "Sektionsindex (System)",
      "section-index-system-description":
        "Systemprompt för att generera ett strukturerat index av sektioner för ett juridiskt dokument.",

      // Select Main Document
      "select-main-document-system-label": "Välj huvuddokument (System)",
      "select-main-document-system-description":
        "Systemprompt för att identifiera det mest relevanta huvuddokumentet för en juridisk uppgift från flera dokumentsammanfattningar.",
      "select-main-document-user-label": "Välj huvuddokument (Användare)",
      "select-main-document-user-description":
        "Användarpromptmall för att identifiera huvuddokumentet för en juridisk uppgift baserat på sammanfattningar av flera dokument.",

      // Section List from Main Document
      "section-list-from-main-system-label":
        "Sektionslista från huvuddokument (System)",
      "section-list-from-main-system-description":
        "Systemprompt för att utforma en JSON-strukturerad lista av sektioner för ett juridiskt dokument baserat på huvuddokumentets innehåll och den juridiska uppgiften.",
      "section-list-from-main-user-label":
        "Sektionslista från huvuddokument (Användare)",
      "section-list-from-main-user-description":
        "Användarpromptmall för att tillhandahålla den juridiska uppgiften och huvuddokumentets innehåll för att generera en sektionslista.",

      // Section List from Summaries
      "section-list-from-summaries-system-label":
        "Sektionslista från sammanfattningar (System)",
      "section-list-from-summaries-system-description":
        "Systemprompt för att utforma en JSON-strukturerad lista av sektioner baserat på dokumentsammanfattningar och den juridiska uppgiften när inget huvuddokument finns.",
      "section-list-from-summaries-user-label":
        "Sektionslista från sammanfattningar (Användare)",
      "section-list-from-summaries-user-description":
        "Användarpromptmall för att tillhandahålla den juridiska uppgiften och dokumentsammanfattningar för att generera en sektionslista när inget huvuddokument finns.",
    },
  },

  // =========================
  // REXOR
  // =========================
  rexor: {
    "register-project": "Registrera ärendenummer",
    "project-id": "Ärendenummer",
    "resource-id": "Resurs-ID",
    "activity-id": "Aktivitets-ID",
    register: "Registrera ärende",
    registering: "registrerar ...",
    "not-active": "Detta ärende är inte aktivt för registrering",
    "not-exist": "Detta ärende kunde inte hittas",
    account: {
      title: "Logga in på Rexor",
      username: "Användarnamn",
      password: "Lösenord",
      "no-token":
        "Inloggning misslyckades (Ingen token mottagen i handleLoginSuccess)",
      logout: "Logga ut",
      "no-user": "Vänligen logga in först",
      connected: "Ansluten till Rexor",
      "not-connected": "Inte ansluten",
      "change-account": "Byt konto",
      "session-expired": "Sessionen har gått ut. Logga in igen.",
    },
    "hide-article-transaction": "Dölj formulär för artikel transaktion",
    "show-article-transaction": "Visa formulär för artikel transaktion",
    "article-transaction-title": "Lägg till artikel transaktion",
    "registration-date": "Registreringsdatum",
    description: "Beskrivning",
    "description-internal": "Intern beskrivning",
    "hours-worked": "Arbetade timmar",
    "invoiced-hours": "Fakturerade timmar",
    invoiceable: "Fakturerbar",
    "sending-article-transaction": "Skickar artikel transaktion...",
    "save-article-transaction": "Spara artikel transaktion",
    "project-not-register": "Projektet måste registreras först.",
    "article-transaction-error":
      "Misslyckades att registrera artikel transaktion",
    "invoice-text": "Foynet antal slagningar",
  },

  // =========================
  // OPTIONS
  // =========================
  options: {
    yes: "Ja",
    no: "Nej",
  },

  // =========================
  // GENERIC PROVIDER SELECTION SETTINGS
  // =========================

  // =========================
  // GENERIC MODEL SELECTION SETTINGS
  // =========================

  // =========================
  // ANTHROPIC
  // =========================

  // =========================
  // AZURE SETTINGS
  // =========================

  // =========================
  // AWS BEDROCK SETTINGS
  // =========================

  // =========================
  // COHERE
  // =========================

  // =========================
  // DEEPSEEK LLM SETTINGS
  // =========================

  // =========================
  // FIREWORKSAI LLM SELECTION
  // =========================

  // =========================
  // GEMINI LLM OPTIONS
  // =========================

  // =========================
  // GROQ LLM SELECTION
  // =========================

  // =========================
  // HUGGINGFACE LLM SETTINGS
  // =========================

  // =========================
  // KOBOLDCPP SETTINGS
  // =========================

  // =========================
  // LITELLM
  // =========================

  // =========================
  // LMSTUDIO LLM SELECTION
  // =========================

  // =========================
  // LOCALAI LLM SELECTION
  // =========================

  // =========================
  // MISTRAL LLM OPTIONS
  // =========================

  // =========================
  // NATIVE LLM SETTINGS
  // =========================

  // =========================
  // OLLAMA LLM SELECTION
  // =========================

  // =========================
  // OPENAI LLM SELECTION
  // =========================

  // =========================
  // OPENROUTER LLM SELECTION
  // =========================

  // =========================
  // PERPLEXITY LLM SELECTION
  // =========================

  // =========================
  // LEGAL TEMPLATES MODAL
  // =========================

  // moved to legalTemplates

  // =========================
  // TEXTGENWEBUI COMPONENT
  // =========================
  textgenwebui: {
    "base-url": "Bas-URL",
    "base-url-placeholder": "http://127.0.0.1:5000/v1",
    "token-window": "Token-kontextfönster",
    "token-window-placeholder": "Innehållsfönstergräns (t.ex: 4096)",
    "api-key": "API-nyckel (Valfritt)",
    "api-key-placeholder": "TextGen Web UI API-nyckel",
    "max-tokens": "Max Tokens",
    "max-tokens-placeholder": "Max tokens per förfrågan (t.ex: 1024)",
  },

  // =========================
  // TOGETHERAI LLM SELECTION
  // =========================

  // =========================
  // XAI LLM OPTIONS
  // =========================

  // =========================
  // PIPER TTS OPTIONS
  // =========================
  piperTTS: {
    description:
      "Alla PiperTTS-modeller körs lokalt i din webbläsare. Detta kan vara resurskrävande på enheter med lägre prestanda.",
    "voice-model": "Val av röstmodell",
    "loading-models": "-- laddar tillgängliga modeller --",
    "stored-indicator":
      'Ett "✔" visar att modellen redan är lagrad lokalt och inte behöver laddas ner när den körs.',
    "flush-cache": "Töm röstcache",
    "flush-success": "Alla röster togs bort från webbläsarens lagring",
    "voice-generation-error": "Kunde inte generera röst: {{error}}",
    demo: {
      stop: "Stoppa demo",
      loading: "Laddar röst",
      play: "Spela exempel",
      text: "Detta är en exempeltext för att demonstrera rösten.",
    },
  },

  // =========================
  // AGENTS
  // =========================
  agents: {
    title: "Agentfärdigheter",
    "agent-skills": "Konfigurera och hantera agentfunktioner",
    "custom-skills": "Anpassade färdigheter",
    back: "Tillbaka",
    "select-skill": "Välj en färdighet att konfigurera",
    "preferences-saved": "Agentinställningar sparades",
    "preferences-failed": "Det gick inte att spara agentinställningar",
    "skill-status": {
      on: "På",
      off: "Av",
    },
  },

  // =========================
  // CHAT SETTINGS
  // =========================
  chatSettings: {
    placeholder: {
      drafting:
        "Givet följande konversation, relevant kontext och en följdfråga – besvara frågan. Returnera endast ditt svar givet informationen ovan och följ användarens instruktioner vid behov.",
      "legal-questions":
        "Vilka juridiska frågor uppstår utifrån den givna kontexten med prompt",
      "legal-memo": "Ge ett memo om var och en av dessa juridiska frågor",
    },
  },

  // =========================
  // LOGGING
  // =========================
  logging: {
    show: "visa",
    hide: "dölj",
    "event-metadata": "Händelsemetadata",
  },

  // =========================
  // EMBED CONFIGS
  // =========================
  embedConfigs: {
    "show-code": "Visa Kod",
    enable: "Aktivera",
    disable: "Inaktivera",
    "all-domains": "alla",
    "disable-confirm":
      "Är du säker på att du vill inaktivera denna inbäddning?\nNär den är inaktiverad kommer den inte längre att svara på några chattförfrågningar.",
    "delete-confirm":
      "Är du säker på att du vill radera denna inbäddning?\nNär den är raderad kommer den inte längre att svara på chattar.\n\nDenna åtgärd går inte att ångra.",
    "disabled-toast": "Inbäddning har inaktiverats",
    "enabled-toast": "Inbäddning är aktiverad",
  },

  badges: {
    default: {
      text: "Standard",
      tooltip:
        "Denna färdighet är aktiverad som standard och kan inte stängas av.",
    },
  },

  // =========================
  // EMBEDDING PROVIDER OPTIONS
  // =========================
  embedderchoice: {
    // Vanliga strängar
    "provider-logo": "{{provider}} logotyp",

    // LMStudio-inbäddningsalternativ
    lmstudio: {
      "model-label": "LM Studio Inbäddningsmodell",
      "max-chunk-length": "Maximal segmentlängd",
      "max-chunk-length-help": "Maximal längd på textsegment för inbäddning.",
      "hide-endpoint": "Dölj manuell slutpunktsinmatning",
      "show-endpoint": "Visa manuell slutpunktsinmatning",
      "base-url": "LM Studio Bas-URL",
      "base-url-placeholder": "http://localhost:1234/v1",
      "base-url-help": "Ange URL:en där LM Studio körs.",
      "auto-detect": "Upptäck automatiskt",
      "loading-models": "--laddar tillgängliga modeller--",
      "enter-url-first": "Ange först LM Studio-URL",
      "model-help":
        "Välj LM Studio-modellen för inbäddningar. Modeller laddas efter att en giltig LM Studio-URL har angetts.",
      "loaded-models": "Dina inlästa modeller",
    },

    // Ollama-inbäddningsalternativ
    ollama: {
      "model-label": "Ollama Inbäddningsmodell",
      "max-chunk-length": "Maximal segmentlängd",
      "max-chunk-length-help": "Maximal längd på textsegment för inbäddning.",
      "hide-endpoint": "Dölj manuell slutpunktsinmatning",
      "show-endpoint": "Visa manuell slutpunktsinmatning",
      "base-url": "Ollama Bas-URL",
      "base-url-placeholder": "http://127.0.0.1:11434",
      "base-url-help": "Ange URL:en där Ollama körs.",
      "auto-detect": "Upptäck automatiskt",
      "loading-models": "--laddar tillgängliga modeller--",
      "enter-url-first": "Ange först Ollama-URL",
      "model-help":
        "Välj Ollama-modellen för inbäddningar. Modeller laddas efter att en giltig Ollama-URL har angetts.",
      "loaded-models": "Dina inlästa modeller",
    },

    // LiteLLM-inbäddningsalternativ
    litellm: {
      "model-label": "Val av inbäddningsmodell",
      "max-chunk-length": "Maximal segmentlängd",
      "max-chunk-length-help": "Maximal längd på textsegment för inbäddning.",
      "api-key": "API-nyckel",
      optional: "valfritt",
      "api-key-placeholder": "sk-mysecretkey",
      "loading-models": "-- laddar tillgängliga modeller --",
      "waiting-url": "-- väntar på URL --",
      "loaded-models": "Dina inlästa modeller",
      "model-tooltip": "Visa stödda inbäddningsmodeller på",
      "model-tooltip-link": "LiteLLM:s dokumentation",
      "model-tooltip-more": "för mer information om tillgängliga modeller.",
    },

    // Cohere-inbäddningsalternativ
    cohere: {
      "api-key": "Cohere API-nyckel",
      "api-key-placeholder": "Ange din Cohere API-nyckel",
      "model-label": "Val av modell",
      "available-models": "Tillgängliga inbäddningsmodeller",
    },

    // Jina-inbäddningsalternativ
    jina: {
      "api-key": "Jina API-nyckel",
      "api-key-format": "Jina API-nyckel måste börja med 'jina_'",
      "api-key-placeholder": "Ange din Jina API-nyckel",
      "api-key-error": "API-nyckeln måste börja med 'jina_'",
      "model-label": "Val av modell",
      "available-models": "Tillgängliga inbäddningsmodeller",
      "embedding-type": "Inbäddningstyp",
      "available-types": "Tillgängliga inbäddningstyper",
      dimensions: "Dimensioner",
      "available-dimensions": "Tillgängliga dimensioner",
      task: "Uppgift",
      "available-tasks": "Tillgängliga uppgifter",
      "late-chunking": "Sen segmentering",
      "late-chunking-help": "Aktivera sen segmentering för dokumentbehandling",
    },

    // LocalAI-inbäddningsalternativ
    localai: {
      "model-label": "Namn på inbäddningsmodell",
      "hide-endpoint": "Dölj avancerade inställningar",
      "show-endpoint": "Visa avancerade inställningar",
      "base-url": "Local AI Bas-URL",
      "base-url-placeholder": "http://localhost:8080/v1",
      "base-url-help": "Ange URL:en där LocalAI körs.",
      "auto-detect": "Upptäck automatiskt",
      "loading-models": "-- laddar tillgängliga modeller --",
      "waiting-url": "-- väntar på URL --",
      "loaded-models": "Dina inlästa modeller",
    },

    // Generiska OpenAI-kompatibla inbäddningsalternativ
    generic: {
      "base-url": "Bas-URL",
      "base-url-placeholder": "https://api.openai.com/v1",
      "base-url-help": "Ange bas-URL för din OpenAI-kompatibla API-slutpunkt.",
      "model-label": "Inbäddningsmodell",
      "model-placeholder": "Ange modellnamn (t.ex. text-embedding-ada-002)",
      "model-help": "Ange modellidentifieraren för att generera inbäddningar.",
      "api-key": "API-nyckel",
      "api-key-placeholder": "sk-mysecretkey",
      "api-key-help": "Ange din API-nyckel för autentisering.",
    },

    // OpenAI-inbäddningsalternativ
    openai: {
      "api-key": "OpenAI API-nyckel",
      "api-key-placeholder": "Ange din OpenAI API-nyckel",
      "model-label": "Val av modell",
      "available-models": "Tillgängliga inbäddningsmodeller",
    },

    // VoyageAI-inbäddningsalternativ
    voyageai: {
      "api-key": "VoyageAI API-nyckel",
      "api-key-placeholder": "Ange din VoyageAI API-nyckel",
      "model-label": "Val av modell",
      "available-models": "Tillgängliga inbäddningsmodeller",
    },

    // Azure OpenAI-inbäddningsalternativ
    azureai: {
      "service-endpoint": "Azure OpenAI-tjänstens slutpunkt",
      "service-endpoint-placeholder": "https://my-azure.openai.azure.com",
      "service-endpoint-help": "Ange URL för din Azure OpenAI-tjänst",
      "api-key": "Azure OpenAI API-nyckel",
      "api-key-placeholder": "Ange din Azure OpenAI API-nyckel",
      "api-key-help": "Ange din Azure OpenAI API-nyckel för autentisering",
      "deployment-name": "Distribueringsnamn för inbäddningsmodell",
      "deployment-name-placeholder":
        "Ange ditt distribueringsnamn för Azure OpenAI-inbäddningsmodell",
      "deployment-name-help":
        "Distribueringsnamnet för din Azure OpenAI-inbäddningsmodell",
    },

    // Inbyggd (native) inbäddningsleverantör
    native: {
      description: "Använder inbyggd inbäddningsleverantör för textbehandling",
    },
  },

  // =========================
  // BROWSER EXTENSION API KEYS
  // =========================
  "browser-extension-api": {
    title: "API-nycklar",
    description: "Hantera API-nycklar för anslutning till denna instans.",
    "generate-key": "Generera ny API-nyckel",
    "table-headers": {
      "connection-string": "Anslutningssträng",
      "created-by": "Skapad av",
      "created-at": "Skapad",
      actions: "Åtgärder",
    },
    "no-keys": "Inga API-nycklar hittades",
    modal: {
      title: "Ny webbläsartillägg API-nyckel",
      "multi-user-warning":
        "Varning: Du är i fleranvändarläge. Denna API-nyckel kommer att ge tillgång till alla arbetsytor kopplade till ditt konto. Var försiktig med delning.",
      "create-description":
        'Efter att du klickat på "Skapa API-nyckel" kommer denna instans att försöka skapa en ny API-nyckel för webbläsartillägget.',
      "connection-help":
        'Om du ser "Ansluten till IST Legal" i tillägget var anslutningen lyckad. Om inte, vänligen kopiera anslutningssträngen och klistra in den manuellt i tillägget.',
      cancel: "Avbryt",
      "create-key": "Skapa API-nyckel",
      "copy-key": "Kopiera API-nyckel",
      "key-copied": "API-nyckel kopierad!",
    },
    tooltips: {
      "copy-connection": "Kopiera anslutningssträng",
      "auto-connect": "Anslut automatiskt till tillägget",
    },
    confirm: {
      revoke:
        "Är du säker på att du vill återkalla denna webbläsartillägg API-nyckel?\nEfter detta kommer den inte längre att vara användbar.\n\nDenna åtgärd kan inte ångras.",
    },
    toasts: {
      "key-revoked": "Webbläsartillägg API-nyckel har permanent återkallats",
      "revoke-failed": "Kunde inte återkalla API-nyckeln",
      copied: "Anslutningssträng kopierad till urklipp",
      connecting: "Försöker ansluta till webbläsartillägget...",
    },
    "revoke-title": "Återkalla webbläsartillägg API-nyckel",
    "revoke-message":
      "Är du säker på att du vill återkalla denna webbläsartillägg API-nyckel?\nEfter detta kommer den inte längre att vara användbar.\n\nDenna åtgärd kan inte ångras.",
  },

  // =========================
  // EXPERIMENTAL FEATURES
  // =========================
  experimental: {
    title: "Experimentella Funktioner",
    description: "Funktioner som för närvarande är i betatestningsfas",
    "live-sync": {
      title: "Live-dokumentsynkronisering",
      description:
        "Aktivera automatisk innehållssynkronisering från externa källor",
      "manage-title": "Övervakade dokument",
      "manage-description":
        "Detta är alla dokument som för närvarande övervakas i din instans. Innehållet i dessa dokument kommer att synkroniseras regelbundet.",
      "document-name": "Dokumentnamn",
      "last-synced": "Senast synkroniserad",
      "next-refresh": "Tid till nästa uppdatering",
      "created-on": "Skapad den",
      "auto-sync": "Automatisk innehållssynkronisering",
      "sync-description":
        'Aktivera möjligheten att specificera en innehållskälla som ska "övervakas". Övervakat innehåll kommer regelbundet att hämtas och uppdateras i denna instans.',
      "sync-workspace-note":
        "Övervakat innehåll kommer automatiskt att uppdateras i alla arbetsytor där de refereras.",
      "sync-limitation":
        "Denna funktion gäller endast webbaserat innehåll, såsom webbplatser, Confluence, YouTube och GitHub-filer.",
      documentation: "Funktionsdokumentation och varningar",
      "manage-content": "Hantera övervakat innehåll",
    },
    tos: {
      title: "Användarvillkor för experimentella funktioner",
      description:
        "Experimentella funktioner på denna plattform är funktioner som vi testar och som är valfria. Vi kommer proaktivt att informera om eventuella problem som kan uppstå innan godkännande av någon funktion.",
      "possibilities-title":
        "Användning av en funktion på denna sida kan resultera i, men är inte begränsat till, följande möjligheter:",
      possibilities: {
        "data-loss": "Förlust av data.",
        "quality-change": "Förändring i resultatens kvalitet.",
        "storage-increase": "Ökat lagringsutrymme.",
        "resource-consumption": "Ökad resursförbrukning.",
        "cost-increase":
          "Ökade kostnader eller användning av anslutna LLM- eller inbäddningsleverantörer.",
        "potential-bugs":
          "Potentiella buggar eller problem vid användning av denna applikation.",
      },
      "conditions-title":
        "Användning av en experimentell funktion kommer med följande icke-uttömmande lista över villkor:",
      conditions: {
        "future-updates":
          "Funktionen kanske inte finns i framtida uppdateringar.",
        stability: "Funktionen som används är för närvarande inte stabil.",
        availability:
          "Funktionen kanske inte är tillgänglig i framtida versioner, konfigurationer eller prenumerationer av denna instans.",
        privacy:
          "Dina sekretessinställningar kommer att respekteras vid användning av en betafunktion.",
        changes: "Dessa villkor kan ändras i framtida uppdateringar.",
      },
      "read-more": "Om du vill läsa mer kan du hänvisa till",
      contact: "eller kontakta",
      reject: "Avvisa & Stäng",
      accept: "Jag förstår",
    },
  },

  // =========================
  // AGENT MENU
  // =========================
  "agent-menu": {
    ability: {
      "chart-generation": "Diagramgenerering",
      "list-documents": "Lista dokument",
      "rag-search": "RAG-sökning",
      "save-file-to-browser": "Spara fil till webbläsare",
      "summarize-document": "Sammanfatta dokument",
      "web-browsing": "Webbsurfning",
      "web-scraping": "Webbskrapning",
    },
    "default-agent": "Standardagent",
  },

  // =========================
  // JINA
  // =========================
  jina: {
    "api-key": "Jina API-nyckel",
    "api-key-placeholder": "Ange din Jina API-nyckel",
    "model-preference": "Modellpreferens",
  },

  // =========================
  // LOADING MODELS
  // =========================
  "loading-models": "Laddar modeller...",

  // =========================
  // METRICS VISIBILITY
  // =========================
  metrics: {
    visibility: {
      hover: "Mätvärden är synliga.",
      available: "Mätvärden är tillgängliga.",
    },
  },

  // =========================
  // MODEL SELECTION
  // =========================
  "model-selection": "Modellval",

  // =========================
  // OLLAMA
  // =========================
  ollama: {
    "max-embedding-chunk-length": "Maximal inbäddningschunklängd",
  },

  // =========================
  // PROMPT
  // =========================
  prompt: {
    error: {
      empty: "Prompt kan inte vara tom",
      upgrade: "Fel vid uppgradering av prompt",
    },
    decline: "Avböj",
  },

  // =========================
  // SAFETY SETTING
  // =========================
  "safety-setting": "Säkerhetsinställning",

  // =========================
  // STABLE
  // =========================
  stable: "Stabil",

  // =========================
  // TOAST
  // =========================
  toast: {
    success: "Framgång",
    error: "Fel",
    warning: "Varning",
    info: "Information",
    settings: {
      "preferences-failed":
        "Det gick inte att spara inställningarna: {{error}}",
      "multi-user-failed":
        "Det gick inte att aktivera flerlägesläge: {{error}}",
      "public-user-failed":
        "Det gick inte att aktivera offentligt läge: {{error}}",
      "password-failed": "Det gick inte att uppdatera lösenordet: {{error}}",
      "vector-db-failed":
        "Det gick inte att spara vektordatabasinställningarna: {{error}}",
      "llm-failed": "Det gick inte att spara LLM-inställningarna: {{error}}",
      "llm-save-failed":
        "Det gick inte att spara {{name}} inställningar: {{error}}",
      "preferences-save-failed":
        "Det gick inte att spara inställningarna: {{error}}",
      "transcription-save-failed":
        "Det gick inte att spara transkriptionsinställningarna: {{error}}",
      "embedding-save-failed":
        "Det gick inte att spara inbäddningsinställningarna: {{error}}",
      "welcome-messages-failed":
        "Det gick inte att uppdatera välkomstmeddelandena: {{error}}",
      "welcome-messages-fetch-failed":
        "Det gick inte att hämta välkomstmeddelanden",
      "welcome-messages-empty":
        "Vänligen ange antingen en rubrik eller ett textmeddelande",
      "welcome-messages-success": "Välkomstmeddelanden sparades framgångsrikt",
      "user-update-failed": "Det gick inte att uppdatera användaren: {{error}}",
      "logs-clear-failed": "Det gick inte att rensa loggarna: {{error}}",
      "generic-error": "Fel: {{message}}",
      "prompt-examples-failed":
        "Det gick inte att spara promptexemplen: {{error}}",
      "prompt-examples-success": "Promptexemplen sparades",
      "prompt-examples-validation":
        "Exempel {{number}} saknar obligatoriska fält: {{fields}}",
      "import-success": "Tråden har importerats: {{threadName}}",
    },
    experimental: {
      "feature-enabled": "Experimentella funktioner aktiverade",
      "feature-disabled": "Experimentella funktioner inaktiverade",
      "update-failed":
        "Det gick inte att uppdatera status för experimentell funktion",
      "features-enabled":
        "Experimentella funktioner aktiverade. Sidan laddas om.",
      "live-sync": {
        enabled: "Live-dokumentsynkronisering aktiverad",
        disabled: "Live-dokumentsynkronisering inaktiverad",
      },
    },
    document: {
      "move-success": "Flyttade {{count}} dokument framgångsrikt",
      "pdr-failed": "Misslyckades med PDR-dokumentet: {{message}}",
      "watch-failed": "Misslyckades med att bevaka dokumentet: {{message}}",
      "pdr-added": "Dokument tillagt i Parent Document Retrieval",
      "pdr-removed": "Dokument borttaget från Parent Document Retrieval",
      "pin-success": "Dokument {{action}} arbetsyta",
    },
  },

  // =========================
  // VOYAGEAI
  // =========================
  voyageai: {
    "api-key": "VoyageAI API-nyckel",
    "api-key-placeholder": "Ange din VoyageAI API-nyckel",
    "model-preference": "Modellpreferens",
  },

  promptLogging: {
    title: "Loggning av promptutdata",
    description:
      "Aktivera eller inaktivera loggning av promptutdata för systemövervakning.",
    label: "Loggning av promptutdata: ",
    state: {
      enabled: "Aktiverad",
      disabled: "Inaktiverad",
    },
  },

  userAccess: {
    title: "Tillåt användaråtkomst",
    description:
      "Aktivera för att låta vanliga användare få tillgång till juridiska uppgifter. Som standard har endast superusers, chefer och administratörer tillgång.",
    label: "Användaråtkomst: ",
    state: {
      enabled: "aktiverad",
      disabled: "inaktiverad",
    },
  },
  "cdb-llm-preference": {
    title: "CDB LLM-preferens",
    settings: "CDB LLM",
    description: "Konfigurera LLM-leverantören för CDB",
  },
  "template-llm-preference": {
    title: "Mall-LLM-preferens",
    settings: "Mall-LLM",
    description:
      "Välj LLM-leverantören som används när dokumentmallar genereras. Standard är systemleverantören.",
    "toast-success": "Mall-LLM-inställningar uppdaterade",
    "toast-fail": "Misslyckades med att uppdatera Mall-LLM-inställningar",
    saving: "Sparar...",
    "save-changes": "Spara ändringar",
  },
  "custom-user-ai": {
    title: "Anpassad användar-AI",
    settings: "Anpassad användar-AI",
    description: "Konfigurera den anpassade användar-AI-leverantören",
    "custom-model-reference": "Anpassat modellnamn & beskrivning",
    "custom-model-reference-description":
      "Lägg till en anpassad referens för denna modell. Detta kommer att vara synligt när du använder valväljaren för Anpassad användar-AI i promptpanelen.",
    "custom-model-reference-name": "Anpassat modellnamn",
    "custom-model-reference-description-label": "Modellbeskrivning (Valfritt)",
    "custom-model-reference-description-placeholder":
      "Ange en valfri beskrivning för denna modell",
    "custom-model-reference-name-placeholder":
      "Ange ett anpassat namn för denna modell",
    "model-ref-placeholder":
      "Ange ett anpassat namn eller beskrivning för denna modellinställning",
    "enter-custom-model-reference": "Ange ett anpassat namn för denna modell",
    "standard-engine": "Standard AI-motor",
    "standard-engine-description":
      "Vår standardmotor som är användbar för de flesta uppgifter",
    "dynamic-context-window-percentage":
      "Procentandel för dynamiskt kontextfönster",
    "dynamic-context-window-percentage-desc":
      "Styr hur mycket av denna LLM:s kontextfönster som kan användas för ytterligare källor (10-100%)",
    "no-alternative-title": "Ingen alternativ modell vald",
    "no-alternative-desc":
      "När detta alternativ är valt har användare inte möjlighet att välja en alternativ modell.",
    "select-option": "Välj anpassad AI-profil",
    tab: {
      "custom-1": "Anpassad motor 1",
      "custom-2": "Anpassad motor 2",
      "custom-3": "Anpassad motor 3",
      "custom-4": "Anpassad motor 4",
      "custom-5": "Anpassad motor 5",
      "custom-6": "Anpassad motor 6",
    },
    engine: {
      "custom-1": "Anpassad motor 1",
      "custom-2": "Anpassad motor 2",
      "custom-3": "Anpassad motor 3",
      "custom-4": "Anpassad motor 4",
      "custom-5": "Anpassad motor 5",
      "custom-6": "Anpassad motor 6",
      "custom-1-title": "Anpassad motor 1",
      "custom-2-title": "Anpassad motor 2",
      "custom-3-title": "Anpassad motor 3",
      "custom-4-title": "Anpassad motor 4",
      "custom-5-title": "Anpassad motor 5",
      "custom-6-title": "Anpassad motor 6",
      "custom-1-description": "Konfigurera inställningar för Anpassad motor 1",
      "custom-2-description": "Konfigurera inställningar för Anpassad motor 2",
      "custom-3-description": "Konfigurera inställningar för Anpassad motor 3",
      "custom-4-description": "Konfigurera inställningar för Anpassad motor 4",
      "custom-5-description": "Konfigurera inställningar för Anpassad motor 5",
      "custom-6-description": "Konfigurera inställningar för Anpassad motor 6",
    },
    "option-number": "Alternativ {{number}}",
    "llm-provider-selection": "LLM-leverantörsval",
    "llm-provider-selection-desc":
      "Välj LLM-leverantören för denna anpassade AI-konfiguration",
    "custom-option": "Anpassat alternativ",
    saving: "Sparar...",
    "save-changes": "Spara ändringar",
    "model-ref-saved": "Anpassade modellinställningar sparade",
    "model-ref-save-failed":
      "Kunde inte spara anpassade modellinställningar: {{error}}",
    "llm-settings-save-failed": "Kunde inte spara LLM-inställningar: {{error}}",
    "settings-fetch-failed": "Kunde inte hämta inställningar",
    "llm-saved": "LLM-inställningar sparade",
    "select-provider-first":
      "Välj en LLM-leverantör för att konfigurera modellinställningar. När det är konfigurerat kommer detta alternativ att kunna väljas som en anpassad AI-motor i användargränssnittet.",
  },

  // =========================
  // CHAT LOGS & PREVIEW
  // =========================
  chat_logs: {
    display_description:
      "Visa loggning av rådata till/från LLM, öppna och ladda ner filen",
    display_prompt_output: "Visa rådata",
    loading_prompt_output: "Laddar rådata...",
    not_available: "*** Rådata är inte tillgängligt för denna chatt.",
    token_count: "Tokens (i all rådata): {{count}}",
    token_count_detailed:
      "Tokens till LLM: {{promptTokens}} | Tokens i svar från LLM: {{completionTokens}} | Totalt antal tokens: {{totalTokens}}",
  },

  // LLM-leverantörer beskrivningar
  "llm-provider.textgenwebui": "Anslut till Text Generation WebUI-instans.",
  "llm-provider.litellm": "Anslut till en LLM via LiteLLM.",
  "llm-provider.openai-generic": "Anslut till en OpenAI-kompatibel API-punkt.",
  "llm-provider.system-default": "Använd den inbyggda Native-modellen.",

  // =========================
  // INVITATION RELATED KEYS
  // =========================
  invite: {
    "accept-button": "Acceptera inbjudan",
    newUser: {
      title: "Skapa ett nytt konto",
      usernameLabel: "Användarnamn",
      passwordLabel: "Lösenord",
      description:
        "Efter att du har skapat ditt konto kommer du att kunna logga in med dessa autentiseringsuppgifter och börja använda arbetsytor.",
    },
  },

  // =========================
  // CONTEXT WINDOW DISPLAY
  // =========================
  context_window: {
    "context-window": "Kontextfönster",
    "max-output-tokens": "Max utdata-tokens",
    "output-limit": "Utdatagräns",
    tokens: "tokens",
    "fallback-value": "Standardvärde används",
  },
  // =========================
  // CUSTOM LEGAL TEMPLATES MODAL
  // =========================

  // moved to customLegalTemplates.js

  // =========================
  // VARIOUS MODALS AND NEW FEATURES
  // =========================

  // Organization & Organizations translations for user management
  organization: {
    label: "Organisation",
    select: "-- Välj organisation --",
    none: "Ingen",
    "create-new": "+ Skapa ny organisation",
    "new-name": "Nytt organisationsnamn",
    "new-name-ph": "Ange nytt organisationsnamn",
  },
  organizations: {
    "fetch-error": "Misslyckades med att hämta organisationer",
  },
  // =========================
  // REQUEST LEGAL ASSISTANCE
  // =========================
  "request-legal-assistance": {
    title: "Begär juridisk hjälp",
    description:
      "Konfigurera synligheten för knappen för att begära juridisk hjälp.",
    enable: "Aktivera begäran om juridisk hjälp",
    "law-firm-name": "Advokatbyråns namn",
    "law-firm-placeholder": "Ange advokatbyråns namn",
    "law-firm-help":
      "Namnet på advokatbyrån som kommer att hantera begäran om juridisk hjälp",
    email: "E-post för juridisk hjälp",
    "email-placeholder": "Ange e-postadress för juridisk hjälp",
    "email-help":
      "E-postadress dit begäran om juridisk hjälp kommer att skickas",
    "settings-saved": "Inställningar för juridisk hjälp sparades framgångsrikt",
    "save-error": "Kunde inte spara inställningar för juridisk hjälp",
    status: "Knapp för juridisk hjälp: ",
    "load-error": "Kunde inte ladda inställningar för juridisk hjälp",
    "save-button": "Spara ändringar",
    request: {
      title: "Begär juridisk hjälp",
      description:
        "Skicka en begäran till {{lawFirmName}} för juridisk hjälp, fördjupad research eller andra konsultationer. Du kommer att bli meddelad via e-post när begäran har behandlats.",
      button: "Begär juridisk hjälp",
      message: "Meddelande",
      "message-placeholder":
        "Ange specifika instruktioner eller information för teamet för juridisk hjälp",
      send: "Skicka begäran",
      cancel: "Avbryt",
      error: "Kunde inte skicka begäran om juridisk hjälp",
      success: "Begäran om juridisk hjälp skickades framgångsrikt",
      submitting: "Skickar begäran...",
      submit: "Skicka begäran",
      partyName: "Namn på part",
      partyOrgId: "Organisationsnummer för part",
      partyNamePlaceholder: "Ange namnet på din organisation",
      partyOrgIdPlaceholder: "Ange ditt organisationsnummer",
      partyNameRequired: "Namn på part krävs",
      partyOrgIdRequired: "Organisationsnummer för part krävs",
      opposingPartyName: "Namn på motpart (om tillämpligt)",
      opposingPartyOrgId: "Organisationsnummer för motpart (om känt)",
      opposingPartyNamePlaceholder: "Ange namnet på motparten",
      opposingPartyOrgIdPlaceholder: "Ange motpartens organisationsnummer",
    },
  },

  // =========================
  // STREAMDD PROGRESS MODAL
  // =========================

  streamdd_progress_modal: {
    title: "Svarsgenereringens förlopp",
    description:
      "Visar framdriften för uppgifter för att slutföra prompten, utifrån länkning till andra arbetsområden och filstorlekar. Fönstret stängs automatiskt när alla steg är klara.",
    step_fetching_memos: "Hämtar juridisk data om aktuella ämnen",
    step_processing_chunks: "Bearbetar uppladdade dokument",
    step_combining_responses: "Slutför svar",
    sub_step_chunk_label: "Bearbetar dokumentgrupp {{index}}",
    sub_step_memo_label: "Hämtade juridisk data från {{workspaceSlug}}",
    placeholder_sub_task: "Köad deluppgift",
    desc_fetching_memos:
      "Hämtar relevant juridisk information från länkade arbetsområden",
    desc_processing_chunks:
      "Analyserar och extraherar information från dokumentgrupper",
    desc_combining_responses:
      "Sammanställer information till ett omfattande svar",
  },

  // =========================
  // MCP SERVER PAGE
  // =========================

  mcp: {
    title: "MCP-serverhantering",
    description:
      "Hantera konfigurationer för Multi-Component Processing (MCP) server.",
    currentServers: "Aktuella servrar",
    noServers: "Inga MCP-servrar konfigurerade.",
    fetchError: "Kunde inte hämta servrar: {{error}}",
    addServerButton: "Lägg till ny server",
    addServerModalTitle: "Lägg till ny MCP-server",
    addServerModalDesc:
      "Definiera konfigurationen för den nya MCP-serverprocessen.",
    serverName: "Servernamn (unikt ID)",
    configJson: "Konfiguration (JSON)",
    addButton: "Lägg till server",
    addSuccess: "Server har lagts till.",
    addError: "Kunde inte lägga till server: {{error}}",
  },

  // =========================
  // ADMIN SYSTEM SETTINGS (UNIVERSITY MODE)
  // =========================
  admin: {
    system: {
      universityMode: {
        title: "Universitetsläge",
        description:
          "När aktiverat, döljer validering, promptuppdatering, mallar och webbsökning för alla användare.",
        enable: "Aktivera universitetsläge",
        saved: "Universitetsläge aktiverat.",
        error: "Kunde inte aktivera universitetsläge.",
        saveChanges: "Aktivera universitetsläge",
      },
    },
  },

  // Months
  "month.1": "jan",
  "month.2": "feb",
  "month.3": "mar",
  "month.4": "apr",
  "month.5": "maj",
  "month.6": "jun",
  "month.7": "jul",
  "month.8": "aug",
  "month.9": "sep",
  "month.10": "okt",
  "month.11": "nov",
  "month.12": "dec",

  // =========================
  // FEATURE CARDS
  // =========================
  featureCards: {
    "draft-from-template-title": "Skapa dokumentutkast från mall",
    "draft-from-template-description":
      "Använd funktionen för att till exempel ta fram en AML-policy, ett protokoll för bolagsstämma, eller ett standardiserat skiljeavtal.",
    "complex-document-builder-title": "Utför komplex juridisk uppgift",
    "complex-document-builder-description":
      "Perfekt när du exempelvis behöver granska hundratals dokument inför en företagsöverlåtelse eller utforma en detaljerad stämningsansökan.",
  },

  // =========================
  // WORKSPACE SELECTOR MODAL
  // =========================
  workspaceSelector: {
    chooseWorkspace: "Starta en ny chatt",
    selectAiType: "Val av modul och arbetsyta för att initiera funktionen",
    cloudAiDescription:
      "Använder en molnbaserad AI-modell för chatt och frågesvar. Dina dokument kommer att bearbetas och lagras säkert i molnet.",
    localAiDescription:
      "Använder en lokal AI-modell för chatt och dokumentredigering. Dina dokument kommer att bearbetas och lagras på din lokala maskin.",
    cloudAiDescriptionTemplateFeature:
      "Skapa mall i befintlig arbetsyta med juridisk data, mallen kan hämta juridisk data beroende på frågan.",
    localAiDescriptionTemplateFeature:
      "Skapa mall i egen arbetsyta, med helt lokal AI om det är aktiverat på servern.",
    cloudAiDescriptionComplexFeature:
      "Redigering av komplexa dokument är inte tillgängligt för dessa arbetsytor, eftersom användaren måste ladda upp dokument till arbetsytan innan initiering",
    localAiDescriptionComplexFeature:
      "Välj en av dina arbetsytor för att initiera en juridisk uppgift, och se till att nödvändiga dokument är uppladdade i arbetsytan innan initiering.",
    newWorkspaceComplexTaskInfo:
      "Om du skapar en ny arbetsyta kommer du att gå till uppladdningsvyn för att ladda upp alla nödvändiga dokument, vilket är nödvändigt för att utföra generering av juridiska uppgiftsdokument.",
    selectExistingWorkspace: "Välj en befintlig arbetsyta",
    selectExistingDocumentDraftingWorkspace:
      "Välj en befintlig arbetsyta för dokumentredigering",
    orCreateNewBelow:
      "Eller skapa en ny arbetsyta för dokumentredigering nedan.",
    newWorkspaceName: "Ange ett namn för din nya arbetsyta",
    newWorkspaceNameOptional:
      "Ange ett namn för din nya arbetsyta (om du inte använder en befintlig arbetsyta)",
    workspaceNamePlaceholder: "t.ex. Min nya arbetsyta",
    next: "Nästa",
    pleaseSelectWorkspace: "Vänligen välj en arbetsyta.",
    workspaceNameRequired: "Namn på arbetsyta krävs.",
    workspaceNameOrExistingWorkspaceRequired:
      "Vänligen ange ett namn på arbetsytan eller välj en befintlig arbetsyta.",
    workspaceNameMustBeMoreThanOneCharacter:
      "Namnet på arbetsytan måste vara längre än ett tecken.",
    noWorkspacesAvailable: "Inga arbetsområden tillgängliga",
    selectWorkspacePlaceholder: "Vänligen välj",
    featureUnavailable: {
      title: "Funktionen är inte tillgänglig",
      description:
        "Funktionen är inte aktiverad för ditt konto eller avstängd i detta system. Kontakta en administratör för att aktivera denna funktion om det behövs.",
      close: "Stäng",
    },
    createNewWorkspace: {
      title: "Skapa ny arbetsyta för dokumentredigering",
      description:
        "Detta skapar en ny arbetsyta specifikt för komplex dokumentredigering med den valda mallen.",
      workspaceName: "Namn på arbetsyta",
      create: "Skapa arbetsyta",
    },
    selectExisting: {
      title: "Välj arbetsyta för juridiska frågor",
      description:
        "Välj en befintlig arbetsyta för att starta en juridisk frågor och svar-session.",
      selectWorkspace: "Välj arbetsyta",
    },
  },
};

export default TRANSLATIONS;
