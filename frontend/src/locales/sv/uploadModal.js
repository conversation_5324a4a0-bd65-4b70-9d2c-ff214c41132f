export default {
  // =========================
  // DOCUMENTS & PINNING
  // =========================
  documents: {
    "pin-info-button": "Om fästning",
    "pin-title": "Vad är att fästa (pin) ett dokument?",
    "pin-desc-1":
      "När du fäster ett dokument kommer plattformen att injicera hela dokumentets innehåll i din prompt, så att LLM fullt ut förstår det.",
    "pin-desc-2":
      "Detta fungerar bäst med modeller som har stort kontextfönster eller mindre filer som är kritiska för dess kunskapsbas.",
    "pin-desc-3":
      "Om du inte får de svar du önskar från standardinställningen kan fästning av dokument vara ett effektivt sätt att förbättra svarskvaliteten.",
    "pin-add": "Fäst till arbetsyta",
    "pin-unpin": "Ta bort från arbetsyta",
    "watch-title": "Vad innebär att bevaka (watch) ett dokument?",
    "watch-desc-1":
      "När du bevakar ett dokument kommer plattformen automatiskt att synka dokumentets innehåll från dess ursprungskälla med jämna mellanrum. Detta uppdaterar automatiskt innehållet i varje arbetsyta där filen används.",
    "watch-desc-2":
      "Den här funktionen stöder för närvarande endast online-baserat innehåll och är inte tillgänglig för dokument som laddas upp manuellt.",
    "watch-desc-3": "Du kan hantera vilka dokument som bevakas från",
    "file-manager": "fildelaren",
    "admin-view": "adminvyn",
    "pdr-add": "Alla dokument har lagts till Parent Document Retrieval",
    "pdr-remove":
      "Alla dokument har tagits bort från Parent Document Retrieval",
    empty: "Inga dokument hittades",
    tooltip: {
      date: "Datum: ",
      type: "Typ: ",
      cached: "Cachad",
    },
    actions: {
      removing: "Tar bort fil från arbetsytan",
    },
    costs: {
      estimate: "Uppskattad kostnad: $",
      minimum: "< $0.01",
    },
    "new-folder": {
      title: "Skapa ny mapp",
      "name-label": "Mappnamn",
      "name-placeholder": "Ange mappnamn",
      create: "Skapa mapp",
    },
    error: {
      "create-folder": "Misslyckades med att skapa mapp",
    },
  },
};
