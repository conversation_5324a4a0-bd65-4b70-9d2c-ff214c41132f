import { buildCDBPromptTemplate } from "../cdbPromptBuilder";
import System from "@/models/system";

// Mock the System model, specifically getDocumentBuilder and getCDBDocumentation
jest.mock("@/models/system", () => ({
  __esModule: true,
  default: {
    getDocumentBuilder: jest.fn(),
    getCDBDocumentation: jest.fn(),
  },
}));

// Mock resolved prompt values that System.getDocumentBuilder would return in the flat settings object
const mockResolvedPromptValues = {
  summarySystemPrompt: "Resolved Summary System",
  defaultSummarySystemPrompt: "Default Summary System Fallback",
  summaryUserPrompt: "Resolved Summary User",
  defaultSummaryUserPrompt: "Default Summary User Fallback",
  relevanceSystemPrompt: "Resolved Relevance System",
  defaultRelevanceSystemPrompt: "Default Relevance System Fallback",
  relevanceUserPrompt: "Resolved Relevance User",
  defaultRelevanceUserPrompt: "Default Relevance User Fallback",
  selectMainDocumentSystemPrompt: "Resolved Select Main System",
  defaultSelectMainDocumentSystemPrompt: "Default Select Main System Fallback",
  selectMainDocumentUserPrompt: "Resolved Select Main User",
  defaultSelectMainDocumentUserPrompt: "Default Select Main User Fallback",
  // These values are already flow-specific thanks to backend logic in /get-document-builder
  topicsSectionsSystemPrompt: "RESOLVED TOPICS SYSTEM - MAINDOC VARIANT",
  defaultTopicsSectionsSystemPrompt: "Default Topics System Fallback - MainDoc",
  topicsSectionsUserPrompt: "Resolved Topics User - MainDoc Variant",
  defaultTopicsSectionsUserPrompt: "Default Topics User Fallback - MainDoc",
  sectionSystemPrompt: "Resolved Section System",
  defaultSectionSystemPrompt: "Default Section System Fallback",
  sectionUserPrompt: "Resolved Section User",
  defaultSectionUserPrompt: "Default Section User Fallback",
  // Add mock values for sectionLegalIssues prompts if not already present
  sectionLegalIssuesSystemPrompt: "Resolved Section Legal Issues System",
  defaultSectionLegalIssuesSystemPrompt:
    "Default Section Legal Issues System Fallback",
  sectionLegalIssuesUserPrompt: "Resolved Section Legal Issues User",
  defaultSectionLegalIssuesUserPrompt:
    "Default Section Legal Issues User Fallback",
};

const mockMainDocFlowStructure = [
  { key: "summarySystemPrompt", label: "DOCUMENT SUMMARY SYSTEM PROMPT" },
  { key: "summaryUserPrompt", label: "DOCUMENT SUMMARY USER PROMPT" },
  { key: "relevanceSystemPrompt", label: "DOCUMENT RELEVANCE SYSTEM PROMPT" },
  { key: "relevanceUserPrompt", label: "DOCUMENT RELEVANCE USER PROMPT" },
  {
    key: "selectMainDocumentSystemPrompt",
    label: "SELECT MAIN DOCUMENT SYSTEM PROMPT",
  },
  {
    key: "selectMainDocumentUserPrompt",
    label: "SELECT MAIN DOCUMENT USER PROMPT",
  },
  {
    key: "topicsSectionsSystemPrompt",
    label: "TOPICS & SECTIONS (FROM MAIN DOCUMENT) SYSTEM PROMPT",
  },
  {
    key: "topicsSectionsUserPrompt",
    label: "TOPICS & SECTIONS (FROM MAIN DOCUMENT) USER PROMPT",
  },
  { key: "sectionSystemPrompt", label: "SECTION DRAFTING SYSTEM PROMPT" },
  { key: "sectionUserPrompt", label: "SECTION DRAFTING USER PROMPT" },
  {
    key: "sectionLegalIssuesSystemPrompt",
    label: "SECTION LEGAL ISSUES IDENTIFICATION SYSTEM PROMPT",
  },
  {
    key: "sectionLegalIssuesUserPrompt",
    label: "SECTION LEGAL ISSUES IDENTIFICATION USER PROMPT",
  },
];

const mockNoMainDocFlowStructure = [
  { key: "summarySystemPrompt", label: "DOCUMENT SUMMARY SYSTEM PROMPT" },
  { key: "summaryUserPrompt", label: "DOCUMENT SUMMARY USER PROMPT" },
  { key: "relevanceSystemPrompt", label: "DOCUMENT RELEVANCE SYSTEM PROMPT" },
  { key: "relevanceUserPrompt", label: "DOCUMENT RELEVANCE USER PROMPT" },
  // No selectMainDocument prompts
  {
    key: "topicsSectionsSystemPrompt",
    label: "TOPICS & SECTIONS (FROM SUMMARIES) SYSTEM PROMPT",
  },
  {
    key: "topicsSectionsUserPrompt",
    label: "TOPICS & SECTIONS (FROM SUMMARIES) USER PROMPT",
  },
  { key: "sectionSystemPrompt", label: "SECTION DRAFTING SYSTEM PROMPT" },
  { key: "sectionUserPrompt", label: "SECTION DRAFTING USER PROMPT" },
  {
    key: "sectionLegalIssuesSystemPrompt",
    label: "SECTION LEGAL ISSUES IDENTIFICATION SYSTEM PROMPT",
  },
  {
    key: "sectionLegalIssuesUserPrompt",
    label: "SECTION LEGAL ISSUES IDENTIFICATION USER PROMPT",
  },
];

const MOCK_FETCHED_CDB_DOCS =
  "### Fetched CDB Documentation!\nThis is the official documentation fetched from the server.";

describe("buildCDBPromptTemplate", () => {
  beforeEach(() => {
    // Clear all instances and calls to constructor and all methods:
    System.getDocumentBuilder.mockClear();
    System.getCDBDocumentation.mockClear();
    // Default mock for CDB documentation for most tests
    System.getCDBDocumentation.mockResolvedValue(MOCK_FETCHED_CDB_DOCS);
  });

  it("should use mainDoc flow structure and prompts when requiresMainDocumentFlag is true", async () => {
    System.getDocumentBuilder.mockResolvedValue({
      ...mockResolvedPromptValues, // All resolved values
      // Simulate backend providing mainDoc-specific values for topics/sections
      topicsSectionsSystemPrompt: "RESOLVED TOPICS SYSTEM - MAINDOC VARIANT",
      topicsSectionsUserPrompt: "Resolved Topics User - MainDoc Variant",
      promptFlowStructure: mockMainDocFlowStructure,
    });

    const template = await buildCDBPromptTemplate({
      taskDescription: "Test task for mainDoc",
      requiresMainDocumentFlag: true,
    });

    expect(System.getDocumentBuilder).toHaveBeenCalledTimes(1);
    expect(System.getDocumentBuilder).toHaveBeenCalledWith({
      flowType: "mainDoc",
    });

    // Check for explicit flow statement
    expect(template).toContain(
      "This legal task will utilize the **Main Document Flow** of the Case Document Builder."
    );

    // Check if prompts from the mainDoc structure are present with their resolved values
    expect(template).toContain(
      '1. DOCUMENT SUMMARY SYSTEM PROMPT:\n"Resolved Summary System"'
    );
    expect(template).toContain(
      '5. SELECT MAIN DOCUMENT SYSTEM PROMPT:\n"Resolved Select Main System"'
    );
    expect(template).toContain(
      '7. TOPICS & SECTIONS (FROM MAIN DOCUMENT) SYSTEM PROMPT:\n"RESOLVED TOPICS SYSTEM - MAINDOC VARIANT"'
    );
    expect(template).toContain(
      '9. SECTION DRAFTING SYSTEM PROMPT:\n"Resolved Section System"'
    );
    expect(template).toContain(
      '11. SECTION LEGAL ISSUES IDENTIFICATION SYSTEM PROMPT:\n"Resolved Section Legal Issues System"'
    );
    // Check numbering based on structure length
    expect(template).toContain(
      `${mockMainDocFlowStructure.length}. SECTION LEGAL ISSUES IDENTIFICATION USER PROMPT:`
    );
    expect(template).toContain(MOCK_FETCHED_CDB_DOCS);
  });

  it("should use noMainDoc flow structure and prompts when requiresMainDocumentFlag is false", async () => {
    System.getDocumentBuilder.mockResolvedValue({
      ...mockResolvedPromptValues,
      // Simulate backend providing noMainDoc-specific values for topics/sections
      topicsSectionsSystemPrompt: "RESOLVED TOPICS SYSTEM - NOMAIN DOC VARIANT",
      topicsSectionsUserPrompt: "Resolved Topics User - NoMainDoc Variant",
      promptFlowStructure: mockNoMainDocFlowStructure,
    });

    const template = await buildCDBPromptTemplate({
      taskDescription: "Test task for noMainDoc",
      requiresMainDocumentFlag: false,
    });

    expect(System.getDocumentBuilder).toHaveBeenCalledTimes(1);
    expect(System.getDocumentBuilder).toHaveBeenCalledWith({
      flowType: "noMainDoc",
    });

    expect(template).toContain(
      "This legal task will utilize the **No Main Document Flow** of the Case Document Builder."
    );
    expect(template).not.toContain("SELECT MAIN DOCUMENT SYSTEM PROMPT");
    expect(template).toContain(
      '1. DOCUMENT SUMMARY SYSTEM PROMPT:\n"Resolved Summary System"'
    );
    expect(template).toContain(
      '5. TOPICS & SECTIONS (FROM SUMMARIES) SYSTEM PROMPT:\n"RESOLVED TOPICS SYSTEM - NOMAIN DOC VARIANT"'
    );
    // Adjust index based on mockNoMainDocFlowStructure (sectionSystemPrompt is 7th item)
    expect(template).toContain("7. SECTION DRAFTING SYSTEM PROMPT:");
    expect(template).toContain(
      '9. SECTION LEGAL ISSUES IDENTIFICATION SYSTEM PROMPT:\n"Resolved Section Legal Issues System"'
    );
    // Verify the number of prompts listed matches the noMainDoc structure length
    const templatesSectionRegex =
      /### CURRENTLY ACTIVE PROMPT TEMPLATES FOR THIS FLOW \(Reflects custom settings if applied, otherwise defaults\) ###([\s\S]*?)### STREAMCDB IMPLEMENTATION ###/;
    const templatesSectionMatch = template.match(templatesSectionRegex);
    const templatesBlock = templatesSectionMatch
      ? templatesSectionMatch[1]
      : "";
    const occurrences = (templatesBlock.match(/\n\d+\.\s/g) || []).length;
    expect(occurrences).toBe(mockNoMainDocFlowStructure.length);
    expect(template).toContain(MOCK_FETCHED_CDB_DOCS);
  });

  it("should use default prompt values if primary resolved value is empty, based on promptFlowStructure", async () => {
    const valuesWithEmptyCustom = {
      ...mockResolvedPromptValues,
      summarySystemPrompt: "", // Empty custom for summary
      defaultSummarySystemPrompt: "EXPECTED DEFAULT Summary System",
      // topicsSectionsSystemPrompt is already flow-specific via its key in mockResolvedPromptValues
      // but we can override it here to test the fallback to its *specific* default if its primary value was empty
      topicsSectionsSystemPrompt: "",
      defaultTopicsSectionsSystemPrompt:
        "EXPECTED DEFAULT Topics System - From Structure",
    };
    System.getDocumentBuilder.mockResolvedValue({
      ...valuesWithEmptyCustom,
      promptFlowStructure: mockMainDocFlowStructure, // Using mainDoc structure for this test
    });

    const template = await buildCDBPromptTemplate({
      taskDescription: "Test defaults via structure",
      requiresMainDocumentFlag: true,
    });

    expect(System.getDocumentBuilder).toHaveBeenCalledWith({
      flowType: "mainDoc",
    });
    // Check that the label from promptFlowStructure is used, and value comes from the default
    expect(template).toContain(
      '1. DOCUMENT SUMMARY SYSTEM PROMPT:\n"EXPECTED DEFAULT Summary System"'
    );
    // Check for topics/sections using its specific default when primary is empty
    expect(template).toContain(
      '7. TOPICS & SECTIONS (FROM MAIN DOCUMENT) SYSTEM PROMPT:\n"EXPECTED DEFAULT Topics System - From Structure"'
    );
    expect(template).toContain(MOCK_FETCHED_CDB_DOCS);
  });

  it("should handle missing custom and default prompts gracefully (empty string) via promptFlowStructure", async () => {
    const valuesWithAllMissing = {
      ...mockResolvedPromptValues,
      summarySystemPrompt: "", // Empty custom
      defaultSummarySystemPrompt: "", // Empty default
      topicsSectionsSystemPrompt: null,
      defaultTopicsSectionsSystemPrompt: undefined,
      // Explicitly make sectionLegalIssues prompts empty for this test case
      sectionLegalIssuesSystemPrompt: "",
      defaultSectionLegalIssuesSystemPrompt: "",
      sectionLegalIssuesUserPrompt: null, // Also test user prompt for this category
      defaultSectionLegalIssuesUserPrompt: undefined,
    };
    System.getDocumentBuilder.mockResolvedValue({
      ...valuesWithAllMissing,
      promptFlowStructure: mockMainDocFlowStructure, // Using mainDoc structure
    });

    const template = await buildCDBPromptTemplate({
      taskDescription: "Test missing prompts via structure",
      requiresMainDocumentFlag: true,
    });
    // Check labels from structure, values should be empty string due to builder logic
    expect(template).toContain('1. DOCUMENT SUMMARY SYSTEM PROMPT:\n""');
    expect(template).toContain(
      '7. TOPICS & SECTIONS (FROM MAIN DOCUMENT) SYSTEM PROMPT:\n""'
    );
    expect(template).toContain(
      '11. SECTION LEGAL ISSUES IDENTIFICATION SYSTEM PROMPT:\n""'
    );
    expect(template).toContain(
      '12. SECTION LEGAL ISSUES IDENTIFICATION USER PROMPT:\n""'
    );
    expect(template).toContain(MOCK_FETCHED_CDB_DOCS);
  });

  it("should include taskDescription and description in the output", async () => {
    System.getDocumentBuilder.mockResolvedValue({
      ...mockResolvedPromptValues,
      promptFlowStructure: mockMainDocFlowStructure,
    });
    const taskDesc = "THE ACTUAL TASK DESCRIPTION";
    const additionalDesc = "SOME ADDITIONAL CONTEXTUAL INFO";

    const template = await buildCDBPromptTemplate({
      taskDescription: taskDesc,
      description: additionalDesc,
      requiresMainDocumentFlag: true,
    });

    expect(template).toContain(`Legal Task Description:\n"""${taskDesc}"""`);
    expect(template).toContain(`Additional Context:\n"""${additionalDesc}"""`);
    expect(template).toContain(MOCK_FETCHED_CDB_DOCS);
  });

  it("should handle empty promptFlowStructure from server gracefully", async () => {
    System.getDocumentBuilder.mockResolvedValue({
      ...mockResolvedPromptValues,
      promptFlowStructure: [], // Empty structure
    });

    const template = await buildCDBPromptTemplate({
      taskDescription: "Test empty structure",
      requiresMainDocumentFlag: true,
    });

    expect(template).toContain(
      "### CURRENTLY ACTIVE PROMPT TEMPLATES FOR THIS FLOW (Reflects custom settings if applied, otherwise defaults) ###\n  (No specific prompt templates configured for display for this flow)"
    );
    expect(template).toContain(MOCK_FETCHED_CDB_DOCS);
  });

  it("should always fetch and use CDB documentation from System.getCDBDocumentation", async () => {
    System.getDocumentBuilder.mockResolvedValue({
      ...mockResolvedPromptValues,
      promptFlowStructure: mockMainDocFlowStructure,
    });
    // Mock getCDBDocumentation to return a specific string for this test
    const specificDocs = "Specific documentation for this test case.";
    System.getCDBDocumentation.mockResolvedValueOnce(specificDocs);

    const template = await buildCDBPromptTemplate({
      taskDescription: "Test task",
      requiresMainDocumentFlag: true,
      cdbDocumentation: "This prop should be ignored", // Pass the ignored prop
    });

    expect(System.getCDBDocumentation).toHaveBeenCalledTimes(1);
    expect(template).toContain(specificDocs);
    expect(template).not.toContain("This prop should be ignored");
    // Check it doesn't use the very basic hardcoded fallback from buildCDBPromptTemplate if API failed (though our mock resolves)
    expect(template).not.toContain(
      "The Case Document Builder (CDB) is a specialized feature within the ISTLegal platform"
    );
  });
});
