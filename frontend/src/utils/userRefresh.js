import System from "@/models/system";
import { AUTH_USER } from "@/utils/constants";

/**
 * Refreshes the current user data in localStorage by fetching the latest data from the server
 * @returns {Promise<boolean>} Returns true if refresh was successful, false otherwise
 */
export const refreshCurrentUserData = async () => {
  try {
    const currentUserData = JSON.parse(localStorage.getItem(AUTH_USER) || "{}");

    if (!currentUserData.id) {
      return false;
    }

    const refreshedUser = await System.getCurrentUser();
    if (refreshedUser) {
      localStorage.setItem(AUTH_USER, JSON.stringify(refreshedUser));
      return true;
    }

    return false;
  } catch (error) {
    console.error("Error refreshing user data:", error);
    return false;
  }
};

/**
 * Refreshes user data if the specified userId matches the current user
 * @param {number|string} userId - The ID of the user that was updated
 * @returns {Promise<boolean>} Returns true if refresh was successful, false otherwise
 */
export const refreshUserDataIfCurrent = async (userId) => {
  try {
    const currentUserData = JSON.parse(localStorage.getItem(AUTH_USER) || "{}");

    // Only refresh if the updated user is the current user
    if (
      currentUserData.id === userId ||
      currentUserData.id === Number(userId)
    ) {
      return await refreshCurrentUserData();
    }

    return false;
  } catch (error) {
    console.error("Error checking and refreshing user data:", error);
    return false;
  }
};
