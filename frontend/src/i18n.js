import i18next from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import {
  defaultNS,
  resources,
  loadLanguageAsync,
  initializeLanguageAsync,
} from "./locales/resources";

export const DEFAULT_LANGUAGE = "en";
export const SUPPORTED_LANGUAGES = ["en", "fr", "de", "sv", "rw", "no", "pl"]; // Reflects project structure

const languageDetector = new LanguageDetector(null, {
  order: ["navigator"],
  caches: [],
});

let i18nInstance;

const applyLanguageResources = async (language, bundle) => {
  console.log(`[i18n] Applying language resources for: ${language}`);

  if (language === DEFAULT_LANGUAGE) {
    console.log(
      `[i18n] Default language (${DEFAULT_LANGUAGE}) doesn't need resources applied`
    );
    return true;
  }

  try {
    const mergedBundle = bundle;
    if (mergedBundle && Object.keys(mergedBundle).length > 0) {
      console.log(
        `[i18n] Adding resource bundle for ${language}, keys:`,
        Object.keys(mergedBundle)
      );
      i18next.addResourceBundle(language, defaultNS, mergedBundle);

      // Verify the resource bundle was added correctly
      const hasBundle = i18next.hasResourceBundle(language, defaultNS);
      console.log(`[i18n] Resource bundle added successfully: ${hasBundle}`);

      return true;
    } else {
      console.warn(`[i18n] No bundle or empty bundle for: ${language}`);
      return false;
    }
  } catch (error) {
    console.error(
      `[i18n] Error applying language resources for ${language}:`,
      error
    );
    return false;
  }
};

const initialize = async () => {
  await i18next
    .use(initReactI18next)
    .use(languageDetector)
    .init({
      fallbackLng: "en",
      debug: false,
      defaultNS,
      resources,
      lowerCaseLng: true,
      interpolation: {
        escapeValue: false,
      },
    });

  const targetLanguage = await initializeLanguageAsync();

  i18next.on("languageChanged", async (lng) => {
    if (lng !== DEFAULT_LANGUAGE) {
      await applyLanguageResources(lng, await loadLanguageAsync(lng));
    }

    localStorage.setItem("language", lng);
  });

  if (targetLanguage !== DEFAULT_LANGUAGE) {
    const success = await applyLanguageResources(
      targetLanguage,
      await loadLanguageAsync(targetLanguage)
    );

    if (success) {
      await i18next.changeLanguage(targetLanguage);
    } else {
      localStorage.setItem("language", DEFAULT_LANGUAGE);
      await i18next.changeLanguage(DEFAULT_LANGUAGE);
    }
  }

  i18nInstance = i18next;

  return i18nInstance;
};

let initializationPromise = null;

export const ensureI18nInitialized = () => {
  if (!initializationPromise) {
    initializationPromise = initialize();
  }
  return initializationPromise;
};

export const changeLanguage = async (targetLang) => {
  console.log(`[i18n] Changing language to: ${targetLang}`);
  await ensureI18nInitialized();

  if (!targetLang || !SUPPORTED_LANGUAGES.includes(targetLang)) {
    console.warn(`[i18n] Invalid language: ${targetLang}`);
    return false;
  }

  const hasBundleAlready = i18next.hasResourceBundle(targetLang, defaultNS);
  console.log(
    `[i18n] Has bundle already: ${hasBundleAlready}, current language: ${i18next.language}`
  );

  if (i18next.language === targetLang && hasBundleAlready) {
    console.log(`[i18n] Already using language: ${targetLang}`);
    return true;
  }

  try {
    console.log(`[i18n] Loading language bundle for: ${targetLang}`);
    const bundle = await loadLanguageAsync(targetLang);
    console.log(
      `[i18n] Bundle loaded:`,
      bundle ? `Success (keys: ${Object.keys(bundle).length})` : "Failed"
    );

    if (bundle && Object.keys(bundle).length > 0) {
      console.log(`[i18n] Applying language resources for: ${targetLang}`);
      const appliedSuccessfully = await applyLanguageResources(
        targetLang,
        bundle
      );
      console.log(
        `[i18n] Resources applied successfully: ${appliedSuccessfully}`
      );

      if (appliedSuccessfully) {
        console.log(`[i18n] Changing i18next language to: ${targetLang}`);
        await i18next.changeLanguage(targetLang);
        localStorage.setItem("language", targetLang);
        console.log(`[i18n] Language changed successfully to: ${targetLang}`);
        return true;
      } else {
        console.warn(`[i18n] Failed to apply resources for: ${targetLang}`);
        if (i18next.language !== DEFAULT_LANGUAGE) {
          console.log(
            `[i18n] Falling back to default language: ${DEFAULT_LANGUAGE}`
          );
          await i18next.changeLanguage(DEFAULT_LANGUAGE);
          localStorage.setItem("language", DEFAULT_LANGUAGE);
        }
        return false;
      }
    } else {
      console.warn(`[i18n] No bundle or empty bundle for: ${targetLang}`);
      if (i18next.language !== DEFAULT_LANGUAGE) {
        console.log(
          `[i18n] Falling back to default language: ${DEFAULT_LANGUAGE}`
        );
        await i18next.changeLanguage(DEFAULT_LANGUAGE);
        localStorage.setItem("language", DEFAULT_LANGUAGE);
      }
      return false;
    }
  } catch (error) {
    console.error(`[i18n] Error changing language to ${targetLang}:`, error);
    try {
      console.log(`[i18n] Attempting direct language change to: ${targetLang}`);
      await i18next.changeLanguage(targetLang);
      localStorage.setItem("language", targetLang);
    } catch (changeLangError) {
      console.error(`[i18n] Error in direct language change:`, changeLangError);
      if (i18next.language !== DEFAULT_LANGUAGE) {
        try {
          console.log(
            `[i18n] Falling back to default language: ${DEFAULT_LANGUAGE}`
          );
          await i18next.changeLanguage(DEFAULT_LANGUAGE);
          localStorage.setItem("language", DEFAULT_LANGUAGE);
        } catch (revertError) {
          console.error(
            `[i18n] Error reverting to default language:`,
            revertError
          );
        }
      }
    }
    return false;
  }
};

export default i18next;
