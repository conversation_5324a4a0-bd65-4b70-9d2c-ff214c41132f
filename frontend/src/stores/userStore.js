import { create } from "zustand";
import { persist } from "zustand/middleware";
import { getTextSizeClass } from "@/utils/textSize";

const useUserStore = create(
  persist(
    (set) => ({
      selectedModule: "legal-qa",
      theme: "light",
      textSize: "normal",
      showChatMetrics: false,
      recentUploadsLastSeenTimestamp: null,
      ddShowAllWorkspaces: true,
      selectedFeatureCard: null,

      setSelectedModule: (module) => set({ selectedModule: module }),
      setTheme: (theme) => set({ theme }),
      setTextSize: (size) => set({ textSize: size }),
      toggleShowChatMetrics: () =>
        set((state) => ({ showChatMetrics: !state.showChatMetrics })),
      setRecentUploadsLastSeenTimestamp: (timestamp) =>
        set({ recentUploadsLastSeenTimestamp: timestamp }),
      setDdShowAllWorkspaces: (value) => set({ ddShowAllWorkspaces: value }),
      setSelectedFeatureCard: (featureCard) =>
        set({ selectedFeatureCard: featureCard }),
    }),
    {
      name: "user-settings",
      partialize: (state) => ({
        selectedModule: state.selectedModule,
        theme: state.theme,
        textSize: state.textSize,
        showChatMetrics: state.showChatMetrics,
        recentUploadsLastSeenTimestamp: state.recentUploadsLastSeenTimestamp,
        ddShowAllWorkspaces: state.ddShowAllWorkspaces,
        selectedFeatureCard: state.selectedFeatureCard,
      }),
    }
  )
);

export const useSelectedModule = () =>
  useUserStore((state) => state.selectedModule);
export const useSetSelectedModule = () =>
  useUserStore((state) => state.setSelectedModule);
export const useIsDocumentDrafting = () =>
  useUserStore((state) => state.selectedModule === "document-drafting");
export const useIsLegalQA = () =>
  useUserStore((state) => state.selectedModule === "legal-qa");

export const useTheme = () => useUserStore((state) => state.theme);
export const useSetTheme = () => useUserStore((state) => state.setTheme);

export const useTextSizePreference = () =>
  useUserStore((state) => state.textSize);
export const useTextSize = () =>
  useUserStore((state) => getTextSizeClass(state.textSize));
export const useSetTextSize = () => useUserStore((state) => state.setTextSize);

export const useShowChatMetrics = () =>
  useUserStore((state) => state.showChatMetrics);
export const useToggleShowChatMetrics = () =>
  useUserStore((state) => state.toggleShowChatMetrics);

export const useRecentUploadsLastSeenTimestamp = () =>
  useUserStore((state) => state.recentUploadsLastSeenTimestamp);
export const useSetRecentUploadsLastSeenTimestamp = () =>
  useUserStore((state) => state.setRecentUploadsLastSeenTimestamp);

export const useDdShowAllWorkspaces = () =>
  useUserStore((state) => state.ddShowAllWorkspaces);
export const useSetDdShowAllWorkspaces = () =>
  useUserStore((state) => state.setDdShowAllWorkspaces);

export const useSelectedFeatureCard = () =>
  useUserStore((state) => state.selectedFeatureCard);
export const useSetSelectedFeatureCard = () =>
  useUserStore((state) => state.setSelectedFeatureCard);

export default useUserStore;
