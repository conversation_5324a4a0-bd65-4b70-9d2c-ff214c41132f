import { ArrowSquareOut, Info } from "@phosphor-icons/react";
import { AWS_REGIONS } from "./regions";
import { useTranslation } from "react-i18next";

export default function AwsBedrockLLMOptions({ settings, moduleSuffix = "" }) {
  const { t } = useTranslation();
  return (
    <div className="w-full flex flex-col">
      {!settings?.[`credentialsOnly${moduleSuffix}`] && (
        <div className="flex flex-col md:flex-row md:items-center gap-x-2 text-white mb-4 primary-bg w-fit rounded-lg px-4 py-2">
          <div className="gap-x-2 flex items-center">
            <Info size={40} />
            <p className="text-base">
              {t("bedrock.iam-warning")}
              <br />
              <a
                href="https://docs.istlegal.com/setup/llm-configuration/cloud/aws-bedrock"
                target="_blank"
                rel="noreferrer"
                className="underline flex gap-x-1 items-center"
              >
                {t("bedrock.read-more")}
                <ArrowSquareOut size={14} />
              </a>
            </p>
          </div>
        </div>
      )}

      <div className="w-full flex items-center gap-[36px] my-1.5">
        <div className="flex flex-col w-60">
          <label className="text-foreground text-sm font-semibold block mb-3">
            {t("bedrock.access-id")}
          </label>
          <input
            className="dark-input-mdl text-foreground text-sm rounded-lg outline-none block w-full p-2"
            type="password"
            name={`AwsBedrockLLMAccessKeyId${moduleSuffix}`}
            placeholder={t("bedrock.access-id-placeholder")}
            defaultValue={
              settings?.[`AwsBedrockLLMAccessKeyId${moduleSuffix}`]
                ? "*".repeat(20)
                : ""
            }
            required={true}
            autoComplete="off"
            spellCheck={false}
          />
        </div>
        <div className="flex flex-col w-60">
          <label className="text-foreground text-sm font-semibold block mb-3">
            {t("bedrock.access-key")}
          </label>
          <input
            className="dark-input-mdl text-foreground text-sm rounded-lg outline-none block w-full p-2"
            type="password"
            name={`AwsBedrockLLMAccessKey${moduleSuffix}`}
            placeholder={t("bedrock.access-key-placeholder")}
            defaultValue={
              settings?.[`AwsBedrockLLMAccessKey${moduleSuffix}`]
                ? "*".repeat(20)
                : ""
            }
            required={true}
            autoComplete="off"
            spellCheck={false}
          />
        </div>
        <div className="flex flex-col w-60">
          <label className="text-foreground text-sm font-semibold block mb-3">
            {t("bedrock.region")}
          </label>
          <select
            name={`AwsBedrockLLMRegion${moduleSuffix}`}
            defaultValue={
              settings?.[`AwsBedrockLLMRegion${moduleSuffix}`] || "us-west-2"
            }
            required={true}
            className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2"
          >
            {AWS_REGIONS.map((region) => {
              return (
                <option key={region.code} value={region.code}>
                  {region.name} ({region.code})
                </option>
              );
            })}
          </select>
        </div>
      </div>

      <div className="w-full flex items-center gap-[36px] my-1.5">
        {!settings?.[`credentialsOnly${moduleSuffix}`] && (
          <>
            <div className="flex flex-col w-60">
              <label className="text-foreground text-sm font-semibold block mb-3">
                {t("bedrock.model-id")}
              </label>
              <input
                className="dark-input-mdl text-foreground text-sm rounded-lg outline-none block w-full p-2"
                type="text"
                name={`AwsBedrockLLMModel${moduleSuffix}`}
                placeholder={t("bedrock.model-id-placeholder")}
                defaultValue={settings?.[`AwsBedrockLLMModel${moduleSuffix}`]}
                required={true}
                autoComplete="off"
                spellCheck={false}
              />
            </div>
            <div className="flex flex-col w-60">
              <label className="text-foreground text-sm font-semibold block mb-3">
                {t("bedrock.context-window")}
              </label>
              <input
                className="dark-input-mdl text-foreground text-sm rounded-lg outline-none block w-full p-2"
                type="number"
                name={`AwsBedrockLLMTokenLimit${moduleSuffix}`}
                placeholder={t("bedrock.context-window-placeholder")}
                min={1}
                onScroll={(e) => e.target.blur()}
                defaultValue={
                  settings?.[`AwsBedrockLLMTokenLimit${moduleSuffix}`]
                }
                required={true}
                autoComplete="off"
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
}
