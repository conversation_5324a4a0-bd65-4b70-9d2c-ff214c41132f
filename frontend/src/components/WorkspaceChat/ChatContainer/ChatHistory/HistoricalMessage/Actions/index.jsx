import React, {
  memo,
  useEffect,
  useState,
  useCallback,
  useRef,
  useMemo,
} from "react";
import System from "@/models/system";
import RenderMetrics from "./RenderMetrics";
import useUser from "@/hooks/useUser";
import { Tooltip } from "react-tooltip";
import showToast from "@/utils/toast.js";
import { TbRefresh } from "react-icons/tb";
import Workspace from "@/models/workspace";
import { LuCopyCheck } from "react-icons/lu";
import useCopyText from "@/hooks/useCopyText";
import { GrSearchAdvanced } from "react-icons/gr";
import { useTranslation } from "react-i18next";
import { EditMessageAction } from "./EditMessage";
import { MdOutlineContentCopy, MdOutlineDeleteOutline } from "react-icons/md";
import { BsFiletypeDocx } from "react-icons/bs";
import ValidateResponse from "./ValidateResponse";
import { CircleNotch } from "@phosphor-icons/react";
import { useDocxExport } from "@/utils/docx-exporter";
import CanvasChat from "./CanvasChat";
import Citations from "../../Citation";
import { Button } from "@/components/Button";
import { useConfirmation } from "@/hooks/useConfirmation";
import Modal from "@/components/ui/Modal";
import TTSMessage from "./TTSButton";
import { useUniversityMode } from "@/stores/settingsStore";

const Actions = memo(function Actions({
  chat,
  message,
  chatId,
  slug,
  role,
  isEditing,
  isLastMessage,
  regenerateMessage,
  question = "",
  answer = "",
  sendCommand,
  isCanvasEnabled = true,
  metrics = {},
  workspace,
  threadSlug,
  updateHistory,
  history,
  sources = [],
}) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [quraEnabled, setQuraEnabled] = useState(false);
  const [validationDisabled, setValidationDisabled] = useState(true); // Default to disabled
  const copyOptionRef = useRef(null);
  const {
    isConfirmationOpen,
    openConfirmation,
    closeConfirmation,
    handleConfirm,
  } = useConfirmation();
  const storeUniversityMode = useUniversityMode();

  // Memoize storage functions
  const storage = useMemo(
    () => ({
      get: (key) => {
        try {
          const value =
            localStorage.getItem(key) || sessionStorage.getItem(key);
          return value ? JSON.parse(value) : null;
        } catch {
          return null;
        }
      },
      set: (key, value) => {
        try {
          const stringValue = JSON.stringify(value);
          try {
            localStorage.setItem(key, stringValue);
          } catch {
            try {
              sessionStorage.setItem(key, stringValue);
            } catch {
              // Silently fail if neither storage is available
            }
          }
        } catch (e) {
          console.error("Error writing to storage:", e);
        }
      },
    }),
    []
  );

  useEffect(() => {
    let mounted = true;

    const fetchSettings = async () => {
      if (!mounted) return;

      try {
        const storedQura = storage.get("quraStatus");
        const storedCopy = storage.get("copyOption");
        const storedValidation = storage.get("validationDisabledStatus");

        if (storedQura?.qura) {
          setQuraEnabled(storedQura.qura);
        }
        if (storedCopy) {
          copyOptionRef.current = storedCopy;
        }
        if (typeof storedValidation?.disabled === "boolean") {
          setValidationDisabled(storedValidation.disabled);
        }

        if (
          !storedQura?.qura ||
          !storedCopy ||
          typeof storedValidation?.disabled !== "boolean"
        ) {
          const [quraStatus, copyOptionResult, systemSettings] =
            await Promise.all([
              System.isQura(),
              System.getCopyOption(),
              System.keys(),
            ]);

          if (!mounted) return;

          if (quraStatus?.qura) {
            setQuraEnabled(quraStatus.qura);
            storage.set("quraStatus", quraStatus);
          }

          if (copyOptionResult) {
            copyOptionRef.current = copyOptionResult;
            storage.set("copyOption", copyOptionResult);
          }

          const validationSetting = systemSettings?.disableValidationPrompt;
          if (typeof validationSetting === "boolean") {
            setValidationDisabled(validationSetting);
            storage.set("validationDisabledStatus", {
              disabled: validationSetting,
            });
          }
        }
      } catch (error) {
        console.error("Error fetching settings:", error);
        if (mounted) {
          showToast(t("show-toast.error-fetching-settings"), "error");
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    fetchSettings();

    return () => {
      mounted = false;
    };
  }, [storage, t]);

  const handleOpenQura = useCallback(() => {
    const currentCopyOption = copyOptionRef.current;
    const queryText = currentCopyOption === "question" ? question : answer;
    const url = `https://app.qura.law/se?query=${encodeURIComponent(queryText)}`;

    const width = 1300;
    const height = 600;
    const left = (window.screen.width - width) / 2;
    const top = (window.screen.height - height) / 2;

    window.open(
      url,
      "_blank",
      `width=${width},height=${height},resizable=yes,scrollbars=yes,left=${left},top=${top}`
    );
  }, [question, answer]);

  const handleDelete = async () => {
    openConfirmation(async () => {
      try {
        const result = await Workspace.deleteChat(chatId, slug);

        if (result) {
          window.dispatchEvent(
            new CustomEvent("delete-message", { detail: { chatId } })
          );
          showToast(t("show-toast.chat-deleted"), "success");
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        } else {
          showToast(t("show-toast.failed-delete-chat"), "error");
        }
      } catch (error) {
        console.error("Error deleting chat:", error);
        showToast(t("show-toast.error-deleting-chat"), "error");
      }
    });
  };

  if (loading) return null;

  const llmInput = {
    question: question,
    sources: sources,
  };

  return (
    <>
      <div className="flex w-full justify-between items-end mt-6">
        <div className="flex items-center gap-3 flex-wrap">
          <EditMessageAction
            message={message}
            chatId={chatId}
            role={role}
            isEditing={isEditing}
          />
          {chatId && (
            <>
              <CopyMessage message={message} />
              <ExportToWord message={message} />
            </>
          )}
          {chatId && (
            <>
              {isLastMessage && (
                <RegenerateMessage
                  regenerateMessage={regenerateMessage}
                  slug={slug}
                  chatId={chatId}
                />
              )}
              {role === "assistant" && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleDelete}
                  data-tooltip-id="delete-message"
                  data-tooltip-content={t("prompt-validate.delete")}
                  aria-label={t("common.delete-message")}
                >
                  <MdOutlineDeleteOutline />
                </Button>
              )}
              <Tooltip
                id="delete-message"
                delayShow={300}
                className="tooltip"
              />
              {role === "assistant" && chatId && (
                <TTSMessage slug={slug} chatId={chatId} message={message} />
              )}
              {sources?.length > 0 && (
                <Citations sources={sources} inline={true} chatId={chatId} />
              )}
              {role === "assistant" && isCanvasEnabled && (
                <CanvasChat
                  chat={chat}
                  chatId={chatId}
                  question={question}
                  answer={answer}
                  sendCommand={sendCommand}
                  workspace={workspace}
                  threadSlug={threadSlug}
                  updateHistory={updateHistory}
                  history={history}
                />
              )}
              {role === "assistant" &&
                isLastMessage &&
                !validationDisabled &&
                !storeUniversityMode && (
                  <ValidateResponse
                    answer={answer}
                    chatId={chatId}
                    llmInput={llmInput}
                  />
                )}
            </>
          )}
          {role === "assistant" && chatId && quraEnabled && (
            <Button variant="outline" size="sm" onClick={handleOpenQura}>
              <GrSearchAdvanced />
              {t("qura.copy-to-cora")}
            </Button>
          )}
        </div>
        <RenderMetrics metrics={metrics} />
      </div>
      <Modal
        isOpen={isConfirmationOpen}
        onClose={closeConfirmation}
        title={t("common.delete-message")}
        footer={
          <>
            <Button variant="outline" onClick={closeConfirmation}>
              {t("common.cancel")}
            </Button>
            <Button variant="destructive" onClick={handleConfirm}>
              {t("button.delete")}
            </Button>
          </>
        }
      >
        <p className="text-foreground">{t("sidebar.thread.delete-message")}</p>
      </Modal>
    </>
  );
});

function CopyMessage({ message }) {
  const { t } = useTranslation();
  const { copied, copyText } = useCopyText();

  return (
    <>
      <div className="">
        <Button
          variant="outline"
          size="icon"
          onClick={() => copyText(message)}
          data-tooltip-id="copy-assistant-text"
          data-tooltip-content={t("prompt-validate.copy")}
          aria-label={t("common.copy")}
        >
          {copied ? (
            <LuCopyCheck size={17} className="" />
          ) : (
            <MdOutlineContentCopy size={17} className="" />
          )}
        </Button>
        <Tooltip id="copy-assistant-text" delayShow={300} className="tooltip" />
      </div>
    </>
  );
}

function RegenerateMessage({ regenerateMessage, chatId }) {
  const { user } = useUser();
  const { t } = useTranslation();

  if (!chatId) return null;
  return (
    (!user || user?.role !== "default") && (
      <div className="">
        <Button
          variant="outline"
          size="icon"
          onClick={() => regenerateMessage(chatId)}
          data-tooltip-id="regenerate-assistant-text"
          data-tooltip-content={t("prompt-validate.regenerate")}
          aria-label={t("common.regenerate")}
        >
          <TbRefresh />
        </Button>
        <Tooltip
          id="regenerate-assistant-text"
          delayShow={300}
          className="tooltip"
        />
      </div>
    )
  );
}

function ExportToWord({ message }) {
  const { t } = useTranslation();
  const [isExporting, setIsExporting] = useState(false);
  const { exportContent } = useDocxExport();

  const handleExport = async () => {
    if (isExporting) return;

    setIsExporting(true);
    try {
      await exportContent(message, {
        filename: `chat-export-${new Date().toISOString().split("T")[0]}.docx`,
        title: t("docx.title", "Chat Export"),
        onSuccess: () => setIsExporting(false),
        onError: () => setIsExporting(false),
      });
    } catch (error) {
      console.error("Error exporting to Word:", error);
      showToast(error.message || t("show-toast.export-error"), "error");
      setIsExporting(false);
    }
  };

  return (
    <div className="">
      <Button
        variant="outline"
        size="icon"
        onClick={handleExport}
        data-tooltip-id="export-word"
        data-tooltip-content={t("prompt-validate.export-word")}
        aria-label={t("common.export-word")}
        disabled={isExporting}
      >
        {isExporting ? (
          <div className="animate-spin">
            <CircleNotch />
          </div>
        ) : (
          <BsFiletypeDocx />
        )}
      </Button>
      <Tooltip id="export-word" delayShow={300} className="tooltip" />
    </div>
  );
}

export default Actions;
