import React, { useState, useEffect } from "react";
import { CgPerformance } from "react-icons/cg";
import Modal from "@/components/ui/Modal";
import AskLegalQuestModal from "@/components/Modals/AskLegalQuestion";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import { userFromStorage } from "@/utils/request";
import System from "@/models/system";
import {
  useSelectedFeatureCard,
  useSetSelectedFeatureCard,
} from "@/stores/userStore";

export default function PerformLegalQuestion({ sendCommand }) {
  const { t } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [canPerformLegalTask, setCanPerformLegalTask] = useState(false);
  const user = userFromStorage();
  const selectedFeatureCard = useSelectedFeatureCard();
  const setSelectedFeatureCard = useSetSelectedFeatureCard();

  useEffect(() => {
    const checkLegalTaskAccess = async () => {
      try {
        const response = await System.getPerformLegalTask();
        const isFeatureEnabled = response?.enabled;
        const isUserAccessEnabled = response?.allowUserAccess;

        // User can access if:
        // 1. They are admin/manager/superuser OR
        // 2. Feature is enabled AND user access is enabled for regular users
        const hasAccess =
          user?.role === "admin" ||
          user?.role === "manager" ||
          user?.role === "superuser" ||
          (isFeatureEnabled && isUserAccessEnabled);

        setCanPerformLegalTask(hasAccess);
      } catch (error) {
        console.error("Error checking legal task access:", error);
        setCanPerformLegalTask(false);
      }
    };

    checkLegalTaskAccess();
  }, [user?.role]);

  useEffect(() => {
    if (selectedFeatureCard === "complex-document-builder") {
      setIsModalOpen(true);
      setSelectedFeatureCard(null);
    }
  }, [selectedFeatureCard, setSelectedFeatureCard]);

  if (!canPerformLegalTask) return null;

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        type="button"
        onClick={() => setIsModalOpen(true)}
      >
        <CgPerformance />
        {t("performLegalTask.title")}
      </Button>
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        className="w-[400px] max-w-[90vw] max-h-[90vh] overflow-y-auto"
      >
        <div className="w-full flex flex-col">
          <AskLegalQuestModal
            sendCommand={sendCommand}
            onClose={() => setIsModalOpen(false)}
          />
        </div>
      </Modal>
    </>
  );
}
