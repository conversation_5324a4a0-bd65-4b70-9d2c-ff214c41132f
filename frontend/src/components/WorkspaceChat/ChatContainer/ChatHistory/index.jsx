import React, { useEffect, useRef, useState, useCallback } from "react";
import HistoricalMessage from "./HistoricalMessage";
import PromptReply from "./PromptReply";
import { useManageWorkspaceModal } from "../../../Modals/ManageWorkspace";
import ManageWorkspace from "../../../Modals/ManageWorkspace";
import { ArrowDown } from "@phosphor-icons/react";
import useUser from "@/hooks/useUser";
import Chartable from "./Chartable";
import Workspace from "@/models/workspace";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import paths from "@/utils/paths";
import Appearance from "@/models/appearance";
import System from "@/models/system";
import showToast from "@/utils/toast";
import { useTextSize } from "@/stores/userStore";

export default function ChatHistory({
  history = [],
  workspace,
  sendCommand,
  updateHistory,
  regenerateAssistantMessage,
}) {
  const lastScrollTopRef = useRef(0);
  const { t } = useTranslation();
  const { user } = useUser();
  const { threadSlug = null } = useParams();
  const { showing, showModal, hideModal } = useManageWorkspaceModal();
  const [isAtBottom, setIsAtBottom] = useState(true);
  const chatHistoryRef = useRef(null);
  const textSize = useTextSize();
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const showScrollbar = Appearance.getSettings()?.showScrollbar || false;
  const isStreaming = history[history.length - 1]?.animate;
  const [isCitationEnabled, setIsCitationEnabled] = useState(false);
  const [isLegalTaskEnabled, setIsLegalTaskEnabled] = useState(false);
  const isNewPromptPending = history[history.length - 1]?.pending === true;
  const navigate = useNavigate();

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const performLegalTaskResponse = await System.getPerformLegalTask();
        setIsLegalTaskEnabled(performLegalTaskResponse?.enabled || false);
      } catch (error) {
        console.error("Error fetching legal task status:", error);
      }
    };
    fetchInitialData();
  }, []);

  useEffect(() => {
    const fetchCitation = async () => {
      try {
        const isCitationEnabled = await System.isCitationButton();
        setIsCitationEnabled(isCitationEnabled);
      } catch (error) {
        console.error("Error fetching Citation status:", error);
      } finally {
        //
      }
    };

    fetchCitation();
  }, []);

  const handleScroll = useCallback(
    (e) => {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      const distanceFromBottom = Math.abs(
        scrollHeight - scrollTop - clientHeight
      );
      const isBottom = distanceFromBottom < 10;
      const scrollDistance = scrollTop - lastScrollTopRef.current;
      const isScrollingUp = scrollDistance < 0;

      if (isStreaming && (isScrollingUp || distanceFromBottom > 100)) {
        setIsUserScrolling(true);
      }

      if (isBottom) {
        setIsAtBottom(true);
        if (!isScrollingUp) {
          setIsUserScrolling(false);
        }
      } else {
        setIsAtBottom(false);
      }

      lastScrollTopRef.current = scrollTop;
    },
    [isStreaming]
  );

  useEffect(() => {
    // Force initial scroll when new prompt is submitted and we're at bottom or not user scrolling
    if (isNewPromptPending && (isAtBottom || !isUserScrolling)) {
      scrollToBottom(true); // Use smooth scroll for better UX
      setIsAtBottom(true);
      setIsUserScrolling(false);
      return;
    }

    if (!isUserScrolling && isAtBottom) {
      scrollToBottom(false); // Use instant scroll for auto-scrolling
    } else if (!isStreaming && !isUserScrolling && !isAtBottom) {
      // Ensure we end up at the bottom once streaming finishes
      scrollToBottom(false);
    } else if (isStreaming && !isUserScrolling) {
      // If streaming and not explicitly scrolled up, check if we should auto-scroll
      const scrollTimeout = setTimeout(() => {
        if (chatHistoryRef.current) {
          const { scrollTop, scrollHeight, clientHeight } =
            chatHistoryRef.current;
          const distanceFromBottom = Math.abs(
            scrollHeight - scrollTop - clientHeight
          );

          // Only auto-scroll if very close to bottom and not manually scrolled
          const shouldAutoScroll =
            !isUserScrolling && isAtBottom && distanceFromBottom < 50;

          if (shouldAutoScroll) {
            scrollToBottom(false);
          }
        }
      }, 10);
      return () => clearTimeout(scrollTimeout);
    }
  }, [history, isAtBottom, isStreaming, isUserScrolling, isNewPromptPending]);

  // Remove debounce, we want immediate response
  useEffect(() => {
    const chatHistoryElement = chatHistoryRef.current;
    if (chatHistoryElement) {
      chatHistoryElement.addEventListener("scroll", handleScroll);
      return () =>
        chatHistoryElement.removeEventListener("scroll", handleScroll);
    }
  }, [handleScroll]);

  const scrollToBottom = (smooth = false) => {
    if (chatHistoryRef.current) {
      const scrollOptions = {
        top: chatHistoryRef.current.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      };

      requestAnimationFrame(() => {
        chatHistoryRef.current.scrollTo(scrollOptions);

        // Double-check scroll position after a small delay
        // Double check scroll position after content updates
        setTimeout(() => {
          if (chatHistoryRef.current) {
            const { scrollTop, scrollHeight, clientHeight } =
              chatHistoryRef.current;
            if (Math.abs(scrollHeight - scrollTop - clientHeight) > 10) {
              chatHistoryRef.current.scrollTo(scrollOptions);
            }
          }
        }, 200);
      });
    }
  };

  const saveEditedMessage = async ({
    editedMessage,
    chatId,
    role,
    attachments = [],
  }) => {
    if (!editedMessage) return; // Don't save empty edits.

    // if the edit was a user message, we will auto-regenerate the response and delete all
    // messages post modified message
    if (role === "user") {
      // remove all messages after the edited message
      // technically there are two chatIds per-message pair, this will split the first.
      const updatedHistory = Array.isArray(history)
        ? history.slice(
            0,
            history.findIndex((msg) => msg.chatId === chatId) + 1
          )
        : [];

      if (updatedHistory.length > 0) {
        // update last message in history to edited message
        updatedHistory[updatedHistory.length - 1].content = editedMessage;
        // remove all edited messages after the edited message in backend
        await Workspace.deleteEditedChats(workspace.slug, threadSlug, chatId);
        sendCommand(editedMessage, true, updatedHistory, attachments);
      }
      return;
    }

    // If role is an assistant we simply want to update the comment and save on the backend as an edit.
    if (role === "assistant") {
      if (!Array.isArray(history)) return;
      const updatedHistory = [...history];
      const targetIdx = updatedHistory.findIndex(
        (msg) => msg.chatId === chatId && msg.role === role
      );
      if (targetIdx < 0) return;

      // Update the message with edited flag to prevent regeneration
      updatedHistory[targetIdx] = {
        ...updatedHistory[targetIdx],
        content: editedMessage,
        message: editedMessage,
        edited: true,
      };

      // Update history first
      updateHistory(updatedHistory);

      // Then save to backend
      await Workspace.updateChatResponse(
        workspace.slug,
        threadSlug,
        chatId,
        editedMessage
      );
      return;
    }
  };

  const forkThread = async (chatId) => {
    const newThreadSlug = await Workspace.forkThread(
      workspace.slug,
      threadSlug,
      chatId
    );

    if (newThreadSlug) {
      navigate(paths.workspace.thread(workspace.slug, newThreadSlug));
    } else {
      showToast(t("toast.errors.failed-fork-thread"), "error");
    }
  };

  /* Break out the elements and hide them for now.
     We might need to bring in the future again depending on modules we build (variants of the DD module)
  */
  const welcomeMessage = () => {
    return (
      <>
        <p className="text-foreground font-base text-[12px] md:text-base sm:text-sm">
          {t("workspace-chats.welcome")}
        </p>
        {!user || user.role !== "default" ? (
          <p className="w-full text-foreground text-foreground text-[12px] font-base flex flex-col md:flex-row gap-x-1">
            {t("workspace-chats.desc-start")}{" "}
            <span
              className="underline font-medium cursor-pointer"
              onClick={showModal}
            >
              {t("workspace-chats.desc-mid")}
            </span>
            {t("workspace-chats.desc-or")}{" "}
            <b className="font-medium italic">
              {t("workspace-chats.desc-end")}
            </b>
          </p>
        ) : (
          <p className="w-full text-foreground font-base flex flex-col md:flex-row gap-x-1">
            {t("workspace-chats.start")}{" "}
            <b className="font-medium italic">
              {t("workspace-chats.desc-end")}
            </b>
          </p>
        )}
      </>
    );
  };

  return (
    <div
      className={`bg-background text-foreground font-light ${textSize} h-full flex flex-col justify-start overflow-y-scroll ${
        showScrollbar ? "" : "no-scroll"
      }`}
      id="chat-history"
      ref={chatHistoryRef}
      onScroll={handleScroll}
    >
      <div className="w-full max-w-4xl mx-auto pb-56">
        {history.map((props, index) => {
          let question = "";
          let answer = "";
          if (index % 2 === 0) {
            question = props.content;
            answer = history[index + 1]?.content || "";
          } else {
            question = history[index - 1].content;
            answer = props.content;
          }

          const isLastBotReply =
            index === history.length - 1 && props.role === "assistant";

          if (props?.type === "statusResponse" && !!props.content) {
            return (
              <StatusResponse
                key={`status-${props.uuid}-${index}`}
                props={props}
              />
            );
          }

          if (props.type === "rechartVisualize" && !!props.content) {
            return (
              <Chartable
                key={`chart-${props.uuid}-${index}`}
                workspace={workspace}
                props={props}
              />
            );
          }

          // Prevent rendering empty streaming messages
          if (
            isLastBotReply &&
            props.animate &&
            !props.content &&
            !props.error
          ) {
            if (props.pending) {
              return (
                <PromptReply
                  key={`reply-${props.uuid}-${index}`}
                  uuid={props.uuid}
                  reply=""
                  pending={true}
                  sources={[]}
                  error={null}
                  workspace={workspace}
                  closed={false}
                />
              );
            }
            return null;
          }

          // Handle streaming messages - only use PromptReply for actively streaming messages
          if (isLastBotReply && props.animate && !props.closed) {
            return (
              <PromptReply
                key={`reply-${props.uuid}-${index}`}
                uuid={props.uuid}
                reply={props.content}
                pending={props.pending}
                sources={props.sources}
                error={props.error}
                workspace={workspace}
                closed={props.closed}
              />
            );
          }

          return (
            <HistoricalMessage
              key={`msg-${props.uuid}-${index}`}
              chat={props}
              role={props.role}
              error={props.error}
              chatId={props.chatId}
              workspace={workspace}
              message={props.content}
              sources={props.sources}
              feedbackScore={props.feedbackScore}
              attachments={props.attachments}
              regenerateMessage={regenerateAssistantMessage}
              isLastMessage={isLastBotReply}
              saveEditedMessage={saveEditedMessage}
              forkThread={forkThread}
              metrics={props.metrics}
              index={index}
              question={question}
              answer={answer}
              sendCommand={sendCommand}
              updateHistory={updateHistory}
              threadSlug={threadSlug}
              history={history}
              isLegalTaskEnabled={isLegalTaskEnabled}
            />
          );
        })}
      </div>

      {showing && (
        <ManageWorkspace hideModal={hideModal} providedSlug={workspace.slug} />
      )}

      {!isAtBottom && (
        <div className="fixed bottom-40 right-10 md:right-20 z-50 cursor-pointer animate-pulse">
          <div className="flex flex-col items-center">
            <div
              className="p-1 rounded-full border border-white/10 bg-white/10 hover:bg-white/20 hover:text-white"
              onClick={() => {
                scrollToBottom(true);
                setIsUserScrolling(false);
              }}
            >
              <ArrowDown weight="bold" className="text-white w-5 h-5" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function StatusResponse({ props }) {
  return (
    <div className="flex justify-center items-end w-full">
      <div className="py-2 px-4 w-full flex gap-x-5 md:max-w-[80%] flex-col">
        <div className="flex gap-x-5">
          <span
            className={`text-xs inline-block p-2 rounded-lg text-foreground font-mono whitespace-pre-line`}
          >
            {props.content}
          </span>
        </div>
      </div>
    </div>
  );
}
