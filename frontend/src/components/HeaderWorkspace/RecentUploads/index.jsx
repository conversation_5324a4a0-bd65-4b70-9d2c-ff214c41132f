import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import showToast from "@/utils/toast";
import { API_BASE } from "@/utils/constants";
import { Bell } from "@phosphor-icons/react";
import { cn } from "@/utils/classes";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";
import Preloader from "@/components/Preloader";
import { LuExternalLink } from "react-icons/lu";
import {
  useRecentUploadsLastSeenTimestamp,
  useSetRecentUploadsLastSeenTimestamp,
} from "@/stores/userStore";
import { getLocalizedDate } from "@/utils/dates";

const removeFileExtension = (filename) => {
  return filename.replace(/\.[^/.]+$/, "");
};

const getFileExtension = (filename) => {
  return filename.split(".").pop()?.toLowerCase();
};

export default function RecentUploads() {
  const { t } = useTranslation();
  const [latestAdditions, setLatestAdditions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [hasItems, setHasItems] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [hasUnseenItems, setHasUnseenItems] = useState(false);

  const lastSeenTimestampFromStore = useRecentUploadsLastSeenTimestamp();
  const setLastSeenTimestampInStore = useSetRecentUploadsLastSeenTimestamp();

  useEffect(() => {
    async function fetchLatestAdditions() {
      setLoading(true);
      try {
        const checkResponse = await fetch(`${API_BASE}/v1/recent-uploads`);

        if (!checkResponse.ok) {
          throw new Error(t("recentUploads.error.fetch"));
        }

        const initialData = await checkResponse.json();

        if (initialData.length > 0) {
          setHasItems(true);
          const sortedUploads = initialData.sort(
            (a, b) => new Date(b.dateAdded) - new Date(a.dateAdded)
          );
          setLatestAdditions(sortedUploads);
        } else {
          setHasItems(false);
          setLatestAdditions([]);
        }
        setError(null);
      } catch (err) {
        console.error("Error fetching latest additions:", err);
        setError(err.message);
        showToast(err.message, "error");
        setLatestAdditions([]);
        setHasItems(false);
      } finally {
        setLoading(false);
      }
    }

    fetchLatestAdditions();
  }, []);

  useEffect(() => {
    if (latestAdditions.length > 0) {
      const latestTimestampFromApi = new Date(
        latestAdditions[0].dateAdded
      ).getTime();
      if (
        !lastSeenTimestampFromStore ||
        latestTimestampFromApi > Number(lastSeenTimestampFromStore)
      ) {
        setHasUnseenItems(true);
      } else {
        setHasUnseenItems(false);
      }
    } else {
      setHasUnseenItems(false);
    }
  }, [latestAdditions, lastSeenTimestampFromStore]);

  const handleFileClick = async (file) => {
    if (getFileExtension(file.fileName) !== "pdf") return;
    if (!file.path) {
      console.error("File path is missing for:", file.fileName);
      showToast(
        t("recentUploads.error.missingPath", {
          defaultValue: "File path is missing.",
        }),
        "error"
      );
      return;
    }

    const filePath = file.path;
    const fileName = filePath.split("/").pop();

    const isPdfJson = fileName.endsWith(".pdf.json");
    const pdfFilename = isPdfJson
      ? fileName.replace(/\.json$/, "")
      : fileName.replace(/\.json$/, ".pdf");

    const hotdirUrl = `/hotdir/${pdfFilename}`;

    try {
      const hotdirResponse = await fetch(hotdirUrl, {
        method: "HEAD",
        headers: { Accept: "application/pdf" },
      });

      if (hotdirResponse.ok) {
        window.open(hotdirUrl, "_blank", "noopener,noreferrer");
        return;
      }
    } catch (error) {
      console.warn("Frontend hotdir check failed:", error);
    }

    try {
      let pathForServer = file.path;
      if (pathForServer.endsWith(".pdf.json")) {
        pathForServer = pathForServer.replace(/\.json$/, "");
        console.log(`Adjusted path for server request: ${pathForServer}`);
      }

      const serverUrl = `${API_BASE}/document/pdf-contents?path=${encodeURIComponent(pathForServer)}`;
      window.open(serverUrl, "_blank", "noopener,noreferrer");
    } catch (error) {
      console.error("Error opening PDF:", error);
      showToast(
        t("recentUploads.error.openGeneric", {
          defaultValue: `Could not open PDF: ${error.message}`,
        }),
        "error"
      );
    }
  };

  const handleOpenModal = () => {
    if (latestAdditions.length > 0) {
      const latestTimestamp = new Date(latestAdditions[0].dateAdded).getTime();
      setLastSeenTimestampInStore(latestTimestamp);
    }
    setIsModalOpen(true);
  };

  if (error || !hasItems || latestAdditions.length === 0) {
    return null;
  }

  const groupedFiles = latestAdditions.reduce((acc, file) => {
    if (!acc[file.workspaceName]) {
      acc[file.workspaceName] = [];
    }
    acc[file.workspaceName].push(file);
    return acc;
  }, {});

  const sortedWorkspaces = Object.keys(groupedFiles).sort((a, b) => {
    const latestDateA = Math.max(
      ...groupedFiles[a].map((file) => new Date(file.dateAdded).getTime())
    );
    const latestDateB = Math.max(
      ...groupedFiles[b].map((file) => new Date(file.dateAdded).getTime())
    );
    return latestDateB - latestDateA;
  });

  return (
    <div>
      <Button
        onClick={handleOpenModal}
        variant="outline"
        className="relative w-full md:w-auto"
      >
        <Bell />
        {t("recentUploads.buttonText")}
        {hasUnseenItems && (
          <svg
            className="w-5 h-5 absolute -top-2 -right-2 border-2 border-white bg-white text-primary rounded-full"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="10" cy="10" r="8" fill="currentColor" />
          </svg>
        )}
      </Button>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={t("recentUploads.title")}
        footer={
          <Button onClick={() => setIsModalOpen(false)}>
            {t("common.close")}
          </Button>
        }
        className="w-full max-w-[48rem]"
      >
        <div className="max-h-[24rem] pb-10 overflow-y-auto border rounded-lg text-foreground">
          {loading ? (
            <div className="grid place-items-center h-48 pt-6">
              <Preloader />
            </div>
          ) : (
            <div className="px-4 space-y-6">
              {sortedWorkspaces.map((workspace) => (
                <div key={workspace} className="space-y-2">
                  <h3 className="sticky top-0 text-lg font-semibold bg-background pb-4 pt-6 pl-2">
                    {workspace}
                  </h3>
                  <ul className="m-0 space-y-1">
                    {groupedFiles[workspace].map((file, index) => (
                      <li key={index} className="m-0 list-none">
                        <Button
                          onClick={() => handleFileClick(file)}
                          className={cn(
                            "group w-full justify-between h-auto px-2 py-1 [&_svg]:size-4",
                            getFileExtension(file.fileName) !== "pdf" &&
                              "pointer-events-none"
                          )}
                          variant="ghost"
                        >
                          <div className="flex items-center gap-2 max-w-[75%]">
                            <p className="truncate text-left">
                              {removeFileExtension(file.fileName)}
                            </p>

                            <LuExternalLink className="group-hover:opacity-100 opacity-0 transition-opacity -mt-0.5" />
                          </div>
                          <p className="shrink-0 text-muted">
                            {(() => {
                              const date = new Date(file.dateAdded);
                              const localizedDateString = getLocalizedDate(
                                date,
                                t
                              );
                              const time = `${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
                              return `${localizedDateString}, ${time}`;
                            })()}
                          </p>
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
}
