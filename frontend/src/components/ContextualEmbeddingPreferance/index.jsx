import { useTranslation } from "react-i18next";
import React, { useState } from "react";
import Toggle from "@/components/ui/Toggle";

export default function ContextualEmbeddingPreferance({
  name,
  componentId,
  systemPrompt,
  userPrompt,
  defaultValue,
}) {
  if (defaultValue === "false") {
    defaultValue = false;
  }
  const { t } = useTranslation();
  const [isChecked, setIsChecked] = useState(defaultValue);
  const enabled = t("system.state.enabled");
  const disabled = t("system.state.disabled");

  return (
    <div className="w-full">
      <div className="flex flex-col mb-2">
        <label
          htmlFor={name}
          className="block text-sm font-medium text-foreground"
        >
          {t("contextual.checkbox.label")}
        </label>
        <p className="text-foreground text-xs">
          {t("contextual.checkbox.hint")}
        </p>
      </div>

      <div className="flex flex-col gap-y-1">
        <Toggle
          id={`${componentId}-checkbox`}
          name="ContextualEmbedding"
          checked={isChecked}
          onChange={() => setIsChecked(!isChecked)}
        />
        <small className="text-[12px] text-foreground">
          {t("contextual.checkbox.label")}:{" "}
          <b>{isChecked ? enabled : disabled}</b>
        </small>
      </div>
      {isChecked && (
        <div className="mt-4 space-y-4 slide-down">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              {t("contextual.systemPrompt.label")}
            </label>
            <input
              id={`ContextualSystemPrompt`}
              name={`ContextualSystemPrompt`}
              type="text"
              defaultValue={systemPrompt}
              className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2.5"
              placeholder={t("contextual.systemPrompt.placeholder")}
            />
            <p className="mt-1 text-xs text-foreground text-gray-500">
              {t("contextual.systemPrompt.description")}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              {t("contextual.userPrompt.label")}
            </label>
            <input
              id={`ContextualUserPrompt`}
              name={`ContextualUserPrompt`}
              type="text"
              defaultValue={userPrompt}
              className="dark-input-mdl text-foreground text-sm rounded-lg block w-full p-2.5"
              placeholder={t("contextual.userPrompt.placeholder")}
            />
            <p className="mt-1 text-xs text-foreground text-gray-500">
              {t("contextual.userPrompt.description")}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
