import React from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import Select from "@/components/Select";
import {
  useTabNames,
  useDocumentDraftingEnabled,
} from "@/stores/settingsStore";
import { LuCloudUpload, LuServer } from "react-icons/lu";
import { cn } from "@/utils/classes";
import Separator from "@/components/Separator";
import { useSelectedModule, useSetSelectedModule } from "@/stores/userStore";

export default function ModuleSelection({ className }) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const tabNames = useTabNames();
  const { isDocumentDrafting } = useDocumentDraftingEnabled();
  const isPageSettings = window.location.pathname.startsWith("/settings/");
  const setSelectedModule = useSetSelectedModule();
  const selectedModule = useSelectedModule();

  const changeSelectedModule = (module) => {
    setSelectedModule(module);
    navigate("/");
  };

  const getModuleOptions = () => {
    const options = [
      {
        value: "legal-qa",
        label:
          tabNames?.tabName1 && tabNames.tabName1.trim() !== ""
            ? tabNames.tabName1
            : t("module.legal-qa"),
        icon: <LuCloudUpload className="size-5 text-foreground" />,
      },
      {
        value: "document-drafting",
        label:
          tabNames?.tabName2 && tabNames.tabName2.trim() !== ""
            ? tabNames.tabName2
            : t("module.document-drafting"),
        icon: <LuServer className="size-5 text-foreground" />,
      },
    ];

    return options;
  };

  if (isPageSettings || !isDocumentDrafting) {
    return null;
  }

  return (
    <>
      <div className={cn("w-full min-w-56", className)}>
        <Select
          options={getModuleOptions()}
          defaultValue={selectedModule}
          onChange={changeSelectedModule}
        />
      </div>
      <Separator className="my-4 hidden md:block" />
    </>
  );
}
