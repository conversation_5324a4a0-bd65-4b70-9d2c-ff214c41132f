import React, { useState, useEffect, memo } from "react";
import "react-loading-skeleton/dist/skeleton.css";
import ManageWorkspace, {
  useManageWorkspaceModal,
} from "../../Modals/ManageWorkspace";
import paths from "@/utils/paths";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { Gear, UploadSimple } from "@phosphor-icons/react";
import useUser from "@/hooks/useUser";
import ThreadContainer from "./ThreadContainer";
import { useMatch } from "react-router-dom";
import { Button } from "@/components/Button";
import { useTranslation } from "react-i18next";
import { getWorkspaceTranslatedName } from "@/utils/workspace";
import { GoLaw } from "react-icons/go";
import WorkspaceCreatorTooltip from "./WorkspaceCreatorTooltip";
import {
  useIsDocumentDrafting,
  useSelectedModule,
  useDdShowAllWorkspaces,
  useSetDdShowAllWorkspaces,
} from "@/stores/userStore";
import useWorkspaceStore from "@/stores/workspaceStore";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/Tooltip";

const stableEmptyArray = [];
import ShareButton from "@/components/ShareButton";

const ActiveWorkspaces = memo(function ActiveWorkspaces({ renderEmptyState }) {
  const { slug } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const isDocumentDrafting = useIsDocumentDrafting();
  const selectedModule = useSelectedModule();
  const workspaces = useWorkspaceStore(
    (state) => state.moduleWorkspaces[selectedModule] || stableEmptyArray
  );
  const loading = useWorkspaceStore(
    (state) => state.loadingModule[selectedModule] || false
  );
  const [selectedWs, setSelectedWs] = useState(null);
  const showAllWorkspaces = useDdShowAllWorkspaces();
  const setDdShowAllWorkspaces = useSetDdShowAllWorkspaces();
  const { showing, showModal, hideModal } = useManageWorkspaceModal();
  const { user } = useUser();
  const isInWorkspaceSettings = !!useMatch("/workspace/:slug/settings/:tab");
  const { t } = useTranslation();
  const [hoveredWorkspace, setHoveredWorkspace] = useState(null);

  useEffect(() => {
    useWorkspaceStore.getState().fetchModuleWorkspaces();
  }, [selectedModule]);

  const filteredWorkspaces = workspaces.filter((workspace) => {
    if (!user || user.role === "default" || !isDocumentDrafting) {
      return true;
    }
    return showAllWorkspaces || workspace.user_id === user.id;
  });

  const handleWorkspaceClick = (workspaceSlug) => {
    if (slug !== workspaceSlug) {
      navigate(paths.workspace.chat(workspaceSlug)); // Navigate without reload
    }
  };

  // Open ManageWorkspace modal if navigation state requests it
  useEffect(() => {
    if (location.state?.openManageWorkspace && workspaces.length > 0) {
      const wsToManage = workspaces.find(
        (ws) => ws.slug === location.state.manageWorkspaceSlug
      );
      if (wsToManage) {
        setSelectedWs(wsToManage);
        showModal();
        // Clear navigation state to prevent reopening on back/refresh
        navigate(location.pathname, { replace: true, state: {} });
      }
    }
  }, [location.state, workspaces]);

  return loading ? (
    <div className="flex flex-col gap-3">
      {[...Array(10)].map((_, i) => (
        <div
          key={i}
          className="flex items-center h-12 rounded-md animate-pulse bg-secondary border"
        >
          <div className="size-5 ml-4 bg-primary opacity-20 rounded-full" />
          <div className="h-3 w-1/3 ml-2.5 bg-primary opacity-20 rounded-md" />
        </div>
      ))}
    </div>
  ) : (
    <TooltipProvider delayDuration={100}>
      {renderEmptyState && renderEmptyState(workspaces.length > 0)}
      {isDocumentDrafting &&
        (user?.role === "admin" || user?.role === "manager") && (
          <Button
            onClick={() => setDdShowAllWorkspaces(!showAllWorkspaces)}
            variant="outline"
            className="w-full"
          >
            {showAllWorkspaces
              ? t("workspace.show-my")
              : t("workspace.show-all")}
          </Button>
        )}
      <div
        role="list"
        aria-label="Workspaces"
        className="flex flex-col gap-y-4"
      >
        {filteredWorkspaces.map((workspace) => {
          const isActive = workspace.slug === slug;
          const isHovered = hoveredWorkspace === workspace.id;
          const showActionButtons =
            isHovered && (user?.role !== "default" || isDocumentDrafting);
          const shouldShowCreator =
            showAllWorkspaces &&
            isDocumentDrafting &&
            (user?.role === "admin" || user?.role === "manager");

          const getIconCount = () => {
            const hasOrg = !!user?.organizationId;
            const isPrivileged = user?.role === "admin";
            const canShare = isPrivileged || hasOrg;
            let iconCount = 0;

            if (canShare) {
              iconCount += 1;
            }

            if (user?.role !== "default" || isDocumentDrafting) {
              iconCount += 2;
            }

            return iconCount;
          };

          const actionButtons = getIconCount();
          const maxLabelWidth = 200 - (isHovered ? actionButtons * 34 + 2 : 0);
          const charCount = getWorkspaceTranslatedName(
            workspace.name,
            t
          ).length;
          const showTooltip = charCount > (actionButtons === 3 ? 13 : 17);

          return (
            <div
              className="flex flex-col w-full"
              key={workspace.id}
              role="listitem"
            >
              <Tooltip key={workspace.id}>
                <TooltipTrigger asChild>
                  <div
                    className="workspace-row-hover-wrapper"
                    onMouseEnter={() => setHoveredWorkspace(workspace.id)}
                    onMouseLeave={() => setHoveredWorkspace(null)}
                  >
                    <div className="relative flex items-center">
                      <Button
                        variant="secondary"
                        className={`w-full justify-start font-normal ${isActive ? "" : ""}`}
                        onClick={() => handleWorkspaceClick(workspace.slug)}
                      >
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                          <GoLaw />

                          <span
                            className="truncate transition-all duration-200"
                            style={{ maxWidth: `${maxLabelWidth}px` }}
                          >
                            {getWorkspaceTranslatedName(workspace.name, t)}
                          </span>

                          {shouldShowCreator && (
                            <WorkspaceCreatorTooltip
                              userId={workspace.user_id}
                              isVisible={hoveredWorkspace === workspace.id}
                            />
                          )}
                        </div>
                      </Button>

                      <div
                        className={`absolute right-2 flex items-center gap-0.5 transition-all duration-200 ${
                          showActionButtons
                            ? "opacity-100 translate-x-0"
                            : "opacity-0 translate-x-3"
                        }`}
                      >
                        <ShareButton
                          type="workspace"
                          workspace={workspace}
                          className="w-8 h-8 hover:bg-elevated"
                        />

                        <Button
                          variant="ghost"
                          size="icon"
                          className="w-8 h-8 hover:bg-elevated"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Navigate and set state to open modal after navigation
                            navigate(paths.workspace.chat(workspace.slug), {
                              state: {
                                openManageWorkspace: true,
                                manageWorkspaceSlug: workspace.slug,
                              },
                            });
                          }}
                        >
                          <UploadSimple weight="regular" />
                        </Button>

                        <Button
                          variant="ghost"
                          size="icon"
                          className="w-8 h-8 hover:bg-elevated"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigate(
                              isInWorkspaceSettings
                                ? paths.workspace.chat(workspace.slug)
                                : paths.workspace.settings.generalAppearance(
                                    workspace.slug
                                  )
                            );
                          }}
                        >
                          <Gear />
                        </Button>
                      </div>
                    </div>
                  </div>
                </TooltipTrigger>
                {showTooltip && (
                  <TooltipContent side="right" className="max-w-xs">
                    <p>{getWorkspaceTranslatedName(workspace.name, t)}</p>
                  </TooltipContent>
                )}
              </Tooltip>
              {isActive && (
                <ThreadContainer workspace={workspace} isActive={isActive} />
              )}
            </div>
          );
        })}
        {showing && (
          <ManageWorkspace
            hideModal={hideModal}
            providedSlug={selectedWs ? selectedWs.slug : null}
          />
        )}
      </div>
    </TooltipProvider>
  );
});

export default ActiveWorkspaces;
