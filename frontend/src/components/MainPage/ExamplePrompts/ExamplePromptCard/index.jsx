import React from "react";
import { CircleNotch } from "@phosphor-icons/react";
import { Button } from "@/components/Button";
import { icons } from "@/utils/examplePromptIcons";

export default function ExamplePromptCard({ example, onClick, isLoading }) {
  const IconComponent =
    icons.find((icon) => icon.name === example.icon)?.icon || null;

  return (
    <Button
      variant="secondary"
      onClick={() => onClick(example)}
      className={`!justify-start w-full p-4 md:pt-[1.15rem] md:px-6 md:pb-4 h-[72px] md:h-[140px] rounded-xl transition-colors hover:cursor-pointer bg-secondary text-left ${
        isLoading
          ? "opacity-60 hover:cursor-default hover:bg-secondary"
          : "hover:bg-secondary-hover"
      }`}
      disabled={isLoading}
    >
      <div className="flex flex-col h-full justify-end w-full min-w-0">
        <div className="flex flex-col h-full justify-start w-full">
          <div className="mb-auto invisible md:visible [&_svg]:size-6">
            {isLoading ? (
              <CircleNotch
                weight="bold"
                className="animate-spin text-foreground"
              />
            ) : (
              IconComponent && <IconComponent className="hidden md:block" />
            )}
          </div>
          <h3 className="mb-2 text-lg font-medium text-foreground text-balance line-clamp-1 md:line-clamp-2">
            {example.title}
          </h3>
          <div className="flex items-center gap-2 text-sm md:text-base text-foreground opacity-70">
            {IconComponent && (
              <IconComponent className="block md:hidden flex-shrink-0" />
            )}
            <span className="truncate">{example.area}</span>
          </div>
        </div>
      </div>
    </Button>
  );
}
