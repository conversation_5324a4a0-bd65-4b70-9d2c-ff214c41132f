import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import Workspace from "@/models/workspace";
import System from "@/models/system";
import { useParams } from "react-router-dom";
import { AiOutlineLoading } from "react-icons/ai";
import { MdArrowForwardIos } from "react-icons/md";
import { TbArrowNarrowLeft } from "react-icons/tb";
import { IoIosCheckmarkCircle, IoIosWarning } from "react-icons/io";
import { IoWarningOutline } from "react-icons/io5";
import { FiSettings } from "react-icons/fi";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";
import useUser from "@/hooks/useUser";
import LegalTasksSettingsModal from "@/components/Modals/LegalTasksSettingsModal";

// Warning modal component for when no JSON files are found
const NoFilesWarningModal = ({ onClose }) => {
  const { t } = useTranslation();

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title={t("performLegalTask.warning-title")}
      className="w-[400px]"
    >
      <div className="flex flex-col items-center justify-center p-4 text-center">
        <IoWarningOutline className="text-yellow-500 text-5xl mb-4" />
        <h4 className="text-lg font-medium text-foreground mb-2">
          {t("performLegalTask.no-files-title")}
        </h4>
        <p className="text-foreground text-opacity-80 mb-4">
          {t("performLegalTask.no-files-description")}
        </p>
        <Button onClick={onClose}>{t("common.ok")}</Button>
      </div>
    </Modal>
  );
};

const LegalProgress = ({ flowType = "main", onComplete }) => {
  const { t } = useTranslation();

  // Function to clean file names by removing UUIDs and file extensions
  const cleanFileName = (filename) => {
    if (!filename) return "";

    try {
      // Make sure filename is a string
      const filenameStr = String(filename);

      // First, handle JSON files specifically (from server)
      if (filenameStr.endsWith(".json")) {
        // Remove .json extension
        let cleanName = filenameStr.replace(/\.json$/, "");

        // Remove UUID pattern (common in JSON files)
        return cleanName;
      }

      // For other files, remove any file extension
      let cleanName = filenameStr.replace(/\.[^/.]+$/, "");

      // Remove UUID pattern (8-4-4-4-12 format or similar)
      // This regex looks for patterns like -a1b2c3d4 or -a1b2-c3d4-etc
      cleanName = cleanName.replace(
        /-[0-9a-f]{4,}(?:-[0-9a-f]{4,}){0,4}$/i,
        ""
      );

      // Also remove any standalone UUIDs that might be at the end
      cleanName = cleanName.replace(
        /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
        ""
      );

      return cleanName;
    } catch (error) {
      console.error("Error cleaning filename:", error, filename);
      return String(filename || "");
    }
  };

  // Generate progress steps based on the flowType (main or noMain)
  const getStepsForFlow = (flow) => {
    const stepsArr = [];
    for (let i = 1; i <= 7; i++) {
      stepsArr.push({
        id: i,
        title: (
          <span>
            {t(
              `cdbProgress.${flow}.step${i}.label`,
              t(`cdbProgress.main.step${i}.label`)
            )}
          </span>
        ),
        description: (
          <span>
            {t(
              `cdbProgress.${flow}.step${i}.desc`,
              t(`cdbProgress.main.step${i}.desc`)
            )}
          </span>
        ),
        progress: 0,
        status: "pending",
        isActive: i === 1,
      });
    }
    return stepsArr;
  };

  const [steps, setSteps] = useState(() => getStepsForFlow(flowType));

  const updateStepProgress = (stepId, newProgress) => {
    setSteps((prevSteps) =>
      prevSteps.map((step) => {
        if (step.id === stepId) {
          const prevStep = prevSteps.find((s) => s.id === stepId - 1);
          const prevCompleted = prevStep ? prevStep.progress >= 100 : true;

          const status =
            newProgress === -2
              ? "error"
              : newProgress >= 100
                ? "completed"
                : newProgress < 0
                  ? "pending"
                  : "pending";

          return {
            ...step,
            progress: newProgress,
            status,
            isActive: prevCompleted && newProgress < 100,
          };
        }
        // Auto-activate next step only when previous is fully completed
        if (step.id === stepId + 1 && newProgress >= 100) {
          return { ...step, isActive: true };
        }
        return step;
      })
    );
  };

  // Utility to add or update a sub-step within a main step
  const addOrUpdateSubStep = (
    parentId,
    subIndex,
    total,
    label,
    progress = 100
  ) => {
    setSteps((prev) =>
      prev.map((s) => {
        if (s.id !== parentId) return s;

        // ensure subSteps array exists
        const subSteps = s.subSteps ? [...s.subSteps] : [];

        // Expand array to total length
        if (total && subSteps.length < total) {
          for (let k = subSteps.length; k < total; k++) {
            subSteps.push({ id: k + 1, label: "", status: "pending" });
          }
        }

        // Update the specific sub-step
        if (subSteps[subIndex - 1]) {
          const subStatus =
            progress === -2
              ? "error"
              : progress === -1
                ? "loading"
                : "completed";
          subSteps[subIndex - 1] = {
            ...subSteps[subIndex - 1],
            label: label || subSteps[subIndex - 1].label,
            status: subStatus,
          };
        }

        // Calculate overall progress by completed subs
        const completed = subSteps.filter(
          (ss) => ss.status === "completed"
        ).length;
        const newProgress = total
          ? Math.round((completed / total) * 100)
          : s.progress;

        return {
          ...s,
          subSteps,
          progress: newProgress,
          status: newProgress >= 100 ? "completed" : s.status,
          isActive: newProgress < 100,
        };
      })
    );
  };

  // Listen for CDB process events to update progress
  useEffect(() => {
    // Handler for stream events from the CDB process
    const handleCDBStreamEvent = (event) => {
      const data = event.detail;

      // Check if this is a finalizeResponseStream event
      if (data.type === "finalizeResponseStream") {
        // Mark all steps as complete when the process is done
        for (let i = 1; i <= 7; i++) {
          updateStepProgress(i, 100);
        }

        // Call onComplete after a short delay
        if (onComplete) {
          setTimeout(onComplete, 500);
        }
      }
    };

    // Handler for granular progress events
    const handleProgressEvent = (event) => {
      const { step, subStep, total, label, progress } = event.detail;
      if (!step) return;

      // If we have subStep info, treat as sub-step progress
      if (subStep && total) {
        // Log the original label for debugging
        if (label) {
          console.log(`Processing file: ${label} -> ${cleanFileName(label)}`);
        }
        addOrUpdateSubStep(step, subStep, total, label, progress);
      } else if (total && !subStep) {
        /*
         * Prefill the sub-task list as soon as we know the total number of
         * documents (even before the first one has completed) so the user sees
         * placeholders immediately.
         */
        setSteps((prev) =>
          prev.map((s) => {
            if (s.id !== step) return s;

            const subSteps = s.subSteps ? [...s.subSteps] : [];

            // Expand array to full length with pending placeholders
            if (subSteps.length < total) {
              for (let k = subSteps.length; k < total; k++) {
                subSteps.push({
                  id: k + 1,
                  label: t("cdbProgress.general.placeholderSubTask", {
                    index: k + 1,
                  }),
                  status: "pending",
                });
              }
            }

            return { ...s, subSteps };
          })
        );
      }

      // Direct progress update for main steps only. Sub-task events
      // (identified by the presence of `subStep`) should not trigger
      // activation or completion of the parent step here.
      if (
        progress !== undefined &&
        (subStep === undefined || subStep === null)
      ) {
        updateStepProgress(step, progress);
      }
    };

    // Create a custom event for testing progress updates
    window.addEventListener("CDB_STREAM_EVENT", handleCDBStreamEvent);
    window.addEventListener("CDB_PROGRESS_EVENT", handleProgressEvent);

    return () => {
      window.removeEventListener("CDB_STREAM_EVENT", handleCDBStreamEvent);
      window.removeEventListener("CDB_PROGRESS_EVENT", handleProgressEvent);
    };
  }, [onComplete]);

  useEffect(() => {
    setSteps(getStepsForFlow(flowType));
  }, [flowType]);

  const getStatusIcon = (status) => {
    switch (status) {
      case "completed":
        return <IoIosCheckmarkCircle className="text-green-500 w-5 h-5" />;
      case "loading":
        return (
          <AiOutlineLoading className="animate-spin text-blue-500 w-5 h-5" />
        );
      case "error":
        return <IoIosWarning className="text-red-500 w-5 h-5" />;
      default:
        return <IoIosCheckmarkCircle className="text-gray-400 w-5 h-5" />;
    }
  };

  return (
    <div className="progressContainer">
      <ul className="progress text-foreground">
        {steps.map((step) => (
          <li
            key={step.id}
            className={`progress__item list-none text-foreground ${
              step.status === "completed"
                ? "progress__item--completed"
                : step.isActive
                  ? "progress__item--active"
                  : "progress__item--inactive"
            } ${step.subSteps && step.subSteps.length > 0 ? "progress__item--has-subtasks" : ""}`}
          >
            <p className="progress__title">{step.title}</p>
            <small>{step.description}</small>
            <br />
            <small className="flex flex-row font-bold items-center gap-1">
              Status: {getStatusIcon(step.status)}
            </small>

            {/* Render sub-steps if they exist */}
            {step.subSteps && step.subSteps.length > 0 && (
              <ul className="progress-subtasks">
                {step.subSteps.map((ss) => (
                  <li key={ss.id} className="progress-subtask-item">
                    <div className="progress-subtask-icon">
                      {getStatusIcon(ss.status)}
                    </div>
                    <span className="progress-subtask-text">
                      {ss.label
                        ? cleanFileName(ss.label)
                        : t("cdbProgress.general.placeholderSubTask", {
                            index: ss.id,
                          })}
                    </span>
                  </li>
                ))}
              </ul>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};

// Helper function to extract all file names from the workspace tree
const extractFileNames = (items) => {
  let files = [];
  const recurse = (currentItems) => {
    for (const item of currentItems) {
      // CDB processes .json files from the root or nested, ensure we get them
      if (item.type === "file" && item.name.endsWith(".json")) {
        files.push(item.name);
      } else if (item.type === "folder" && item.items) {
        recurse(item.items);
      }
    }
  };
  if (items) recurse(items);
  return files;
};

export default function PerformLegalTaskModal({ sendCommand, onClose }) {
  const { t } = useTranslation();
  const { slug } = useParams();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [, setIsLoading] = useState(false);
  const [workspace, setWorkspace] = useState(null);
  const [legalTasks, setLegalTasks] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [stepsProcess, setStepsProcess] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [, setIsSubtaskSelected] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [customInstructions, setCustomInstructions] = useState("");
  const [loadingSubCategories, setLoadingSubCategories] = useState(false);
  const [abortController] = useState(new AbortController());
  const [showNoFilesWarning, setShowNoFilesWarning] = useState(false);
  const [, setHasWorkspaceFiles] = useState(true);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [generatedFiles, setGeneratedFiles] = useState([]);
  const [workspaceFiles, setWorkspaceFiles] = useState([]);
  const [selectedMainDocName, setSelectedMainDocName] = useState("");
  const [flowType, setFlowType] = useState();

  useEffect(() => {
    const handleGeneratedFiles = (event) => {
      const { label } = event.detail;
      if (label) {
        setGeneratedFiles((prev) =>
          prev.includes(label) ? prev : [...prev, label]
        );
      }
    };
    window.addEventListener("CDB_PROGRESS_EVENT", handleGeneratedFiles);
    return () =>
      window.removeEventListener("CDB_PROGRESS_EVENT", handleGeneratedFiles);
  }, []);

  useEffect(() => {
    async function fetchFilesForSelection() {
      if (workspace?.id) {
        try {
          const localFilesData = await System.localFiles(workspace.id);
          if (localFilesData && localFilesData.items) {
            const extracted = extractFileNames(localFilesData.items);
            // Store names without .json for display, will add back when sending
            setWorkspaceFiles(
              extracted.map((name) => name.replace(".json", ""))
            );
          } else {
            setWorkspaceFiles([]);
          }
        } catch (error) {
          console.error(
            "Error fetching workspace files for main doc selection:",
            error
          );
          setWorkspaceFiles([]);
        }
      }
    }
    fetchFilesForSelection();
  }, [workspace]);

  // Check if user has admin, manager, or superuser role
  const hasAdminAccess =
    user?.role === "admin" ||
    user?.role === "manager" ||
    user?.role === "superuser";

  const handleFetchSubCategories = async (name) => {
    setLoadingSubCategories(true);

    try {
      // Use the System model which now uses our centralized store internally
      const response = await System.fetchSubCategories(name);

      if (response.success && response.data) {
        setSubCategories(response.data);
        setSelectedCategory({ name, subCategories: response.data });
      } else {
        console.error(
          "Error fetching sub-categories:",
          response?.error || "No data returned."
        );
        setSubCategories([]);
        setSelectedCategory({ name, subCategories: [] });
      }
    } catch (error) {
      console.error("Error in handleFetchSubCategories:", error);
      setSubCategories([]);
      setSelectedCategory({ name, subCategories: [] });
    } finally {
      setLoadingSubCategories(false);
    }
  };

  const handleCategorySelect = async (category) => {
    await handleFetchSubCategories(category.name);
  };

  const handleTaskSelect = (subCategory) => {
    setSelectedTask(subCategory);
    setSelectedMainDocName("");
  };

  const checkWorkspaceFiles = async (workspaceId) => {
    try {
      const localFiles = await System.localFiles(workspaceId);

      // Check if there are any JSON files in the workspace
      let hasFiles = false;

      if (localFiles && localFiles.items) {
        // Recursively check for files in all folders
        const checkFolderForFiles = (folder) => {
          if (folder.type === "file") {
            return true;
          }

          if (folder.items && folder.items.length > 0) {
            return folder.items.some((item) => {
              if (item.type === "file") {
                return true;
              }
              return (
                item.items && item.items.length > 0 && checkFolderForFiles(item)
              );
            });
          }

          return false;
        };

        hasFiles = localFiles.items.some((item) => checkFolderForFiles(item));
      }

      setHasWorkspaceFiles(hasFiles);
      return hasFiles;
    } catch (error) {
      console.error("Error checking workspace files:", error);
      return false;
    }
  };

  const handleConfirmTask = async () => {
    if (!selectedTask) return;

    // If task requires a main document, and none is selected, prevent confirmation
    if (selectedTask.requiresMainDocument && !selectedMainDocName) {
      console.warn("This task requires a main document to be selected.");
      return;
    }

    const hasFiles = await checkWorkspaceFiles(workspace.id);
    if (!hasFiles) {
      setShowNoFilesWarning(true);
      return;
    }

    const cdbOptions = [
      selectedTask?.legalPrompt || null,
      customInstructions || null,
      selectedMainDocName ? `${selectedMainDocName}.json` : null,
    ];

    // Initialize flowType based on whether this task needs a main document
    setFlowType(selectedTask.requiresMainDocument ? "main" : "noMain");

    try {
      setStepsProcess(true);
      let chatId = null;

      await sendCommand(selectedTask?.name, true, [], [], {
        preventNewChat: true,
        preventChatCreation: false,
        cdb: true,
        cdbOptions: cdbOptions,
        abortController: abortController,
        chatHandler: (chatResult) => {
          if (chatResult.type === "cdbProgress") {
            setFlowType(chatResult.flowType);
            window.dispatchEvent(
              new CustomEvent("CDB_PROGRESS_EVENT", {
                detail: chatResult,
              })
            );
          }
          if (
            chatResult.type === "finalizeResponseStream" &&
            chatResult.chatId
          ) {
            chatId = chatResult.chatId;
            window.dispatchEvent(
              new CustomEvent("CDB_STREAM_EVENT", {
                detail: chatResult,
              })
            );
          }
        },
        displayMessage: selectedTask?.name,
      });

      // Cleanup generated files after successful processing
      if (generatedFiles.length > 0) {
        try {
          await System.deleteDocuments(generatedFiles);
        } catch (error) {
          console.error("Error cleaning generated files:", error);
        }
      }

      handleProcessComplete();

      // Close the modal and trigger a refresh of the chat history
      setTimeout(function () {
        onClose();

        window.dispatchEvent(
          new CustomEvent("CDB_PROCESS_COMPLETE", {
            detail: { chatId: chatId },
          })
        );
      }, 1000); // Reduced to 1 second for faster feedback
    } catch (error) {
      console.error("Error during API call:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleProcessComplete = () => {
    setIsSubtaskSelected(true);
    setIsLoading(true);
  };

  const handleOpenSettings = () => {
    // Open the legal tasks settings modal
    setIsSettingsModalOpen(true);
  };

  const fetchGroupedLegalTasks = async () => {
    setLoading(true);

    // Use the System model which now uses our centralized store internally
    const response = await System.fetchGroupedLegalTasks();
    if (response.success) {
      setLegalTasks(response.data);
    } else {
      console.error("Error fetching grouped legal tasks:", response.error);
    }

    setLoading(false);
  };

  useEffect(() => {
    fetchGroupedLegalTasks();
  }, []);

  useEffect(() => {
    async function getWorkspace() {
      if (!slug) return;
      setLoading(true);

      try {
        const _workspace = await Workspace.bySlug(slug);
        if (!_workspace) {
          setLoading(false);
          return;
        }

        setWorkspace(_workspace);

        // Check if workspace has files when it's loaded
        await checkWorkspaceFiles(_workspace.id);
      } catch (error) {
        console.error("Error fetching workspace:", error);
      } finally {
        setLoading(false);
      }
    }

    getWorkspace();
  }, [slug]);

  const handleAbortTask = async () => {
    abortController.abort();
    try {
      await System.purgeDocumentBuilder();
    } catch (err) {
      console.error("Failed to purge CDB files on abort", err);
    }
    if (generatedFiles.length > 0) {
      try {
        await System.deleteDocuments(generatedFiles);
      } catch (error) {
        console.error("Error deleting generated files:", error);
      }
    }
    onClose();
  };

  if (showNoFilesWarning) {
    return (
      <Modal
        isOpen={true}
        onClose={() => setShowNoFilesWarning(false)}
        title={t("performLegalTask.warning-title")}
        className="w-[400px] max-w-[90vw]"
      >
        <div className="flex flex-col items-center justify-center p-4 text-center">
          <IoWarningOutline className="text-yellow-500 text-5xl mb-4" />
          <h4 className="text-lg font-medium text-foreground mb-2">
            {t("performLegalTask.no-files-title")}
          </h4>
          <p className="text-foreground text-opacity-80 mb-4">
            {t("performLegalTask.no-files-description")}
          </p>
          <Button onClick={() => setShowNoFilesWarning(false)}>
            {t("common.ok")}
          </Button>
        </div>
      </Modal>
    );
  }

  if (stepsProcess) {
    return (
      <div className="w-full flex flex-col">
        <p className="text-sm text-foreground text-opacity-60 mb-4 break-words">
          {t("performLegalTask.duration-info")}
        </p>
        <LegalProgress flowType={flowType} onComplete={handleProcessComplete} />
        <div className="flex justify-end mt-4">
          <Button onClick={handleAbortTask}>
            {t("common.cancel", "Abort")}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col">
      <div className="flex-1 overflow-y-auto">
        <p className="text-foreground text-opacity-60 mb-6 break-words">
          {t("performLegalTask.duration-info")}
        </p>

        {loading ? (
          <div className="flex items-center justify-center p-4">
            <AiOutlineLoading className="animate-spin text-blue-500 text-lg" />
            <span className="ml-2">
              {t("performLegalTask.loading-subcategory")}
            </span>
          </div>
        ) : selectedCategory ? (
          <div className="flex flex-col">
            <div className="flex items-center mb-4">
              <button
                onClick={() => setSelectedCategory(null)}
                className="flex items-center text-foreground hover:text-opacity-80"
              >
                <TbArrowNarrowLeft className="mr-2" />
                {t("performLegalTask.select-category")}
              </button>
            </div>
            {loadingSubCategories ? (
              <div className="flex items-center justify-center p-4">
                <AiOutlineLoading className="animate-spin text-blue-500 text-lg" />
                <span className="ml-2">
                  {t("performLegalTask.loading-subcategory")}
                </span>
              </div>
            ) : subCategories.length === 0 ? (
              <div className="text-center p-4">
                {t("performLegalTask.noSubtskfund")}
              </div>
            ) : (
              <>
                <h3 className="text-lg font-medium text-foreground mb-4">
                  {t("performLegalTask.choose-task")}
                </h3>
                <div className="space-y-4">
                  {subCategories.map((subCategory) => (
                    <div key={subCategory.name} className="relative">
                      <div className="relative">
                        {/* Use a div instead of a button when the task is selected */}
                        {selectedTask?.name === subCategory.name ? (
                          <div className="flex items-center justify-between w-full p-3 text-left rounded-lg transition-colors duration-200 border bg-secondary border-2 border-primary">
                            <div>
                              <span className="text-foreground">
                                {subCategory.name}
                              </span>
                              {subCategory.description && (
                                <p className="text-sm text-foreground text-opacity-60 mt-1">
                                  {subCategory.description}
                                </p>
                              )}
                            </div>
                          </div>
                        ) : (
                          <button
                            onClick={() => handleTaskSelect(subCategory)}
                            className="flex items-center justify-between w-full p-3 text-left rounded-lg transition-colors duration-200 border bg-secondary hover:bg-secondary-hover"
                          >
                            <div>
                              <span className="text-foreground">
                                {subCategory.name}
                              </span>
                              {subCategory.description && (
                                <p className="text-sm text-foreground text-opacity-60 mt-1">
                                  {subCategory.description}
                                </p>
                              )}
                            </div>
                            <MdArrowForwardIos className="text-foreground text-opacity-60" />
                          </button>
                        )}
                      </div>

                      {selectedTask?.name === subCategory.name && (
                        <div className="mt-2 p-4 bg-background rounded-lg border border-gray-200">
                          <textarea
                            value={customInstructions}
                            onChange={(e) =>
                              setCustomInstructions(e.target.value)
                            }
                            placeholder={t(
                              "performLegalTask.custom-instructions-placeholder"
                            )}
                            className="w-full p-2 text-sm border rounded-md bg-background text-foreground placeholder-foreground-muted"
                            rows="9"
                          />
                          {selectedTask?.requiresMainDocument &&
                            workspaceFiles.length > 0 && (
                              <div className="mt-4">
                                <label
                                  htmlFor="main-document-select"
                                  className="block text-sm font-medium text-foreground mb-1"
                                >
                                  {t(
                                    "performLegalTask.selectMainDocumentRequiredLabel",
                                    "Select Main Document (Required)"
                                  )}
                                </label>
                                <select
                                  id="main-document-select"
                                  value={selectedMainDocName}
                                  onChange={(e) =>
                                    setSelectedMainDocName(e.target.value)
                                  }
                                  className="w-full p-2 text-sm border rounded-md bg-background text-foreground"
                                  required
                                >
                                  <option value="" disabled>
                                    {t(
                                      "performLegalTask.mainDocumentMustSelect",
                                      "-- Select a document --"
                                    )}
                                  </option>
                                  {workspaceFiles.map((fileName) => (
                                    <option key={fileName} value={fileName}>
                                      {fileName}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            )}
                          <div className="mt-4 flex justify-end space-x-3">
                            <Button
                              onClick={() => {
                                setSelectedTask(null);
                                setCustomInstructions("");
                                setSelectedMainDocName("");
                              }}
                              variant="secondary"
                            >
                              {t("common.cancel")}
                            </Button>
                            <Button
                              onClick={handleConfirmTask}
                              disabled={
                                selectedTask?.requiresMainDocument &&
                                !selectedMainDocName
                              }
                            >
                              {t("common.confirmstart")}
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        ) : (
          <>
            <h3 className="text-lg font-medium text-foreground mb-4">
              {t("performLegalTask.select-category")}
            </h3>
            <div className="grid grid-cols-1 gap-4 pb-4 text-foreground">
              {legalTasks.length === 0 ? (
                <div className="text-center p-4">
                  {t("performLegalTask.noTaskfund")}
                </div>
              ) : (
                legalTasks.map((category, index) => (
                  <button
                    key={index}
                    className="text-left p-4 rounded-lg border transition-colors relative bg-secondary hover:bg-secondary-hover"
                    onClick={() => handleCategorySelect(category)}
                  >
                    <h4 className="font-medium text-foreground">
                      {category.name}
                    </h4>
                    <MdArrowForwardIos className="absolute right-4 top-1/2 transform -translate-y-1/2" />
                  </button>
                ))
              )}
            </div>
          </>
        )}
      </div>

      {/* Settings button for admin, manager, and superuser roles */}
      {hasAdminAccess && (
        <div className="mt-6 border-t border-gray-200 pt-4 flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={handleOpenSettings}
            className="flex items-center gap-2"
          >
            <FiSettings className="h-4 w-4" />
            {t("performLegalTask.settings-button")}
          </Button>
        </div>
      )}

      {/* Legal Tasks Settings Modal */}
      <LegalTasksSettingsModal
        isOpen={isSettingsModalOpen}
        onClose={() => {
          setIsSettingsModalOpen(false);
          // Refresh the categories list so newly added categories appear
          fetchGroupedLegalTasks();

          // If the user is already viewing a category, refresh its sub-categories
          if (selectedCategory?.name) {
            handleFetchSubCategories(selectedCategory.name);
            // Clear any previously selected task so the UI reflects fresh data
            setSelectedTask(null);
          }
        }}
      />
    </div>
  );
}
