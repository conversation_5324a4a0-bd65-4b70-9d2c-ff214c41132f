import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import { useTranslation } from "react-i18next";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useState, useEffect } from "react";
import Select from "@/components/Select";
import FormItem from "@/components/ui/FormItem";
import Workspace from "@/models/workspace";
import paths from "@/utils/paths";
import { useSetSelectedModule } from "@/stores/userStore";
import { useModuleWorkspaces } from "@/stores/workspaceStore";
import { LuArrowRight } from "react-icons/lu";

const SelectExistingWorkspaceModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const moduleWorkspaces = useModuleWorkspaces();
  const setSelectedModule = useSetSelectedModule();
  const [workspaces, setWorkspaces] = useState([]);
  const [navigating, setNavigating] = useState(false);

  const formSchema = z.object({
    selectedWorkspace: z
      .string()
      .optional()
      .nullable()
      .refine((value) => typeof value === "string" && value.trim().length > 0, {
        message: t("workspaceSelector.pleaseSelectWorkspace"),
      }),
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      selectedWorkspace: undefined,
    },
  });

  const onSubmit = async (data) => {
    setNavigating(true);
    try {
      const { thread, error } = await Workspace.threads.new(
        data.selectedWorkspace
      );

      if (error) {
        throw new Error(error);
      }

      const redirectUrl = paths.workspace.thread(
        data.selectedWorkspace,
        thread.slug
      );

      setSelectedModule("legal-qa");
      window.location.replace(redirectUrl);
    } catch (error) {
      console.error("Failed to create thread:", error);
      setNavigating(false);
    }
  };

  useEffect(() => {
    const options = moduleWorkspaces["legal-qa"].map((workspace) => ({
      value: workspace.slug,
      label: workspace.name,
    }));

    setWorkspaces(options);
  }, [moduleWorkspaces]);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t("workspaceSelector.selectWorkspaceTitle")}
      footer={
        <Button
          type="submit"
          form="select-existing-workspace-form"
          isLoading={navigating}
          disabled={workspaces.length === 0}
        >
          {t("workspaceSelector.next")}
          <LuArrowRight />
        </Button>
      }
      className="max-w-lg p-8"
    >
      <form
        id="select-existing-workspace-form"
        onSubmit={handleSubmit(onSubmit)}
      >
        <FormItem>
          <Controller
            name="selectedWorkspace"
            control={control}
            render={({ field }) => (
              <Select
                {...field}
                options={workspaces}
                placeholder={
                  workspaces.length === 0
                    ? t("workspaceSelector.noWorkspacesAvailable")
                    : t("workspaceSelector.selectWorkspacePlaceholder")
                }
                className="mt-1"
                onChange={(valueFromSelect) => {
                  field.onChange(valueFromSelect);
                }}
                disabled={workspaces.length === 0}
              />
            )}
          />
          {errors.selectedWorkspace && (
            <p className="pt-1 text-sm text-red-600">
              {errors.selectedWorkspace.message}
            </p>
          )}
        </FormItem>
      </form>
    </Modal>
  );
};

export default SelectExistingWorkspaceModal;
