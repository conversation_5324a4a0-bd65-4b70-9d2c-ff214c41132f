import { useLanguageOptions } from "@/hooks/useLanguageOptions";
import System from "@/models/system";
import Appearance from "@/models/appearance";
import { AuthContext } from "@/AuthContext";
import showToast from "@/utils/toast";
import Toggle from "@/components/ui/Toggle";
import { Info, X } from "@phosphor-icons/react";
import { useTranslation } from "react-i18next";
import { useState, useEffect, useContext } from "react";
import { Tooltip } from "react-tooltip";

export default function AccountModal({ user, hideModal }) {
  const { t } = useTranslation();
  const { store, actions } = useContext(AuthContext);

  const handleUpdate = async (e) => {
    e.preventDefault();

    const data = {};
    const form = new FormData(e.target);
    for (var [key, value] of form.entries()) {
      if (!value || value === null) continue;
      data[key] = value;
    }

    const { success, error } = await System.updateUser(data);
    if (success) {
      // Refresh the user data using AuthContext
      const refreshedUser = await System.getCurrentUser();
      if (refreshedUser) {
        actions.updateUser(refreshedUser, store.authToken);
      }
      showToast(t("show-toast.profile-updated"), "success", { clear: true });
      hideModal();
    } else {
      showToast(t("toast.errors.failed-update-user", { error }), "error");
    }
  };

  return (
    <div
      id="account-modal"
      className="bg-black/60 backdrop-blur-sm fixed z-10 top-0 left-0 outline-none w-screen h-screen flex items-center justify-center"
    >
      <div className="bg-background relative z-5 w-[500px] max-w-2xl max-h-full rounded-lg shadow">
        <div className="flex items-start justify-between py-2.5 px-4 modal-header">
          <h3 className="font-semibold text-foreground">
            {t("user-menu.edit")}
          </h3>
          <button
            onClick={hideModal}
            type="button"
            className="transition-all duration-300 bg-transparent rounded-lg text-sm p-1.5 ml-auto inline-flex items-center"
          >
            <X className="text-foreground text-lg" />
          </button>
        </div>
        <form onSubmit={handleUpdate} className="space-y-2 pt-2">
          <div className="flex flex-col gap-y-4 px-6 pb-4">
            <div className="">
              <label
                htmlFor="username"
                className="block mb-2 text-sm font-medium text-foreground"
              >
                {t("user-menu.email")}
              </label>
              <input
                name="username"
                type="email"
                placeholder={t("user-menu.email-placeholder")}
                minLength={2}
                defaultValue={user.username}
                disabled={user?.role != "admin" && user?.role != "manager"}
                required
                autoComplete="off"
                className="dark-input-mdl text-foreground w-full text-sm rounded-lg block p-2"
              />
              <p className="mt-2 text-[10px] text-foreground">
                {t("new-user.invalid-email")}
              </p>
            </div>
            <div className="">
              <label
                htmlFor="password"
                className="block mb-2 text-sm font-medium text-foreground"
              >
                {t("user-menu.new-password")}
              </label>
              <input
                name="password"
                type="text"
                placeholder={t("user-menu.new-password-placeholder")}
                className="dark-input-mdl w-full text-foreground text-sm rounded-lg block p-2"
                minLength={8}
              />
              <p className="mt-2 text-[10px] text-foreground">
                {t("user-setting.password-rule")}
              </p>
            </div>
            <LanguagePreference />
          </div>
          <div className="flex justify-end items-center pt-4 p-3 modal-footer">
            <button
              onClick={hideModal}
              type="button"
              className="text-sm px-4 py-2 rounded-lg text-foreground bg-transparent focus:outline-none"
            >
              {t("user-menu.cancel")}
            </button>
            <button
              type="submit"
              className="primary-bg text-white px-5 py-2 rounded-lg text-[13px]"
            >
              {t("user-menu.update")}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

function LanguagePreference() {
  const { t } = useTranslation();

  const {
    currentLanguage,
    supportedLanguages,
    getLanguageName,
    changeLanguage,
  } = useLanguageOptions();

  return (
    <div className="flex flex-col gap-y-4 pt-4">
      <label
        htmlFor="userLang"
        className="block mb-2 text-sm font-medium text-foreground"
      >
        {t("user-menu.language")}
      </label>
      <select
        className="dark-input-mdl w-full mt-2 px-4 text-foreground text-sm rounded-lg block py-2"
        name="userLang"
        defaultValue={currentLanguage || "en"}
        onChange={(e) => changeLanguage(e.target.value)}
      >
        {supportedLanguages.map((lang) => {
          return (
            <option key={lang} value={lang} className="">
              {getLanguageName(lang)}
            </option>
          );
        })}
      </select>
      <div className="flex flex-col gap-y-4">
        <AutoSubmitPreference />
        <AutoSpeakPreference />
      </div>
    </div>
  );
}

function AutoSubmitPreference() {
  const [autoSubmitSttInput, setAutoSubmitSttInput] = useState(true);
  const { t } = useTranslation();

  useEffect(() => {
    const settings = Appearance.getSettings();
    setAutoSubmitSttInput(settings.autoSubmitSttInput ?? true);
  }, []);

  const handleChange = (e) => {
    const newValue = e.target.checked;
    setAutoSubmitSttInput(newValue);
    Appearance.updateSettings({ autoSubmitSttInput: newValue });
  };

  return (
    <div>
      <div className="flex items-center gap-x-1 mb-2">
        <label
          htmlFor="autoSubmit"
          className="block text-sm font-medium text-foreground"
        >
          {t("chat-ui-settings.auto_submit.title")}
        </label>
        <div
          data-tooltip-id="auto-submit-info"
          data-tooltip-content={t("chat-ui-settings.auto_submit.description")}
          className="cursor-pointer h-fit"
        >
          <Info size={16} weight="bold" className="text-foreground" />
        </div>
      </div>
      <div className="flex items-center gap-x-4">
        <Toggle
          id="autoSubmit"
          name="autoSubmit"
          checked={autoSubmitSttInput}
          onChange={handleChange}
        />
      </div>
      <Tooltip
        id="auto-submit-info"
        place="bottom"
        delayShow={300}
        className="allm-tooltip !allm-text-xs"
      />
    </div>
  );
}

function AutoSpeakPreference() {
  const [autoPlayAssistantTtsResponse, setAutoPlayAssistantTtsResponse] =
    useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    const settings = Appearance.getSettings();
    setAutoPlayAssistantTtsResponse(
      settings.autoPlayAssistantTtsResponse ?? false
    );
  }, []);

  const handleChange = (e) => {
    const newValue = e.target.checked;
    setAutoPlayAssistantTtsResponse(newValue);
    Appearance.updateSettings({ autoPlayAssistantTtsResponse: newValue });
  };

  return (
    <div>
      <div className="flex items-center gap-x-1 mb-2">
        <label
          htmlFor="autoSpeak"
          className="block text-sm font-medium text-foreground"
        >
          {t("chat-ui-settings.auto_speak.title")}
        </label>
        <div
          data-tooltip-id="auto-speak-info"
          data-tooltip-content={t("chat-ui-settings.auto_speak.description")}
          className="cursor-pointer h-fit"
        >
          <Info size={16} weight="bold" className="text-foreground" />
        </div>
      </div>
      <div className="flex items-center gap-x-4">
        <Toggle
          id="autoSpeak"
          name="autoSpeak"
          checked={autoPlayAssistantTtsResponse}
          onChange={handleChange}
        />
      </div>
      <Tooltip
        id="auto-speak-info"
        place="bottom"
        delayShow={300}
        className="allm-tooltip !allm-text-xs"
      />
    </div>
  );
}
