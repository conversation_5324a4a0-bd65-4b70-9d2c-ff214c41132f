import React, { forwardRef, useEffect, useState, useId } from "react";
import { cn } from "@/utils/classes";

const variants = {
  default: {
    border: "border-white",
    checkmark: "text-white",
    focusRing:
      "focus:ring-white/50 focus:ring-offset-1 focus:ring-offset-gray-800",
  },
  primary: {
    border: "border-primary",
    checkmark: "text-primary",
    focusRing: "focus:ring-primary focus:ring-offset-2",
  },
};

const sizes = {
  sm: "w-4 h-4",
  md: "w-5 h-5",
  lg: "w-6 h-6",
};

const svgSizes = {
  sm: "w-3 h-3",
  md: "w-4 h-4",
  lg: "w-5 h-5",
};

const Checkbox = forwardRef(
  (
    {
      className,
      variant = "default",
      size = "md",
      checked,
      defaultChecked,
      label,
      id: idProp,
      value,
      disabled,
      onChange,
      onCheckedChange,
      readOnly,
      ...props
    },
    ref
  ) => {
    // Generate a stable id if none provided
    const generatedId = useId();
    const checkboxId = idProp || generatedId;

    // Use internal state if no onChange is provided
    const [internalChecked, setInternalChecked] = useState(
      defaultChecked || checked || false
    );

    // Update internal state if checked prop changes
    useEffect(() => {
      if (checked !== undefined) {
        setInternalChecked(checked);
      }
    }, [checked]);

    // Determine if component is controlled
    const isControlled = checked !== undefined;
    const effectiveChecked = isControlled ? checked : internalChecked;

    const handleChange = (e) => {
      if (disabled || readOnly) return;

      const newChecked = e.target.checked;
      if (!isControlled) {
        setInternalChecked(newChecked);
      }

      if (onChange) {
        onChange(newChecked, e);
      }

      if (onCheckedChange) {
        onCheckedChange(newChecked);
      }
    };

    const variantConfig = variants[variant] || variants.default;

    return (
      <label
        className={cn(
          "relative inline-flex items-center gap-2",
          disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer",
          className
        )}
        htmlFor={checkboxId}
      >
        <div className="relative flex items-center justify-center">
          <input
            ref={ref}
            type="checkbox"
            className={cn(
              "appearance-none border rounded !bg-transparent focus:outline-none focus:ring-2 cursor-pointer",
              variantConfig.border,
              variantConfig.focusRing,
              sizes[size],
              disabled && "pointer-events-none cursor-not-allowed"
            )}
            checked={effectiveChecked}
            disabled={disabled}
            readOnly={readOnly}
            onChange={handleChange}
            id={checkboxId}
            value={value}
            {...props}
          />
          {effectiveChecked && (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={cn(
                "absolute pointer-events-none",
                svgSizes[size],
                variantConfig.checkmark
              )}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={3}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M5 13l4 4L19 7"
              />
            </svg>
          )}
        </div>
        {label && (
          <span
            className={cn("text-foreground", disabled && "cursor-not-allowed")}
          >
            {label}
          </span>
        )}
      </label>
    );
  }
);

Checkbox.displayName = "Checkbox";
export default Checkbox;
