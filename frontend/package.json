{"name": "ist-legal-frontend", "private": false, "license": "Copyright", "type": "module", "scripts": {"start": "node scripts/copyPdfWorker.js && vite --open", "dev": "node scripts/copyPdfWorker.js && cross-env NODE_ENV=development vite --debug --host=0.0.0.0", "build": "node scripts/copyPdfWorker.js && vite build && node scripts/postbuild.js", "lint": "npx prettier --ignore-path ../.prettierignore --check ./src && npx eslint ./src package.json --quiet", "preview": "vite preview", "copy-pdf-worker": "node scripts/copyPdfWorker.js", "test": "jest --config=jest.config.cjs", "test:watch": "jest --watch --config=jest.config.cjs", "test:coverage": "jest --coverage --config=jest.config.cjs"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@microsoft/fetch-event-source": "^2.0.1", "@mintplex-labs/piper-tts-web": "^1.0.4", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/highlight": "^3.12.0", "@react-pdf-viewer/search": "^3.12.0", "@react-pdf/renderer": "^3.4.4", "@tremor/react": "^3.15.1", "@vunamhung/react-pdf-highlighter": "^8.1.2", "axios": "^1.7.2", "change-case": "^5.4.4", "dompurify": "^3.0.8", "dotenv": "^16.4.5", "file-saver": "^2.0.5", "he": "^1.2.0", "highlight.js": "^11.9.0", "i18next": "^23.12.1", "i18next-browser-languagedetector": "^7.2.1", "immer": "^10.1.1", "immutability-helper": "^3.1.1", "install": "^0.13.0", "js-levenshtein": "^1.1.6", "js-tiktoken": "^1.0.16", "katex": "^0.16.11", "lodash.debounce": "^4.0.8", "markdown-it": "^13.0.1", "markdown-it-katex": "^2.0.3", "moment": "^2.30.1", "onnxruntime-web": "^1.18.0", "pdfjs-dist": "3.11.174", "pluralize": "^8.0.0", "react": "^18.2.0", "react-color": "^2.19.3", "react-color-picker": "^4.0.2", "react-device-detect": "^2.2.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.56.1", "react-i18next": "^14.1.3", "react-icons": "^5.4.0", "react-loading-skeleton": "^3.4.0", "react-markdown": "^9.0.1", "react-modal": "^3.16.1", "react-pdf": "^9.1.0", "react-pdf-highlighter": "^7.0.0", "react-router-dom": "^6.3.0", "react-speech-recognition": "^3.10.0", "react-syntax-highlighter": "^15.5.0", "react-tag-input-component": "^2.0.2", "react-toastify": "^9.1.3", "react-tooltip": "^5.25.2", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.10", "recharts": "^2.12.5", "recharts-to-png": "^2.3.1", "regenerator-runtime": "^0.14.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "truncate": "^3.0.0", "uuid": "^9.0.0", "zod": "^3.24.3", "zustand": "^5.0.3"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@esbuild-plugins/node-globals-polyfill": "^0.1.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/react": "^18.2.23", "@types/react-dom": "^18.2.8", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.14", "babel-jest": "^29.7.0", "buffer": "^6.0.3", "cross-env": "^7.0.3", "eslint": "^9.25.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-ft-flow": "^3.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "eslint-plugin-unused-imports": "^4.1.4", "flow-bin": "^0.217.0", "flow-remove-types": "^2.217.1", "globals": "^13.21.0", "hermes-eslint": "^0.15.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.23", "prettier": "^3.0.3", "rollup-plugin-visualizer": "^5.9.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "vite": "^6.0.11"}}