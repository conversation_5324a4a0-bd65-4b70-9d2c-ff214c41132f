# Locale Scripts

This document describes the scripts available for managing and verifying locale translations in the frontend.

## Overview

- **fixLocaleStructure.mjs**: Reorders translation keys in locale files to match the English template, preserves comments, and applies Prettier formatting.
- **verifyLocaleFiles.mjs**: Checks that all locale files are present and consistent with English, reporting missing, extra, or out-of-order keys.

---

## `fixLocaleStructure.mjs`

**Location:** `scripts/fixLocaleStructure.mjs`

**Purpose:**
Ensures that all locale translation files under `frontend/src/locales` have the same object key structure and ordering as the English files, and preserves comments during transformation.

**Key features:**

- Uses AST-based reordering with [recast](https://github.com/benjaminp/screencast) and Babel parser to preserve comments.
- Automatically runs `npx prettier --write` on locale files after transformation.

### Usage

```bash
node scripts/fixLocaleStructure.mjs
```

You can also add an npm script for convenience:

```json
"scripts": {
  "fix:locales": "node scripts/fixLocaleStructure.mjs"
}
```

---

## `verifyLocaleFiles.mjs`

**Location:** `scripts/verifyLocaleFiles.mjs`

**Purpose:**
Verifies that all locale files in `frontend/src/locales/{lang}` (excluding `__tests__`) are consistent with the English locale files in terms of keys and key ordering.

**Behavior:**

- Compares keys recursively between English and each target locale.
- Reports missing, extra, and order mismatches.
- Exits with a non-zero code if any errors are found.

### Usage

```bash
node scripts/verifyLocaleFiles.mjs
```

You can also add an npm script for convenience:

```json
"scripts": {
  "verify:locales": "node scripts/verifyLocaleFiles.mjs"
}
```

---

## Integration in CI / Pre-PR Checks

Include these commands in your CI pipeline or pre-PR checks to ensure locale consistency:

```bash
npm run fix:locales
npm run verify:locales
```
