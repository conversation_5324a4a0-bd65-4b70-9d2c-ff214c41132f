# Legal Task Prompt Generation

## Overview

The legal task prompt generation feature allows users to automatically generate high-quality prompts for legal tasks based on the task name and description. This feature is available in both the Create New Legal Task and Edit Legal Task modals, as well as in the Ask Legal Question modal.

## Implementation

The feature consists of:

1. A "Generate Prompt" button that appears when a task name or subcategory is entered
2. An API call to the LLM to generate a prompt based on the task name and description
3. A loading state while the prompt is being generated
4. Automatic insertion of the generated prompt into the legal prompt field

## Usage

### In Create New Legal Task Modal

1. Enter a category name or select an existing category
2. Enter a subcategory name (legal task name)
3. Optionally enter a description
4. Click the "Generate Prompt" button
5. The system will generate a prompt based on the entered information and insert it into the Legal Task Prompt field

### In Edit Legal Task Modal

1. Modify the subcategory name (legal task name) if needed
2. Optionally modify the description
3. Click the "Generate Prompt" button
4. The system will generate a prompt based on the current information and insert it into the Legal Task Prompt field

### In Ask Legal Question Modal

1. Select a legal task category
2. Choose a specific legal task from the subcategory list
3. In the custom instructions section, click the "Generate Prompt" button
4. The system will generate a prompt based on the selected task and insert it into the custom instructions textarea

## Technical Details

### API Endpoint

The feature uses the `/generate-legal-task-prompt` endpoint, which is implemented in `server/endpoints/generateLegalTaskPrompt.js`. This endpoint:

1. Takes a task description and an optional prompt template
2. Uses the system's base LLM to generate a prompt
3. Returns the generated prompt

### Prompt Template

The prompt template includes:

1. Context about the Case Document Builder (CDB) system. This documentation is now always fetched from a dedicated server endpoint (`/system/cdb-documentation`) which provides a comprehensive overview (from `streamCDB.md`), details on specific drafting flows (from `legal_drafting_flows.md`), and includes a list of all default prompts used in CDB flows.
2. An explicit statement indicating the intended CDB flow (e.g., "This legal task will utilize the **Main Document Flow**..."), based on the `requiresMainDocument` flag.
3. A dynamically generated list of "Current document builder prompt templates" relevant to the active flow. This list is constructed by:
   a. The `buildCDBPromptTemplate` utility calling `System.getDocumentBuilder({ flowType })` (where `flowType` is `'mainDoc'` or `'noMainDoc'`).
   b. The backend API `/system/get-document-builder` returning a `promptFlowStructure` array (defining which prompts and their labels to show for the given `flowType`) and the resolved values for all prompts.
   c. `buildCDBPromptTemplate` then iterating this structure to display only the relevant prompts (e.g., "SELECT MAIN DOCUMENT" prompts only appear for the mainDoc flow; shared prompts like "DOCUMENT SUMMARY", "DOCUMENT RELEVANCE", "SECTION DRAFTING", and "SECTION LEGAL ISSUES IDENTIFICATION" always appear) and their current values (custom or default), with flow-specific labels for items like "Topics & Sections".
4. The task description and any additional context
5. Instructions for generating a clear, specific, and well-structured prompt

### Integration

The feature is integrated into:

1. `frontend/src/components/Modals/CreateNewLegalTask/index.jsx`
2. `frontend/src/components/Modals/EditLegalTask/index.jsx`
3. `frontend/src/components/Modals/AskLegalQuestion/index.jsx`

Both components use the `System.generateLegalTaskPrompt` method from `frontend/src/models/system.js` to make the API call.

The Ask Legal Question modal integration includes:
- Import of the `LegalTaskPromptGenerator` component
- A `handlePromptGenerated` function that updates the custom instructions textarea
- Proper handling of the `requiresMainDocument` prop using nullish coalescing (`??`) to ensure the prompt generator's visibility condition is met

## Internationalization

The feature includes the following translation keys:

```javascript
"generate-prompt": "Generate Prompt",
"generate-prompt-success": "Prompt generated successfully",
"generate-prompt-error": "Please enter a name or subcategory first",
```

These keys are available in both the `document-builder.create-task` and `document-builder.edit-task` sections of the translation files.

## UI/UX Considerations

1. The button only appears when there is text in the name/subcategory field
2. The button shows a loading spinner while generating the prompt
3. Success and error messages are displayed as toasts
4. The generated prompt is automatically resized to fit the textarea
