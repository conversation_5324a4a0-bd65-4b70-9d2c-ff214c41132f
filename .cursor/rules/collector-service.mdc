---
description: Guidelines for the Collector service
globs: collector/**/*.js
alwaysApply: false
---

# Collector Service

## Purpose

The Collector service is responsible for ingesting and processing user-uploaded documents from various sources:

- Local files (PDF, DOCX, XLSX, MBOX, EPub, text, audio, etc.)
- Remote links (websites, GitHub repos, GitLab repos, YouTube transcripts, Confluence pages)
- Raw text input

## Architecture

The Collector service is designed as a standalone service that communicates with the main server. It follows a modular architecture with specialized converters for different file types and sources.

```
collector/
├── index.js                 # Main entry point
├── processSingleFile/       # File processing logic
│   ├── index.js             # Orchestration
│   └── convert/             # File type converters
├── processLink/             # Link processing logic
│   ├── index.js             # Orchestration
│   └── convert/             # Link type converters
├── processRawText/          # Raw text processing
│   └── index.js             # Text processing logic
├── extensions/              # Extension system
│   ├── index.js             # Extension registration
│   ├── github/              # GitHub connector
│   ├── youtube/             # YouTube connector
│   └── resync/              # Resync functionality
└── utils/                   # Shared utilities
    ├── files/               # File operations
    ├── tokenizer/           # Text tokenization
    ├── comKey/              # Communication key
    └── EncryptionWorker/    # Encryption utilities
```

## High-Level Flow

1. **Upload or Provide Link**: User sends a request with a file path (`/process`), a link (`/process-link`), or raw text (`/process-text`)
2. **Validation**: Request is validated for integrity via `verifyIntegrity.js`
3. **Extraction & Conversion**: Content is extracted and converted to text by specialized converters
4. **Tokenization & Metadata**: Text is tokenized and metadata is added
5. **Storage**: Completed JSON is stored in the server's storage

## Key Components

### File Processing

- `processSingleFile/index.js`: Orchestrates single-file processing
- `processSingleFile/convert/*.js`: Specialized converters for different file types (PDF, DOCX, XLSX, etc.)
- Handles chunking, metadata extraction, and output generation
- Supports various file types including documents, emails, audio, images, and code files

### Link Processing

- `processLink/index.js`: Handles link processing and scraping
- `processLink/convert/*.js`: Specialized converters for different link types (generic, GitHub, YouTube, etc.)
- Implements web scraping, API integration, and content extraction
- Handles authentication, rate limiting, and error handling for external sources

### Raw Text Processing

- `processRawText/index.js`: Handles raw text input
- Performs text analysis, formatting, chunking, and metadata generation
- Supports various text formats and structures
- Optimizes processing for different content types

### Extension System

- `extensions/index.js`: Registers routes for special connectors
- `extensions/github/`: Processes GitHub repositories and files
- `extensions/youtube/`: Processes YouTube video transcripts
- `extensions/confluence/`: Processes Confluence pages and spaces
- `extensions/resync/`: Handles document resyncing for watched sources

### Utilities

- `utils/files/index.js`: File operations (writing, copying, deleting)
- `utils/tokenizer/index.js`: Text tokenization
- `utils/comKey/index.js`: Communication key for verification
- `utils/EncryptionWorker/index.js`: Encryption and decryption
- `utils/cache.js`: Caching utilities
- `utils/rate-limiter.js`: Rate limiting utilities

## Server Integration

- Communication via REST API with standardized request/response formats
- Authentication using communication key and HMAC signatures
- File transfer via multipart/form-data
- Error handling with standardized error responses

## Best Practices

### General

- Follow the established patterns for file and link processing
- Use the appropriate converter for each file type
- Handle errors gracefully and provide meaningful error messages
- Consider performance implications of large file processing
- Implement proper validation and security checks
- Document complex processing logic
- Test with various file types and sizes

### Security

- Validate all input parameters
- Sanitize URLs and other inputs
- Implement proper error handling
- Prevent injection attacks
- Securely store API keys and tokens
- Respect API rate limits

### Performance

- Implement caching for API responses
- Use streaming for large content
- Process content in chunks
- Optimize resource usage
- Clean up temporary files

### Documentation

- Document new file converters, link handlers, or extensions
- Update documentation when changing processing flow or output format
- Provide examples for complex functionality
- Document configuration options and environment variables
