---
description: Guidelines for document processing and RAG implementation
globs: server/**/*.js,collector/**/*.js
alwaysApply: false
---
# Document Processing and RAG Implementation

## Document Ingestion

### Supported Document Types

- **Document Files**: PDF, DOCX, XLSX, PPTX, TXT, MD, RTF, HTML, XML, JSON, EPUB
- **Email Files**: MBOX, EML
- **Media Files**: Audio (MP3, WAV, M4A) and Video (MP4, AVI) via transcription
- **Code Files**: Various programming languages
- **Archive Files**: ZIP, TAR (contents extracted and processed)
- **Remote Sources**: Web pages, GitHub repositories, YouTube videos, Confluence pages
- **Raw Text**: Direct text input

### Ingestion Process

- The Collector service handles document ingestion from various sources
- Documents are converted to a standardized text format
- Metadata is extracted and stored with the document content
- Large documents are split into manageable chunks

## Document Processing Flow

1. **Upload or Provide Link/Text**: User sends a request with a file, link, or raw text
2. **Validation**: Request is validated for integrity and format
3. **Extraction & Conversion**: Content is extracted and converted to text using specialized converters
4. **Text Processing**: Text is cleaned, normalized, and structured
5. **Chunking**: Large content is split into manageable chunks using semantic or size-based strategies
6. **Metadata Extraction**: Document metadata and processing metadata are collected
7. **Output Generation**: Standardized JSON output is created
8. **Storage**: Completed JSON is stored in the server's storage

## Chunking Strategies

- **Semantic Chunking**: Respects document structure (paragraphs, sections)
- **Size-Based Chunking**: Ensures chunks don't exceed token limits
- **Hybrid Chunking**: Combines semantic and size-based approaches
- **Specialized Chunking**: Table chunking, code chunking, conversation chunking

## Output Format

```json
{
  "metadata": {
    "title": "Document Title",
    "author": "Document Author",
    "created_at": "2023-01-01T00:00:00Z",
    "source_type": "pdf",
    "language": "en",
    "processed_at": "2023-03-01T00:00:00Z",
    "token_count_estimate": 1500
  },
  "chunks": [
    {
      "id": "chunk-1",
      "content": "Chunk content...",
      "token_count_estimate": 250,
      "metadata": {
        "page": 1,
        "section": "Introduction"
      }
    }
    // Additional chunks...
  ]
}
```

## Vector Database Integration

- Multiple vector database providers are supported (Pinecone, Qdrant, Weaviate, LanceDB, etc.)
- Document text is embedded using various embedding models
- Vector search is used to retrieve relevant documents for RAG
- Search modes (similarity, MMR) can be configured
- Results are ranked and filtered based on relevance

## RAG Implementation

- Retrieved documents are used to augment LLM prompts
- Context windows are managed to stay within token limits
- Sources are tracked and can be displayed to users
- Document similarity thresholds can be configured
- Context backfilling maintains conversation coherence

## Watched Document Synchronization

- External documents can be watched for changes
- Background jobs periodically check for updates
- Changed documents are re-processed and re-embedded
- This ensures RAG queries use the most up-to-date information
- Sync frequency can be configured per document

## Legal Document Handling

- Special handling for legal documents
- Detection of legal content based on filename patterns and content analysis
- Support for legal document templates and formatting
- Integration with document drafting workflows
- Preservation of document structure and formatting

## Dual-Flow Document Drafting (`streamChatWithWorkspaceCDB`)

The `streamChatWithWorkspaceCDB` function and its underlying modules implement a sophisticated, multi-step process for drafting legal documents. This system now operates using a dual-flow architecture, managed by `server/utils/chats/flowDispatcher.js`.

### Core Modules:
-   **`server/utils/chats/streamCDB.js`**: Main entry point for these drafting tasks.
-   **`server/utils/chats/flowDispatcher.js`**: Routes to the appropriate flow based on `flowType`.
-   **`server/utils/chats/flows/mainDoc.js`**: Handles drafting when a main document is specified.
-   **`server/utils/chats/flows/noMainDoc.js`**: Handles drafting when no main document is specified.
-   **`server/utils/chats/helpers/documentProcessing.js`**: Contains shared logic for document analysis, section generation, LLM-based drafting steps, etc.
-   **`server/utils/chats/prompts/legalDrafting.js`**: Centralizes prompt templates for consistency and i18n.

### Flow Determination (`flowType`)

The choice between the two flows is determined by the `flowType` parameter, which is set based on the presence of `mainDocName` when `streamChatWithWorkspaceCDB` is invoked:

-   **`flowType: "main"`**:
    -   **Trigger**: `mainDocName` is provided.
    -   **Module**: `flows/mainDoc.js` is executed.
    -   **Logic**: The drafting process is centered around the `mainDocName`. For example, the initial list of document sections is typically derived from the content of this main document.
-   **`flowType: "noMain"`**:
    -   **Trigger**: `mainDocName` is *not* provided.
    -   **Module**: `flows/noMainDoc.js` is executed.
    -   **Logic**: Suited for tasks without a single central document (e.g., reviewing multiple files, generating a report from various sources). The initial section list is typically generated based on summaries of all relevant documents and the overall legal task description.

### Key Differences & Guidance:

-   **Main Document Flow (`mainDoc.js`)**: Use when the legal task inherently revolves around a primary document that will form the backbone of the output. The flow leverages this document for structure and initial content.
-   **No Main Document Flow (`noMainDoc.js`)**: Use for broader analytical tasks, multi-document summaries, or when a new document structure needs to be created thematically from multiple sources rather than a single existing one.

Both flows utilize common helpers from `documentProcessing.js` for many steps like document description, relevance checking, legal issue identification, memo generation (via `legalMemo.js`), and section-by-section drafting. The primary divergence is in how the initial document structure (section list) is established and which documents primarily inform that structure.

Developers working on or extending these drafting capabilities should familiarize themselves with these core modules and the distinct logic paths for each `flowType`. Ensure that any new shared functionality is added to `helpers/documentProcessing.js` and new prompts to `prompts/legalDrafting.js` where appropriate.
