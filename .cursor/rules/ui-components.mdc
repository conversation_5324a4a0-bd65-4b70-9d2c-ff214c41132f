---
description: Rule for instructing on how to implement different standard ui components
globs:
alwaysApply: false
---
# UI Components

## Standard UI Components

Always use standard UI components from the `frontend/src/components/ui/` directory when available:

- **Toggle**
  - Import: `import Toggle from "@/components/ui/Toggle"`
  - Use for boolean toggles. Supports controlled (`checked`) and uncontrolled (`defaultChecked`) modes, with `onCheckedChange` callback.
  - Props:
    - `label` (string): displayed next to the toggle
    - `description` (string): text below the label
    - `disabled` (boolean): disable interaction
    - `size` ("sm" | "md" | "lg"): size variants
    - `labelPosition` ("left" | "right"): position of label
  - Accessibility: automatically sets `aria-labelledby` and `aria-describedby` when `label` or `description` are provided.

- **Modal**
  - Import: `import Modal from "@/components/ui/Modal"`
  - Use for dialogs with focus trapping and header/footer support. Control via `useModal` hook (`isOpen`, `onOpen`, `onClose`).

- **Slider**
  - Import: `import Slider from "@/components/ui/Slider"`
  - Use for selecting numeric ranges. Supports `min`, `max`, `step`, `value`/`defaultValue`, and `onValueChange` callback.

- **Checkbox**
  - Import: `import Checkbox from "@/components/ui/Checkbox"`
  - Use for boolean options. Supports controlled/uncontrolled modes and `onCheckedChange` callback, with optional `label` and `description` props.

- **Markdown**
  - Import: `import Markdown from "@/components/ui/Markdown"`
  - Use for safe rendering of markdown content. Pass markdown source as children or via `content` prop.

- **TabButton**
  - Import: `import TabButton from "@/components/ui/TabButton"`
  - Use for individual tab labels. Manage active state and click handling via parent `Tabs` component.

- **Tabs**
  - Import: `import Tabs from "@/components/ui/Tabs"`
  - Use to group `TabButton` elements and render corresponding content panels.

- **MetricsToggle**
  - Import: `import MetricsToggle from "@/components/ui/MetricsToggle"`
  - Use for toggling between metric views in dashboards or charts.

- **TextSizeSelector**
  - Import: `import TextSizeSelector from "@/components/ui/TextSizeSelector"`
  - Use for selecting and previewing text size options.

- **Textarea**
  - Import: `import Textarea from "@/components/ui/Textarea"`
  - Use for multi-line text input. Integrates with `react-hook-form` via `register`.

- **Input**
  - Import: `import Input from "@/components/ui/Input"`
  - Use for single-line text input. Integrates with `react-hook-form` via `register`.

- **Label**
  - Import: `import Label from "@/components/ui/Label"`
  - Use for form field labels. Pass `htmlFor` to link with form controls.

- **FormItem**
  - Import: `import FormItem from "@/components/ui/FormItem"`
  - Use to wrap form fields with consistent layout, including label, input, and error message.

- **DocxImportButton**
  - Import: `import DocxImportButton from "@/components/ui/DocxImportButton"`
  - Use for triggering .docx file import workflows with built-in styling and interactions.

- **MarqueeText**
  - Import: `import MarqueeText from "@/components/ui/MarqueeText"`
  - Use for scrolling overflow text on hover. Configure via `maxLength` and `variant` props.

## Best Practices

- Ensure all components are accessible: keyboard navigable and use proper ARIA attributes.
- Use translation keys for `label` and `description` text; never hardcode strings.
- Prefer controlled components for predictable state management, or use React state/hooks when uncontrolled.
- Wrap form controls in `FormItem` and pair with `Label` for consistent styling and error display.
- Do not apply custom styles that break built-in focus rings or layout assumptions.
- Test UI components with React Testing Library, covering interaction, accessibility, and variant rendering.
