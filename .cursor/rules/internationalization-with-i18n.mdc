---
description: Guidelines for internationalization (i18n) and translation handling
globs: "**/*.{js,jsx,ts,tsx}"
alwaysApply: true
---
# Internationalization (i18n) Guidelines

## Core Principles
- NEVER use hardcoded text strings for user interface elements
- ALWAYS use translation keys for ALL user-facing text
- NEVER replace existing translation keys with hardcoded text
- When adding new features or modifying existing ones, ensure ALL text is internationalized
- Add translation keys to ALL locale files, not just the English version

## Frontend Translation Keys
- Translation keys are located in feature-specific files in `frontend/src/locales/[lang]/`
- DO NOT use or create translation.json files
- Always use existing keys when possible
- Add new keys to the appropriate section or file
- Use the English translation as fallback together with the proper translation key
- Use singular form for internationalization keys (e.g., CONTENT_EXAMPLE instead of CONTENT_EXAMPLES)

## Backend Translation Keys
- Server and collector files have specific existing files for i18n keys
- Add new translation keys to the appropriate locale files
- Ensure error messages and system messages are internationalized

## Supported Languages
- English (en) - Default
- French (fr)
- Swedish (sv)
- Ki<PERSON>rwanda (rw)
- German (de)
- Norwegian (no)
- Polish (pl)

## Special Considerations
- Internationalize content example tags with language-specific versions
- When English is set as system language, apply English as the default language
- Make language selector half-width in modals
- Code that checks for content example tags should use localized tag values from i18n

## Common Mistakes to Avoid
- Using hardcoded strings like "Save", "Cancel", "Submit", etc. instead of translation keys
- Adding placeholder text with comments like `// Placeholder` that gets forgotten during development
- Using hardcoded error messages or success messages in toast notifications
- Forgetting to internationalize dynamic content (e.g., form validation messages)
- Adding new translation keys to only one locale file instead of all supported languages
- Using template literals with hardcoded text instead of translation keys with variables

## Best Practices for Implementation
- Use the `useTranslation` hook in React components to access translation functions
- For conditional text, use translation keys with fallbacks: `t("custom-key", t("fallback-key"))`
- For dynamic content, use translation keys with variables: `t("key", { variable: value })`
- When adding new features, create all necessary translation keys before implementing the UI
- Run linting checks to identify hardcoded strings before committing code
- Review PRs specifically for internationalization compliance
- When fixing bugs, check if any hardcoded text was introduced and replace with translation keys

## Organizing Translation Keys in Multiple Files

To improve maintainability, you can split translation keys into multiple files under each locale directory (e.g., `common.js`, `recentUploads.js`, `sidebar.js`, `legalTemplates.js`). The static loader in `resources.js` automatically imports and merges all files for each language.

### File Naming Conventions
- Filenames should reflect the feature or component (e.g., `recentUploads.js`, `sidebar.js`, `legalTemplates.js`).
- Filenames can be arbitrary; the loader doesn't depend on filenames, only on content.

### Directory Structure Example
```text
frontend/src/locales/
  en/
    common.js
    recentUploads.js
    sidebar.js
    legalTemplates.js
  fr/
    common.js
    recentUploads.js
    sidebar.js
    legalTemplates.js
```

### Migration Steps
1. Identify related keys in `common.js` by feature or component.
2. Create a new file in each locale directory (e.g., `recentUploads.js`, `legalTemplates.js`).
3. Move the relevant key-value pairs from `common.js` into the new file.
4. Run tests to verify bundles include the new keys.
5. Remove migrated keys from `common.js` and delete any empty files.

### Adding New Locale Files
When adding support for a new language:
1. Create a new `[name≈].js` file in the new language's locale directory
2. Copy the structure from the English version
3. Translate all strings while maintaining the exact same key structure
4. Verify that all keys from the English version exist in the new locale file

## Document Builder Prompts Internationalization

When adding or modifying prompts for the Document Builder page (defined in `server/utils/chats/prompts/legalDrafting.js` and displayed in `frontend/src/pages/GeneralSettings/DocumentBuilder/index.jsx`), specific attention must be paid to internationalization:

1.  **Identify UI Text**: Determine which parts of your prompt configuration in `exportedLegalPrompts` will be displayed as UI text. This includes:
    *   Group titles (from `GROUP_TITLE` entries, using their `defaultContent` as English text for the `label` key).
    *   Group descriptions (from `GROUP_DESCRIPTION` entries, using their `defaultContent` as English text for the `label` key).
    *   Individual prompt labels/titles (from `SYSTEM_PROMPT`, `USER_PROMPT`, `PROMPT_TEMPLATE` entries, using their `label` field as the key and `defaultContent` as the English text).
    *   Individual prompt help text/descriptions (from `SYSTEM_PROMPT`, `USER_PROMPT`, `PROMPT_TEMPLATE` entries, using their `description` field as the key).

2.  **Define Translation Keys in `legalDrafting.js`**:
    *   For `GROUP_TITLE` and `GROUP_DESCRIPTION` objects in `exportedLegalPrompts`:
        *   The `label` field (e.g., `"document-builder.prompts.group.document_summary.title"`) **is the translation key**.
        *   The `defaultContent` field (e.g., `"Document Summary Prompts"`) is the **default English translation** for that key.
    *   For `SYSTEM_PROMPT`, `USER_PROMPT`, and `PROMPT_TEMPLATE` objects:
        *   The `label` field (e.g., `"document-builder.prompts.document-summary-system-label"`) is the **translation key for the UI title/label** of that prompt.
        *   The `description` field (e.g., `"document-builder.prompts.document-summary-system-description"`) is the **translation key for the UI help text/description** for that prompt.
        *   The `defaultContent` of these prompt objects is the actual prompt text, *not* directly a translation key's value, but it serves as the English version for the `label` key.

3.  **Add Keys and Translations to ALL Frontend Locale Files**:
    *   Take all translation keys identified from the `label` and `description` fields in `exportedLegalPrompts`.
    *   Add these keys and their corresponding English translations (derived from `defaultContent` for group titles/descriptions, and specific English text for prompt labels/descriptions) to `frontend/src/locales/en/common.js`.
    *   Crucially, add these same keys with their respective translations to **all other language files** (e.g., `sv/common.js`, `fr/common.js`, etc.).

**Example Snippet for `frontend/src/locales/en/common.js`:**

```javascript
// ... other translations
"document-builder": {
  "prompts": {
    "group": {
      "document_summary": {
        "title": "Document Summary Prompts", // from defaultContent of GROUP_TITLE
        "description": "Configure system and user prompts for Document Summary." // from defaultContent of GROUP_DESCRIPTION
      },
      // ... other groups
    },
    "document-summary-system-label": "Document Summary (System)", // English label for the system prompt
    "document-summary-system-description": "System prompt for instructing the AI on how to summarize a document's content and relevance to a legal task.", // English help text
    // ... other prompt labels and descriptions
  }
},
// ...
```

**Key Takeaway**: Every piece of text from `legalDrafting.js` that appears in the Document Builder UI must be represented by a translation key in `exportedLegalPrompts` and then defined in all frontend `common.js` locale files. Refer to the comments in `server/utils/chats/prompts/legalDrafting.js` and the documentation in `server/docs/document_builder_prompts.md` for more detailed examples.
