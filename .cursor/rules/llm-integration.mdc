---
description: Guidelines for LLM integration and usage
globs: server/**/*.js,frontend/**/*.js,frontend/**/*.jsx
alwaysApply: false
---
# LLM Integration and Usage

## Supported LLM Providers
- OpenAI (GPT models)
- Anthropic (Claude models)
- Azure OpenAI
- LocalAI
- And many others (Cohere, Gemini, Mistral, etc.)

## Provider Integration
- LLM providers are integrated in `server/utils/AiProviders`
- Each provider has its own implementation with consistent interfaces
- Configuration is managed through environment variables and system settings.
- For specific tasks like prompt upgrading (via `System.generateLegalTaskPrompt` or its underlying endpoint),
- LLM selection follows a hierarchy:
-   1. Explicitly passed LLM instance (e.g., resolved from workspace settings for a template-specific upgrade).
-   2. System Prompt Upgrade LLM: `process.env.LLM_PROVIDER_PU` (if set and no explicit LLM is passed).
-   3. Default System LLM: `process.env.LLM_PROVIDER` (as a fallback if the above are not applicable).

## Embedding Models
- Multiple embedding models are supported for vector search
- Embedding engines are implemented in `server/utils/EmbeddingEngines`
- Models include OpenAI, Cohere, Gemini, and others

## Prompt Construction
- Prompts are constructed in `server/utils/helpers/chat`
- Context from vector search is included in prompts
- System prompts and user messages are combined appropriately
- Token limits are managed to stay within model constraints

## Streaming Responses
- LLM responses are streamed to the client using Server-Sent Events (SSE)
- Streaming is implemented in the chat endpoints
- Frontend components handle streaming updates

## Document Drafting
- Special handling for document drafting workflows
- Templates and formatting for legal documents
- Support for document generation and editing

## Agent Plugins
- Advanced capabilities through agent plugins
- SQL queries, web scraping, memory, and other tools
- Implemented in `server/utils/agents`

## Best Practices
- Use appropriate models for different tasks
- Manage token usage efficiently
- Implement proper error handling for API calls
- Consider rate limiting and fallback options
