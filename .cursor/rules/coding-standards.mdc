---
description:
globs:
alwaysApply: true
---
# Coding Standards and Best Practices

When working on this codebase, prioritize the following qualities:

## 1. Minimal
- Write the absolute minimum code needed to accomplish the task
- Avoid unnecessary abstractions or complexity
- Keep functions and components focused on a single responsibility

## 2. Self-documenting
Code should explain itself through:
- Precise naming (verbs for functions, nouns for variables and objects)
- Single-responsibility components
- Obvious data flow
- Do not add comments unless it's for complex implementations, non-obvious design choices, or edge cases

## 3. Secure
- Ensure code is secure and protects against common vulnerabilities
- Follow built-in security practices for auth/data handling
- Validate user inputs and sanitize data

## 4. Performant
- Write efficient and fast code
- Follow optimization guides and principles
- Be mindful of unnecessary re-renders in React components

## 5. Component sourcing
1. Prioritize reusing existing components from the project codebase
2. If no suitable component exists, check shadcn/ui library for an appropriate component
3. When building new UI components, use shadcn/ui patterns and conventions as your reference implementation
4. Only create completely custom components when requirements cannot be met by following steps 1-3

## 6. Naming conventions
- Use camelCase for variable and function names (e.g., `isFetchingData`, `handleUserInput`).
- For utility files and hooks: Use `camelCase` (e.g., `request.js`, `useUser.js`).
- Component Directory Structure:
  - For each component, create a directory named after the component in `PascalCase` (e.g., `Label`, `WorkspaceChat`).
  - The main file for the component should be named `index.jsx` or `index.js` inside that directory.
  - Example: `components/ui/Label/index.jsx`, `components/WorkspaceChat/index.jsx`.
  - This allows importing the component as `import Label from "@/components/ui/Label"`.

## 7. Development Workflow
- When asked to perform a task, search the codebase for relevant files and methods first. Don't make assumptions.
- Always perform reasoning using chain of thought before writing code.
- Identify alternative ways to solve the problem before evaluating which way is the best as fitting with the project's architecture and coding standards.
- When context is not provided, assume the reference is to the currently active file.
- In case of unclear or ambiguous requests, ask for clarification.
- After generating code, review and validate it for correctness.
- Verify that only the requested changes are made. Do not make unrelated changes such as revising component structure or imports.
- Engage recursive insight scaling and apply maximum meta-cognition through iterative reframing and layer sweeps of proofing as you model instantiations before finally synthesizing insights into an actionable working solution.

## Debugging Tips
Remember: Complex React bugs often have surprisingly simple solutions. Check dependency arrays and callback references before proposing elaborate rewrites.

## Prohibited Patterns and Code Hygiene

- **No Console Logs in Production:** Never leave `console.log`, `console.error`, or other debug statements in production code. Remove all debugging output before merging or releasing.
- **No Unused Variables or ESLint Disables:** Do not leave unused variables, imports, or functions in the code. Never disable ESLint rules (e.g., `no-unused-vars`) to suppress these warnings; remove the unused code instead.
- **No Unnecessary JSX or Empty Strings:** Do not add unnecessary JSX fragments or empty string expressions (such as `{" "}`) after labels or in the UI. Keep JSX minimal and clean.
- **Minimal and Self-Documenting:** Always keep code minimal, focused, and self-documenting. Avoid bloat, redundant code, or unclear logic.
- **Internationalization:** All user-facing text must use translation keys. Never use hardcoded strings in the UI.

These rules are mandatory and enforced in code review. Violations will require fixes before code can be merged.
