---
description: Guidelines for frontend development
globs: frontend/**/*.js,frontend/**/*.jsx
alwaysApply: false
---
# Frontend Development Guidelines

## Component Structure
- Use functional components with hooks
- Keep components small and focused on a single responsibility
- Extract reusable logic into custom hooks
- Use context for global state management

## UI vs Regular Components
- UI components: Simple, reusable, visual-only elements (e.g., Button, Label) with minimal/no logic, stored in `frontend/src/components/ui`.
- Regular components: Combine UI components, add business logic and data handling, implement app features, stored in `frontend/src/components/`.
- Use UI components for pure presentation; use regular components for feature logic and data.

## UI Component Library
- **Location**: Store UI components in `frontend/src/components/ui`.
- **Core Components**:
  - **Modal** (`frontend/src/components/ui/Modal/index.jsx`): Accessible dialog with focus trapping, header/footer support, and works with the `useModal` hook.
  - **<PERSON><PERSON>** (`frontend/src/components/Button/index.jsx`): Supports variants (`default`, `outline`, `secondary`, `ghost`, `destructive`, `link`), sizes (`default`, `sm`, `icon`), `isLoading`, `asChild`, and icon integration.
  - **MarqueeText** (`frontend/src/components/ui/MarqueeText.jsx`): Scrolls long text on hover, configurable via `maxLength` and `variant`.
- **Hooks & Utilities**:
  - `useModal` (`frontend/src/hooks/useModal.js`): Manages modal state (`isOpen`, `openModal`, `closeModal`).
  - `cn` (`frontend/src/utils/classes.js`): Conditional classNames helper using `clsx` and `tailwind-merge`.
- **Best Practices for UI Components**:
  - **Modal**:
    - Use the `useModal` hook and props (`title`, `description`, `footer`) instead of customizing the header directly.
    - Avoid adding custom classes that may break layout or accessibility.
  - **Button**:
    - Choose appropriate `variant` and `size`.
    - Use `isLoading` for loading feedback.
    - Use `asChild` to compose custom elements.
    - Rely on built-in styling; do not add extra CSS classes.
  - **MarqueeText**:
    - Use for displaying overflow text in constrained spaces.
    - Adjust `maxLength` prop based on container width.
    - Select the appropriate `variant` (`default` or `upload`).

## State Management
- Use React's built-in state management (useState, useReducer) for local state
- Use context (useContext) for shared state across components
- Consider Zustand for more complex state management needs

## Styling
- Use Tailwind CSS for styling
- Only use traditional CSS for edge cases that Tailwind cannot handle
- Do **not** use the dark utility class i.e `dark:bg-black`
- Design UI to be responsive across different screen sizes i.e `flex-col md:flex-row`
- Use tailwindcss-animate for animations

## Shadcn UI Usage
- Leverage existing components provided by Shadcn UI whenever possible (see `coding-standards.mdc` component sourcing).
- Use the `cn` utility from `@/lib/utils` for conditional and merged class names.
- Customize component appearance primarily via Tailwind utility classes or CSS variables.
- Build more complex UI elements by composing multiple Shadcn components.
- Do **not** directly modify the underlying Shadcn component code in `node_modules`.
- For forms, follow the `form-guidelines.mdc` which uses Shadcn components with react-hook-form and Zod.

## Internationalization (i18n)
- Frontend translation keys are located in `frontend/src/locales/en/common.js`
- DO NOT use or create translation.json, instead use existing files and keys
- Add new translation keys to the appropriate section in the common.js file
- Ensure all user-facing text is internationalized

## Performance
- Memoize expensive calculations with useMemo
- Prevent unnecessary re-renders with useCallback and React.memo
- Lazy load components when appropriate
- Use virtualization for long lists

## Accessibility
- Ensure all interactive elements are keyboard accessible
- Use semantic HTML elements
- Provide appropriate ARIA attributes
- Ensure sufficient color contrast

## Testing
- Write unit tests for components and hooks
- Test edge cases and error states
- Use mock data for API responses

## Imports and Exports
- **Default Exports:** Use `export default function ComponentName...` when a file's primary purpose is to export a single component (e.g., `frontend/src/components/ui/Modal/index.jsx`).
  - **Import Syntax:** `import ComponentName from '@/path/to/Component';`
  - **Example (Modal):** `import Modal from '@/components/ui/Modal';` // Modal uses default export
- **Named Exports:** Use `export function utilityFunction()...` or `export { variableName }` when exporting multiple items from a single file (often used for utilities or constants).
  - **Import Syntax:** `import { utilityFunction, variableName } from '@/path/to/utils';`
  - **Example (Button):** `import { Button } from '@/components/Button';` // Button component uses named export
- **Verification:** Always check how a module is exported (default or named) before importing it to avoid `SyntaxError: The requested module does not provide an export named '...'` errors.
