---
description: Guidelines for backend development
globs: server/**/*.js,collector/**/*.js
alwaysApply: false
---

# Backend Development Guidelines

## Server Structure
- The server is built with Express.js
- Endpoints are organized in the `server/endpoints` directory
- Models for database access are in the `server/models` directory
- Utilities and helpers are in the `server/utils` directory

## API Design
- Follow RESTful principles for API endpoints
- Use appropriate HTTP methods (GET, POST, PUT, DELETE)
- Return consistent response formats
- Document APIs using Swagger/OpenAPI

## Database
- Use Prisma for database access
- Define database schema in `server/prisma/schema.prisma`
- Create migrations for schema changes
- Use transactions for operations that modify multiple records

## Error Handling
- Use try/catch blocks for async operations
- Return appropriate HTTP status codes
- Provide meaningful error messages
- Log errors with sufficient context

## Authentication & Authorization
- Validate user permissions for protected routes
- Use middleware for authentication checks
- Implement proper token validation
- Follow security best practices

## Internationalization (i18n)
- Server and collector files have specific existing files for i18n keys
- Add new translation keys to the appropriate locale files
- Ensure error messages and system messages are internationalized

## Performance
- Optimize database queries
- Use caching where appropriate
- Implement pagination for large result sets
- Monitor and optimize resource usage

## RAG Implementation
- Follow the established patterns for Retrieval-Augmented Generation
- Properly handle document ingestion and processing
- Implement vector search with appropriate similarity thresholds
- Manage context windows for LLM prompts
