name: Test Suite

on:
  pull_request:
    branches:
      - develop
  push:
    branches:
      - develop

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "20"

      - name: Install root dependencies
        run: npm install

      - name: Install server dependencies
        working-directory: server
        run: npm install

      - name: Install frontend dependencies
        working-directory: frontend
        run: npm install

      - name: Run Jest tests
        run: NODE_ENV=test npx jest
        env:
          NODE_ENV: test
          CI: true
