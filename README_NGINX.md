
#### Docker
The configuration for building the Docker image is located in `docker/Dockerfile`. Briefly, the steps performed when the image is built are:
sudo yum update -y
sudo yum install -y docker
sudo service docker start
sudo systemctl enable docker
sudo usermod -aG docker ec2-user

#### AWS setup
A new EC2 stack and instance can be created by following `cloud-deployments/aws/cloudformation/DEPLOY.md`. The configuration to be loaded is found in `cloud-deployments/aws/cloudformation/cloudfcloudformation_create_istLegal.json`.
Set the GitHub Token and Username:
export GITHUB_TOKEN=<my_token>
export GITHUB_USERNAME=<my_username>
Login to GitHub Container Registry:
sudo docker login ghcr.io -u $GITHUB_USERNAME -p $GITHUB_TOKEN

### RESTART DOCKER, UPDATE DOCKER -> main branch [Previous PROD instances]
sudo docker stop istlegal
sudo docker rm istlegal
sudo docker pull ghcr.io/rahswe/istlegal:main
sudo docker run -d --restart unless-stopped -p 8081:3001 -p 8443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:main

### RESTART DOCKER, UPDATE DOCKER -> main-prod branch [Current PROD instances]
sudo docker stop istlegal
sudo docker rm istlegal
sudo docker pull ghcr.io/rahswe/istlegal:prod
sudo docker run -d --restart unless-stopped -p 8081:3001 -p 8443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:prod

### RESTART DOCKER, UPDATE DOCKER -> main-stage branch [STAGE instances]
sudo docker stop istlegal
sudo docker rm istlegal
sudo docker pull ghcr.io/rahswe/istlegal:stage
sudo docker run -d --restart unless-stopped -p 8081:3001 -p 8443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:stage

### RESTART DOCKER, UPDATE DOCKER -> main-dev branch [DEVELOP instances]
sudo docker stop istlegal
sudo docker rm istlegal
sudo docker pull ghcr.io/rahswe/istlegal:dev
sudo docker run -d --restart unless-stopped -p 8081:3001 -p 8443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:dev

### RESTART DOCKER, UPDATE DOCKER -> develop branch [Latest PR]
sudo docker stop istlegal
sudo docker rm istlegal
sudo docker pull ghcr.io/rahswe/istlegal:develop
sudo docker run -d --restart unless-stopped -p 8081:3001 -p 8443:3001 -p 3001:3001 --cap-add SYS_ADMIN --name istlegal -v /home/<USER>/istlegal:/app/server/storage -v /home/<USER>/istlegal/.env:/app/server/.env -e STORAGE_DIR=/app/server/storage ghcr.io/rahswe/istlegal:develop

### FIRST TIME DOCKER DEPLOY -> initialize database
sudo docker exec -it istlegal bash -c "cd /app/server && npx prisma migrate dev --name init"

## Grant permission 1:
sudo chmod -R 755 /home/<USER>/istlegal
sudo chown -R ec2-user:ec2-user /home/<USER>/istlegal
sudo chmod 644 /home/<USER>/istlegal/.env

## Grant permission 2:
sudo docker exec -it istlegal /bin/bash
ls -ld /app/collector/hotdir
ls -ld /server/storage/hotdir
chmod -R 755 /app/server/.env
chmod -R 755 /app/collector/hotdir
chmod -R 755 /app/server/storage/hotdir
exit

## Grant permission 3:
sudo chmod -R 755 /home/<USER>/istlegal/hotdir/
sudo usermod -aG ec2-user nginx
chmod -R 750 /home/<USER>/istlegal/hotdir/
sudo -u nginx ls -l /home/<USER>/istlegal/hotdir/
sudo chmod +x /home/<USER>
sudo chmod +x /home/<USER>/istlegal
sudo systemctl reload nginx

## Ensure .env file exists
sudo rm -r /home/<USER>/istlegal/.env
sudo touch /home/<USER>/istlegal/.env
sudo chmod 644 /home/<USER>/istlegal/.env
sudo ls -l /home/<USER>/istlegal/.env
sudo nano /home/<USER>/istlegal/.env

## Nginx: Install, Start, Enable, Check status
sudo yum update -y
sudo yum install nginx -y
sudo systemctl start nginx
sudo systemctl enable nginx
sudo systemctl status nginx

## Nginx: Timeout
sudo nano /etc/nginx/nginx.conf
http {
proxy_read_timeout 3600;
proxy_connect_timeout 3600;
proxy_send_timeout 3600;
}

## Nginx: Increase the client_max_body_size
sudo nano /etc/nginx/nginx.conf
http {
...
client_max_body_size 5120M;
...
}
sudo nginx -t
sudo systemctl restart nginx

## Nginx: File server
sudo nano /etc/nginx/nginx.conf
server {
...
location /hotdir/ {
alias /home/<USER>/istlegal/hotdir/;
autoindex on;
try_files $uri $uri/ =404;
}
...
}
sudo systemctl restart nginx
sudo nginx -t

## Nginx Domain Configuration
sudo yum install certbot python3-certbot-nginx -y
sudo systemctl stop nginx
sudo nano /etc/nginx/conf.d/istlegal.rw.conf
sudo certbot certonly --standalone -d istlegal.rw -d www.istlegal.rw
sudo certbot renew --dry-run

With HTTPS
server {
listen 80;
server_name istlegal.rw **************;
return 301 https://$server_name$request_uri;
}
server {
listen 443 ssl;
server_name istlegal.rw **************;

      ssl_certificate /etc/letsencrypt/live/istlegal.rw/fullchain.pem;
      ssl_certificate_key /etc/letsencrypt/live/istlegal.rw/privkey.pem;
      include /etc/letsencrypt/options-ssl-nginx.conf;
      ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

      location / {
          proxy_pass http://localhost:3001;
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
      }

      location /hotdir/ {
        alias /home/<USER>/istlegal/hotdir/;
        autoindex on;
        try_files $uri $uri/ =404;
      }

}

Without HTTPS
server {
listen 80;
server_name istlegal.rw **************;

    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /hotdir/ {
      alias /home/<USER>/istlegal/hotdir/;
      autoindex on;
      try_files $uri $uri/ =404;
    }

}

sudo nano /etc/nginx/conf.d/www.istlegal.rw.conf
server {
  listen 80;
  server_name www.istlegal.rw;
  return 301 https://$server_name$request_uri;
}
server {
  listen 443 ssl;
  server_name www.istlegal.rw;

    ssl_certificate /etc/letsencrypt/live/www.istlegal.rw/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.istlegal.rw/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

}

### Verify the SSL Setup
sudo ls /etc/letsencrypt/live/istlegal.rw
sudo ls /etc/letsencrypt/live/www.istlegal.rw
sudo ls /etc/letsencrypt/options-ssl-nginx.conf
sudo nginx -t
sudo systemctl start nginx
sudo systemctl reload nginx

### INSTALL SSL
#### 1. Install EPEL repository and certbot with Nginx plugin
sudo dnf install epel-release -y
sudo dnf install certbot python3-certbot-nginx -y
#### 2. Request and install SSL certificate
sudo certbot --nginx -d www.istlegal.rw
#### 3. Create and configure timer file
sudo nano /etc/systemd/system/certbot-renewal.timer
[Unit]
Description=Timer for Certbot Renewal
[Timer]
OnCalendar=daily
RandomizedDelaySec=1h
Persistent=true
[Install]
WantedBy=timers.target
#### 4. Create and configure service file
sudo nano /etc/systemd/system/certbot-renewal.service
[Unit]
Description=Certbot Renewal Service
[Service]
Type=oneshot
ExecStart=/usr/bin/certbot renew --quiet --agree-tos
[Install]
WantedBy=multi-user.target
#### 5. Enable and start renewal timer
sudo systemctl daemon-reload
sudo systemctl enable certbot-renewal.timer
sudo systemctl start certbot-renewal.timer
#### 6. Check timer status
sudo systemctl status certbot-renewal.timer
#### 7. Test renewal configuration
sudo certbot renew --dry-run


## Setup a React Front Static Website
sudo yum update -y
sudo yum install nodejs -y
sudo node -v
sudo npm -v

sudo rm -rf /home/<USER>/www.istlegal.rw
sudo mkdir /home/<USER>/www.istlegal.rw
sudo chown ec2-user:ec2-user /home/<USER>/www.istlegal.rw
cd /home/<USER>/www.istlegal.rw

sudo yum install wget -y
cd /home/<USER>/www.istlegal.rw
ls
sudo rm -rf *
sudo wget https://ist-legal.rw/upload/istlegal-web-dev.zip
sudo unzip istlegal-web-dev.zip
sudo mv istlegal-web-dev/* .
sudo rm -rf istlegal-web-dev
sudo rm istlegal-web-dev.zip

cd /home/<USER>/www.istlegal.rw
sudo npm install --verbose
sudo npm run build
sudo npm install -g pm2
sudo pm2 start npm --name "www.istlegal.rw" -- run start
sudo pm2 list
sudo pm2 startup
sudo pm2 save

enable port 3000
sudo curl http://localhost:3000
http://**************:3000
www.istlegal.rw

sudo pm2 list
sudo pm2 delete www.istlegal.rw
Stop and Remove All Containers


# Run using screen
screen -S www.istlegal.rw
cd www.istlegal.rw
sudo npm run dev
sudo npm run build
sudo npm run start
screen -r www.istlegal.rw
