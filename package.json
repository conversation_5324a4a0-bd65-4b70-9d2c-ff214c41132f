{"name": "ist-legal", "version": "0.2.0", "description": "IST Legal platform.", "main": "index.js", "type": "module", "author": "IST Legal", "license": "Copyright", "engines": {"node": ">=20"}, "scripts": {"lint": "(npm run lint:server && npm run lint:frontend) || (echo '<PERSON><PERSON> failed' && exit 1)", "lint:server": "cd server && npm run lint", "lint:frontend": "cd frontend && npm run lint", "check-translations": "node scripts/verifyLocaleFiles.mjs", "verify:translations": "node scripts/verifyLocaleFiles.mjs", "setup": "cd server && npm install && cd ../collector && npm install && cd ../frontend && npm install && cd .. && npm run setup:envs && npm run prisma:setup && echo \"Please run npm run dev:server, npm run dev:collector, and npm run dev:frontend in separate terminal tabs.\"", "setup:envs": "node scripts/copy-env.js", "dev:server": "cd server && npm run dev", "dev:collector": "cd collector && npm run dev", "dev:frontend": "cd frontend && npm run dev", "prisma:generate": "cd server && npx prisma generate", "prisma:migrate": "cd server && npx prisma migrate dev --name init", "prisma:seed": "cd server && npx prisma db seed", "prisma:setup": "npm run prisma:generate && npm run prisma:migrate && npm run prisma:seed", "prisma:reset": "node scripts/reset-db.js && npm run prisma:migrate", "prisma:devupdate": "cd server/prisma && npx prisma db push", "prod:server": "cd server && npm start", "prod:frontend": "cd frontend && npm run build", "generate:cloudformation": "node cloud-deployments/aws/cloudformation/generate.mjs", "generate::gcp_deployment": "node cloud-deployments/gcp/deployment/generate.mjs"}, "private": false, "devDependencies": {"@jest/globals": "^29.7.0", "cursor-tools": "latest", "eslint": "^9.25.1", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.5.1", "node-mocks-http": "^1.11.0", "prettier": "^3.5.3", "prisma": "^5.15.0", "recast": "^0.23.11"}, "dependencies": {"@ladjs/graceful": "^4.0.0", "@prisma/client": "^5.15.0", "@radix-ui/react-tooltip": "^1.2.7", "bcrypt": "^5.1.1", "bree": "^9.2.3", "docxtemplater": "^3.43.0", "immer": "^10.1.1", "js-tiktoken": "^1.0.16", "pizzip": "^3.0.6", "remark-docx": "^0.1.6", "remark-parse": "^10.0.2", "unified": "^11.0.5"}, "lint-staged": {"frontend/**/*.{js,jsx,json,css,md}": ["eslint --quiet", "prettier --write"], "server/**/*.{js,jsx,json,css,md}": ["eslint --quiet", "prettier --write"], "collector/**/*.{js,jsx,json,css,md}": ["eslint --quiet", "prettier --write"]}}