import packageJson from "./server/node_modules/eslint-plugin-package-json/lib/index.js"
import jsoncESLintParser from "./server/node_modules/jsonc-eslint-parser/lib/index.js"
import globals from "./server/node_modules/globals/index.js"
import eslintRecommended from "./server/node_modules/@eslint/js/src/index.js"
import eslintConfigPrettier from "./server/node_modules/eslint-config-prettier/index.js"
import prettier from "./server/node_modules/eslint-plugin-prettier/eslint-plugin-prettier.js"
import react from "./server/node_modules/eslint-plugin-react/index.js"
import reactRefresh from "./server/node_modules/eslint-plugin-react-refresh/index.js"
import reactHooks from "./server/node_modules/eslint-plugin-react-hooks/index.js"
import ftFlow from "./server/node_modules/eslint-plugin-ft-flow/dist/index.js"
import hermesParser from "./server/node_modules/hermes-eslint/dist/index.js"
import unusedImports from "./server/node_modules/eslint-plugin-unused-imports/dist/index.js"

const reactRecommended = react.configs.recommended
const jsxRuntime = react.configs["jsx-runtime"]

const commonRules = {
  // Less critical issues that should be warnings
  "no-case-declarations": "warn",
  "no-useless-catch": "warn",
  "no-prototype-builtins": "warn",
  "no-unused-vars": "warn",
  "no-useless-escape": "warn",
  "no-irregular-whitespace": "warn",
  "no-empty-pattern": "warn",
  "no-empty": "warn",
  "no-extra-boolean-cast": "warn",
  "prettier/prettier": "warn"
}
export default [
  eslintRecommended.configs.recommended,
  eslintConfigPrettier,
  {
    languageOptions: {
      parser: hermesParser,
      parserOptions: {
        ecmaFeatures: { jsx: true }
      },
      ecmaVersion: 2022,
      sourceType: "module",
      globals: {
        ...globals.browser,
        ...globals.es2020,
        ...globals.node,
        globalThis: true
      }
    },
    linterOptions: { reportUnusedDisableDirectives: true },
    settings: { react: { version: "18.2" } },
    plugins: {
      ftFlow,
      react,
      "jsx-runtime": jsxRuntime,
      "react-hooks": reactHooks,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...reactRecommended.rules,
      ...reactHooks.configs.recommended.rules,
      ...ftFlow.recommended,
      ...commonRules
    }
  },
  {
    files: ["**/package.json"],
    languageOptions: {
      parser: jsoncESLintParser
    },
    plugins: {
      "package-json": packageJson
    },
    rules: {
      "package-json/order-properties": "off",
      "package-json/require-author": "off",
      "package-json/require-description": "off",
      "package-json/require-engines": "off",
      "package-json/require-files": "off",
      "package-json/require-keywords": "off",
      "package-json/require-name": "off",
      "package-json/require-types": "off",
      "package-json/require-version": "off",
      "package-json/sort-collections": "off",
      "package-json/valid-local-dependency": "off",
      "package-json/valid-name": "off",
      "package-json/valid-package-definition": "off",
      "package-json/valid-repository-directory": "off",
      "package-json/valid-version": "off",
      "package-json/no-empty-fields": "off",
      "package-json/no-redundant-files": "off",
      "package-json/repository-shorthand": "off",
      "package-json/restrict-dependency-ranges": "off",
      "package-json/unique-dependencies": "error"
    }
  },
  // Frontend JS files
  {
    files: ["frontend/src/**/*.js"],
    plugins: {
      ftFlow,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules
    }
  },
  // Frontend JSX files
  {
    files: ["frontend/src/**/*.jsx"],
    plugins: {
      ftFlow,
      react,
      "jsx-runtime": jsxRuntime,
      "react-hooks": reactHooks,
      "react-refresh": reactRefresh,
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...jsxRuntime.rules,
      ...commonRules,
      "react/prop-types": "off",
      "react-refresh/only-export-components": "warn",
      "react/no-unescaped-entities": "warn",
      "react-hooks/exhaustive-deps": "off"
    }
  },
  // Server files (non-test)
  {
    files: [
      "server/endpoints/**/*.js",
      "server/models/**/*.js",
      "server/swagger/**/*.js",
      "server/utils/**/*.js",
      "server/index.js"
    ],
    plugins: {
      prettier,
      "unused-imports": unusedImports
    },
    rules: {
      ...commonRules,
      "react-hooks/rules-of-hooks": "off",
      "react/prop-types": "off",
      "react-refresh/only-export-components": "off"
    }
  },
  // ADD A NEW BLOCK FOR TEST FILES
  {
    files: [
      "**/*.test.js", // Pattern for JS test files
      "**/*.test.jsx", // Pattern for JSX test files
      "**/*.spec.js", // Include .spec.js as well if used
      "**/*.spec.jsx", // Include .spec.jsx pattern
      "frontend/src/setupTests.js"
    ],
    languageOptions: {
      globals: {
        ...globals.jest, // Add Jest globals
        ...globals.node // Ensure Node globals like 'require' are also here
      }
    },
    rules: {
      // Add any test-specific rule overrides here if needed
      // For example, you might allow certain patterns common in tests
    }
  }
]
